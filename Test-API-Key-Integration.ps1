# Test SPMJ KOLEJ Password Management Microservice Integration
# Tests the http://localhost:5000/ endpoint with API Key authentication

Write-Host "=== SPMJ KOLEJ PASSWORD MICROSERVICE INTEGRATION TEST ===" -ForegroundColor Green
Write-Host "Testing endpoint: http://localhost:5000/" -ForegroundColor Yellow
Write-Host "API Key authentication: Required" -ForegroundColor Yellow

$baseUrl = "http://localhost:5000"
$apiKey = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
$logFile = "d:\source_code\.NET 3.5. - Q\Microservice-Integration-Test.log"

# Initialize log
"=== SPMJ KOLEJ Microservice Integration Test ===" | Out-File $logFile
"Date: $(Get-Date)" | Out-File $logFile -Append
"Base URL: $baseUrl" | Out-File $logFile -Append
"API Key configured: $($apiKey.Length) characters" | Out-File $logFile -Append
"" | Out-File $logFile -Append

$testsPassed = 0
$testsFailed = 0

# Function to test an endpoint
function Test-Endpoint($url, $method = "GET", $headers = @{}, $body = $null) {
    try {
        $params = @{
            Uri = $url
            Method = $method
            Headers = $headers
            TimeoutSec = 10
        }
        
        if ($body) {
            $params.Body = $body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params
        return @{ Success = $true; Response = $response; Status = "200" }
    }
    catch {
        $statusCode = "Unknown"
        if ($_.Exception.Response) {
            $statusCode = [int]$_.Exception.Response.StatusCode
        }
        return @{ Success = $false; Error = $_.Exception.Message; Status = $statusCode }
    }
}

# Test 1: Basic connectivity
Write-Host "`n1. Testing basic microservice connectivity..." -ForegroundColor Cyan
$result = Test-Endpoint "$baseUrl/api/health"
if ($result.Success) {
    Write-Host "✅ Microservice is accessible" -ForegroundColor Green
    Write-Host "   Response: $($result.Response | ConvertTo-Json -Compress)" -ForegroundColor White
    $testsPassed++
    "✅ Basic connectivity test PASSED" | Out-File $logFile -Append
} else {
    Write-Host "❌ Microservice is not accessible: $($result.Error)" -ForegroundColor Red
    $testsFailed++
    "❌ Basic connectivity test FAILED: $($result.Error)" | Out-File $logFile -Append
}

# Test 2: Check configuration
Write-Host "`n2. Verifying SPMJ application configuration..." -ForegroundColor Cyan
$webConfigPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Web.config"

if (Test-Path $webConfigPath) {
    $webConfig = Get-Content $webConfigPath -Raw
    
    if ($webConfig -like "*EmailServiceApiKey*") {
        Write-Host "✅ EmailServiceApiKey configured correctly" -ForegroundColor Green
        $testsPassed++
    } else {
        Write-Host "❌ EmailServiceApiKey not found" -ForegroundColor Red
        $testsFailed++
    }
    
    if ($webConfig -like "*localhost:5000*") {
        Write-Host "✅ Correct microservice URL configured" -ForegroundColor Green
        $testsPassed++
    } else {
        Write-Host "❌ Microservice URL not configured correctly" -ForegroundColor Red
        $testsFailed++
    }
} else {
    Write-Host "❌ Web.config not found" -ForegroundColor Red
    $testsFailed++
}

# Summary
Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Green
Write-Host "Tests Passed: $testsPassed" -ForegroundColor Green
Write-Host "Tests Failed: $testsFailed" -ForegroundColor $(if ($testsFailed -eq 0) { "Green" } else { "Red" })

if ($testsFailed -eq 0) {
    Write-Host "`n✅ MICROSERVICE INTEGRATION IS READY!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️ Some configuration issues found" -ForegroundColor Yellow
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Ensure microservice is running: http://localhost:5000" -ForegroundColor White
Write-Host "2. Test password change functionality in SPMJ KOLEJ" -ForegroundColor White
Write-Host "3. Verify email notifications are sent" -ForegroundColor White

Write-Host "`nLog saved to: $logFile" -ForegroundColor Gray
