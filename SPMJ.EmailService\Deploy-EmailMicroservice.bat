@echo off
echo ==========================================
echo SPMJ Email Microservice Deployment Script
echo ==========================================
echo.

cd /d "d:\source_code\.NET 3.5. - Q\SPMJ.EmailService"

echo [1/4] Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Package restore failed!
    pause
    exit /b 1
)

echo.
echo [2/4] Building the microservice...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo [3/4] Checking email service configuration...
if not exist "appsettings.json" (
    echo WARNING: appsettings.json not found!
    echo Please configure email settings before running.
)

echo.
echo [4/4] Starting SPMJ Email Microservice...
echo.
echo ==========================================
echo Microservice will start on http://localhost:5000
echo.
echo Available Endpoints:
echo   POST /api/admin/password/send-notification
echo   POST /api/admin/password/send-welcome  
echo   POST /api/admin/password/send-force-reset
echo   POST /api/admin/password/validate-email
echo   GET  /api/admin/password/health
echo.
echo Integration URL: http://localhost:5000/api/admin/password
echo ==========================================
echo.

dotnet run --configuration Release

pause
