namespace SPMJ.EmailService.Models;

public class EmailSettings
{
    public string SmtpServer { get; set; } = string.Empty;
    public int SmtpPort { get; set; }
    public bool UseSsl { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
}

public class PasswordResetRequest
{
    public string UserId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? BaseUrl { get; set; }
}

public class PasswordResetResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Token { get; set; }
}

public class SendPasswordResetEmailRequest
{
    public string UserId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string TempPassword { get; set; } = string.Empty;
}

public class AdminPasswordCreateRequest
{
    public string UserId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string TempPassword { get; set; } = string.Empty;
    public string AdminId { get; set; } = string.Empty;
}

public class AdminPasswordResetRequest
{
    public string UserId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? TempPassword { get; set; }
    public string AdminId { get; set; } = string.Empty;
}

public class OtpRequest
{
    public string UserId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Purpose { get; set; } = "LOGIN"; // LOGIN, PASSWORD_RESET, ADMIN_ACTION
}

public class OtpValidationRequest
{
    public string UserId { get; set; } = string.Empty;
    public string OtpCode { get; set; } = string.Empty;
    public string Purpose { get; set; } = "LOGIN";
}

public class OtpResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? OtpCode { get; set; } // Only for development/testing
}

public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class AdminPasswordNotificationRequest
{
    public string To { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string TemplateType { get; set; } = string.Empty;
    public AdminPasswordNotificationData Data { get; set; } = new();
}

public class AdminPasswordNotificationData
{
    public string UserName { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool IsTemporary { get; set; }
    public string SystemUrl { get; set; } = string.Empty;
    public string SystemName { get; set; } = string.Empty;
    public string Timestamp { get; set; } = string.Empty;
    public string AdminId { get; set; } = string.Empty;
    public string AdminName { get; set; } = string.Empty;
    public string SupportEmail { get; set; } = string.Empty;
}

public class WelcomeEmailRequest
{
    public string To { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string TemplateType { get; set; } = string.Empty;
    public WelcomeEmailData Data { get; set; } = new();
}

public class WelcomeEmailData
{
    public string UserName { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string TempPassword { get; set; } = string.Empty;
    public string SystemUrl { get; set; } = string.Empty;
    public string SupportEmail { get; set; } = string.Empty;
}

public class EmailResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? EmailId { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

public class PasswordChangeNotificationRequest
{
    public string To { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string TemplateType { get; set; } = "password_change_notification";
    public PasswordChangeNotificationData Data { get; set; } = new();
}

public class PasswordChangeNotificationData
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string SystemName { get; set; } = string.Empty;
    public string Timestamp { get; set; } = string.Empty;
    public string SupportEmail { get; set; } = string.Empty;
}
