﻿<%@ Page Language="vb" MaintainScrollPositionOnPostback="true" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_Pelatih_Pinda4.aspx.vb" Inherits="SPMJ.P1_Pelatih_Pinda4" 
    title="Pendaftaran Pinda" Trace="false" uiCulture="ms" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        .style12
        {
            height: 21px;
            width: 498px;
        }
        .style26
        {
            height: 20px;
            width: 765px;
        }
        .style33
    {
            height: 20px;
            width: 498px;
        }
        .style43
        {
            height: 21px;
            width: 765px;
        }
        .style50
        {
            height: 22px;
            width: 498px;
        }
        p.MsoNormal
	{margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	        margin-left: 0in;
            margin-right: 0in;
            margin-top: 0in;
        }
        .style63
        {
            height: 22px;
            width: 170px;
        }
        .style65
        {
            height: 24px;
            }
        .style67
        {
            height: 24px;
            margin-left: 40px;
            width: 498px;
        }
        .style68
        {
            height: 6px;
        }
        .style69
        {
            width: 69px;
        }
        .style76
        {
            height: 20px;
            width: 170px;
        }
        .style77
        {
            height: 21px;
            width: 170px;
        }
        .style78
        {
            width: 170px;
        }
        .style80
        {
            height: 13px;
        }
        .style83
        {
            height: 23px;
            color: #CC0000;
        }
        .style84
        {
            height: 24px;
            width: 170px;
        }
        .style85
        {
            width: 77px;
        }
        .style86
        {
            width: 498px;
        }
        .style87
        {
            height: 23px;
            width: 498px;
        }
        .style88
        {
            width: 75px;
        }
        .style89
        {
            height: 23px;
            width: 75px;
        }
        .style90
        {
            height: 22px;
            width: 75px;
        }
        .style91
        {
            width: 99px;
        }
        .style92
        {
            height: 23px;
            width: 99px;
        }
        .style93
        {
            width: 30px;
        }
        .style94
        {
            height: 23px;
            width: 30px;
        }
        .style95
        {
            height: 35px;
            width: 30px;
        }
        .style96
        {
            width: 219px;
            height: 35px;
        }
        .style97
        {
            height: 35px;
            width: 99px;
        }
        .style98
        {
            height: 35px;
            width: 75px;
        }
        .style99
        {
            height: 17px;
            width: 30px;
        }
        .style100
        {
            width: 219px;
            height: 17px;
        }
        .style101
        {
            height: 17px;
            width: 99px;
        }
        .style102
        {
            height: 17px;
            width: 75px;
        }
        .style103
        {
            height: 21px;
            width: 30px;
        }
        .style104
        {
            width: 219px;
            height: 21px;
        }
        .style105
        {
            height: 21px;
            width: 99px;
        }
        .style106
        {
            height: 21px;
            width: 75px;
        }
        .style107
        {
            height: 25px;
            width: 30px;
        }
        .style108
        {
            width: 219px;
            height: 25px;
        }
        .style109
        {
            height: 25px;
            width: 99px;
        }
        .style110
        {
            height: 25px;
            width: 75px;
        }
        .style111
        {
            height: 4px;
            width: 220px;
        }
        .style112
        {
            height: 4px;
        }
        .style114
        {
            height: 12px;
            width: 220px;
        }
        .style115
        {
            height: 12px;
        }
        .style123
        {
            width: 219px;
            height: 23px;
        }
        .style124
        {
            height: 28px;
            width: 30px;
        }
        .style125
        {
            width: 219px;
            height: 28px;
        }
        .style126
        {
            height: 13px;
            text-align: center;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    &nbsp;<asp:Localize ID="Localize1" runat="server"></asp:Localize><div style="width:100%; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
    <table id="Table1" width="100%" style="font-variant: small-caps">
                            <br>
    <tr><td>
    
    <table id="Table2" align="center" border="0" cellpadding="-1" cellspacing="0"       
            style="border: 1px solid black; WIDTH: 780px; HEIGHT:80px; left: 0px; top: 70px; margin-left: 0px; position: static; font-family: Arial; font-size: 8pt; text-transform: none; line-height: 21px;" 
            bgcolor="#F5F5F1">
        <tr>
            <td align="center" bgcolor="#006699" 
                valign="top" colspan="3" 
                style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #000000; vertical-align: middle; text-align: center; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bolder;" 
                height="20" >
                pinda/semak rekod  pelatih</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style26">
                            <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                            </asp:ScriptManagerProxy>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" valign="middle" class="style26">
                <asp:Panel ID="Panel7" runat="server" Width="100%">
                    <table style="height: 100%;" ID="Table10" cellpadding="-1" cellspacing="-1" width="100%">
                    <tr style="line-height: 21px; background-color: #999966;" bgcolor="#999966">
						    <td valign="middle" align="left" bgcolor="#ffffff" class="style114"                 
                                style="background-color: #999966; font-family: Arial; font-size: 8pt; color: #FFFFFF;" colspan="2">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>                           
                    </tr>                                           
                        <tr style="line-height: 21px; background-color: #999966;" bgcolor="#999966">
						    <td valign="middle" align="left" bgcolor="#ffffff" class="style114"                 
                                style="background-color: #999966; font-family: Arial; font-size: 8pt; color: #FFFFFF;">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JENIS KURSUS</TD>
                            <td align="left" bgcolor="#999966" valign="bottom" class="style115">
                                <asp:DropDownList ID="Cb_Kursus" runat="server" AutoPostBack="True" 
                                    CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                                    Width="300px">
                                    <asp:ListItem></asp:ListItem>
                                </asp:DropDownList>
                            </td>
                        </tr>
                        <tr>
                            <td bgcolor="#999966" class="style111" colspan="1"                                 
                                style="background-color: #999966; font-family: Arial; font-size: 8pt; color: #FFFFFF;">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;KELAYAKAN</td>
                            <td bgcolor="#999966" class="style112" style="border-style: none" 
                                valign="middle">
                                <asp:DropDownList ID="Cb_Kelayakan" runat="server" AutoPostBack="True" 
                                Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="1" 
                                    Width="150px">
                                </asp:DropDownList>
                                <asp:DropDownList ID="Cb_Kelayakan_Taraf" runat="server" AutoPostBack="True" 
                                    Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="1" 
                                    Width="100px">
                                    <asp:ListItem Value="0">-</asp:ListItem>
                                    <asp:ListItem Value="1">SETARAF</asp:ListItem>
                                </asp:DropDownList>
                            </td>
                        </tr>                           
                        <tr style="line-height: 21px; background-color: #999966;" bgcolor="#999966">
						    <td valign="middle" align="left" bgcolor="#ffffff" class="style114"                 
                                style="background-color: #999966; font-family: Arial; font-size: 8pt; color: #FFFFFF;" colspan="2">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>                           
                    </tr>                      
                    </table>
                </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>
        <tr>
             <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-right-width: 1px" valign="middle" class="style26">
                <asp:Panel ID="Panel6" runat="server" Width="100%">
                    <table style="height: 100%;" ID="Table14" cellpadding="-1" cellspacing="-1" width="100%">                                                
                        <tr>
                            <td bgcolor="#DFDFD0"                                                          
                                
                                style="border-left-style: none; border-left-width: 1px; border-left-color: #999966; border-bottom-style: none; border-bottom-width: 1px; 
                                border-bottom-color: #999966; border-right-style: none; border-right-width: 1px; border-right-color: #999966;" 
                                class="style126" height="1%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>MAKLUMAT 
                                KHAS</b></td>
                        </tr>
                        <tr>
                            <td bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-bottom-width: 1px; 
                                border-right-width: 1px" align="center">
                                <table id="Table15" align="center" border="0" cellpadding="-1" cellspacing="-1" style="border: 1px solid #999966; height: 100%;">
                                    <tr>
                                        <td>
                                            <table id="Table16" align="left" border="0" cellpadding="-1" cellspacing="-1" style="text-align: left; ">
                                                <tr bgcolor="WhiteSmoke" 
                                                                    style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966">
                                                                    <td bordercolor="#000000"                                                                     
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style93">
                                                                        &nbsp;</td>
                                                                    <td align="left" 
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" colspan="3">
                                                                        &nbsp;<b>KEPUTUSAN MUET</b>&nbsp;</td>                                                                    
                                                                </tr>                                                                                                                                                                                                                                                               
                                                                <tr bgcolor="White">
                                                                    <td class="style95">
                                                                        <asp:TextBox ID="Textbox21" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                                                    </td>
                                                                    <td class="style96">
                                                                        <asp:UpdatePanel ID="UpdatePanel34" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Cb_Muet" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="36" Width="230px">MUET</asp:TextBox>
                                                                                <asp:TextBox ID="Cb_Muetx" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style97">
                                                                        <asp:UpdatePanel ID="UpdatePanel37" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsMuet" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="2" Width="80px">
                                                                                    <asp:ListItem Value="0">(TAHAP)</asp:ListItem>
                                                                                    <asp:ListItem Value="1">Tiada MUET</asp:ListItem>
                                                                                    <asp:ListItem Value="2">Tahap 1</asp:ListItem>
                                                                                    <asp:ListItem Value="3">Tahap 2</asp:ListItem>
                                                                                    <asp:ListItem Value="4">Tahap 3</asp:ListItem>
                                                                                    <asp:ListItem Value="5">Tahap 3</asp:ListItem>
                                                                                    <asp:ListItem Value="6">Tahap 4</asp:ListItem>
                                                                                    <asp:ListItem Value="7">Tahap 5Tahap 6</asp:ListItem>
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style98">&nbsp;</td>
                                                                </tr>                                                                                                        
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; 
                                border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px" 
                                height="15" valign="middle">
                            </td>                                
                        </tr>
                    </table>
                </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>
        <tr>
             <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-right-width: 1px" valign="middle" class="style26">
                <asp:Panel ID="Panel5" runat="server" Width="100%">
                    <table style="height: 100%;" ID="Table11" cellpadding="-1" cellspacing="-1" width="100%">                                                
                        <tr>
                            <td bgcolor="#DFDFD0"                                                          
                                style="border-left-style: none; border-left-width: 1px; border-left-color: #999966; border-bottom-style: none; border-bottom-width: 1px; 
                                border-bottom-color: #999966; border-right-style: none; border-right-width: 1px; border-right-color: #999966;" 
                                align="left" class="style80" height="1%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>MAKLUMAT 
                                KHAS</b></td>
                        </tr>
                        <tr>
                            <td bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-bottom-width: 1px; 
                                border-right-width: 1px" align="center">
                                <table id="Table12" align="center" border="0" cellpadding="-1" cellspacing="-1" style="border: 1px solid #999966; height: 100%;">
                                    <tr>
                                        <td>
                                            <table id="Table13" align="left" border="0" cellpadding="-1" cellspacing="-1" style="text-align: left; ">                                                                                                                   
                                                                <tr bgcolor="WhiteSmoke" 
                                                                    style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966">
                                                                    <td bordercolor="#000000"                                                                     
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style93">
                                                                        &nbsp;</td>
                                                                    <td align="left" 
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" colspan="3">
                                                                        &nbsp;<b>KEPUTUSAN BAHASA INGGERIS (CALON SETARAF / LUAR NEGARA)</b>&nbsp;</td>                                                                    
                                                                </tr>                                                             
                                                                <tr bgcolor="White">
                                                                    <td class="style95">
                                                                        <asp:TextBox ID="Textbox4" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                                                    </td>
                                                                    <td class="style96">
                                                                        <asp:UpdatePanel ID="UpdatePanel25" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Luar" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px" 
                                                                                    AutoPostBack="True">
                                                                                    <asp:ListItem Value="0">(PILIHAN)</asp:ListItem>                                                                                    
                                                                                    <asp:ListItem Value="1">IELTS</asp:ListItem>
                                                                                    <asp:ListItem Value="2">TOEFL</asp:ListItem>
                                                                                    <asp:ListItem Value="3">ENGLISH GCE O LEVEL</asp:ListItem>
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_Luarx" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style97">
                                                                        <asp:UpdatePanel ID="UpdatePanel26" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Cb_KptsLuar" runat="server" CssClass="std" Width="80px" 
                                                                                    Wrap="False"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style98">&nbsp;</td>
                                                                </tr>                                                                                                        
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; 
                                border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px" 
                                height="15" valign="middle">                                
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>         
        <tr>
             <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-right-width: 1px" valign="middle" class="style26">
                <asp:Panel ID="Panel4" runat="server" Width="100%">
                    <table style="height: 100%;" ID="Table7" cellpadding="-1" cellspacing="-1" width="100%">                                                
                        <tr>
                            <td bgcolor="#DFDFD0"                                                          
                                style="border-left-style: none; border-left-width: 1px; border-left-color: #999966; border-bottom-style: none; border-bottom-width: 1px; 
                                border-bottom-color: #999966; border-right-style: none; border-right-width: 1px; border-right-color: #999966;" 
                                align="left" class="style80" height="1%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>MAKLUMAT MATRIKULASI / ASASI</b></td>
                        </tr>
                        <tr>
                            <td bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-bottom-width: 1px; 
                                border-right-width: 1px" align="center">
                                <table id="Table8" align="center" border="0" cellpadding="-1" cellspacing="-1" style="border: 1px solid #999966; height: 100%;">
                                    <tr>
                                        <td>
                                            <table id="Table9" align="left" border="0" cellpadding="-1" cellspacing="-1" style="text-align: left; ">
                                                <tr bgcolor="WhiteSmoke" 
                                                                    style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966">
                                                                    <td bordercolor="#000000"                                                                     
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style93">
                                                                        &nbsp;<b>BIL.</b></td>
                                                                    <td align="left" 
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;">
                                                                        &nbsp;<b>SUBJEK</b>&nbsp;</td>
                                                                    <td align="char" style="BORDER-BOTTOM: 1px solid #999966; font-weight: 700;" 
                                                                        class="style91">
                                                                        KEPUTUSAN</td>
                                                                    <td style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style88">
                                                                        &nbsp;</td>
                                                                </tr>                                                                
                                                                <tr bgcolor="White">
                                                                    <td class="style95">
                                                                        <asp:TextBox ID="Textbox16" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                                                    </td>
                                                                    <td class="style96">
                                                                        <asp:UpdatePanel ID="UpdatePanel27" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_SbjM" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px">
                                                                                    <asp:ListItem Value="0">(PILIHAN)</asp:ListItem>
                                                                                    <asp:ListItem Value="1">MATRIKULASI</asp:ListItem>
                                                                                    <asp:ListItem Value="2">PROGRAM ASASI</asp:ListItem>
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_SbjMx" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style97">
                                                                        <asp:UpdatePanel ID="UpdatePanel28" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Cb_KptsM" runat="server" CssClass="std" Width="80px" 
                                                                                    Wrap="False"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style98"><i>(PNGK)</i>                                                                       
                                                                    </td>
                                                                </tr>                                                                                                        
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; 
                                border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px" 
                                height="15" valign="middle">                                
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr> 
        <tr>
             <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-right-width: 1px" valign="middle" class="style26">
                <asp:Panel ID="Panel3" runat="server" Width="100%">
                    <table style="height: 100%;" ID="Table4" cellpadding="-1" cellspacing="-1" width="100%">                                               
                        <tr>
                            <td bgcolor="#DFDFD0"                                                          
                                
                                style="border-left-style: none; border-left-width: 1px; border-left-color: #999966; border-bottom-style: none; border-bottom-width: 1px; 
                                border-bottom-color: #999966; border-right-style: none; border-right-width: 1px; border-right-color: #999966;" 
                                class="style126" height="1%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>MAKLUMAT 
                                SUBJEK</b></td>
                        </tr>
                        <tr>
                            <td bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-bottom-width: 1px; 
                                border-right-width: 1px" align="center">
                                <table id="Table5" align="center" border="0" cellpadding="-1" cellspacing="-1" style="border: 1px solid #999966; height: 100%;">
                                    <tr>
                                        <td>
                                            <table id="Table6" align="left" border="0" cellpadding="-1" cellspacing="-1" style="text-align: left; ">
                                                <tr bgcolor="White">
                                                                    <td class="style107">
                                                                        <asp:TextBox ID="Textbox14" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px" Visible="False"></asp:TextBox>
                                                                    </td>
                                                                    <td class="style108">
                                                                        <asp:UpdatePanel ID="UpdatePanel29" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Tx_Aliran" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Bold="True" 
                                                                                    Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="36" 
                                                                                    Width="230px">ALIRAN</asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style109" colspan="2">
                                                                        <asp:UpdatePanel ID="UpdatePanel31" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Aliran" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="6" Width="80px" 
                                                                                    AutoPostBack="True">
                                                                                    <asp:ListItem></asp:ListItem>
                                                                                    <asp:ListItem Value="1">SAINS</asp:ListItem>
                                                                                    <asp:ListItem Value="2">SASTERA</asp:ListItem>
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>                                                                    
                                                                </tr> 
                                                <tr bgcolor="WhiteSmoke" 
                                                                    style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966">
                                                                    <td bordercolor="#000000"                                                                     
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style93">
                                                                        &nbsp;<b>BIL.</b></td>
                                                                    <td align="left" 
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;">
                                                                        &nbsp;<b>SUBJEK</b>&nbsp;</td>
                                                                    <td align="char" style="BORDER-BOTTOM: 1px solid #999966; font-weight: 700;" 
                                                                        class="style91">
                                                                        KEPUTUSAN</td>
                                                                    <td style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style88">
                                                                        &nbsp;</td>
                                                </tr>                                           
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox5" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel23" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_SbjT1" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px" Visible="False">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_SbjT1x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel24" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsT1" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="8" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                    </td>
                                                                </tr>  
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox3" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">2.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel22" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_SbjT2" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px" Visible="False">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_SbjT2x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel30" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsT2" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="8" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                    </td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox15" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">3.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel32" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_SbjT3" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px" Visible="False">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_SbjT3x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel33" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsT3" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="8" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                    </td>
                                                                </tr> 
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox18" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">4.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel35" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_SbjT4" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px" Visible="False">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_SbjT4x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel36" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsT4" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="8" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">                                                                        
                                                                    </td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox20" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">5.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel38" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_SbjT5" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px" Visible="False">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_SbjT5x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel39" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsT5" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="8" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                    </td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style124">
                                                                        <asp:TextBox ID="Textbox19" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px" Visible="False"></asp:TextBox>
                                                                    </td>
                                                                    <td class="style125" colspan="3">
                                                                        <asp:UpdatePanel ID="UpdatePanel44" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="TextBox23" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Bold="True" 
                                                                                    Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="36" 
                                                                                    Width="230px">MAKLUMAT SPM</asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>                                                                                                                                       
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:UpdatePanel ID="UpdatePanel45" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Textbox13" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>                                                                        
                                                                    </td>
                                                                    <td class="style123">
                                                                        <asp:UpdatePanel ID="UpdatePanel40" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Cb_SbjT6" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">MATEMATIK</asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel41" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsT6" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="6" Width="80px">
                                                                                    <asp:ListItem></asp:ListItem>
                                                                                    <asp:ListItem Value="1">A+</asp:ListItem>
                                                                                    <asp:ListItem Value="2">1A\A</asp:ListItem>
                                                                                    <asp:ListItem Value="3">2A\A-</asp:ListItem>
                                                                                    <asp:ListItem Value="4">3B\B+</asp:ListItem>
                                                                                    <asp:ListItem Value="5">4B\B</asp:ListItem>
                                                                                    <asp:ListItem Value="6">5C\C+</asp:ListItem>
                                                                                    <asp:ListItem Value="7">6C\C</asp:ListItem>
                                                                                    <asp:ListItem Value="8">7D\D</asp:ListItem>
                                                                                    <asp:ListItem Value="9">8E\E</asp:ListItem>
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                    </td>
                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:UpdatePanel ID="UpdatePanel46" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Textbox22" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">2.</asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>                                                                        
                                                                    </td>
                                                                    <td class="style123">
                                                                        <asp:UpdatePanel ID="UpdatePanel42" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:TextBox ID="Cb_SbjT7" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">SAINS</asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel43" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_KptsT7" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="6" Width="80px">
                                                                                    <asp:ListItem></asp:ListItem>
                                                                                    <asp:ListItem Value="1">A+</asp:ListItem>
                                                                                    <asp:ListItem Value="2">1A\A</asp:ListItem>
                                                                                    <asp:ListItem Value="3">2A\A-</asp:ListItem>
                                                                                    <asp:ListItem Value="4">3B\B+</asp:ListItem>
                                                                                    <asp:ListItem Value="5">4B\B</asp:ListItem>
                                                                                    <asp:ListItem Value="6">5C\C+</asp:ListItem>
                                                                                    <asp:ListItem Value="7">6C\C</asp:ListItem>
                                                                                    <asp:ListItem Value="8">7D\D</asp:ListItem>
                                                                                    <asp:ListItem Value="9">8E\E</asp:ListItem>
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                    </td>
                                                </tr>                                                                                                                                                    
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; 
                                border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px" 
                                height="15" valign="middle">
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>
        <tr>
             <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-right-width: 1px" valign="middle" class="style26">
                <asp:Panel ID="Panel2" runat="server" Width="100%">
                    <table ID="Tbl_Top" cellpadding="-1" cellspacing="-1" width="100%">                       
                        <tr>
                            <td bgcolor="#DFDFD0"                                                          
                                style="border-left-style: none; border-left-width: 1px; border-left-color: #999966; border-bottom-style: none; border-bottom-width: 1px; 
                                border-bottom-color: #999966; border-right-style: none; border-right-width: 1px; border-right-color: #999966;" 
                                align="left" class="style80" height="1%">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>MAKLUMAT 
                                SUBJEK</b></td>
                        </tr>
                        <tr>
                            <td bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-bottom-width: 1px; 
                                border-right-width: 1px" align="center">
                                <table id="Table3x" align="center" border="0" cellpadding="-1" cellspacing="-1" style="border: 1px solid #999966; height: 100%;">
                                    <tr>
                                        <td>
                                            <table id="Table3" align="left" border="0" cellpadding="-1" cellspacing="-1" style="text-align: left; ">
                                                <tr bgcolor="WhiteSmoke" 
                                                                    style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966">
                                                                    <td bordercolor="#000000"                                                                     
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style93">
                                                                        &nbsp;<b>BIL.</b></td>
                                                                    <td align="left" 
                                                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;">
                                                                        &nbsp;<b>SUBJEK</b>&nbsp;</td>
                                                                    <td align="char" style="BORDER-BOTTOM: 1px solid #999966; font-weight: 700;" 
                                                                        class="style91">
                                                                        KEPUTUSAN</td>
                                                                    <td style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                                                        class="style88">
                                                                        &nbsp;</td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td bgcolor="White" class="style94">
                                                                        <asp:TextBox ID="Textbox1" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="White" style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="181px">BAHASA MELAYU</asp:TextBox>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel16" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts1" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="2" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                    </td>
                                                                </tr>
                                                <tr>
                                                                    <td bgcolor="whitesmoke" class="style94">
                                                                        <asp:TextBox ID="Textbox2" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">2.</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="whitesmoke" style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">BAHASA INGGERIS</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="whitesmoke" class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel17" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts2" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="3" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td bgcolor="WhiteSmoke" class="style89">
                                                                    </td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td bgcolor="White" class="style103">
                                                                        <asp:TextBox ID="Textbox6" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">3.</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="White" class="style104">
                                                                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">MATEMATIK</asp:TextBox>
                                                                    </td>
                                                                    <td class="style105">
                                                                        <asp:UpdatePanel ID="UpdatePanel18" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts3" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="4" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style106">
                                                                    </td>
                                                                </tr>
                                                <tr>
                                                                    <td bgcolor="#f5f5f5" class="style107">
                                                                        <asp:TextBox ID="Textbox7" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">4.</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style108">                                                                    
                                                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">SAINS</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style109">
                                                                        <asp:UpdatePanel ID="UpdatePanel19" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts4" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="5" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style110">
                                                                    </td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox8" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px" Visible="False">5.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Sbj5" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px" Visible="False">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_Sbj5x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel6" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts5" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="8" Width="80px" Visible="False">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                        <asp:UpdatePanel ID="UpdatePanel11" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:Button ID="Bt4" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                                                    Height="20px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                </tr>
                                                <tr>
                                                                    <td bgcolor="#f5f5f5" class="style99">
                                                                        <asp:TextBox ID="Textbox9" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">6.</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style100">
                                                                        <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Sbj6" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_Sbj6x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style101">
                                                                        <asp:UpdatePanel ID="UpdatePanel7" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts6" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="7" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style102">
                                                                        <asp:UpdatePanel ID="UpdatePanel12" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:Button ID="Bt5" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                                                    Height="20px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox10" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">7.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Sbj7" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_Sbj7x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel8" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts7" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="8" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                        <asp:UpdatePanel ID="UpdatePanel13" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:Button ID="Bt6" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                                                    Height="20px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                </tr>
                                                <tr>
                                                                    <td bgcolor="#f5f5f5" class="style94">
                                                                        <asp:TextBox ID="Textbox11" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">8.</asp:TextBox>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel4" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Sbj8" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_Sbj8x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel9" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts8" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td bgcolor="#f5f5f5" class="style90">
                                                                        <asp:UpdatePanel ID="UpdatePanel14" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:Button ID="Bt7" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                                                    Height="20px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                </tr>
                                                <tr bgcolor="White">
                                                                    <td class="style94">
                                                                        <asp:TextBox ID="Textbox12" runat="server" BackColor="Transparent" 
                                                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">9.</asp:TextBox>
                                                                    </td>
                                                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                                                        <asp:UpdatePanel ID="UpdatePanel5" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Sbj9" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="230px">
                                                                                </asp:DropDownList>
                                                                                <asp:TextBox ID="Cb_Sbj9x" runat="server" BackColor="Transparent" 
                                                                                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="35" Visible="False" Width="181px"></asp:TextBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style92">
                                                                        <asp:UpdatePanel ID="UpdatePanel10" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:DropDownList ID="Cb_Kpts9" runat="server" Font-Names="Arial" 
                                                                                    Font-Size="8pt" Height="19px" tabIndex="18" Width="80px">
                                                                                </asp:DropDownList>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                    <td class="style89">
                                                                        <asp:UpdatePanel ID="UpdatePanel15" runat="server">
                                                                            <ContentTemplate>
                                                                                <asp:Button ID="Bt8" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                                                    Height="23px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </td>
                                                                </tr>                                                                                                             
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; 
                                border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px" 
                                height="15" valign="middle">
                                &nbsp;
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>
        <tr>
             <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
            <td align="left" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; border-left-width: 1px; border-right-width: 1px" valign="middle" class="style26">
                <asp:Panel ID="PanelSemak" runat="server" Width="100%">
                    <table style="height: 100%;" ID="Table17" cellpadding="-1" cellspacing="-1" width="100%">                                                                                               
                        <tr>
                            <td align="center" bgcolor="#DFDFD0" style="border-style: none none none none; border-color: #999966; 
                                border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px" 
                                height="15" valign="middle">
                                <asp:Button ID="cmd_Semak" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                    Height="20px" tabIndex="3" Text="SEMAK" Width="70px" />
                            </td>                                
                        </tr>
                    </table>
                </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>                              
        <tr style="line-height: 21px">
             <td align="left" bgcolor="#ffffff" 
                valign="top" class="style69" width="50">
                &nbsp;</td>
			<td valign="top" align="left" bgcolor="#ffffff" class="style43">
                <asp:Panel ID="Panel1" runat="server" Width="100%">
                                        <table ID="Tbl_Main" cellpadding="-1" cellspacing="-1" 
                                            style="width:100%; height: 100%;">
                                            <tr style="line-height: 21px">
                                                <td align="center" bgcolor="white" class="style65" valign="top" colspan="2" 
                                                    style="color: #CC0000">
                                                    &nbsp; * maklumat mandatori&nbsp;</td>
                                            </tr>
                                            <tr style="line-height: 21px">
                                                <td align="left" bgcolor="white" class="style84" valign="top">
                                                    <span class="style83">*</span>&nbsp; NAMA</td>
                                                <td bgcolor="White" class="style67">
                                                    <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="282px" 
                                                        Wrap="False"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style63" valign="top">
                                                    <span class="style83">*</span>&nbsp; NO. KP/ TENTERA/ PASPORT</td>
                                                <td bgcolor="White" class="style86">
                                                    <asp:UpdatePanel ID="UpdatePanel21" runat="server">
                                                        <ContentTemplate>
                                                            <asp:DropDownList ID="Cb_NoKP" runat="server" AutoPostBack="True" 
                                                                CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="11" 
                                                                Width="80px" Visible="False">
                                                                <asp:ListItem>BARU</asp:ListItem>
                                                                <asp:ListItem>TENTERA</asp:ListItem>
                                                                <asp:ListItem>PASPORT</asp:ListItem>
                                                            </asp:DropDownList>
                                                            <asp:TextBox ID="Tx_NoKP" runat="server" CssClass="std" Width="200px" 
                                                                Wrap="False"></asp:TextBox>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                    <asp:Button ID="cmd_Cari" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                     Height="20px" tabIndex="3" Text="SEMAK" Width="70px" Visible="False" />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style63" valign="top">
                                                    <span class="style83">*</span>&nbsp; WARGANEGARA</td>
                                                <td align="left" bgcolor="white" class="style12" valign="top">
                                                    <asp:DropDownList ID="Cb_Warga" runat="server" CssClass="std" 
                                                        Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="11" 
                                                        Width="282px">
                                                        <asp:ListItem></asp:ListItem>
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                              <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    <span class="style83">*</span>&nbsp; TARIKH LAHIR</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:TextBox ID="Tx_Lahir" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                    <cc1:MaskedEditExtender ID="MaskedEditExtender2" runat="server" 
                                                        CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                        CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                        CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                        Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Lahir" 
                                                        UserDateFormat="DayMonthYear">
                                                    </cc1:MaskedEditExtender>
                                                    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" 
                                                        Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                        TargetControlID="Tx_Lahir">
                                                    </cc1:CalendarExtender>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; JANTINA</td>
                                                <td align="left" bgcolor="white" class="style12" valign="top">
                                                    <asp:DropDownList ID="Cb_Jantina" runat="server" CssClass="std" 
                                                        Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="11" 
                                                        Width="190px">
                                                        <asp:ListItem></asp:ListItem>
                                                        <asp:ListItem Value="L">LELAKI</asp:ListItem>
                                                        <asp:ListItem Value="P">PEREMPUAN</asp:ListItem>
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; BANGSA</td>
                                                <td align="left" bgcolor="white" class="style12" valign="top">
                                                    <asp:DropDownList ID="Cb_Bangsa" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="12" Width="190px">
                                                        <asp:ListItem></asp:ListItem>
                                                        <asp:ListItem Value="L">MELAYU</asp:ListItem>
                                                        <asp:ListItem Value="P">CINA</asp:ListItem>
                                                        <asp:ListItem Value="India">INDIA</asp:ListItem>
                                                        <asp:ListItem Value="Lain-Lain">LAIN-LAIN</asp:ListItem>
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style63" valign="top">
                                                    &nbsp;&nbsp; AGAMA</td>
                                                <td align="left" bgcolor="white" class="style12" valign="top">
                                                    <asp:DropDownList ID="Cb_Agama" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="13" Width="190px">
                                                        <asp:ListItem></asp:ListItem>
                                                        <asp:ListItem Value="L">ISLAM</asp:ListItem>
                                                        <asp:ListItem Value="P">BUDDHA</asp:ListItem>
                                                        <asp:ListItem Value="Hindu">HINDU</asp:ListItem>
                                                        <asp:ListItem Value="Kristian">KRISTIAN</asp:ListItem>
                                                        <asp:ListItem Value="Lain-Lain">LAIN-LAIN</asp:ListItem>
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; TARAF PERKAHWINAN</td>
                                                <td align="left" bgcolor="white" class="style12" valign="top">
                                                    <asp:DropDownList ID="Cb_Kahwin" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="22" Width="190px">
                                                        <asp:ListItem></asp:ListItem>
                                                        <asp:ListItem Value="L">BUJANG</asp:ListItem>
                                                        <asp:ListItem Value="P">BERKAHWIN</asp:ListItem>
                                                        <asp:ListItem Value="Duda">DUDA</asp:ListItem>
                                                        <asp:ListItem Value="Janda">JANDA</asp:ListItem>
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; ALAMAT TETAP</td>
                                                <td ID="Tx_TP_Alamat" align="left" bgcolor="white" class="style33" valign="top">
                                                    <asp:TextBox ID="Tx_TP_Alamat" runat="server" CssClass="std" Height="60px" 
                                                        TextMode="MultiLine" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; POSKOD</td>
                                                <td align="left" bgcolor="white" class="style33" valign="top">
                                                    <asp:TextBox ID="Tx_TP_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style63" valign="top">
                                                    &nbsp;&nbsp; BANDAR</td>
                                                <td align="left" bgcolor="white" class="style87" valign="top">
                                                    <asp:TextBox ID="Tx_TP_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style77" valign="top">
                                                    &nbsp;&nbsp; NEGERI</td>
                                                <td align="left" bgcolor="white" class="style12" valign="top">
                                                <asp:UpdatePanel ID="UpdatePanel47" runat="server">
                                                  <ContentTemplate>
                                                    <asp:DropDownList ID="Cb_TP_Negeri" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="18" Width="282px">
                                                        <asp:ListItem></asp:ListItem>
                                                    </asp:DropDownList>
                                                    </ContentTemplate>
                                                  </asp:UpdatePanel> 
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; NO. TELEFON</td>
                                                <td align="left" bgcolor="white" class="style33" valign="top">
                                                    <asp:TextBox ID="Tx_Tel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; E-MEL</td>
                                                <td align="left" bgcolor="white" class="style33" valign="top">
                                                    <asp:TextBox ID="Tx_Emel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" valign="top">
                                                    &nbsp;&nbsp; NAMA WARIS TERDEKAT</td>
                                                <td align="left" bgcolor="white" class="style33" valign="top">
                                                    <asp:TextBox ID="Tx_W_Nama" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="white" class="style76" colSpan="1" rowSpan="1" 
                                                    valign="top">
                                                    &nbsp;&nbsp; ALAMAT WARIS</td>
                                                <td align="left" bgcolor="white" class="style33" valign="top">
                                                    <asp:TextBox ID="Tx_W_Alamat" runat="server" BorderStyle="Groove" 
                                                        Font-Names="Arial" Font-Size="8pt" Height="60px" tabIndex="26" 
                                                        TextMode="MultiLine" Width="283px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="white" class="style78">&nbsp;&nbsp; POSKOD</td>
                                                <td bgcolor="white" class="style86">
                                                    <asp:TextBox ID="Tx_W_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="white" class="style78">&nbsp;&nbsp; BANDAR</td>
                                                <td bgcolor="white" class="style86">
                                                    <asp:TextBox ID="Tx_W_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="white" class="style78">&nbsp;&nbsp; NEGERI</td>
                                                <td bgcolor="white" class="style86">
                                                <asp:UpdatePanel ID="UpdatePanel49" runat="server">
                                                  <ContentTemplate>
                                                    <asp:DropDownList ID="Cb_W_Negeri" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="18" Width="282px">
                                                        <asp:ListItem></asp:ListItem>
                                                    </asp:DropDownList>
                                                    </ContentTemplate>
                                                  </asp:UpdatePanel> 
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="white" class="style78">&nbsp;&nbsp; NEGARA</td>
                                                <td bgcolor="white" class="style86">
                                                    <asp:DropDownList ID="Cb_W_Negara" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="18" Width="282px">
                                                        <asp:ListItem></asp:ListItem>
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="white" class="style78">&nbsp;&nbsp; NO. TELEFON WARIS</td>
                                                <td bgcolor="white" class="style86">
                                                    <asp:TextBox ID="Tx_W_Tel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="#ffffff" class="style78" valign="top">
                                                    <span class="style83">*</span>&nbsp; KOLEJ/INSTITUSI</td>
                                                <td bgcolor="#ffffff" valign="top" class="style86">
                                                    <asp:UpdatePanel ID="UpdatePanel20" runat="server">
                                                        <ContentTemplate>
                                                            <asp:RadioButtonList ID="RadioButtonList1" runat="server" AutoPostBack="True" 
                                                                CellPadding="0" CellSpacing="0" Height="16px" RepeatDirection="Horizontal" 
                                                                Width="276px">
                                                                <asp:ListItem Value="1">KERAJAAN</asp:ListItem>
                                                                <asp:ListItem Value="2">SWASTA</asp:ListItem>
                                                                <asp:ListItem Value="3">LUAR NEGARA</asp:ListItem>
                                                            </asp:RadioButtonList>
                                                            <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="520px">
                                                            </asp:DropDownList>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="#ffffff" class="style78" valign="top">
                                                    &nbsp;&nbsp; TAJAAN</td>
                                                <td bgcolor="#ffffff" valign="top" class="style86">
                                                    <asp:DropDownList ID="Cb_Tajaan" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="33" Width="193px">
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr style="line-height: 21px">
                                                <td bgcolor="#ffffff" class="style63" valign="top"><span class="style83">*</span>&nbsp; SESI PENGAMBILAN</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-SIZE: 8pt; FONT-FAMILY: arial; FONT-VARIANT: small-caps; white-space: normal;" 
                                                    valign="top">
                                                    <asp:DropDownList ID="Cb_Sesi_Bulan" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="33" Width="100px">
                                                    </asp:DropDownList>
                                                    <asp:DropDownList ID="Cb_Sesi_Tahun" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="33" Width="93px">
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    <span class="style83">*</span>&nbsp; TARIKH MULA LATIHAN</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:TextBox ID="Tx_M_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                    <cc1:MaskedEditExtender ID="Tx_M_Latihan_MaskedEditExtender" runat="server" 
                                                        CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                        CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                        CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                        Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_M_Latihan" 
                                                        UserDateFormat="DayMonthYear">
                                                    </cc1:MaskedEditExtender>
                                                    <cc1:CalendarExtender ID="Tx_M_Latihan_CalendarExtender" runat="server" 
                                                        Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                        TargetControlID="Tx_M_Latihan">
                                                    </cc1:CalendarExtender>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    &nbsp;&nbsp;&nbsp; TARIKH TAMAT LATIHAN</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:TextBox ID="Tx_T_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                    <cc1:MaskedEditExtender ID="MaskedEditExtender1" runat="server" 
                                                        CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                        CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                        CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                        Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_T_Latihan" 
                                                        UserDateFormat="DayMonthYear">
                                                    </cc1:MaskedEditExtender>
                                                    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" 
                                                        Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                        TargetControlID="Tx_T_Latihan">
                                                    </cc1:CalendarExtender>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="#ffffff" class="style78">
                                                </td>
                                                <td bgcolor="#ffffff" class="style86">
                                                    <br />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    &nbsp;&nbsp; MARKAH BERTERUSAN <br />
                                                    &nbsp;&nbsp; (JUMLAH KESELURUHAN)</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:TextBox ID="Tx_Markah" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    &nbsp;&nbsp; TINDAKAN TATATERTIB</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:DropDownList ID="Cb_Tatatertib" runat="server" Font-Names="Arial" 
                                                        Font-Size="8pt" Height="19px" tabIndex="18" Width="100px">
                                                        <asp:ListItem></asp:ListItem>
                                                    </asp:DropDownList>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    &nbsp;&nbsp; Jumlah cuti sakit</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:TextBox ID="Tx_Cuti" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                    &nbsp;*Sepanjang tempoh latihan
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    &nbsp;&nbsp; Jumlah cuti sakit yang <br />
                                                    &nbsp;&nbsp; diganti</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:TextBox ID="Tx_Cuti_Ganti" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                    &nbsp;*Sepanjang tempoh latihan
                                                </td>
                                            </tr>
                                             <tr>
                                                <td align="left" bgcolor="#ffffff" class="style63" valign="top">
                                                    &nbsp;&nbsp; Sebab cuti sakit</td>
                                                <td bgcolor="#ffffff" class="style50" 
                                                    style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                                                    valign="top">
                                                    <asp:TextBox ID="Tx_Cuti_Sebab" runat="server" CssClass="std" Height="57px" 
                                                        TextMode="MultiLine" Width="383px"></asp:TextBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="#ffffff" class="style78">
                                                </td>
                                                <td bgcolor="#ffffff" class="style86">
                                                    <br />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="#999966" colspan="2" 
                                                    style="border: 1px solid #999966; color: #FFFFFF;">
                                                    &nbsp; SENARAI SEMAK</td>
                                            </tr>
                                            <tr>
                                                <td align="left" bgcolor="#F5F5F1" colspan="2" 
                                                    style="border: 1px solid #999966; line-height: normal" valign="top">
                                                    <table align="center">
                                                        <tr>
                                                            <td align="left">
                                                                <asp:CheckBoxList ID="chk_Semak" runat="server" Font-Names="Arial" 
                                                                    Font-Size="8pt" Height="31px" RepeatColumns="2" RepeatDirection="Horizontal" 
                                                                    Width="510px">
                                                                    <asp:ListItem VALUE="Salinan Kad Pengenalan">SALINAN KAD PENGENALAN/TENTERA/PASPORT</asp:ListItem>
                                                                    <asp:ListItem VALUE="Salinan Sijil Lahir">Salinan Sijil Lahir</asp:ListItem>
                                                                    <asp:ListItem VALUE="Salinan Sijil Warganegara">Salinan Sijil Warganegara</asp:ListItem>
                                                                    <asp:ListItem VALUE="Salinan Sijil Kesihatan">Salinan Sijil Kesihatan</asp:ListItem>
                                                                    <asp:ListItem VALUE="Salinan Sijil Akademik">Salinan Sijil Akademik / Sijil Latihan</asp:ListItem>
                                                                    <asp:ListItem VALUE="Salinan KPT (Pelatih Asing)">Salinan KPT (Pelatih Asing)</asp:ListItem>
                                                                    <asp:ListItem VALUE="Salinan IELTS/TOFEL (Pelatih Asing)">Salinan IELTS/TOFEL 
                                                                    (Pelatih Asing)</asp:ListItem>
                                                                    <asp:ListItem VALUE="Salinan MUET">Salinan MUET</asp:ListItem>
                                                                </asp:CheckBoxList>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="#ffffff" colspan="2" valign="top">
                                                    &nbsp;</td>
                                            </tr>
                                            <tr>
                                                <td bgcolor="#CE0000" colspan="2" 
                                                    style="border: 1px solid #CC1A1E; color: #FFFFFF;" valign="top">
                                                    &nbsp; PERAKUAN KOLEJ</td>
                                            </tr>
                                            <tr>
                                                <td align="center" bgcolor="#ffffff" colspan="2" 
                                                    style="border: 1px solid #CE0000;" valign="middle">
                                                    <table align="center" style="width: 530px">
                                                        <tr style="line-height: 10px">
                                                            <td class="style68">
                                                            </td>
                                                            <td align="left" class="style68" 
                                                                style="font-size: 8pt; font-family: Arial; text-align: justify; color: #CE0000;">
                                                            </td>
                                                            <td class="style68">
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                            </td>
                                                            <td align="left" 
                                                                style="font-size: 8pt; font-family: Arial; text-align: justify; color: #CE0000;">
                                                                Kolej ini memperakui bahawa semua maklumat yang diberikan adalah benar. 
                                                                Sekiranya maklumat tersebut didapati palsu, permohonan pelajar ini akan terbatal 
                                                                dan kolej ini akan bertanggungjawab sepenuhnya terhadap sebarang tuntutan 
                                                                mahkamah oleh pihak pelajar sekiranya ada.</td>
                                                            <td>
                                                            </td>
                                                        </tr>
                                                        <tr style="line-height: 10px">
                                                            <td>
                                                                &nbsp;</td>
                                                            <td align="left" 
                                                                style="font-size: 8pt; font-family: Arial; text-align: justify; color: #CE0000;">
                                                                &nbsp;</td>
                                                            <td>
                                                                &nbsp;</td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="center" bgcolor="#ffffff" colspan="2" valign="middle">
                                                    &nbsp;</td>
                                            </tr>
                                            <tr>
                                                <td align="center" bgcolor="#ffffff" colspan="2" valign="middle">
                                                    <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                        Height="20px" tabIndex="3" Text="HANTAR" Width="96px" />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="center" bgcolor="#ffffff" colspan="2" valign="middle">
                                                    &nbsp;</td>
                                            </tr>
                                        </table>
                                    </asp:Panel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style85">
                &nbsp;&nbsp;</td>
        </tr>
    </table>
    <tr><td>  
        <br />
        </td></tr>
    </td></tr></table>
    </div>
</asp:Content>
