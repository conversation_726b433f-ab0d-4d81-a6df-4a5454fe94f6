﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm75
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")        
        
    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        If Cb_Kursus.SelectedIndex < 1 Then Cb_Kursus.Focus() : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select top 1 * from pn_xm where j_xm=" & Cb_Kursus.SelectedIndex & " order by tahun desc, siri desc, j_xm desc"
        Rdr = Cmd.ExecuteReader()        
        If Rdr.Read Then
            If Not IsDBNull(Rdr("tkh_papar")) Then
                Fn_Check()
            Else
                If CInt(Rdr("ag")) = 0 And CInt(Rdr("status")) = 1 Then Fn_Check0() Else Fn_Check1()
            End If
        End If
        Rdr.Close()
        Cn.Close()

    End Sub

    Public Sub Fn_Check()
        'have result
        Session("Lpr_Nama") = "Senarai_Calon"
        Session("Var_1") = Session("Id_KOLEJ")
        Session("Var_2") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_3") = 5 Else Session("Var_3") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_4") = 8 Else Session("Var_4") = Cb_Kursus.SelectedValue
        Select Case Cb_Kursus.SelectedValue
            'Comment Original 08022017 - OSH
            'Case 1, 5, 8 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR JURURAWAT BERDAFTAR - " & Session("Dc_KOLEJ") 
            'Change Label ticket request #470477 08022017 - OSH
            Case 1, 5, 8 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT - " & Session("Dc_KOLEJ")
            Case 2 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT - " & Session("Dc_KOLEJ")
            Case 3 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT - " & Session("Dc_KOLEJ")
            Case 4 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN KEBIDANAN BAHAGIAN I - " & Session("Dc_KOLEJ")
        End Select

        Response.Write("<script language='javascript'>win=window.open('Cetak.aspx',null,'width=1000,height=750,top='+ (screen.height-750)/3 +',left='+ (screen.width-1000)/2 +'','true');</script>")
    End Sub

    Public Sub Fn_Check0()
        'saring result
        Session("Lpr_Nama") = "Senarai_Calon0"
        Session("Var_1") = Session("Id_KOLEJ")
        Session("Var_2") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_3") = 5 Else Session("Var_3") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_4") = 8 Else Session("Var_4") = Cb_Kursus.SelectedValue
        Select Case Cb_Kursus.SelectedValue
            'Comment Original 08022017 - OSH
            'Case 1, 5, 8 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR JURURAWAT BERDAFTAR - " & Session("Dc_KOLEJ")
            'Change Label ticket request #470477 08022017 - OSH
            Case 1, 5, 8 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT - " & Session("Dc_KOLEJ")
            Case 2 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT - " & Session("Dc_KOLEJ")
            Case 3 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT - " & Session("Dc_KOLEJ")
            Case 4 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR KEBIDANAN BAHAGIAN I - " & Session("Dc_KOLEJ")
        End Select

        Response.Write("<script language='javascript'>win=window.open('Cetak.aspx',null,'width=1000,height=750,top='+ (screen.height-750)/3 +',left='+ (screen.width-1000)/2 +'','true');</script>")
    End Sub

    Public Sub Fn_Check1()
        'jana result
        Session("Lpr_Nama") = "Senarai_Calon1"
        Session("Var_1") = Session("Id_KOLEJ")
        Session("Var_2") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_3") = 5 Else Session("Var_3") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_4") = 8 Else Session("Var_4") = Cb_Kursus.SelectedValue
        Select Case Cb_Kursus.SelectedValue
            'Comment Original 08022017 - OSH
            'Case 1, 5, 8 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR JURURAWAT BERDAFTAR - " & Session("Dc_KOLEJ")
            'Change Label ticket request #470477 08022017 - OSH
            Case 1, 5, 8 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT - " & Session("Dc_KOLEJ")
            Case 2 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT - " & Session("Dc_KOLEJ")
            Case 3 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT - " & Session("Dc_KOLEJ")
            Case 4 : Session("Tajuk") = "SENARAI CALON BAGI PEPERIKSAAN AKHIR KEBIDANAN BAHAGIAN I - " & Session("Dc_KOLEJ")
        End Select

        Response.Write("<script language='javascript'>win=window.open('Cetak.aspx',null,'width=1000,height=750,top='+ (screen.height-750)/3 +',left='+ (screen.width-1000)/2 +'','true');</script>")
    End Sub
End Class