﻿'Imports CrystalDecisions.Shared
'Imports CrystalDecisions.CrystalReports.Engine
'Imports System.Data.DataSet

Partial Public Class P2_SN_Penuh
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Dim x As logoninfo

        'With CrystalReportViewer1
        '    .LogOnInfo.Item(0).ConnectionInfo.ServerName = "Fz"
        '    .LogOnInfo.Item(0).ConnectionInfo.DatabaseName = "SPMJ_25022008"
        '    .LogOnInfo.Item(0).ConnectionInfo.UserID = "sa"
        '    .LogOnInfo.Item(0).ConnectionInfo.Password = "aljaroom5621"

        '    .SelectionFormula = 

        'End With
        'Response.Write("<script language='javascript'>win=window.open('P3_Lpr_APC.aspx',null,'width=600,height=400,top=250,left=250','true');</script>")

    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        If Tx_NoPd1.Text.Trim = "" Or Not IsNumeric(Tx_NoPd1.Text) Then Exit Sub
        If Tx_NoPd2.Text.Trim = "" Or Not IsNumeric(Tx_NoPd2.Text) Then Exit Sub
        Session("Var_0") = CInt(Tx_NoPd1.Text.Trim)
        Session("Var_1") = CInt(Tx_NoPd2.Text.Trim)
        If Cb_Jenis.SelectedValue = 1 Then Session("Lpr_Nama") = "Daftar_Penuh"
        If Cb_Jenis.SelectedValue = 2 Then
            If chk_Gazet.Checked Then
                Session("Lpr_Nama") = "Gazet_JM"
            Else
                Session("Lpr_Nama") = "Daftar_Penuh_JM"
            End If
        End If
        If Cb_Jenis.SelectedValue = 3 Then Session("Lpr_Nama") = "Daftar_Penuh_PJ"
        If Cb_Jenis.SelectedValue = 4 Then
            If chk_Gazet.Checked Then
                Session("Lpr_Nama") = "Gazet_KB1"
            Else
                Session("Lpr_Nama") = "Daftar_Penuh_KB1"
            End If
        End If
        Response.Write("<script language='javascript'>win=window.open('P6_Laporan.aspx',null,'width=1000,height=700,top='+ (screen.height-600)/3 +',left='+ (screen.width-800)/2 +'','true');</script>")
    End Sub
End Class