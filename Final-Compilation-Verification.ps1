# SPMJ KOLEJ Final Compilation Verification Script
# Tests compilation success and microservice connectivity

Write-Host "=== SPMJ <PERSON>EJ FINAL COMPILATION VERIFICATION ===" -ForegroundColor Green
Write-Host "Testing compilation and integration readiness..." -ForegroundColor White

# Test 1: Verify compilation output exists
Write-Host "`n1. COMPILATION OUTPUT VERIFICATION" -ForegroundColor Cyan
$dllPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Test.dll"
if (Test-Path $dllPath) {
    $dll = Get-ChildItem $dllPath
    Write-Host "✓ SUCCESS: Compiled assembly found" -ForegroundColor Green
    Write-Host "  File: $($dll.Name)" -ForegroundColor Gray
    Write-Host "  Size: $($dll.Length) bytes" -ForegroundColor Gray
    Write-Host "  Modified: $($dll.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "✗ FAILED: No compiled assembly found" -ForegroundColor Red
    exit 1
}

# Test 2: Check key files exist
Write-Host "`n2. KEY FILES VERIFICATION" -ForegroundColor Cyan
$keyFiles = @(
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx.vb",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx.designer.vb",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx.vb",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx.designer.vb",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\EmailServiceClient.vb",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\PasswordHelper.vb"
)

foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $([System.IO.Path]::GetFileName($file))" -ForegroundColor Green
    } else {
        Write-Host "✗ $([System.IO.Path]::GetFileName($file)) - MISSING" -ForegroundColor Red
    }
}

# Test 3: Test microservice connectivity
Write-Host "`n3. MICROSERVICE CONNECTIVITY TEST" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Email microservice is accessible" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  URL: http://localhost:5000" -ForegroundColor Gray
    } else {
        Write-Host "⚠ Email microservice responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Email microservice not accessible (may need to be started)" -ForegroundColor Yellow
    Write-Host "  URL: http://localhost:5000" -ForegroundColor Gray
    Write-Host "  Note: This is normal if the microservice is not running" -ForegroundColor Gray
}

# Test 4: Final compilation test
Write-Host "`n4. FINAL COMPILATION TEST" -ForegroundColor Cyan
Set-Location "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ"
$compileResult = & "C:\Windows\Microsoft.NET\Framework\v3.5\vbc.exe" /target:library /reference:System.Web.dll,System.dll,System.Data.dll,System.Configuration.dll /out:FinalTest.dll Pwd.aspx.vb Pwd.aspx.designer.vb ForcePasswordChange.aspx.vb ForcePasswordChange.aspx.designer.vb EmailServiceClient.vb PasswordHelper.vb 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Final compilation successful - NO ERRORS" -ForegroundColor Green
    if (Test-Path "FinalTest.dll") {
        $finalDll = Get-ChildItem "FinalTest.dll"
        Write-Host "  Output: $($finalDll.Name) ($($finalDll.Length) bytes)" -ForegroundColor Gray
    }
} else {
    Write-Host "✗ Final compilation failed" -ForegroundColor Red
    Write-Host "Compilation output:" -ForegroundColor Yellow
    Write-Host $compileResult -ForegroundColor White
}

Write-Host "`n=== VERIFICATION COMPLETE ===" -ForegroundColor Green
Write-Host "SPMJ KOLEJ password management system compilation verified!" -ForegroundColor White
Write-Host "Ready for deployment and integration testing." -ForegroundColor White
