Imports System.Net
Imports System.IO
Imports System.Text
Imports System.Web.Script.Serialization

''' <summary>
''' Client for communicating with the .NET 9 Email Microservice
''' Provides email functionality for .NET 3.5 application
''' </summary>
Public Class EmailServiceClient
    Private ReadOnly _baseUrl As String
    Private ReadOnly _timeout As Integer = 30000 ' 30 seconds
    Private ReadOnly _apiKey As String = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"

    Public Sub New(baseUrl As String)
        _baseUrl = baseUrl.TrimEnd("/"c)
        
        ' Enable SSL for email service communication (.NET 3.5 compatible)
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 Or SecurityProtocolType.Tls
    End Sub

#Region "Password Reset Methods"

    ''' <summary>
    ''' Request password reset for end user
    ''' </summary>
    Public Function RequestPasswordReset(userId As String, email As String, Optional baseUrl As String = Nothing) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .Email = email,
                .BaseUrl = baseUrl
            }

            Dim response = PostRequest("/api/password/reset/request", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

    ''' <summary>
    ''' Validate password reset token
    ''' </summary>
    Public Function ValidateResetToken(token As String) As EmailServiceResponse
        Try
            Dim response = GetRequest($"/api/password/reset/validate/{token}")
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

    ''' <summary>
    ''' Complete password reset with new password
    ''' </summary>
    Public Function CompletePasswordReset(token As String, newPassword As String) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .Token = token,
                .NewPassword = newPassword
            }

            Dim response = PostRequest("/api/password/reset/complete", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

#End Region

#Region "Admin Password Methods"

    ''' <summary>
    ''' Admin create password for user
    ''' </summary>
    Public Function CreateAdminPassword(userId As String, email As String, tempPassword As String, adminId As String) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .Email = email,
                .TempPassword = tempPassword,
                .AdminId = adminId
            }

            Dim response = PostRequest("/api/password/admin/create", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

    ''' <summary>
    ''' Admin reset password for user
    ''' </summary>
    Public Function ResetAdminPassword(userId As String, email As String, adminId As String, Optional tempPassword As String = Nothing) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .Email = email,
                .AdminId = adminId,
                .TempPassword = tempPassword
            }

            Dim response = PostRequest("/api/password/admin/reset", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

#End Region

#Region "OTP Methods"

    ''' <summary>
    ''' Generate OTP for user
    ''' </summary>
    Public Function GenerateOtp(userId As String, email As String, purpose As String) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .Email = email,
                .Purpose = purpose.ToUpper()
            }

            Dim response = PostRequest("/api/otp/generate", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

    ''' <summary>
    ''' Validate OTP code
    ''' </summary>
    Public Function ValidateOtp(userId As String, otpCode As String, purpose As String) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .OtpCode = otpCode,
                .Purpose = purpose.ToUpper()
            }

            Dim response = PostRequest("/api/otp/validate", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

#End Region

#Region "Health Check"

    ''' <summary>
    ''' Check if email service is healthy
    ''' </summary>
    Public Function CheckHealth() As Boolean
        Try
            Dim response = GetRequest("/health")
            Return Not String.IsNullOrEmpty(response) AndAlso response.Contains("healthy")
        Catch
            Return False
        End Try
    End Function

#End Region

#Region "Admin Notification Methods"

    ''' <summary>
    ''' Send admin notification email
    ''' </summary>
    Public Function SendAdminNotification(action As String, details As String, Optional userId As String = "") As EmailServiceResponse
        Try
            Dim requestData = New With {
                .Action = action,
                .Details = details,
                .UserId = userId,
                .Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            }

            Dim response = PostRequest("/api/notifications/admin", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat menghantar notifikasi admin: " & ex.Message
            }
        End Try
    End Function

    ''' <summary>
    ''' Send password change notification
    ''' </summary>
    Public Function SendPasswordChangeNotification(userId As String, email As String, Optional details As String = "") As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .Email = email,
                .Details = details,
                .Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            }

            Dim response = PostRequest("/api/notifications/password-change", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat menghantar notifikasi perubahan kata laluan: " & ex.Message
            }
        End Try
    End Function

#End Region

#Region "Private HTTP Methods"
    Private Function GetRequest(endpoint As String) As String
        Dim request As HttpWebRequest = CType(WebRequest.Create(_baseUrl & endpoint), HttpWebRequest)
        request.Method = "GET"
        request.ContentType = "application/json"
        request.Headers.Add("X-API-Key", _apiKey)
        request.Timeout = _timeout

        Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
            Using reader As New StreamReader(response.GetResponseStream())
                Return reader.ReadToEnd()
            End Using
        End Using
    End Function

    Private Function PostRequest(endpoint As String, data As Object) As String
        Dim request As HttpWebRequest = CType(WebRequest.Create(_baseUrl & endpoint), HttpWebRequest)
        request.Method = "POST"
        request.ContentType = "application/json"
        request.Headers.Add("X-API-Key", _apiKey)
        request.Timeout = _timeout

        ' Serialize data to JSON
        Dim serializer As New JavaScriptSerializer()
        Dim jsonData As String = serializer.Serialize(data)
        Dim dataBytes As Byte() = Encoding.UTF8.GetBytes(jsonData)

        request.ContentLength = dataBytes.Length

        Using requestStream As Stream = request.GetRequestStream()
            requestStream.Write(dataBytes, 0, dataBytes.Length)
        End Using

        Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
            Using reader As New StreamReader(response.GetResponseStream())
                Return reader.ReadToEnd()
            End Using
        End Using
    End Function

    Private Function ParseResponse(jsonResponse As String) As EmailServiceResponse
        Try
            Dim serializer As New JavaScriptSerializer()
            Dim responseDict As Dictionary(Of String, Object) = serializer.DeserializeObject(jsonResponse)

            Dim result As New EmailServiceResponse()
            
            If responseDict.ContainsKey("success") Then
                result.Success = CBool(responseDict("success"))
            End If
            
            If responseDict.ContainsKey("message") Then
                result.Message = responseDict("message").ToString()
            End If
            
            If responseDict.ContainsKey("data") AndAlso responseDict("data") IsNot Nothing Then
                result.Data = responseDict("data").ToString()
            End If

            Return result
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat memproses respons dari sistem email: " & ex.Message
            }
        End Try
    End Function

#End Region

End Class

''' <summary>
''' Response model for email service operations
''' </summary>
Public Class EmailServiceResponse
    Public Property Success As Boolean
    Public Property Message As String = ""
    Public Property Data As String = ""
End Class
