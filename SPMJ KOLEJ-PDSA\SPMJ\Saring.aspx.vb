﻿Imports System.Data.OleDb
Imports System.Globalization
Imports System.Threading
Imports System.Data.SqlClient

Partial Public Class WebForm74
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        cmd_Hantar.Visible = False
        Dim SQL As String = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', isnull(CONVERT(char(12), p.tkh_latihan_tamat, 103),'') as 'TARIKH TAMAT', isnull(p.MARKAH,'0') as 'MARKAH BERTERUSAN', isnull(p.cuti,0)-isnull(p.cuti_ganti,0) as 'LEBIHAN CUTI SAKIT', p.SARING, case p.TATATERTIB when 1 then 'YA' when 2 then 'TIDAK' else '' end 'TATATERTIB' from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej where p.id_kolej = " & Session("Id_KOLEJ") & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by p.nama"
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)

        List_Adp.Fill(List_Data, "pelatih")
        Gd.DataSource = List_Data.Tables("pelatih")
        DataBind()
        Gd.Visible = True
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Comment Original 05042022 - OSH 
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")

        'New Landing Page05042022-OSH      
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login_j.aspx")
        Cb_Kolej.Items.Add(Session("Dc_KOLEJ"))
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Session("Id_KOLEJ")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KURSUS
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmdHantar0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar0.Click
        If Cb_Kursus.SelectedItem.Text = "" Then Cb_Kursus.Focus() : Exit Sub
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" Then Exit Sub
        If Cb_Saring.SelectedIndex = 1 Then
            Cari("p.nama like '" & Tx_Nama.Text.Replace("'", "''") & "%' and p.nokp like '" & Tx_NoKP.Text & "%' and p.saring = 1")
        Else
            Cari("p.nama like '" & Tx_Nama.Text.Replace("'", "''") & "%' and p.nokp like '" & Tx_NoKP.Text & "%' and (p.saring < 1 or p.saring is null)")
        End If
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then  Else e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
        e.Row.Cells(8).Visible = False
        e.Row.Cells(5).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(6).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(7).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(9).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(5).Width = Unit.Pixel(60)
        e.Row.Cells(6).Width = Unit.Pixel(60)
        e.Row.Cells(7).Width = Unit.Pixel(60)
        e.Row.Cells(9).Width = Unit.Pixel(60)
    End Sub

    Protected Sub cmd_Hantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Hantar.Click
        Dim i As Int16, x As CheckBox, SQL As String = ""

        For i = 0 To Gd.Rows.Count - 1
            x = Gd.Rows.Item(i).Cells(1).FindControl("chk_pilih")
            If x.Checked Then
                SQL += "update pelatih set saring = 1 where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' AND j_kursus= '" & Cb_Kursus.SelectedValue & "'" & vbCrLf
            Else
                SQL += "update pelatih set saring = 0 where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' AND J_Kursus= '" & Cb_Kursus.SelectedValue & "'" & vbCrLf
            End If
        Next
        'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal)

        Try
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Session("Msg_Tajuk") = "Saring Pelatih untuk Peperiksaan"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("Mesej.aspx")
            'Msg(Me, "Rekod Telah Dikemaskini...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        e.Row.Cells(4).Visible = False
        If e.Row.RowIndex < 0 Then Exit Sub
        cmd_Hantar.Visible = True

        Dim x As CheckBox
        x = e.Row.FindControl("chk_pilih")
        If e.Row.Cells(8).Text = "1" Then
            x.Checked = True
        End If
        If e.Row.Cells(5).Text.Trim = "" Then 'tarikh tamat
            x.Checked = False
            x.Enabled = False
            e.Row.Cells(0).ForeColor = Drawing.Color.Red
            e.Row.Cells(2).ForeColor = Drawing.Color.Red
            e.Row.Cells(3).ForeColor = Drawing.Color.Red
            e.Row.Cells(4).ForeColor = Drawing.Color.Red
            e.Row.Cells(5).ForeColor = Drawing.Color.Red
            e.Row.Cells(6).ForeColor = Drawing.Color.Red
            e.Row.Cells(7).ForeColor = Drawing.Color.Red
            e.Row.Cells(9).ForeColor = Drawing.Color.Red
        End If
        If IsNumeric(e.Row.Cells(6).Text) Then
            If e.Row.Cells(6).Text < 2 Then 'markah bterusan (cgpa)
                x.Checked = False
                x.Enabled = False
                e.Row.Cells(0).ForeColor = Drawing.Color.Red
                e.Row.Cells(2).ForeColor = Drawing.Color.Red
                e.Row.Cells(3).ForeColor = Drawing.Color.Red
                e.Row.Cells(4).ForeColor = Drawing.Color.Red
                e.Row.Cells(5).ForeColor = Drawing.Color.Red
                e.Row.Cells(6).ForeColor = Drawing.Color.Red
                e.Row.Cells(7).ForeColor = Drawing.Color.Red
                e.Row.Cells(9).ForeColor = Drawing.Color.Red
            End If
        ElseIf Not IsNumeric(e.Row.Cells(6).Text) Then
            If e.Row.Cells(6).Text <> "LULUS" Then 'markah bterusan (status)
                x.Checked = False
                x.Enabled = False
                e.Row.Cells(0).ForeColor = Drawing.Color.Red
                e.Row.Cells(2).ForeColor = Drawing.Color.Red
                e.Row.Cells(3).ForeColor = Drawing.Color.Red
                e.Row.Cells(4).ForeColor = Drawing.Color.Red
                e.Row.Cells(5).ForeColor = Drawing.Color.Red
                e.Row.Cells(6).ForeColor = Drawing.Color.Red
                e.Row.Cells(7).ForeColor = Drawing.Color.Red
                e.Row.Cells(9).ForeColor = Drawing.Color.Red
            End If
        End If        
        If e.Row.Cells(7).Text > 0 Then 'lebihan cuti sakit
            x.Checked = False
            x.Enabled = False
            e.Row.Cells(0).ForeColor = Drawing.Color.Red
            e.Row.Cells(2).ForeColor = Drawing.Color.Red
            e.Row.Cells(3).ForeColor = Drawing.Color.Red
            e.Row.Cells(4).ForeColor = Drawing.Color.Red
            e.Row.Cells(5).ForeColor = Drawing.Color.Red
            e.Row.Cells(6).ForeColor = Drawing.Color.Red
            e.Row.Cells(7).ForeColor = Drawing.Color.Red
            e.Row.Cells(9).ForeColor = Drawing.Color.Red
        End If
        If e.Row.Cells(9).Text <> "TIDAK" Then 'tatatertib
            x.Checked = False
            x.Enabled = False
            e.Row.Cells(0).ForeColor = Drawing.Color.Red
            e.Row.Cells(2).ForeColor = Drawing.Color.Red
            e.Row.Cells(3).ForeColor = Drawing.Color.Red
            e.Row.Cells(4).ForeColor = Drawing.Color.Red
            e.Row.Cells(5).ForeColor = Drawing.Color.Red
            e.Row.Cells(6).ForeColor = Drawing.Color.Red
            e.Row.Cells(7).ForeColor = Drawing.Color.Red
            e.Row.Cells(9).ForeColor = Drawing.Color.Red
        End If
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select distinct sesi_bulan, sesi_tahun, case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=" & Cb_Kursus.SelectedValue & " order by sesi_tahun, sesi_bulan"
        Rdr = Cmd.ExecuteReader()
        Cb_Sesi.Items.Clear()
        Cb_Sesi.Items.Add("SEMUA")
        Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Sesi.Items.Add(Rdr(1) & " - " & Rdr(2))
            Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = " p.sesi_bulan = " & Rdr(0) & " and p.sesi_tahun = " & Rdr(1) & " and "
        End While
        Rdr.Close()
        Cn.Close()
    End Sub
End Class