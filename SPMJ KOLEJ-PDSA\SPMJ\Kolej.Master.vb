﻿Public Partial Class Kolej
    Inherits System.Web.UI.MasterPage

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Protected Sub Menu1_MenuItemClick(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.MenuEventArgs) Handles Menu1.MenuItemClick
        Select Case e.Item.Value
            Case "1"
                Response.Redirect("blank.aspx")
                'Comment Original Old Format 20112023 - OSH 
            'Case "a1"
            '    Response.Redirect("pelatih_daftar1a.aspx")
            '    'Add New Policies page 31082013-OSH
            '    'Response.Redirect("pelatih_daftar1.aspx")
            '    'Comment Ori 31082013-OSH
            '    'Response.Redirect("pelatih_daftar.aspx")

            '    'Add Degree SOP Policies 2018 20092019 - OSH 
            'Case "a2"
            '    'Response.Redirect("pelatih_daftar2.aspx")
            '    Response.Redirect("pelatih_daftar2a.aspx")

            ' Menu Reindexing New Format SOP Policies 2018 20112023 - OSH  
            Case "a1"
                Response.Redirect("pelatih_daftar1a.aspx")
                'Add Diploma - IGCSE (SOP Policies 2018) 25082021 - OSH 
                'Case "a3"
                'Adjust menu indexing 20112023 -OSH 
            Case "a2"
                'Comment Original 01122023 - OSH 
                'Response.Redirect("Pelatih_Daftar3ai.aspx")
                'Fix Insert Issue 01122023 - OSH 
                Response.Redirect("Pelatih_Daftar3bi.aspx")
                'Add 3 credit form 14012025 - OSH
            Case "a3"
                Response.Redirect("pelatih_daftar1c.aspx")
            Case "b"
                Response.Redirect("pelatih_cr.aspx")
            Case "c"
                'Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
                'Session("Msg_Isi") = "Rekod Telah Dihantar..."
                Response.Redirect("saring.aspx")
                'Response.Redirect("saring2.aspx")

                'Case "d"
                '    Response.Redirect("sn_calon.aspx")
                'Add New Registration(BLESS) 12032014 -OSH
            Case "e"
                Response.Redirect("SN_Calon_B.aspx")
                'Response.Redirect("SN_Pendaftaran_Baru.aspx")
            Case "x"
                Response.Redirect("pwd.aspx")
            Case "z"
                Session.Clear()
                'Comment Original 03032022 - OSH 
                'Response.Redirect("login.aspx")

                'Rediect to javascript scoll list 03032022 - OSH 
                Response.Redirect("Login_J.aspx")
        End Select
    End Sub
End Class