# Simple Security Test for SPMJ Email Service

Write-Host "🔧 Testing SPMJ Email Service Security Fixes" -ForegroundColor Cyan

$EmailServiceUrl = "http://localhost:5000"
$ApiKey = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"

Write-Host "`n🔍 Testing Service Health..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "$EmailServiceUrl/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Health Check: SUCCESS" -ForegroundColor Green
    Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
    $healthPass = $true
}
catch {
    Write-Host "❌ Health Check: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor White
    $healthPass = $false
}

Write-Host "`n🔍 Testing API Without Key (Should Fail)..." -ForegroundColor Yellow

try {
    $body = '{"UserId":"test","Email":"<EMAIL>"}'
    $response = Invoke-WebRequest -Uri "$EmailServiceUrl/api/password/reset/request" -Method POST -Body $body -ContentType "application/json" -UseBasicParsing -TimeoutSec 5
    Write-Host "❌ Security Test: FAILED - Request should have been blocked!" -ForegroundColor Red
    $securityPass = $false
}
catch {
    if ($_.Exception.Message -like "*401*" -or $_.Exception.Message -like "*Unauthorized*") {
        Write-Host "✅ Security Test: SUCCESS - Unauthorized request properly blocked" -ForegroundColor Green
        $securityPass = $true
    } else {
        Write-Host "❌ Security Test: UNEXPECTED ERROR" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor White
        $securityPass = $false
    }
}

Write-Host "`n📊 TEST RESULTS SUMMARY" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host "Health Check:      $(if($healthPass) {'✅ PASS'} else {'❌ FAIL'})" -ForegroundColor $(if($healthPass) {'Green'} else {'Red'})
Write-Host "Security (No Key): $(if($securityPass) {'✅ PASS'} else {'❌ FAIL'})" -ForegroundColor $(if($securityPass) {'Green'} else {'Red'})

if ($healthPass -and $securityPass) {
    Write-Host "`n🎉 SECURITY FIXES VERIFIED!" -ForegroundColor Green
    Write-Host "   The microservice is now secure against third-party hijacking." -ForegroundColor Green
} else {
    Write-Host "`n⚠️ ISSUES DETECTED" -ForegroundColor Yellow
    if (!$healthPass) {
        Write-Host "   Service is not running. Start with: dotnet run" -ForegroundColor Yellow
    }
}

Write-Host "`nTo start the service:" -ForegroundColor Cyan
Write-Host "cd 'd:\2024\.NET 3.5. - Q\SPMJ.EmailService'" -ForegroundColor White
Write-Host "dotnet run" -ForegroundColor White
