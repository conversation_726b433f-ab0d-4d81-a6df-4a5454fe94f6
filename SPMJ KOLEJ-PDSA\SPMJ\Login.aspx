﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="Login.aspx.vb" Inherits="SPMJ.WebForm67" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <style type="text/css">
        .style1
        {
            width: 627px;
        }
        .style2
        {
        }
        .label
{
	font-family: Arial;
	font-size: 8pt;
	font-variant: small-caps;
}
        .style6
    {
        width: 58px;
    }
        .style7
        {
        }
        .style8
        {
            width: 8px;
        }
        .style9
        {
            font-weight: bold;
            text-decoration: underline;
            color: #CC0000;
        }
        .style10
        {
            width: 58px;
            height: 22px;
        }
        .style11
        {
            height: 22px;
        }
        .style12
        {
            width: 58px;
            height: 18px;
        }
        .style13
        {
            height: 18px;
        }
        .style14
        {
            width: 58px;
            height: 24px;
        }
        .style15
        {
            height: 24px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <table style="width:100%; height: 315px;" cellpadding="-1" cellspacing="-1">
        <tr>
            <td class="style8" valign="top" 
                style="background-image: url('../../Image/Bg_Dot2.gif')">&nbsp;</td>
            <td class="style1" valign="middle" 
                style="background-image: url('../../Image/Bg_Dot2.gif')" height="100%"><marquee onmouseover="stop();" onmouseout="start();" scrollAmount="1.5" scrollDelay="1" direction="up" width="100%" height="100%"><asp:Label ID="Label1" runat="server" Text="Label" Width="100%"  height="220px" BorderStyle="None" BorderWidth="0" Font-Names="Arial" Font-Size="8pt"></asp:Label></marquee></td>
            <td align="left" valign="top" style="border-color: #000000; border-left-style: solid; border-width: 1px; background-image: url('../../Image/LJM-Bg.gif'); background-attachment: fixed; background-repeat: no-repeat;">
                <table style="background-position: 250px 90px; width: 100%; font-family: Arial; font-size: 8pt; font-variant: small-caps; background-image: url('../../Image/LJM-Bg.gif'); background-repeat: no-repeat; height: 100%;">
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style2" colspan="2">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style14">
                            </td>                        <td class="style15" colspan="2">
                            <asp:Label ID="LbL" runat="server" ForeColor="#CC0000"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td class="style10">
                            </td>
                        <td class="style11">
                            Kod Pengguna</td>                        <td class="style11">
                            <asp:TextBox ID="Tx_Id" runat="server" 
    CssClass="label" MaxLength="15"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td class="style12">
                            </td>
                        <td class="style13">
                            Kata Laluan</td>                        <td class="style13">
                            <asp:TextBox ID="Tx_Pwd" runat="server" 
    CssClass="label" MaxLength="15" TextMode="Password"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td class="style10">
                            </td>
                        <td class="style11">
                            </td>                        <td class="style11">
                            <asp:Button ID="Button1" runat="server" 
    Text="LOGIN" CssClass="label" 
                                Width="50px" />
                        </td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style7">
                            &nbsp;</td>
                        <td>
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style7" colspan="2" rowspan="2">
                            <span class="style9">
                            <br />
                            Perhatian<br />
                            </span><br />
                            Sistem ini dibangunkan khas utuk kegunaan Kolej-Kolej Kejururawatan sahaja. 
                            Untuk sebarang pertanyaan mengenai penggunaan sistem ini, sila hubungi:<br />
                            <br />
                            Bahagian Kejururawatan, kkm<br />
                            <br />
                            <br />
                            <br />
                        </td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                    </tr>
                    </table>
            </td></tr>
    </table>
</asp:Content>
