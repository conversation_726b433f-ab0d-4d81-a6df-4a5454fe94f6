# Test Email Service Health Check from .NET 3.5 Application
Write-Host "Testing Email Service Health Check Integration" -ForegroundColor Cyan

# Test 1: Direct service health check
Write-Host "`n1. Testing direct service health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "SUCCESS: Direct health check" -ForegroundColor Green
    Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   Content: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "FAILED: Direct health check" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Check if email service is accessible without auth
Write-Host "`n2. Testing email service without authentication..." -ForegroundColor Yellow
try {
    $request = [System.Net.WebRequest]::Create("http://localhost:5000/health")
    $request.Method = "GET"
    $request.Timeout = 5000
    
    $response = $request.GetResponse()
    $responseStream = $response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($responseStream)
    $responseText = $reader.ReadToEnd()
    
    Write-Host "SUCCESS: No-auth health check" -ForegroundColor Green
    Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   Content: $responseText" -ForegroundColor White
    Write-Host "   Contains healthy: $($responseText.Contains('healthy'))" -ForegroundColor White
    
    $reader.Close()
    $responseStream.Close()
    $response.Close()
    
} catch {
    Write-Host "FAILED: No-auth health check" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nHealth Check Diagnostics Complete" -ForegroundColor Cyan
