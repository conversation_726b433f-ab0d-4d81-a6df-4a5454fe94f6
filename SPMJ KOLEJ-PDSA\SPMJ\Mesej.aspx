﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="Mesej.aspx.vb" Inherits="SPMJ.WebForm70" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
    .style2
    {
        }
</style>
<script language=JavaScript>
<!--
window.history.forward(1);
// -->
</script>


</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="width: 100%; height: 100%; background-image: url('../Image/Bg_Dot2.gif'); background-attachment: fixed;">
        <table style="width: 100%; height: 100%;">
            <tr>
                <td align="center">
                    <br />
                    <br />
                    <br />
                    <table cellpadding="-1" cellspacing="-1" 
                        style="width: 100%; font-family: Arial; font-size: 8pt; font-variant: small-caps;">
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td bgcolor="White" class="style2" 
                                
                                style="border-left-style: double; border-top-style: double; border-color: #000000; border-right-style: double;" 
                                width="500">
                                &nbsp;</td>
                            <td>
                                &nbsp;</td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td align="center" bgcolor="White" class="style2" 
                                
                                style="border-left-style: double; border-color: #000000; border-right-style: double;">
                                <asp:Label ID="Label1" runat="server" ForeColor="#CC0000" Font-Bold="True"></asp:Label>
                            </td>
                            <td>
                                &nbsp;</td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td align="center" bgcolor="White" class="style2" 
                                
                                style="border-left-style: double; border-color: #000000; border-right-style: double;">
                                <asp:Label ID="Label2" runat="server"></asp:Label>
                            </td>
                            <td>
                                &nbsp;</td>
                        </tr>
                        <tr>
                            <td>
                                &nbsp;</td>
                            <td bgcolor="White" class="style2" 
                                
                                style="border-bottom-style: double; border-left-style: double; border-color: #000000; border-right-style: double;">
                                &nbsp;</td>
                            <td>
                                &nbsp;</td>
                        </tr>
                    </table>
                    <br />
                    <br />
                    <br />
                    <br />
                </td>
            </tr>
        </table>

    </div></asp:Content>
