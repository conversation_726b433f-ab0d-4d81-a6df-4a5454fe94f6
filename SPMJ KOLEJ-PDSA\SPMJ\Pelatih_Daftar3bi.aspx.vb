﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.SqlClient
Public Class Pelatih_Daftar3bi
    Inherits System.Web.UI.Page

    Private Const V As String = "',"
    Dim CLP, LP As Int16

    Public Sub Isi_Subjek(ByVal X As Int16)
        'Add conditions for correct clear up 07022023 - OSH
        'IGCSE
        If X = 1 Then
            Cb_Sbj1.Items.Clear() : Cb_Sbj2.Items.Clear() : Cb_Sbj3.Items.Clear() : Cb_Sbj4.Items.Clear() : Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
            Cb_Sbj1.Items.Add("") : Cb_Sbj2.Items.Add("") : Cb_Sbj3.Items.Add("") : Cb_Sbj4.Items.Add("") : Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
            Cb_Sbj2.Visible = False : Cb_Sbj3.Visible = False : Cb_Sbj4.Visible = False : Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
            Cb_Kpts2.Visible = False : Cb_Kpts3.Visible = False : Cb_Kpts4.Visible = False : Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        End If
        'STPM/ A-Level
        If X = 3 Or X = 4 Then
            Clr_SPTM_A_Level()
        End If

        'Comment Original 01122023 - OSH  
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'ADO.NET Connection 01122023 - OSH
        Dim txt_Cmd As String = String.Empty
        Dim Cn As New SqlConnection : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        'Add Load IGCSE 24082021 - OSH 
        'If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'IGCSE' order by dc_subjek"
        'If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"

        'If X = 4 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'A-L' order by dc_subjek"

        If X = 1 Then txt_Cmd = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'IGCSE' order by dc_subjek"
        If X = 3 Then txt_Cmd = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"

        If X = 4 Then txt_Cmd = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'A-L' order by dc_subjek"
        If X = 0 Then Exit Sub

        Dim Cmd As New SqlCommand(txt_Cmd, Cn)
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If X = 1 Then 'SPM
                Cb_Sbj1.Items.Add(Rdr(0)) : Cb_Sbj1.Items.Item(Cb_Sbj1.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj2.Items.Add(Rdr(0)) : Cb_Sbj2.Items.Item(Cb_Sbj2.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj3.Items.Add(Rdr(0)) : Cb_Sbj3.Items.Item(Cb_Sbj3.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj4.Items.Add(Rdr(0)) : Cb_Sbj4.Items.Item(Cb_Sbj4.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)

            ElseIf X = 3 Or X = 4 Then  ' STPM or A-Level
                Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
            End If
        End While
        Rdr.Close()
        Cn.Close()

        'Add IGCSE grade loaded 24082021 - OSH 
        If X = 1 Then
            Gred_IGCSE(Cb_Kpts1)
            Gred_IGCSE(Cb_Kpts2)
            Gred_IGCSE(Cb_Kpts3)
            Gred_IGCSE(Cb_Kpts4)
            Gred_IGCSE(Cb_Kpts5)
            Gred_IGCSE(Cb_Kpts6)
            Gred_IGCSE(Cb_Kpts7)
            Gred_IGCSE(Cb_Kpts8)
            Gred_IGCSE(Cb_Kpts9)

        ElseIf X = 3 Then
            Gred_STPM(Cb_KptsT1)
            Gred_STPM(Cb_KptsT2)
            Gred_STPM(Cb_KptsT3)
            Gred_STPM(Cb_KptsT4)
            Gred_STPM(Cb_KptsT5)

            'Populate A-Level Grades 29072019 -OSH 
        ElseIf X = 4 Then
            Gred_O_A_Level(Cb_KptsT1)
            Gred_O_A_Level(Cb_KptsT2)
            Gred_O_A_Level(Cb_KptsT3)
            Gred_O_A_Level(Cb_KptsT4)
            Gred_O_A_Level(Cb_KptsT5)
        End If
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Comment Original 05042022 - OSH 
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        'New Landing Page05042022-OSH      
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login_j.aspx")
        'If Not Akses_Pg("P1", "Pelatih_Daftar", Session("AKSES"), Session("MODUL")) Then
        '    Session("Msg_Tajuk") = "PEPERIKSAAN"
        '    Session("Msg_Isi") = "Akses Terhad"
        '    Response.Redirect("p0_Mesej.aspx")
        '    Exit Sub
        'End If

        UICulture = "en-GB"
        Culture = "en-GB"

        'Comment Original 01122023 - OSH
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'ADO.NET Connection 01122023 - OSH 
        Dim Cn As New SqlConnection : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        'Comment Original 15122023 - OSH 
        'If Cb_NoKP.SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

        'Fix Id Value 15122023 - OSH 
        'If Cb_NoKP.SelectedValue = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

        'KURSUS
        'Load Active Couses Only 08092022 - OSH 
        Dim Cmd As New SqlCommand("SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS WHERE XM = 1 and id_kursus in (1,5) ORDER BY ID_KURSUS ", Cn)
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        Cb_Kursus.Items.Item(0).Value = "0"
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'BULAN
        Cb_Sesi_Bulan.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI") : Cb_Sesi_Bulan.Items.Item(1).Value = "1"
        Cb_Sesi_Bulan.Items.Add("FEBRUARI") : Cb_Sesi_Bulan.Items.Item(2).Value = "2"
        Cb_Sesi_Bulan.Items.Add("MAC") : Cb_Sesi_Bulan.Items.Item(3).Value = "3"
        Cb_Sesi_Bulan.Items.Add("APRIL") : Cb_Sesi_Bulan.Items.Item(4).Value = "4"
        Cb_Sesi_Bulan.Items.Add("MEI") : Cb_Sesi_Bulan.Items.Item(5).Value = "5"
        Cb_Sesi_Bulan.Items.Add("JUN") : Cb_Sesi_Bulan.Items.Item(6).Value = "6"
        Cb_Sesi_Bulan.Items.Add("JULAI") : Cb_Sesi_Bulan.Items.Item(7).Value = "7"
        Cb_Sesi_Bulan.Items.Add("OGOS") : Cb_Sesi_Bulan.Items.Item(8).Value = "8"
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER") : Cb_Sesi_Bulan.Items.Item(9).Value = "9"
        Cb_Sesi_Bulan.Items.Add("OKTOBER") : Cb_Sesi_Bulan.Items.Item(10).Value = "10"
        Cb_Sesi_Bulan.Items.Add("NOVEMBER") : Cb_Sesi_Bulan.Items.Item(11).Value = "11"
        Cb_Sesi_Bulan.Items.Add("DISEMBER") : Cb_Sesi_Bulan.Items.Item(12).Value = "12"

        'TAHUN
        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        For i = 0 To 5
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i)
        Next

        'BANGSA-07092013-OSH
        Cmd.CommandText = "SELECT DC_RACE,ID_RACE FROM SPMJ_REF_RACE ORDER BY ID_RACE"
        Rdr = Cmd.ExecuteReader()
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("")
        While Rdr.Read
            Cb_Bangsa.Items.Add(Rdr(0))
            Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KOLEJ
        Cb_Kolej.Items.Add(Session("Dc_KOLEJ"))
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Session("Id_KOLEJ")
        Cb_Kolej.SelectedIndex = 0

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'STPM OR A-LEVEL
        Cb_Aliran.Items.Clear()
        Cb_Aliran.Items.Add("(PILIHAN)")
        Cb_Aliran.Items(0).Value = 0
        Cb_Aliran.Items.Add("STPM")
        Cb_Aliran.Items(1).Value = 1
        Cb_Aliran.Items.Add("A-LEVEL")
        Cb_Aliran.Items(2).Value = 2

        'Comment Original 15122023 - OSH 
        ''Load Identity list 17092019 - OSH 
        'Cb_NoKP.Items.Clear()
        'Cb_NoKP.Items.Add("MYKAD") : Cb_NoKP.Items.Item(0).Value = "0"
        'Cb_NoKP.Items.Add("TENTERA") : Cb_NoKP.Items.Item(1).Value = "1"
        'Cb_NoKP.Items.Add("PASSPORT") : Cb_NoKP.Items.Item(2).Value = "2"


        'Comment Original 15122023 - OSH 
        'Add control id type loading - 18102013
        'With Cb_NoKP
        '    'Add Army textbox Control 10082013-OSH
        '    If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

        '    'Add Army textbox Control 10082013-OSH
        '    'If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1)

        '    'States or Country 08012020 - OSH 
        '    If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(0)
        '    'Comment Ori 10082013-OSH
        '    'If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : Fn_Negara(1)
        '    'Add Army textbox Control 10082013-OSH
        '    'If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) 
        '    'States or Country 08012020 - OSH 
        '    If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0)
        '    If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        'End With

        'If Cb_NoKP.SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"

        'hiden panels by default 14092022 - OSH
        Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : Panel7.Visible = False : PanelSemak.Visible = False

        'Populate Kelayakan 14092022 -OSH
        Cb_Kelayakan.Items.Add("IGCSE")

        'Unable dropdown sesi pengambilan 03042023 - OSH
        Cb_Sesi_Bulan.Enabled = False : Cb_Sesi_Tahun.Enabled = False
    End Sub
    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        If Cb_Kerakyatan.SelectedValue = "0" Or Cb_Kerakyatan.SelectedValue = "1" Then
            If Not Tx_M_Latihan.Text = "" Then
                Chk_Date_Policy()
            End If
        End If
    End Sub

    Public Sub Load_Panel()
        Cb_Kelayakan.Enabled = True : Cb_Kelayakan.Items.Clear()

        'Disable ComboBoxs if date, citizen aand courses correctly selected 29052023 -OSH 
        Cb_Kerakyatan.Enabled = False : Cb_Kursus.Enabled = False : Cb_Kelayakan.Enabled = False : Tx_M_Latihan.Enabled = False

        If Cb_Kursus.SelectedValue = 1 Then 'Diploma

            'Add SOP 2018 - IGCSE level 25082021 - OSH  
            Cb_Kelayakan.Items.Add("IGCSE") : Isi_Subjek(1)

            If Cb_Kerakyatan.SelectedValue = "0" Then 'Warganegara
                Panel4.Visible = True : Panel5.Visible = False : Panel6.Visible = False : Panel7.Visible = False

            ElseIf Cb_Kerakyatan.SelectedValue = "1" Then 'Bukan Warganegara
                Panel4.Visible = True : Panel5.Visible = False : Panel6.Visible = False : Panel7.Visible = True
                Fn_Inggeris(0)
            End If

            PanelSemak.Visible = True
            Cb_Sbj1.Visible = True : Cb_Kpts1.Visible = True : Bt1.Visible = True
            Cb_Sbj1.Enabled = True : Cb_Kpts1.Enabled = True : Bt1.Enabled = True
            Text1.Visible = True

            'Add Degree Section 07092022 - OSH 
        ElseIf Cb_Kursus.SelectedValue = 5 Then 'Ijazah
            Cb_Kelayakan.Items.Add("IGCSE")
            Panel4.Visible = True : Panel5.Visible = True : Panel6.Visible = True : Panel7.Visible = True

            If Cb_Kerakyatan.SelectedValue = "0" Then 'Warganegara
                Isi_Subjek(1) : Fn_Inggeris(1) : Fn_Lanjutan(1)
            ElseIf Cb_Kerakyatan.SelectedValue = "1" Then 'Bukan Warganegara
                Isi_Subjek(1) : Fn_Inggeris(0) : Fn_Lanjutan(0)
            End If

            PanelSemak.Visible = True
            Cb_Sbj1.Visible = True : Cb_Kpts1.Visible = True : Bt1.Visible = True
            Cb_Sbj1.Enabled = True : Cb_Kpts1.Enabled = True : Bt1.Enabled = True
            Text1.Visible = True
        End If
    End Sub
    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click

        'Medan Mandatori...
        Dim X As String = String.Empty
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.Text.Trim = "-" Then X += "Warganegara, "
        'Add Mandatory for brith date & brith place 17102013-OSH 
        If Tx_Tkh_Lahir.Text.Trim = "" Then X += "Tarikh Lahir, "
        If Tx_Tp_Lahir.Text.Trim = "" Then X += "Tempat Lahir , "
        If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
        If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
        If Cb_Agama.Text.Trim = "" Then X += "Agama, "
        If Cb_Kahwin.Text.Trim = "" Then X += "Taraf Perkahwinan, "
        If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
        If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
        If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
        If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
        If Tx_Tel.Text.Trim = "" Then X += "No. Telefon, "
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        'Original Comment 22122020 - OSH
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        'Fix loophole date 00000/00/00 pass through 24122020 - OSH   
        'If Tx_M_Latihan.Text.Trim = "" Or Not IsDate(Tx_M_Latihan.Text.Trim) Or Chk_Tkh(Tx_M_Latihan.Text) = "NULL" Then X += "Tarikh Mula Latihan, "
        If X.Trim = "" Then Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        'Improve student data verification 17082022 -OSH 
        'If X.Trim = "" Then
        '    If ChkSah.Checked = False Then
        '        Msg(Me, "Sila sahkan maklumat pelatih ini adalah benar ! ") : ChkSah.Focus() : Exit Sub
        '    End If
        'Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila lengkapkan maklumat berikut: " & X) : Exit Sub
        'End If
        'Check No KP length
        X = ""

        'Comment Original 15122023 - OSH 
        'With Cb_NoKP
        '    If .SelectedIndex = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
        '    If .SelectedIndex = 0 Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"
        'End With

        'Fix id value 15122023 - OSH 
        With Cb_NoKP
            If .SelectedValue = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
            If .SelectedValue = 0 Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"
        End With
        If X.Trim = "" Then Else Msg(Me, "Maklumat No. Kad Pengenalan tidak " & X) : Tx_NoKP.Focus() : Exit Sub

        'Recheck 5 credit policy on date 31 july 2013 - 17082013 - OSH
        'If Tx_M_Latihan.Text = "31/07/2010" Then
        '    If Chk_Subjek() = True Msg(Me, "Subjek 5 Kredit SPM Tidak Ditepati.") : Exit Sub
        '    End If



        'ADO.NET Connection 01122023 - OSH 
        Dim Cn As New SqlConnection : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        Dim Cmd As New SqlCommand("select nokp from pelatih where nokp = '" & Tx_NoKP.Text.Trim & "' AND j_kursus='" & Cb_Kursus.SelectedValue & "'", Cn)
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Dim SQL_U As String = String.Empty

        Dim Cn2 As New SqlConnection : Dim Rdr2 As SqlDataReader
        Cn2.ConnectionString = ServerId_SQL : Cn2.Open()

        If Cb_Kursus.SelectedValue = "1" Or Cb_Kursus.SelectedValue = "8" Then ' DIPLOMA
            Dim Cmd2 As New SqlCommand("select nokp from pelatih where nokp = '" & Tx_NoKP.Text.Trim & "' AND j_kursus='" & Cb_Kursus.SelectedValue & "'", Cn2)
            Rdr2 = Cmd2.ExecuteReader()

            While Rdr2.Read
                SQL_U += "update pelatih set status = 1 where status is null and j_kursus = '" & Rdr(0) & "' and nokp  = '" & Tx_NoKP.Text.Trim & "';"
                SQL_U += "insert into kj_pelatih_kemaskini_log (nokp, lama,  baru, log_id, log_tkh) values ( '" & Tx_NoKP.Text.Trim & "', '" & Rdr(0) & "' ,'" & Cb_Kursus.SelectedValue & "','" & Session("Id_PG") & "',getdate()); "
            End While
            Rdr2.Close()
            Cn2.Close()
        End If

        Dim Cn3 As New SqlConnection
        Cn3.ConnectionString = ServerId_SQL : Cn3.Open()
        'If SQL_U <> "" Then
        If Not String.IsNullOrEmpty(SQL_U) Then
            Dim Cmd3 As New SqlCommand(SQL_U, Cn)
            Cmd3.ExecuteNonQuery()
        End If
        Cn3.Close()


        Dim SQL As String = String.Empty
        Try
            'Add Choose type id 11081013-OSH
            'Fixing select value 19082013 -OSH
            If Cb_NoKP.SelectedValue = "0" Or Cb_NoKP.SelectedValue = "2" Then
                'Comment Ori 19082013- OSH
                'If Cb_NoKP.SelectedIndex = "0" Or Cb_NoKP.SelectedIndex = "2" Then
                'Add insert Brith Date & Brith Place query 26032014 -OSH 
                SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                'Comment Ori 26032014- OSH
                'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                SQL += Cb_Kursus.SelectedItem.Value & ","
                If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
                SQL += "" & Cb_Kerakyatan.SelectedValue & ","
                SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "'," ' Add ARMY Number 26032014 -OSH
                'Fixing select value 19082013 -OSH
                SQL += Cb_NoKP.SelectedValue & ","
                'Comment Ori 19082013- OSH
                'SQL += Cb_NoKP.SelectedIndex & ","
                SQL += Cb_Warga.SelectedItem.Value & ","
                'Add Brith Date 16092013 - OSH
                SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & ","
                'Add Brith Place 16092013 - OSH
                SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                SQL += Cb_Jantina.SelectedIndex & ","
                'Fix id value 15122023 - OSH 
                SQL += Cb_Bangsa.SelectedValue & ","
                SQL += Cb_Agama.SelectedValue & ","
                'Comment Original 15122023 - OSH 
                'SQL += Cb_Bangsa.SelectedIndex & ","
                'SQL += Cb_Agama.SelectedIndex & ","
                SQL += Cb_Kahwin.SelectedIndex & ","
                SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Tx_Tel.Text.Trim & "',"
                'Comment Original 04072023 -OSH
                'SQL += "'" & Tx_Emel.Text.Trim & "',"
                'Casting email to lower case 04072023 -OSH
                SQL += "'" & Tx_Emel.Text.Trim.ToLower & "',"
                SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
                SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                SQL += Chk_Tkh(Tx_M_Latihan.Text) & "," 'Comment Original 22122020 - OSH 
                'Fix loophole date 00000/00/00 pass through 22122020 - OSH  
                'If Not String.IsNullOrEmpty(Chk_Tkh(Tx_M_Latihan.Text)) Then
                '    SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
                'Else
                '    Msg(Me, "Sila isikan tarikh mula latihan")
                'End If
                SQL += SSemak(0) & ","
                SQL += SSemak(1) & ","
                SQL += SSemak(2) & ","
                SQL += SSemak(3) & ","
                SQL += SSemak(4) & ","
                SQL += SSemak(5) & ","
                SQL += SSemak(6) & ","
                SQL += SSemak(7) & ","
                SQL += "'" & Session("Id_PG") & "',"
                SQL += "getdate()"
                SQL += ");"
                'Add AMRY ID SQL INSERT 11082013-OSH
                'Fixing select value 19082013 -OSH
                'ElseIf Cb_NoKP.SelectedValue = "1" Then
                'Comment Ori 19082013- OSH
            ElseIf Cb_NoKP.SelectedIndex = "1" Then
                'Add insert Brith Date & Brith Place query 16092013 -OSH 
                SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                'Comment Ori 160920213 -OSH
                'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                SQL += Cb_Kursus.SelectedItem.Value & ","
                If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
                ' SQL += "" & Cb_Kelayakan_Taraf.SelectedValue & ","
                SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "',"
                'Fixing select value 19082013 -OSH
                'SQL += Cb_NoKP.SelectedValue & ","
                'Comment Ori 19082013- OSH
                SQL += Cb_NoKP.SelectedIndex & ","
                SQL += Cb_Warga.SelectedItem.Value & ","
                'Add Brith Date 16092013 - OSH
                SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & ","
                'Add Brith Place 16092013 - OSH
                SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                SQL += Cb_Jantina.SelectedIndex & ","
                'Comment Original 15122023 - OSH 
                'SQL += Cb_Bangsa.SelectedIndex & ","
                'SQL += Cb_Agama.SelectedIndex & ","
                'Fix id value 15122023 - OSH 
                SQL += Cb_Bangsa.SelectedValue & ","
                SQL += Cb_Agama.SelectedValue & ","
                SQL += Cb_Kahwin.SelectedIndex & ","
                SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Tx_Tel.Text.Trim & "',"
                SQL += "'" & Tx_Emel.Text.Trim & "',"
                SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                SQL += "'" & Cb_Kolej.SelectedItem.Value & V
                SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
                SQL += SSemak(0) & ","
                SQL += SSemak(1) & ","
                SQL += SSemak(2) & ","
                SQL += SSemak(3) & ","
                SQL += SSemak(4) & ","
                SQL += SSemak(5) & ","
                SQL += SSemak(6) & ","
                SQL += SSemak(7) & ","
                SQL += "'" & Session("Id_PG") & "',"
                SQL += "getdate()"
                SQL += ");"
            End If

            If Panel7.Visible = True Then 'BI luar/setaraf
                If Cb_Luar.SelectedValue > 0 And Cb_KptsLuar.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_Luar.SelectedValue & "','" & Cb_KptsLuar.Text.ToUpper & "', 'IEL',getdate());" & vbCrLf
            End If

            If Panel6.Visible = True Then 'matrik
                If Cb_SbjM.SelectedValue > 0 And Cb_KptsM.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjM.SelectedValue & "','" & Cb_KptsM.Text.ToUpper & "', 'FL', getdate());" & vbCrLf
            End If

            If Panel5.Visible = True Then 'stpm
                If Cb_Aliran.SelectedValue = 1 Then 'STPM
                    If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "','STPM',getdate());" & vbCrLf
                    If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "','STPM',getdate());" & vbCrLf
                    If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "','STPM',getdate());" & vbCrLf
                    If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "','STPM',getdate());" & vbCrLf
                    If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "','STPM',getdate());" & vbCrLf

                ElseIf Cb_Aliran.SelectedValue = 2 Then 'A-LEVEL
                    If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "','A-L',getdate());" & vbCrLf
                    If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "','A-L',getdate());" & vbCrLf
                    If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "','A-L',getdate());" & vbCrLf
                    If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "','A-L',getdate());" & vbCrLf
                    If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "','A-L',getdate());" & vbCrLf
                End If
            End If

            If Panel4.Visible = True Then 'IGCSE

                'Subjects and scores 15092022 - OSH 
                If Cb_Kpts1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj1.SelectedItem.Value & "','" & Cb_Kpts1.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj2.SelectedItem.Value & "','" & Cb_Kpts2.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj3.SelectedItem.Value & "','" & Cb_Kpts3.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj4.SelectedItem.Value & "','" & Cb_Kpts4.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj7.SelectedItem.Value & "','" & Cb_Kpts7.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts8.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj8.SelectedItem.Value & "','" & Cb_Kpts8.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
                If Cb_Kpts9.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj9.SelectedItem.Value & "','" & Cb_Kpts9.SelectedItem.Text & "','IGCSE',getdate());" & vbCrLf
            End If

            'Final verification and confirmation student final college exam details 21102022 - OSH  
            If ChkSah.Checked = True Then
                SQL += "update pelatih set " &
                       "Id_Sah_Data_Peribadi_Akademik = '" & Session("Id_PG") & "', " &
                       "Tkh_Sah_Data_Peribadi_Akademik = getdate() " &
                       "where nokp = '" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "' and j_kursus = " & Cb_Kursus.SelectedItem.Value & ";"
            End If

            Dim Cmd_Final As New SqlCommand(SQL, Cn)
            Cmd_Final.ExecuteNonQuery()


            'Check Limit Date Range 05072023 - OSH
            'Dim today As DateTime
            'Dim limit As DateTime
            'Dim valid As Integer

            ''Cmd.CommandText = "select getdate();"
            'Dim Cmd_limit As New SqlCommand("select getdate();", Cn)
            'Rdr = Cmd_limit.ExecuteReader()
            'While Rdr.Read
            '    today = Rdr(0)
            'End While
            'Rdr.Close()

            ''limit = today.AddDays(30)
            'limit = today.AddDays(2000)
            'valid = Date.Compare(Tx_M_Latihan.Text.Trim, limit)

            'If valid = "1" Then
            '    Msg(Me, "Tarikh Daftar Melebihi Tempoh Yang Dibenarkan") : Exit Sub

            '    If SQL = "" Then

            '    Else
            '        'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")

            '        'add error handle to catch  29112023 - OSH 
            '        Try

            '            'Cmd.CommandText = SQL
            '            'Dim Cmd_Final As New SqlCommand(SQL, Cn)
            '            'Cmd_Final.CommandType = CommandType.Text
            '            Dim Cmd_Final As New SqlCommand
            '            Cmd_Final = Cn.CreateCommand
            '            Cmd_Final.CommandText = SQL

            '            If Cmd_Final.CommandText = "" Then
            '                Msg(Me, "ERROR NOT ABLE TO SAVE")
            '            Else
            '                Cmd_Final.ExecuteNonQuery()
            '            End If

            '        Catch ex As Exception
            '            Msg(Me, ex.Message)
            '        End Try
            '    End If
            'End If

            Cn.Close()
            Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
            Session("Msg_Isi") = "Rekod Telah Dihantar..."
            Response.Redirect("Mesej.aspx")
            'Msg(Me, "Rekod Telah Dihantar...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Function Chk_Tkh(ByVal X As String)
        If IsDate(X) Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
        Return X
    End Function

    Function Chk_Staff()
        Dim A, T, J As Integer

        'Comment Original 01122023 - OSH 
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'ADO.NET Connection 01122023 - OSH 
        Dim Cn As New SqlConnection : Dim Cmd As New SqlCommand : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        'PENDAFTARAN PENUH
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where ret=0 and apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        'Comment Original 17072019 - OSH
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        'Add SOP 2018 rules, active apc only and reg types 17092019 - OSH 
        Cmd.CommandText = "SELECT nokp, apc_tahun, j_daftar FROM jt_penuh_apc where apc_tahun <= year(getdate()) and ret = 0 and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A += 1
            T = Rdr(1)
            J = Rdr(2) 'Type of registration
        End While
        Rdr.Close()
        Cn.Close()
        'ADD REG TYPES CHECK 17072019 - OSH 
        If A > 2 And J > 1 And T = CInt(Year(Now)) Then
            'ADD Change 5 years work EXP to 3 years working EXP 29102013- OSH
            'If A > 2 And T = CInt(Year(Now)) Then
            'Comment Ori  5 YEAR WORKING EXP 29102013 -OSH
            'If A > 4 And T = CInt(Year(Now)) Then
            Chk_Staff = False
        Else
            Msg(Me, "Pengalaman bekerja tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
            Chk_Staff = True
        End If
        Return Chk_Staff
    End Function
    Public Sub Fn_Lanjutan(ByVal X As Int16)
        'Comment Original 01122023 - OSH 
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'ADO.NET Connection 01122023 - OSH 
        Dim Lanjutan_SQL As String = String.Empty
        Dim Cn As New SqlConnection : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        'WARGANEGARA
        If X = 1 Then
            '  Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NATIVE = 1" 'Local
            Dim Cmd As New SqlCommand("SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NATIVE = 1", Cn) ' Local
            Rdr = Cmd.ExecuteReader()
        Else
            'Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NNATIVE = 1" 'International
            Dim Cmd As New SqlCommand("Select DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NNATIVE = 1", Cn) 'International
            Rdr = Cmd.ExecuteReader()
        End If

        Cb_SbjM.Items.Clear()
        Cb_SbjM.Items.Add("(PILIHAN)")
        Cb_SbjM.Items.Item(0).Value = "0"
        While Rdr.Read
            Cb_SbjM.Items.Add(Rdr(0))
            Cb_SbjM.Items.Item(Cb_SbjM.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        Dim X As String = String.Empty
        Session("POLISI") = "OMEGA"
        Chk_Subjek()

        If Chk_Subjek() = True Then
            Panel1.Visible = True : Panel2.Visible = True : Panel3.Visible = True : PanelSemak.Visible = True

            'Adjusting Panels 04122023 - OSH
            Panel5.Enabled = False : Panel6.Enabled = False : Panel7.Enabled = False
            If Cb_Luar.SelectedValue = "0" Then
                Panel7.Visible = False 'Exclude dataset from insert query - International English
            ElseIf Cb_SbjM.SelectedValue = "0" Then
                Panel6.Visible = False  'Exclude dataset from insert query - Foundation Studies
            ElseIf Cb_SbjT1.SelectedValue = "0" Then
                Panel5.Visible = False 'Exclude dataset from insert query - SPTM / A-Level
            Else
            End If

            'Remove Unused Comboboxes 07092022 - OSH
            Cb_Sbj1.Enabled = False : Cb_Kpts1.Enabled = False : Cb_Sbj2.Enabled = False : Cb_Kpts2.Enabled = False : Cb_Sbj3.Enabled = False : Cb_Kpts3.Enabled = False
            Cb_Sbj4.Enabled = False : Cb_Kpts4.Enabled = False : Cb_Sbj5.Enabled = False : Cb_Kpts5.Enabled = False : Cb_Sbj6.Enabled = False : Cb_Kpts6.Enabled = False
            Cb_Sbj7.Enabled = False : Cb_Kpts7.Enabled = False : Cb_Sbj8.Enabled = False : Cb_Kpts8.Enabled = False : Cb_Sbj9.Enabled = False : Cb_Kpts9.Enabled = False
            Bt1.Enabled = False : Bt2.Enabled = False : Bt3.Enabled = False : Bt4.Enabled = False : Bt5.Enabled = False : Bt6.Enabled = False : Bt7.Enabled = False : Bt8.Enabled = False

            'Add Diasble Course Selection After Qualification Check FOR IGCSE 19082022- OSH
            Cb_Kursus.Enabled = False : Cb_Kelayakan.Enabled = False : Tx_M_Latihan.Enabled = False : Cb_Kerakyatan.Enabled = False
            'Disable 'SEMAK' button after qualification check 15092022 - OSH
            cmd_Semak.Enabled = False
        Else
            Msg(Me, "Subjek 5 Kredit O-Level/GCSE/IGCSE Tidak Ditepati.") : Exit Sub
        End If

    End Sub

    Protected Sub Cb_NoKP_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_NoKP.SelectedIndexChanged
        Tx_NoKP.Text = ""
        'Comment Original 15122023 - OSH 
        'With Cb_NoKP
        '    If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
        '    If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan" : Tx_Tentera_TextBoxWatermarkExtender.Enabled = True : Tx_Tentera_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_Tentera_TextBoxWatermarkExtender.WatermarkText = "Nombor Tentera"
        '    If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Passport"
        '    If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        'End With

        'Fix id value 15122023 - OSH 
        With Cb_NoKP
            If .SelectedValue = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
            If .SelectedValue = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan" : Tx_Tentera_TextBoxWatermarkExtender.Enabled = True : Tx_Tentera_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_Tentera_TextBoxWatermarkExtender.WatermarkText = "Nombor Tentera"
            If .SelectedValue = 2 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Passport"
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
    End Sub

    Public Sub Fn_Negara(ByVal X As Int16)
        'Comment Original 01122023 - OSH 
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'ADO.NET Connection 01122023 - OSH 
        Dim txt_Cmd As String = String.Empty
        Dim Cn As New SqlConnection : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()


        'WARGANEGARA
        If X = 1 Then
            'Comment Original 01122023 - OSH 
            'Cmd.CommandText = "Select Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"
            txt_Cmd = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"

        Else
            'Comment Original 01122023 - OSH 
            'Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
            txt_Cmd = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Dim Cmd As New SqlCommand(txt_Cmd, Cn)
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        Cb_W_Negara.Items.Clear()
        Cb_W_Negara.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negara.Items.Add(Rdr(0))
            Cb_W_Negara.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub
    'Check IGCSE Sciences 07022023 - OSH 
    Function Chk_Degree_IGCSE_S(ByVal cb As DropDownList)
        Chk_Degree_IGCSE_S = False
        If cb.SelectedValue >= 479 And cb.SelectedValue <= 481 Then Chk_Degree_IGCSE_S = True : Exit Function 'physical
        If cb.SelectedValue >= 489 And cb.SelectedValue <= 491 Then Chk_Degree_IGCSE_S = True : Exit Function 'science
        If cb.SelectedValue >= 414 And cb.SelectedValue <= 415 Then Chk_Degree_IGCSE_S = True : Exit Function 'chemistry
        If cb.SelectedValue >= 410 And cb.SelectedValue <= 411 Then Chk_Degree_IGCSE_S = True : Exit Function 'biology
        'If cb.SelectedValue = 481 Then Chk_Degree_IGCSE_S = True : Exit Function 'applied science
        Return Chk_Degree_IGCSE_S
    End Function

    'Check Sciences Subjects -Non-Citizen 05092022 - OSH 
    Function Chk_Dip_IGCSE_N_S(ByVal cb As DropDownList)
        Chk_Dip_IGCSE_N_S = False
        If cb.SelectedValue >= 489 And cb.SelectedValue <= 491 Then Chk_Dip_IGCSE_N_S = True : Exit Function 'science
        If cb.SelectedValue >= 479 And cb.SelectedValue <= 481 Then Chk_Dip_IGCSE_N_S = True : Exit Function 'physical
        If cb.SelectedValue >= 414 And cb.SelectedValue <= 415 Then Chk_Dip_IGCSE_N_S = True : Exit Function 'chemistry
        If cb.SelectedValue >= 410 And cb.SelectedValue <= 411 Then Chk_Dip_IGCSE_N_S = True : Exit Function 'biology
        Return Chk_Dip_IGCSE_N_S
    End Function

    'Check Maths Subjects -Non-Citizen 05092022 - OSH 
    Function Chk_Dip_IGCSE_N_M(ByVal cb As DropDownList)
        Chk_Dip_IGCSE_N_M = False
        If cb.SelectedValue >= 470 And cb.SelectedValue <= 475 Then Chk_Dip_IGCSE_N_M = True : Exit Function 'math
        Return Chk_Dip_IGCSE_N_M
    End Function

    'Check Primary Subjects 02092022 - OSH 
    Function Chk_Dip_IGCSE_P(ByVal cb As DropDownList)
        Chk_Dip_IGCSE_P = False
        'Comment Original 19042023 -OSH
        'If cb.SelectedValue = 467 Then Chk_Dip_IGCSE_P = True : Exit Function 'malay
        'Add Malay -Foreign Language 19042023 -OSH 
        If cb.SelectedValue >= 467 And cb.SelectedValue <= 468 Then Chk_Dip_IGCSE_P = True : Exit Function 'malay
        Return Chk_Dip_IGCSE_P
    End Function

    'Check Math Subjects 17092022 - OSH 
    Function Chk_Dip_IGCSE_M(ByVal cb As DropDownList)
        Chk_Dip_IGCSE_M = False
        If cb.SelectedValue >= 470 And cb.SelectedValue <= 475 Then Chk_Dip_IGCSE_M = True : Exit Function 'math
        Return Chk_Dip_IGCSE_M
    End Function

    'Check Pure and General Science Subjects 02092022 - OSH 
    Function Chk_Dip_IGCSE_S(ByVal cb As DropDownList)
        Chk_Dip_IGCSE_S = False
        If cb.SelectedValue >= 479 And cb.SelectedValue <= 483 Then Chk_Dip_IGCSE_S = True : Exit Function 'physical
        If cb.SelectedValue >= 489 And cb.SelectedValue <= 491 Then Chk_Dip_IGCSE_S = True : Exit Function 'science
        If cb.SelectedValue >= 414 And cb.SelectedValue <= 415 Then Chk_Dip_IGCSE_S = True : Exit Function 'chemistry
        If cb.SelectedValue >= 410 And cb.SelectedValue <= 411 Then Chk_Dip_IGCSE_S = True : Exit Function 'biology
        Return Chk_Dip_IGCSE_S
    End Function

    'Check English Language Subjects 02092022 - OSH 
    Function Chk_Dip_IGCSE_E(ByVal cb As DropDownList)
        Chk_Dip_IGCSE_E = False
        'Comment Original 14042023 - OSH
        'If cb.SelectedValue >= 429 And cb.SelectedValue <= 435 Then Chk_Dip_IGCSE_E = True : Exit Function 'english non-native
        If cb.SelectedValue >= 429 And cb.SelectedValue <= 439 Then Chk_Dip_IGCSE_E = True : Exit Function 'english non-native extend to secondary language
        Return Chk_Dip_IGCSE_E
    End Function

    'Check English Language Subjects 17032023 - OSH 
    Function Chk_Dip_IGCSE_A(ByVal cb As DropDownList)
        Chk_Dip_IGCSE_A = False
        'Comment Original 19042023 - OSH
        'If cb.SelectedValue = 467 Then Chk_Dip_IGCSE_A = True : Exit Function 'malay
        'Add Malay -Foreign Language 19042023 -OSH 
        If cb.SelectedValue >= 467 And cb.SelectedValue <= 468 Then Chk_Dip_IGCSE_A = True : Exit Function 'malay
        If cb.SelectedValue >= 470 And cb.SelectedValue <= 475 Then Chk_Dip_IGCSE_A = True : Exit Function 'math
        If cb.SelectedValue >= 479 And cb.SelectedValue <= 483 Then Chk_Dip_IGCSE_A = True : Exit Function 'physical
        If cb.SelectedValue >= 489 And cb.SelectedValue <= 491 Then Chk_Dip_IGCSE_A = True : Exit Function 'science
        If cb.SelectedValue >= 414 And cb.SelectedValue <= 415 Then Chk_Dip_IGCSE_A = True : Exit Function 'chemistry
        If cb.SelectedValue >= 410 And cb.SelectedValue <= 411 Then Chk_Dip_IGCSE_A = True : Exit Function 'biology
        'If cb.SelectedValue >= 429 And cb.SelectedValue <= 435 Then Chk_Dip_IGCSE_A = True : Exit Function 'english non-native
        If cb.SelectedValue >= 429 And cb.SelectedValue <= 439 Then Chk_Dip_IGCSE_A = True : Exit Function 'english non-native
        Return Chk_Dip_IGCSE_A
    End Function
    Function Chk_Record()
        Dim A As Integer

        'Comment Original 01122023 - OSH 
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'ADO.NET Connection 01122023 - OSH 
        Dim Cn As New SqlConnection : Dim Cmd As New SqlCommand : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        'Add SOP 2018 rules, exist registion record 21102019 - OSH  
        Cmd.CommandText = "SELECT nokp from jt_penuh where j_daftar = 1 and nokp='" & Tx_NoKP.Text & "' "
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A = A + 1
        End While
        Rdr.Close()
        Cn.Close()
        'ADD REG TYPES CHECK 17072019 - OSH 
        If A > 0 Then
            Chk_Record = False
        Else
            Msg(Me, "Pelatih tidak berdaftar denagan Lembaga Jururawat Malaysia ! Sila semak maklumat kelayakan.")
            Chk_Record = True
        End If
        Return Chk_Record
    End Function

    'Add states switch to country for internations student 20122019 - OSH  
    Public Sub Fn_Negeri(ByVal X As Int16)

        'Comment Original 01122023 - OSH
        'Dim Cn5 As New OleDbConnection : Dim Cmd5 As New OleDbCommand : Dim Rdr5 As OleDbDataReader
        'Cn5.ConnectionString = ServerId_SQL : Cn5.Open() : Cmd5.Connection = Cn5

        'ADO.NET Connection 01122023 - OSH 
        Dim txt_cmd5 As String = String.Empty
        Dim Cn5 As New SqlConnection : Dim Rdr5 As SqlDataReader
        Cn5.ConnectionString = ServerId_SQL : Cn5.Open()

        'WARGANEGARA
        If X = 1 Then
            'Comment Original 25082020 - OSH 
            'Lb_States.Text = "NEGERI" : Lb_States.Enabled = False : Cb_W_Negeri.Enabled = False

            'Fix disable states dropdownlist issue 25082020 - OSH  
            Lb_States.Text = "NEGERI" : Lb_States.Enabled = False : Cb_W_Negeri.Enabled = True
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("")
            'Comment Original 21082020 - OSH 
            'Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri order by dc_negeri"

            'Improve query exclude 'luar negeri' from states populate 21082020 - OSH
            'Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri where dc_negeri <> 'LUAR NEGERA' order by dc_negeri"
            txt_cmd5 = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri where dc_negeri <> 'LUAR NEGERA' order by dc_negeri"

        Else
            Lb_States.Text = "NEGARA" : Lb_States.Enabled = False
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False
            'Cmd5.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
            txt_cmd5 = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Dim Cmd5 As New SqlCommand(txt_cmd5, Cn5)
        Rdr5 = Cmd5.ExecuteReader()
        Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
        'Comment Original 25082020 - OSH 
        'Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False

        While Rdr5.Read
            Cb_TP_Negeri.Items.Add(Rdr5(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr5(1)
            Cb_W_Negeri.Items.Add(Rdr5(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr5(1)
        End While
        Rdr5.Close()
        Cn5.Close()
    End Sub

    Protected Sub Bt1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt1.Click
        If Cb_Sbj1.SelectedIndex < 1 Or Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        Text2.Visible = True
        Cb_Sbj2.Visible = True
        Cb_Kpts2.Visible = True
        Bt2.Visible = True
        Bt1.Visible = False
    End Sub

    Protected Sub Bt2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt2.Click
        If Cb_Sbj2.SelectedIndex < 1 Or Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
        Text3.Visible = True
        Cb_Sbj3.Visible = True
        Cb_Kpts3.Visible = True
        Bt3.Visible = True
        Bt2.Visible = False
    End Sub

    Protected Sub Bt3_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt3.Click
        If Cb_Sbj3.SelectedIndex < 1 Or Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub
        Text4.Visible = True
        Cb_Sbj4.Visible = True
        Cb_Kpts4.Visible = True
        Bt4.Visible = True
        Bt3.Visible = False
    End Sub
    Protected Sub Bt4_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt4.Click
        If Cb_Sbj4.SelectedIndex < 1 Or Cb_Kpts4.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #4!") : Exit Sub
        Text5.Visible = True
        Cb_Sbj5.Visible = True
        Cb_Kpts5.Visible = True
        Bt5.Visible = True
        Bt4.Visible = False
    End Sub
    Protected Sub Bt5_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt5.Click
        If Cb_Sbj5.SelectedIndex < 1 Or Cb_Kpts5.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #5!") : Exit Sub
        Text6.Visible = True
        Cb_Sbj6.Visible = True
        Cb_Kpts6.Visible = True
        Bt6.Visible = True
        Bt5.Visible = False
    End Sub
    Protected Sub Bt6_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt6.Click
        If Cb_Sbj6.SelectedIndex < 1 Or Cb_Kpts6.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #6!") : Exit Sub
        Text7.Visible = True
        Cb_Sbj7.Visible = True
        Cb_Kpts7.Visible = True
        Bt7.Visible = True
        Bt6.Visible = False
    End Sub
    Protected Sub Bt7_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt7.Click
        If Cb_Sbj7.SelectedIndex < 1 Or Cb_Kpts7.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #7!") : Exit Sub
        Text8.Visible = True
        Cb_Sbj8.Visible = True
        Cb_Kpts8.Visible = True
        Bt8.Visible = True
        Bt7.Visible = False
    End Sub
    Protected Sub Bt8_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt8.Click
        If Cb_Sbj8.SelectedIndex < 1 Or Cb_Kpts8.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #8!") : Exit Sub
        Text9.Visible = True
        Cb_Sbj9.Visible = True
        Cb_Kpts9.Visible = True
        Bt8.Visible = False
    End Sub
    Protected Sub Tx_M_Latihan_TextChanged(sender As Object, e As EventArgs) Handles Tx_M_Latihan.TextChanged
        'Comment Original 19042023 - OSH
        'Chk_Date_Policy()

        'Improvement check training date available and disable date picking box 19042023 - OSH 
        If Cb_Kerakyatan.SelectedItem.Text = "" Then
            Msg(Me, "Sila pilih jenis warganegara !")

        ElseIf Cb_Kursus.SelectedValue = "0" Then
            Msg(Me, "Sila pilih jenis Kursus !")

        ElseIf Not Tx_M_Latihan.Text = "" Then
            Chk_Date_Policy()
        End If
    End Sub
        Protected Sub Cb_Kelayakan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan.SelectedIndexChanged
        If Cb_Kelayakan.SelectedItem.Text = "IGCSE" Then
            'Panel2.Visible = True : Isi_Subjek(1)
            'Chk_Subjek()
        End If
    End Sub
    Public Sub Fn_Inggeris(ByVal X As Int16)
        'Comment Original 01122023 - OSH 
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'ADO.NET Connection 01122023 - OSH 
        Dim txt_Cmd As String = String.Empty
        Dim Cn As New SqlConnection : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        'WARGANEGARA
        If X = 1 Then
            'Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NATIVE = 1" 'Local
            txt_Cmd = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NATIVE = 1" 'Local
        Else
            'Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NNATIVE = 1" 'International
            txt_Cmd = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NNATIVE = 1" 'International
        End If
        Dim Cmd As New SqlCommand(txt_Cmd, Cn)
        Rdr = Cmd.ExecuteReader()
        Cb_Luar.Items.Clear()
        Cb_Luar.Items.Add("(PILIHAN)")
        Cb_Luar.Items.Item(0).Value = "0"
        While Rdr.Read
            Cb_Luar.Items.Add(Rdr(0))
            Cb_Luar.Items.Item(Cb_Luar.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Cb_Kerakyatan_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cb_Kerakyatan.SelectedIndexChanged
        'Populate Mykad, Army and Passport 18122023 - OSH 
        'Cb_NoKP.Items.Clear()
        If Cb_Kerakyatan.SelectedValue = "0" Then 'Citizen
            Cb_NoKP.Items.Add("MYKAD") : Cb_NoKP.Items.Item(0).Value = 0
            Cb_NoKP.Items.Add("TENTERA") : Cb_NoKP.Items.Item(1).Value = 1
        ElseIf Cb_Kerakyatan.SelectedValue = "1" Then 'Non-Citizen
            Cb_NoKP.Items.Add("PASSPORT") : Cb_NoKP.Items.Item(0).Value = 2 : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "PASSPORT" : Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False
        End If

        'Add Reset for SPTM / A-Level 07022023 - OSH 
        Cb_Aliran.SelectedValue = "0" : Clr_SPTM_A_Level()
        If Cb_Kursus.SelectedValue > "0" Then
            If Not Tx_M_Latihan.Text = "" Then
                Chk_Date_Policy()
            End If
        End If

        'Add Load MYKAD or passport 18122023 - OSH 
        If Cb_Kerakyatan.SelectedValue = "0" Or Cb_Kerakyatan.SelectedValue = "1" Then
            Mykad_Passport()
        End If
    End Sub

    Protected Sub Cb_Aliran_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cb_Aliran.SelectedIndexChanged
        'If Cb_Aliran.SelectedValue = 1 Then
        '    Isi_Subjek(3) : Isi_Subjek(1) 'SPTM
        'ElseIf Cb_Aliran.SelectedValue = 2 Then
        '    Isi_Subjek(4) : Isi_Subjek(1) 'A-LEVEL
        'End If
        If Cb_Aliran.SelectedValue = 1 Then
            Isi_Subjek(3)  'SPTM
        ElseIf Cb_Aliran.SelectedValue = 2 Then
            Isi_Subjek(4)  'A-LEVEL
        End If
    End Sub

    Function Chk_Subjek() As Boolean
        If Cb_Kelayakan.Items.Count = 0 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        If Cb_Kursus.SelectedIndex = 8 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        'Chk_Subjek = True
        'Assign Default Value for Semak Variable 08092022 -OSH 
        Dim PL, ML, CL, CLP, LP, CP, L As Int16, semak As String = "G", Total As Double = 0, Tot As Double = 0, FC As Boolean
        PL = 0 : ML = 0 : CL = 0 : CLP = 0 : LP = 0 : CP = 0 : L = 0 : FC = False

        Dim Y As String = String.Empty
        'Comment Original 08092022 -OSH 
        'Dim PL, CL, CLP, LP, CP As Int16, semak As String = String.Empty, Total As Double = 0, Tot As Double = 0
        'Dim SC As Boolean


        'STANDARD CRITERIA ADDMISSION POLICY JULY 2018 - IGCSE -19082022 - OSH
        If Session("POLISI") = "OMEGA" Then

            'SC = False 'Flag language check
            'Diploma
            If Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "1" Then ' Warganegara
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj1) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj1) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj1) = True Then CL = CL + 1 ' Malay, Math and Science 
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj1) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts1.SelectedIndex > 4 And Cb_Kpts1.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj1) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj1) Then CP = CP + 1 'Other Credits

                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj2) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj2) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj2) = True Then CL = CL + 1  ' Malay, Math and Science 
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj2) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts2.SelectedIndex > 4 And Cb_Kpts2.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj2) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj2) Then CP = CP + 1 'Other Credits

                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj3) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj3) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj3) = True Then CL = CL + 1  ' Malay, Math and Science 
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj3) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts3.SelectedIndex > 4 And Cb_Kpts3.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj3) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj3) Then CP = CP + 1 'Other Credits

                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj4) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj4) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj4) = True Then CL = CL + 1  ' Malay, Math and Science 
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj4) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts4.SelectedIndex > 4 And Cb_Kpts4.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj4) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj4) Then CP = CP + 1 'Other Credits

                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj5) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj5) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj5) = True Then CL = CL + 1  ' Malay, Math and Science 
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj5) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts5.SelectedIndex > 4 And Cb_Kpts5.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj5) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj5) Then CP = CP + 1 'Other Credits

                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj6) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj6) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj6) = True Then CL = CL + 1 ' Malay, Math and Science 
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj6) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts6.SelectedIndex > 4 And Cb_Kpts6.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj6) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj6) Then CP = CP + 1 'Other Credits

                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj7) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj7) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj7) = True Then CL = CL + 1 ' Malay, Math and Science 
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj7) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts7.SelectedIndex > 4 And Cb_Kpts7.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj7) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj7) Then CP = CP + 1 'Other Credits

                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj8) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj8) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj8) = True Then CL = CL + 1  ' Malay, Math and Science 
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj8) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts8.SelectedIndex > 4 And Cb_Kpts8.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj8) = True Then LP = LP + 1 'English Pass

                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 5 Then If Chk_Dip_IGCSE_P(Cb_Sbj9) = True Then PL = PL + 1 Else If Chk_Dip_IGCSE_M(Cb_Sbj9) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj9) = True Then CL = CL + 1 ' Malay, Math and Science 
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj9) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts9.SelectedIndex > 4 And Cb_Kpts9.SelectedIndex < 9 Then If Chk_Dip_IGCSE_E(Cb_Sbj9) = True Then LP = LP + 1 'English Pass
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 5 Then If Not Chk_Dip_IGCSE_A(Cb_Sbj9) Then CP = CP + 1 'Other Credits

            ElseIf Cb_Kerakyatan.SelectedValue = "1" And Cb_Kursus.SelectedValue = "1" Then 'Bukan Warganegara
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj1) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj1) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj2) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj2) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj3) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj3) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj4) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj4) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj5) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj5) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj6) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj6) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj7) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj7) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj8) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj7) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj9) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj7) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science

                'Degree
            ElseIf Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "5" Then ' Warganegara
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj1) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj1) = True Then CL = CL + 1 Else CP = CP + 1 ' Math and Science 
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj1) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj2) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj2) = True Then CL = CL + 1 Else CP = CP + 1 ' Math and Science 
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj2) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj3) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj3) = True Then CL = CL + 1 Else CP = CP + 1 ' Math and Science 
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj3) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj4) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj4) = True Then CL = CL + 1 Else CP = CP + 1 ' Math and Science 
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj4) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj5) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj5) = True Then CL = CL + 1 Else CP = CP + 1 ' Math and Science 
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj5) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj6) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj6) = True Then CL = CL + 1 Else CP = CP + 1 ' Math and Science 
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj6) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj7) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj7) = True Then CL = CL + 1 Else CP = CP + 1 ' Math and Science 
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj7) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj8) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj8) = True Then CL = CL + 1 Else CP = CP + 1 ' Malay, Math and Science 
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj8) = True Then CLP = CLP + 1 'English Credit
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 5 Then If Chk_Dip_IGCSE_M(Cb_Sbj9) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_S(Cb_Sbj9) = True Then CL = CL + 1 Else CP = CP + 1 ' Malay, Math and Science 
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 5 Then If Chk_Dip_IGCSE_E(Cb_Sbj9) = True Then CLP = CLP + 1 'English Credit

            ElseIf Cb_Kerakyatan.SelectedValue = "1" And Cb_Kursus.SelectedValue = "5" Then 'Bukan Warganegara
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj1) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj1) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj2) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj2) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj3) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj3) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj4) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj4) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj5) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj5) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj6) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj6) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj7) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj7) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj8) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj7) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 5 Then If Chk_Dip_IGCSE_N_M(Cb_Sbj9) = True Then ML = ML + 1 Else If Chk_Dip_IGCSE_N_S(Cb_Sbj7) = True Then CL = CL + 1 Else CP = CP + 1 ' Maths and Science
            End If

            If Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "1" Then ' Warganegara
                'Check Credit 5+0 or 5+1 - IGCSE-LEVEL
                If PL < 1 Then Y += "Bahasa Melayu," 'CHECK MALAY - CREDIT
                If ML < 1 Then Y += "Matematik," 'CHECK MATH - CREDIT
                If CL < 1 Then Y += "Sains," 'CHECK SCIENCE  - CREDIT
                If (LP + CLP) < 1 Then Y += "Bahasa Inggeris" 'CHECK ENGLISH - PASS OR CREDIT
                If Y.Trim = "" Then Else Y = Mid(Y, 1, Len(Y) - 2) : Msg(Me, "Kelayakan Pelatih Tidak Melepasi Syarat Minimum Kemasukkan 2018 - Warganegara ! Sila isikan maklumat subjek berikut:  " & Y) : Exit Function

                If (PL + ML + CL + CLP + CP) > 4 Then ' ROUND CHECK
                    semak = "L"
                Else
                    semak = "G"
                    Msg(Me, "kelayakan pelatih tidak melepasi syarat minimum kemasukan 2018 - peringkat diploma ! Sila semak maklumat kelayakan.") : Exit Function
                End If

            ElseIf Cb_Kerakyatan.SelectedValue = "1" And Cb_Kursus.SelectedValue = "1" Then 'Bukan Warganegara
                If Cb_Luar.SelectedValue = "0" Then
                ElseIf Cb_Luar.SelectedValue = "2" And CDbl(Cb_KptsLuar.Text) > 5.4 Then 'IELTS
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "3" And CInt(Cb_KptsLuar.Text) > 514 Then 'TOEFL PBT 
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "4" And CInt(Cb_KptsLuar.Text) > 214 Then 'TOEFL CBT
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "5" And CInt(Cb_KptsLuar.Text) > 79 Then 'TOEFL iBT 
                    LP = LP + 1
                Else
                    Y += "IELTS / TOEFL"
                End If

                If ML < 1 Then Y += "Matematik," 'CHECK MATH - CREDIT
                If CL < 1 Then Y += "Sains," 'CHECK SCIENCE  - CREDIT
                If (LP + CLP) < 1 Then Y += "Bahasa Inggeris" 'CHECK ENGLISH - PASS OR CREDIT
                If Y.Trim = "" Then Else Y = Mid(Y, 1, Len(Y) - 2) : Msg(Me, "Kelayakan Pelatih Tidak Melepasi Syarat Minimum Kemasukkan 2018 - peringkat diploma ! Sila isikan maklumat subjek berikut:  " & Y) : Exit Function

                If (PL + ML + CL + CLP + CP) > 4 Then ' ROUND CHECK
                    semak = "L"
                Else
                    semak = "G"
                    Msg(Me, "kelayakan pelatih antarabangsa tidak melepasi syarat minimum kemasukan 2018 - - peringkat diploma  ! Sila semak maklumat kelayakan.") : Exit Function
                End If

            ElseIf Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "5" Then ' Warganegara

                'Check Credit 5+0 or 5+1 - IGCSE-LEVEL
                If Cb_Aliran.SelectedValue = "1" Then 'SPTM
                    If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                    If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                    If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                    If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                    If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                    Total = Total / L
                    If Total >= 2.5 Then FC = True

                ElseIf Cb_Aliran.SelectedValue = "2" Then 'A-Level
                    If Cb_KptsT1.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT2.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT3.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT4.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT5.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If L > 2 Then FC = True
                End If

                'Foundation Studies
                If Cb_SbjM.SelectedValue = "0" Then 'NULL 
                ElseIf Cb_SbjM.SelectedValue = "1" And CDbl(Cb_KptsM.Text) > 2.4 Then 'MATRIKULASI 
                    FC = True
                ElseIf Cb_SbjM.SelectedValue = "2" And CDbl(Cb_KptsM.Text) > 2.4 Then 'PROGRAM ASASI
                    FC = True
                ElseIf Cb_SbjM.SelectedValue = "3" And CDbl(Cb_KptsM.Text) > 2.4 Then 'DIPLOMA (BUKAN KEJURURAWATAN) 
                    FC = True
                End If

                'Intertional English 
                If Cb_Luar.SelectedValue = "0" Then
                ElseIf Cb_Luar.SelectedValue = "1" And CDbl(Cb_KptsLuar.Text) > 2 Then 'MUET
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "2" And CDbl(Cb_KptsLuar.Text) > 5.4 Then 'IELTS
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "3" And CInt(Cb_KptsLuar.Text) > 514 Then 'TOEFL PBT 
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "4" And CInt(Cb_KptsLuar.Text) > 214 Then 'TOEFL CBT
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "5" And CInt(Cb_KptsLuar.Text) > 79 Then 'TOEFL iBT 
                    LP = LP + 1
                Else
                    Y += "IELTS / TOEFL"
                End If

                If FC = False Then Y += "STPM / A-Level / Matriculation / Any Diploma," 'CHECK MATH - CREDIT
                If ML < 1 Then Y += "Matematik," 'CHECK MATH - CREDIT
                If CL < 1 Then Y += "Sains," 'CHECK SCIENCE  - CREDIT
                If (LP + CLP) < 1 Then Y += "Bahasa Inggeris / MUET" 'CHECK ENGLISH - PASS OR CREDIT
                If Y.Trim = "" Then Else Y = Mid(Y, 1, Len(Y) - 2) : Msg(Me, "Kelayakan Pelatih Tidak Melepasi Syarat Minimum Kemasukkan 2018 - Warganegara ! Sila isikan maklumat subjek berikut:  " & Y) : Exit Function

                If (PL + ML + CL + CLP + CP + LP) > 2 Then ' ROUND CHECK
                    semak = "L"
                Else
                    semak = "G"
                    Msg(Me, "kelayakan pelatih antarabangsa tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah (pelatih warganegara) ! Sila semak maklumat kelayakan.") : Exit Function
                End If

            ElseIf Cb_Kerakyatan.SelectedValue = "1" And Cb_Kursus.SelectedValue = "5" Then 'Bukan Warganegara

                If Cb_Aliran.SelectedValue = "1" Then 'SPTM
                    If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                    If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                    If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                    If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                    If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                    Total = Total / L
                    If Total >= 2.5 Then FC = True

                ElseIf Cb_Aliran.SelectedValue = "2" Then 'A-Level
                    If Cb_KptsT1.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT2.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT3.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT4.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If Cb_KptsT5.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 5 Then L = L + 1
                    If L > 2 Then FC = True
                End If

                'Foundation Studies
                If Cb_SbjM.SelectedValue = "0" Then
                ElseIf Cb_SbjM.SelectedValue = "2" And CDbl(Cb_KptsM.Text) > 2.4 Then 'PROGRAM ASASI 
                    FC = True
                ElseIf Cb_SbjM.SelectedValue = "3" And CDbl(Cb_KptsM.Text) > 2.4 Then 'DIPLOMA (BUKAN KEJURURAWATAN) 
                    FC = True
                End If

                'Check international English inputs 10092024 - OSH 
                If Cb_Luar.SelectedValue < 2 Or Cb_KptsLuar.Text = "" Then
                    Y += "IELTS / TOEFL, "
                ElseIf Cb_Luar.SelectedValue = "2" And CDbl(Cb_KptsLuar.Text) > 5.4 Then 'IELTS
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "3" And CInt(Cb_KptsLuar.Text) > 514 Then 'TOEFL PBT 
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "4" And CInt(Cb_KptsLuar.Text) > 214 Then 'TOEFL CBT
                    LP = LP + 1
                ElseIf Cb_Luar.SelectedValue = "5" And CInt(Cb_KptsLuar.Text) > 79 Then 'TOEFL iBT
                    LP = LP + 1
                End If

                If FC = False Then Y += "STPM / A-Level / Matriculation / Any Diploma," 'CHECK MATH - CREDIT
                If ML <1 Then Y +="Matematik," 'CHECK MATH - CREDIT
                If CL <1 Then Y +="Sains," 'CHECK SCIENCE  - CREDIT
                If (LP + CLP) <1 Then Y +="Bahasa Inggeris" 'CHECK ENGLISH - PASS OR CREDIT
                If Y.Trim = "" Then Else Y = Mid(Y, 1, Len(Y) - 2) : Msg(Me, "Kelayakan Pelatih Tidak Melepasi Syarat Minimum Kemasukkan 2018 ! Sila isikan maklumat subjek berikut:  " & Y) : Exit Function

                If (PL + ML + CL + CLP + CP + LP) > 4 Then ' ROUND CHECK
                    semak="L"
                                  Else
                    semak="G"
                    Msg(Me, "kelayakan pelatih antarabangsa tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah (pelatih antabangsa) ! Sila semak maklumat kelayakan.") : Exit Function
                End If
            End If
        End If

        If semak = "L" Then
            Chk_Subjek = True
        End If
        Return Chk_Subjek
    End Function
    'CHECK DATE POLICY 14092022 - OSH 
    Public Sub Chk_Date_Policy()

        'ADO.NET Connection 01122023 - OSH 
        Dim Cn As New SqlConnection : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open()

        Dim Tkh_Pengajian As Date = DateTime.ParseExact(Tx_M_Latihan.Text, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        Dim Tkh_SOP2018 As Date = CDate("2018-07-01")
        Dim Valid As Integer

        Dim today As DateTime
        Dim limit As DateTime

        Dim Cmd As New SqlCommand("select getdate();", Cn)
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            today = Rdr(0)
        End While
        Rdr.Close()
        Cn.Close()

        'disable date limit 04122023 - OSH 
        'limit = today.AddDays(-30)
        limit = today.AddDays(-3000)
        Valid = Date.Compare(Tkh_Pengajian, Tkh_SOP2018)

        If Valid > 0 Then
            Cb_Sesi_Bulan.SelectedValue = Tkh_Pengajian.Month
            Cb_Sesi_Tahun.SelectedValue = Tkh_Pengajian.Year

            ' Reable 29 May 2023 12042023 - OSH 
            If CInt(Date.Compare(Tkh_Pengajian, today)) > 0 Then
                Session("POLISI") = "OMEGA" : Load_Panel()
            ElseIf CInt(Date.Compare(Tkh_Pengajian, limit)) > 0 And CInt(Date.Compare(Tkh_Pengajian, today)) < 0 Then
                Session("POLISI") = "OMEGA" : Load_Panel()
            Else

                Msg(Me, "Melebihi  Had Tempoh 30 Hari Wajib Daftar") : Tx_M_Latihan.Enabled = True : Panel4.Visible = False : PanelSemak.Visible = False : Exit Sub

            End If
        Else
            'Load hiden panels by default 07092022 - OSH
            Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : Panel7.Visible = False : PanelSemak.Visible = False
            Msg(Me, "Sila Pilih Tarikh Yang Betul")
        End If
    End Sub

    Protected Sub ChkSah_CheckedChanged(sender As Object, e As EventArgs) Handles ChkSah.CheckedChanged
        'Enable Submit Button When Declaration Is Confirm  20042023 - OSH
        If ChkSah.Checked = True Then
            cmdHantar.Enabled = True : Panel3.Enabled = False
        ElseIf ChkSah.Checked = False Then
            cmdHantar.Enabled = False : Panel3.Enabled = True
        End If
    End Sub
    'Clear SPTM or A-Level Panel 07022023 - OSH 
    Public Sub Clr_SPTM_A_Level()
        Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
        Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
    End Sub

    Public Sub Mykad_Passport()
        With Cb_NoKP
            'Add Army textbox Control 10082013-OSH
            If .SelectedValue = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

            'States or Country 08012020 - OSH 
            If .SelectedValue = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(0)

            'States or Country 08012020 - OSH 
            If .SelectedValue = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0)
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
        If Cb_NoKP.SelectedValue = 0 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
    End Sub
End Class
