using Microsoft.AspNetCore.Mvc;
using SPMJ.EmailService.Models;
using SPMJ.EmailService.Services;

namespace SPMJ.EmailService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PasswordController : ControllerBase
{
    private readonly IPasswordService _passwordService;
    private readonly ILogger<PasswordController> _logger;

    public PasswordController(IPasswordService passwordService, ILogger<PasswordController> logger)
    {
        _passwordService = passwordService;
        _logger = logger;
    }

    /// <summary>
    /// Request password reset for end user
    /// </summary>
    [HttpPost("reset/request")]
    public async Task<ActionResult<PasswordResetResponse>> RequestPasswordReset([FromBody] PasswordResetRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.UserId))
            {
                return BadRequest(new PasswordResetResponse
                {
                    Success = false,
                    Message = "ID Pengguna diperlukan"
                });
            }

            var result = await _passwordService.RequestPasswordResetAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RequestPasswordReset");
            return StatusCode(500, new PasswordResetResponse
            {
                Success = false,
                Message = "Ralat sistem dalaman"
            });
        }
    }

    /// <summary>
    /// Validate password reset token
    /// </summary>
    [HttpGet("reset/validate/{token}")]
    public async Task<ActionResult<ApiResponse<string>>> ValidateResetToken(string token)
    {
        try
        {
            if (string.IsNullOrEmpty(token))
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Token diperlukan"
                });
            }

            var result = await _passwordService.ValidateResetTokenAsync(token);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ValidateResetToken");
            return StatusCode(500, new ApiResponse<string>
            {
                Success = false,
                Message = "Ralat sistem dalaman"
            });
        }
    }

    /// <summary>
    /// Complete password reset with new password
    /// </summary>
    [HttpPost("reset/complete")]
    public async Task<ActionResult<ApiResponse<bool>>> CompletePasswordReset([FromBody] CompletePasswordResetRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Token) || string.IsNullOrEmpty(request.NewPassword))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Token dan kata laluan baru diperlukan"
                });
            }

            if (request.NewPassword.Length < 6)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Kata laluan mestilah sekurang-kurangnya 6 aksara"
                });
            }

            var result = await _passwordService.CompletePasswordResetAsync(request.Token, request.NewPassword);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CompletePasswordReset");
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "Ralat sistem dalaman"
            });
        }
    }

    /// <summary>
    /// Admin create new password for user
    /// </summary>
    [HttpPost("admin/create")]
    public async Task<ActionResult<ApiResponse<string>>> CreateAdminPassword([FromBody] AdminPasswordCreateRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.UserId) || string.IsNullOrEmpty(request.AdminId))
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "ID Pengguna dan ID Admin diperlukan"
                });
            }

            if (string.IsNullOrEmpty(request.TempPassword) || request.TempPassword.Length < 6)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Kata laluan sementara mestilah sekurang-kurangnya 6 aksara"
                });
            }

            var result = await _passwordService.CreateAdminPasswordAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateAdminPassword");
            return StatusCode(500, new ApiResponse<string>
            {
                Success = false,
                Message = "Ralat sistem dalaman"
            });
        }
    }

    /// <summary>
    /// Admin reset password for user
    /// </summary>
    [HttpPost("admin/reset")]
    public async Task<ActionResult<ApiResponse<string>>> ResetAdminPassword([FromBody] AdminPasswordResetRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.UserId) || string.IsNullOrEmpty(request.AdminId))
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "ID Pengguna dan ID Admin diperlukan"
                });
            }

            var result = await _passwordService.ResetAdminPasswordAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ResetAdminPassword");
            return StatusCode(500, new ApiResponse<string>
            {
                Success = false,
                Message = "Ralat sistem dalaman"
            });
        }
    }

    /// <summary>
    /// Send password reset email with temporary password (for self-service password recovery)
    /// </summary>
    [HttpPost("reset/send")]
    public async Task<ActionResult<PasswordResetResponse>> SendPasswordResetEmail([FromBody] SendPasswordResetEmailRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.UserId) || string.IsNullOrEmpty(request.Email) ||
                string.IsNullOrEmpty(request.TempPassword))
            {
                return BadRequest(new PasswordResetResponse
                {
                    Success = false,
                    Message = "ID Pengguna, email, dan kata laluan sementara diperlukan"
                });
            }

            var result = await _passwordService.SendPasswordResetEmailAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SendPasswordResetEmail");
            return StatusCode(500, new PasswordResetResponse
            {
                Success = false,
                Message = "Ralat sistem dalaman"
            });
        }
    }
}

public class CompletePasswordResetRequest
{
    public string Token { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}
