﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
'Imports CrystalDecisions.Shared

Partial Public Class WebForm66
    Inherits System.Web.UI.Page


    Public Sub Cari(ByVal X As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(X, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        Tx_Tkh.Text = Format(Now, "01/01/yyyy")
        Tx_Tkh2.Text = Format(Now, "31/12/yyyy")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select Nama, id_pg as 'ID PENGGUNA' from pn_pengguna where id_pg <> 'fz' and modul like '__1%' order by nama"
        Rdr = Cmd.ExecuteReader()
        Cb_Pengguna.Items.Clear()
        Cb_Pengguna.Items.Add("")
        While Rdr.Read
            Cb_Pengguna.Items.Add(Rdr(0))
            Cb_Pengguna.Items.Item(Cb_Pengguna.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'cmd_Cari1.Attributes.Add("onclick", "window.open('P3_Lpr_APC.aspx','','height=100,width=200');return false")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim SQL As String = ""
        Dim Id_Pg As String = ""
        If Cb_Pengguna.SelectedIndex = 0 Then
        Else
            Id_Pg = " and jpa.log_id = '" & Cb_Pengguna.SelectedValue & "' "
        End If

        If Cb_Tahun.SelectedIndex = 0 Then
            SQL = "select 1 '#','Jururawat Berdaftar (JB)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 1 and apc_tahun = " & Year(Now) & " and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & Id_Pg & " union "
            SQL += "select 2,'Jururawat Masyarakat (JM)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 2 and apc_tahun = " & Year(Now) & " and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & Id_Pg & " union "
            SQL += "select 3, 'Penolong Jururawat (PJ)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 3 and apc_tahun = " & Year(Now) & " and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & Id_Pg
        Else
            SQL = "select 1 '#','Jururawat Berdaftar (JB)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 1 and apc_tahun = " & Year(Now) + 1 & " and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & Id_Pg & " union "
            SQL += "select 2,'Jururawat Masyarakat (JM)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 2 and apc_tahun = " & Year(Now) + 1 & " and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & Id_Pg & " union "
            SQL += "select 3, 'Penolong Jururawat (PJ)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 3 and apc_tahun = " & Year(Now) + 1 & " and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & Id_Pg
        End If
        Cari(SQL)
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        If Tx_Tkh.Text.Trim = "" Or Not IsDate(Tx_Tkh.Text) Then Exit Sub
        If Tx_Tkh2.Text.Trim = "" Or Not IsDate(Tx_Tkh2.Text) Then Exit Sub
        Session("Lpr_Nama") = "Stat_APC"
        Session("Var_1") = Cb_Tahun.Text.ToUpper
        Session("Var_2") = Tx_Tkh.Text
        Session("Var_3") = Tx_Tkh2.Text
        If Cb_Tahun.SelectedIndex = 0 Then Session("Var_6") = Year(Now) Else Session("Var_6") = Year(Now) + 1
        If Cb_Pengguna.Text = "" Then Session("Var_4") = "SEMUA" Else Session("Var_4") = Cb_Pengguna.SelectedItem.Text
        If Cb_Pengguna.Text = "" Then Session("Var_5") = "" Else Session("Var_5") = " and jpa.log_id = '" & Cb_Pengguna.Text & "' "

        Response.Write("<script language='javascript'>win=window.open('P6_Laporan.aspx',null,'width=800,height=600,top='+ (screen.height-600)/3 +',left='+ (screen.width-800)/2 +'','true');</script>")
    End Sub
End Class