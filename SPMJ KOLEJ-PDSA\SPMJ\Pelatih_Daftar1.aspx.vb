﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class Pelatih_Daftar1
    Inherits System.Web.UI.Page
    Dim CLP, LP As Int16


    Public Sub Isi_Subjek(ByVal X As Int16)
        Cb_Muet.Visible = True : Cb_Muetx.Visible = False
        Cb_Luar.Visible = True : Cb_Luar.SelectedIndex = 0 : Cb_Luarx.Visible = False
        Cb_SbjM.Visible = True : Cb_SbjM.SelectedIndex = 0 : Cb_SbjMx.Visible = False

        Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
        Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
        Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
        Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
        Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
        Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

        Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
        Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
        Cb_SbjT1.Visible = True : Cb_SbjT2.Visible = True : Cb_SbjT3.Visible = True : Cb_SbjT4.Visible = True : Cb_SbjT5.Visible = True
        Cb_SbjT1x.Visible = False : Cb_SbjT2x.Visible = False : Cb_SbjT3x.Visible = False : Cb_SbjT4x.Visible = False : Cb_SbjT5x.Visible = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'PMR' order by dc_subjek"
        If X = 2 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
        If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
        'Add Load IGCSE 24082021 - OSH 
        'If X = 4 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'IGCSE' order by dc_subjek"
        If X = 0 Then Exit Sub
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Cb_Kursus.SelectedIndex <> 4 Then

                If Rdr(0) = "MUET" Then
                Else
                    Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
                End If
            Else
                Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
            End If
        End While
        Rdr.Close()
        Cn.Close()

        If X = 1 Then
            Gred_PMR(Cb_Kpts1)
            Gred_PMR(Cb_Kpts2)
            Gred_PMR(Cb_Kpts3)
            Gred_PMR(Cb_Kpts4)
            Gred_PMR(Cb_Kpts5)
            Gred_PMR(Cb_Kpts6)
            Gred_PMR(Cb_Kpts7)
            Gred_PMR(Cb_Kpts8)
            Gred_PMR(Cb_Kpts9)

        ElseIf X = 2 Then
            Gred_SPM(Cb_Kpts1)
            Gred_SPM(Cb_Kpts2)
            Gred_SPM(Cb_Kpts3)
            Gred_SPM(Cb_Kpts4)
            Gred_SPM(Cb_Kpts5)
            Gred_SPM(Cb_Kpts6)
            Gred_SPM(Cb_Kpts7)
            Gred_SPM(Cb_Kpts8)
            Gred_SPM(Cb_Kpts9)

        ElseIf X = 3 Then
            Gred_STPM(Cb_KptsT1)
            Gred_STPM(Cb_KptsT2)
            Gred_STPM(Cb_KptsT3)
            Gred_STPM(Cb_KptsT4)
            Gred_STPM(Cb_KptsT5)

            'Add IGCSE grade loaded 24082021 - OSH 
            'ElseIf X = 4 Then
            '    Gred_IGCSE(Cb_Kpts1)
            '    Gred_IGCSE(Cb_Kpts2)
            '    Gred_IGCSE(Cb_Kpts3)
            '    Gred_IGCSE(Cb_Kpts4)
            '    Gred_IGCSE(Cb_Kpts5)
        Else
        End If
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Comment Original 05042022- OSH 
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        'New LANDING pAGE 05042022 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login_j.aspx")
        'If Not Akses_Pg("P1", "Pelatih_Daftar", Session("AKSES"), Session("MODUL")) Then
        '    Session("Msg_Tajuk") = "PEPERIKSAAN"
        '    Session("Msg_Isi") = "Akses Terhad"
        '    Response.Redirect("p0_Mesej.aspx")
        '    Exit Sub
        'End If

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        If Cb_NoKP.SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

        'NEGERI
        'Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        'Rdr = Cmd.ExecuteReader()
        'Cb_TP_Negeri.Items.Clear()
        'Cb_TP_Negeri.Items.Add("")
        'Cb_W_Negeri.Items.Clear()
        'Cb_W_Negeri.Items.Add("")
        'While Rdr.Read
        '    Cb_TP_Negeri.Items.Add(Rdr(0))
        '    Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
        '    Cb_W_Negeri.Items.Add(Rdr(0))
        '    Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()


        'KURSUS
        'Load Active Couses Only 19092019 - OSH 
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS WHERE XM = 1 and id_kursus not in (5) ORDER BY ID_KURSUS "
        'Comment Original 17092019 - OSH 
        'Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'BULAN
        Cb_Sesi_Bulan.Items.Add("(BULAN)") : Cb_Sesi_Bln.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI") : Cb_Sesi_Bln.Items.Add("JANUARI")
        Cb_Sesi_Bulan.Items.Add("FEBRUARI") : Cb_Sesi_Bln.Items.Add("FEBRUARI")
        Cb_Sesi_Bulan.Items.Add("MAC") : Cb_Sesi_Bln.Items.Add("MAC")
        Cb_Sesi_Bulan.Items.Add("APRIL") : Cb_Sesi_Bln.Items.Add("APRIL")
        Cb_Sesi_Bulan.Items.Add("MEI") : Cb_Sesi_Bln.Items.Add("MEI")
        Cb_Sesi_Bulan.Items.Add("JUN") : Cb_Sesi_Bln.Items.Add("JUN")
        Cb_Sesi_Bulan.Items.Add("JULAI") : Cb_Sesi_Bln.Items.Add("JULAI")
        Cb_Sesi_Bulan.Items.Add("OGOS") : Cb_Sesi_Bln.Items.Add("OGOS")
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER") : Cb_Sesi_Bln.Items.Add("SEPTEMBER")
        Cb_Sesi_Bulan.Items.Add("OKTOBER") : Cb_Sesi_Bln.Items.Add("OKTOBER")
        Cb_Sesi_Bulan.Items.Add("NOVEMBER") : Cb_Sesi_Bln.Items.Add("NOVEMBER")
        Cb_Sesi_Bulan.Items.Add("DISEMBER") : Cb_Sesi_Bln.Items.Add("DISEMBER")

        'TAHUN
        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        Cb_Sesi_Thn.Items.Add("(TAHUN)")
        For i = 0 To 5 '8 '5
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i)
            Cb_Sesi_Thn.Items.Add(Year(Now) - i)
        Next

        'BANGSA-07092013-OSH
        Cmd.CommandText = "SELECT DC_RACE,ID_RACE FROM SPMJ_REF_RACE ORDER BY ID_RACE"
        Rdr = Cmd.ExecuteReader()
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("")
        While Rdr.Read
            Cb_Bangsa.Items.Add(Rdr(0))
            Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KOLEJ
        Cb_Kolej.Items.Add(Session("Dc_KOLEJ"))
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Session("Id_KOLEJ")
        Cb_Kolej.SelectedIndex = 0

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'Add control id type loading - 18102013
        With Cb_NoKP
            'Add Army textbox Control 10082013-OSH
            If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

            'Add Army textbox Control 10082013-OSH
            'If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1)

            'States or Country 08012020 - OSH 
            If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(0)
            'Comment Ori 10082013-OSH
            'If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : Fn_Negara(1)
            'Add Army textbox Control 10082013-OSH
            'If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) 
            'States or Country 08012020 - OSH 
            If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0)
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With

        'Populate MUET options 19072019 - OSH 
        Gred_MUET(Cb_KptsMuet)

        'LOAD DISPALYS SETTING 22072019 - OSH
        'Cb_Kelayakan_Taraf.Enabled = True : Cb_Kelayakan_Taraf.Visible = False : Cb_Kelayakan_Taraf.SelectedIndex = 0
        'Panel1.Visible = False : Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : PanelSemak.Visible = True
        'Panel1.Enabled = True : Panel2.Enabled = True : Panel3.Enabled = True : Panel4.Enabled = True : Panel5.Enabled = True : Panel6.Enabled = True : PanelSemak.Enabled = True
        'Bt4.Enabled = False
        'Cb_KptsMuet.SelectedIndex = 0 : Cb_Luar.SelectedIndex = 0 : Cb_KptsLuar.Text = "" : Cb_KptsM.Text = "" : Cb_Aliran.SelectedIndex = 0
        'Cb_Kpts1.Enabled = True : Cb_Kpts2.Enabled = True : Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_NoKP.SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"

        'Hidden Panel Confirmation 08112023 - OSH
        Panel8.Visible = False
    End Sub
    
    Protected Sub Bt4_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt4.Click
        'If Panel5.Visible = True Then
        '    If Cb_Kpts2.Enabled = True And Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
        'Else
        '    If Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        '    If Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
        'End If
        'If Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub

        Dim L As Integer = 0

        If Session("POLISI") = "LAMA" Then
            'Msg(Me, "test")
            If Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
            If Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
            If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
            If L > 2 Then  Else Exit Sub
        End If

        If Session("POLISI") = "BARU" Then
            If Panel5.Visible = True Then
                If Cb_Kpts2.Enabled = True And Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
                If Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub
            Else
                If Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
                If Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
                If Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub
            End If
        End If

        Cb_Sbj5.Visible = True
        Cb_Kpts5.Visible = True
        Bt5.Visible = True
        Bt4.Visible = False
        Textbox8.Visible = True
    End Sub

    Protected Sub Bt5_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt5.Click
        If Cb_Sbj5.SelectedIndex < 1 Or Cb_Kpts5.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #5!") : Exit Sub
        Cb_Sbj6.Visible = True
        Cb_Kpts6.Visible = True
        Bt6.Visible = True
        Bt5.Visible = False
        Textbox9.Visible = True
    End Sub

    Protected Sub Bt6_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt6.Click
        If Cb_Sbj6.SelectedIndex < 1 Or Cb_Kpts6.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #6!") : Exit Sub
        Cb_Sbj7.Visible = True
        Cb_Kpts7.Visible = True
        Bt7.Visible = True
        Bt6.Visible = False
        Textbox10.Visible = True
    End Sub

    Protected Sub Bt7_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt7.Click
        If Cb_Sbj7.SelectedIndex < 1 Or Cb_Kpts7.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #7!") : Exit Sub
        Cb_Sbj8.Visible = True
        Cb_Kpts8.Visible = True
        Bt8.Visible = True
        Bt7.Visible = False
        Textbox11.Visible = True
    End Sub

    Protected Sub Bt8_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt8.Click
        If Cb_Sbj8.SelectedIndex < 1 Or Cb_Kpts8.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #8!") : Exit Sub
        Cb_Sbj9.Visible = True
        Cb_Kpts9.Visible = True
        Bt8.Enabled = False
        Textbox12.Visible = True
    End Sub

    Public Sub Fn_Polisi()
        If Cb_Sesi_Thn.SelectedIndex > 0 Then
            If Cb_Sesi_Bln.SelectedIndex > 0 Then
                'Improve check rule 2018 21102019 - OSH
                If (Cb_Sesi_Bln.SelectedIndex > 0 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2018) Then
                    Session("POLISI") = "GAMMA"
                    'Add check rule 16072019 - OSH
                ElseIf Cb_Sesi_Bln.SelectedIndex > 6 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2017 Then
                    Session("POLISI") = "GAMMA"
                    'Add check rule 04092018 - OSH
                    'ElseIf Cb_Kursus.SelectedValue = 1 And (Cb_Sesi_Bln.SelectedIndex >= 1 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2015) Then
                    '    Session("POLISI") = "NEO"
                    '    'Add check rule Ticket #235899 27112020 - OSH
                    'ElseIf Cb_Kursus.SelectedValue = 3 And (Cb_Sesi_Bln.SelectedIndex >= 1 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2015) Then
                    '    Session("POLISI") = "NEO"
                    '    'Add check rule policy 2015 01102019 - OSH
                    'ElseIf Cb_Kursus.SelectedValue = 8 And (Cb_Sesi_Bln.SelectedIndex >= 1 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2015) Then
                    '    Session("POLISI") = "NEO"
                    '    'MAINTAINACE BATCH - DEGREE =20012022 -OSH 
                    'ElseIf Cb_Kursus.SelectedValue = 5 And (Cb_Sesi_Bln.SelectedIndex >= 1 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2015) Then
                    '    Session("POLISI") = "NEO"
                    'Add check diploma rule 16092013 -OSH
                    'If Cb_Kursus.SelectedIndex = 1 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2010 Then
                    'Comment Ori 16092013 - OSH
                    'If CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2010 Then
                    'Session("POLISI") = "BARU"
                    'ElseIf CInt(Cb_Sesi_Thn.SelectedItem.Text) >= 2010 And CInt(Cb_Sesi_Thn.SelectedItem.Text) < 2015 Then
                    '    'Add check diploma rule 16092013 -OSH
                    '    If Cb_Kursus.SelectedIndex = 1 And Cb_Sesi_Bln.SelectedIndex > 7 Then Session("POLISI") = "BARU" Else Session("POLISI") = "LAMA"
                    '    'Comment Ori 16092013 - OSH
                    '    'If Cb_Sesi_Bln.SelectedIndex > 7 Then Session("POLISI") = "BARU" Else Session("POLISI") = "LAMA"
                    'ElseIf Cb_Kursus.SelectedIndex = 1 And CInt(Cb_Sesi_Thn.SelectedItem.Text) < 2010 Then
                    '    'Comment Ori 16092013 - OSH
                    '    'ElseIf CInt(Cb_Sesi_Thn.SelectedItem.Text) < 2010 Then
                    '    Session("POLISI") = "LAMA"
                    '    'Add 5 credit policy date 17082013 -OSH
                    '    'ElseIf Tx_M_Latihan.Text = "31/07/2013" Then
                    '    '    Session("POLISI") = "BARU" : Panel2.Enabled = True
                    '    'Add policy 2010 01102019 - OSH 
                    'ElseIf Cb_Kursus.SelectedValue = 8 And CInt(Cb_Sesi_Thn.SelectedItem.Text) < 2010 Then
                    '    Session("POLISI") = "LAMA"
                End If
                'Add Fixed for AN SPM 150912013 - OSH
                'If Cb_Kursus.SelectedIndex = 2 Or Cb_Kursus.SelectedIndex = 3 Or Cb_Kursus.SelectedIndex = 4 Or Cb_Kursus.SelectedIndex > 5 Then Panel1.Visible = True : PanelSemak.Visible = True : Cb_Sesi_Bln.Enabled = False : Cb_Sesi_Thn.Enabled = False

                'Fix for AN SPM 20022020 - OSH 
                'If Cb_Kursus.SelectedIndex = 2 Or Cb_Kursus.SelectedIndex = 3 Then Panel1.Visible = True : PanelSemak.Visible = True : Cb_Sesi_Bln.Enabled = False : Cb_Sesi_Thn.Enabled = False

                'Comment Ori 15092013 -OSH
                If Cb_Kursus.SelectedIndex = 4 Or Cb_Kursus.SelectedIndex > 5 Then Panel1.Visible = True : PanelSemak.Visible = True : Cb_Sesi_Bln.Enabled = False : Cb_Sesi_Thn.Enabled = False
            Else
                Exit Sub
            End If
            Else
                Exit Sub
            End If
    End Sub

    Protected Sub Cb_Sesi_Bln_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Bln.SelectedIndexChanged
        Cb_Sesi_Bulan.SelectedIndex = Cb_Sesi_Bln.SelectedIndex
        Fn_Polisi()
    End Sub

    Protected Sub Cb_Sesi_Thn_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Thn.SelectedIndexChanged
        Cb_Sesi_Tahun.SelectedIndex = Cb_Sesi_Thn.SelectedIndex
        Fn_Polisi()
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Cb_Kelayakan.Enabled = True : Cb_Kelayakan.Items.Clear()
        Cb_Kelayakan_Taraf.Enabled = True : Cb_Kelayakan_Taraf.Visible = False : Cb_Kelayakan_Taraf.SelectedIndex = 0
        Cb_Sesi_Bln.Enabled = True : Cb_Sesi_Bln.SelectedIndex = 0
        Cb_Sesi_Thn.Enabled = True : Cb_Sesi_Thn.SelectedIndex = 0
        Panel1.Visible = False : Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : PanelSemak.Visible = False:
        Panel1.Enabled = True : Panel2.Enabled = True : Panel3.Enabled = True : Panel4.Enabled = True : Panel5.Enabled = True : Panel6.Enabled = True : PanelSemak.Enabled = True
        Bt4.Enabled = False
        Cb_KptsMuet.SelectedIndex = 0 : Cb_Luar.SelectedIndex = 0 : Cb_KptsLuar.Text = "" : Cb_KptsM.Text = "" : Cb_Aliran.SelectedIndex = 0
        Cb_Kpts1.Enabled = True : Cb_Kpts2.Enabled = True : Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        'fix value option 01102019 - OSH  
        If Cb_Kursus.SelectedValue = 1 Then 'Diploma
            'Comment Original 01102019 - OSH
            'If Cb_Kursus.SelectedIndex = 1 Then 'Diploma
            'Add SOP 2018 - DIPLOMA level 17072019 - OSH  
            Cb_Kelayakan.Items.Add("SPM") : Isi_Subjek(2)

            'Add IGCSE  & GRADE 24082021 - OSH
            'Cb_Kelayakan.Items.Add("IGCSE") : Isi_Subjek(4)

            'Comment Original 17072019 - OSH
            'Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Add("STPM") : Isi_Subjek(2)
            'Comment Ori 16082013 - OSH
            Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : PanelSemak.Visible = True
            Bt4.Enabled = True
            ' Enabling 'semak' button  15072015 - OSH
            'cmd_Cari.Visible = True

            'fix value option 01102019 - OSH  
        ElseIf Cb_Kursus.SelectedValue = 2 Then
            'Comment Original 01102019 - OSH
            'ElseIf Cb_Kursus.SelectedIndex = 2 Then

            'Add Policy 2018 - OSH 
            Cb_Kelayakan.Items.Add("SPM") : Isi_Subjek(2)
            'Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = True

            'Comment Original 16072019 - OSH
            'Add Policy 2010 16092013 - OSH
            'Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Enabled = False
            'Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = False

            'Comment Ori 16092013 -OSH
            'Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Add("PMR") : Cb_Kelayakan.Items.Add("SRP") : Isi_Subjek(2)
            'Comment Ori 16082013 - OSH
            'Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : PanelSemak.Visible = True
            Bt4.Enabled = True
            ' Enabling 'semak' button  15072015 - OSH
            'cmd_Cari.Visible = True

            'fix value option 01102019 - OSH  
        ElseIf Cb_Kursus.SelectedValue = 3 Then
            'Comment Original 01102019 - OSH
            'ElseIf Cb_Kursus.SelectedIndex = 3 Then
            'Add AN Cert Policy 2010 15092013-OSH
            'Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Enabled = False
            'Comment Original 20022020 - OSH 
            'Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = False

            'Fix Check Button Ticket #227834 20022020 - OSH 
            Cb_Kelayakan.Items.Add("SPM") : Isi_Subjek(2)
            Panel2.Visible = True : Panel2.Enabled = True : PanelSemak.Visible = True : PanelSemak.Enabled = True
            'Comment Ori 15092013 - OSH
            'Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Add("PMR") : Cb_Kelayakan.Items.Add("SRP") : Isi_Subjek(2)
            'Comment Ori 16082013 - OSH
            'Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : PanelSemak.Visible = True
            Bt4.Enabled = True
            ' Enabling 'semak' button  15072015 - OSH
            'cmd_Cari.Visible = True

            'fix value option 01102019 - OSH  
        ElseIf Cb_Kursus.SelectedValue = 4 Then
            'Comment Original 01102019 - OSH
            'ElseIf Cb_Kursus.SelectedIndex = 4 Then
            'Comment Ori 14072015 - OSH
            Cb_Kelayakan.Enabled = False
            Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = False
            'cmd_Cari.Enabled = False ' Diasble search match profaile 30032022 - OSH 
            'cmd_Cari.Visible = False 'Hidden button 31032022 - OSH 


            'fix value option 01102019 - OSH  
        ElseIf Cb_Kursus.SelectedValue = 5 Then
            'Comment Original 01102019 - OSH
            'ElseIf Cb_Kursus.SelectedIndex = 5 Then
            'Disable Foundation Options request #379868 and #605331 12052016 - OSH 
            'Cb_Kelayakan.Items.Add("STPM") : Cb_Kelayakan.Items.Add("MATRIKULASI/ASASI") : Isi_Subjek(3)

            'Comment Ori 16082013 - OSH
            Cb_Kelayakan_Taraf.Visible = True
            Cb_Kelayakan.Items.Add("STPM") : Isi_Subjek(3)
            Panel3.Visible = True : Panel6.Visible = True : PanelSemak.Visible = True

            'Add SPM rules (SOP 2015- Blue Book) 08042022 - OSH 
            'Cb_Kelayakan.Items.Add("SPM") : Isi_Subjek(2)
            'Panel2.Visible = True : Panel2.Enabled = True : PanelSemak.Visible = True : PanelSemak.Enabled = True

            'fix value option 01102019 - OSH  
        ElseIf Cb_Kursus.SelectedValue = 8 Then
            Cb_Kelayakan.Items.Add("SPM")
            Cb_Kelayakan.Enabled = False
            Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = False
            'Disable 'SEMAK' button for Diploma Covertion Programme 15072015 - OSH
            'cmd_Cari.Visible = False
            Panel1.Visible = True : PanelSemak.Visible = True
        End If

        'Comment Original 01102019 - OSH
        'ElseIf Cb_Kursus.SelectedIndex > 5 Then
        '    If Cb_Kursus.SelectedIndex = 8 Then Cb_Kelayakan.Items.Add("SPM")
        '    Cb_Kelayakan.Enabled = False
        '    Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = False
        '    'Disable 'SEMAK' button for Diploma Covertion Programme 15072015 - OSH
        '    cmd_Cari.Visible = False
        'End If
        'Comment Ori 15072015 - OSH
        'If Cb_Kursus.SelectedIndex = 8 Then cmd_Cari.Visible = True Else cmd_Cari.Visible = False


    End Sub

    Protected Sub Cb_Kelayakan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan.SelectedIndexChanged
        'Fixed Disable Foreign Student-16082013 - OSH
        'Cb_Kelayakan_Taraf.Enabled = False : Cb_Kelayakan_Taraf.Visible = False : Cb_Kelayakan_Taraf.SelectedIndex = 0
        'Comment Ori 16082013 - OSH
        'Comment Ori 22072019 - OSH
        'Cb_Kelayakan_Taraf.Enabled = True : Cb_Kelayakan_Taraf.Visible = False : Cb_Kelayakan_Taraf.SelectedIndex = 0
        'Panel1.Visible = False : Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : PanelSemak.Visible = True
        'Panel1.Enabled = True : Panel2.Enabled = True : Panel3.Enabled = True : Panel4.Enabled = True : Panel5.Enabled = True : Panel6.Enabled = True : PanelSemak.Enabled = True
        'Bt4.Enabled = False
        'Cb_KptsMuet.SelectedIndex = 0 : Cb_Luar.SelectedIndex = 0 : Cb_KptsLuar.Text = "" : Cb_KptsM.Text = "" : Cb_Aliran.SelectedIndex = 0
        'Cb_Kpts1.Enabled = True : Cb_Kpts2.Enabled = True : Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_Kelayakan.SelectedItem.Text = "SRP" Then
            Panel2.Visible = True : Panel2.Enabled = False : Isi_Subjek(1)
            PanelSemak.Enabled = False
            Panel1.Visible = True
        End If

        If Cb_Kelayakan.SelectedItem.Text = "PMR" Then
            Panel2.Visible = True : Isi_Subjek(1)
        End If

        If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
            Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : Isi_Subjek(2)
            Bt4.Enabled = True
        End If

        If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
            Cb_Kelayakan_Taraf.Visible = True
            Panel3.Visible = True : Isi_Subjek(3)
            Panel6.Visible = True
        End If

        ''Add IGCSE Option 24082021 - OSH 
        'If Cb_Kelayakan.SelectedItem.Text = "IGCSE" Then
        '    Cb_Kelayakan_Taraf.Visible = True
        '    Panel2.Visible = True : Isi_Subjek(4)
        '    Bt4.Enabled = True
        'End If

        'Comment Original 24082018 - OSH 
        'If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
        '    Panel4.Visible = True : Isi_Subjek(0)
        '    Panel6.Visible = True
        'End If
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        'Add name and colleges details 26082022 - OSH
        Dim P As String
        Dim list As New List(Of String)
        P = Tx_Nama.Text.Trim.ToString 'student name

        'Check session timeout avoid save to db 14122023 - OSH 
        If String.IsNullOrEmpty(Session("Id_PG")) Then Session.Abandon() : Response.Redirect("Login_j.aspx")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        If Chk_Subjek() = True Then Exit Sub
        If Cb_Kursus.SelectedValue = 8 Then If Chk_Staff() = True Then Exit Sub


            'Fix loophole date 00000/00/00 pass through 23122020 - OSH  
            'If Chk_Tkh(Tx_M_Latihan.Text) = "NULL" Then
            '    Msg(Me, "Sila isikan tarikh mula latihan")
            'End If

            'Add rule check 22102019 - OSH 
            'Midwife 
            If Cb_Kursus.SelectedValue = 4 Then
                'check registration exist 
                If Chk_Record() = True Then
                    Exit Sub
                Else
                    If Chk_Staff_Midwife() = True Then Exit Sub
                End If
            End If


            'Medan Mandatori...
            Dim X As String = ""
            If Tx_Nama.Text.Trim = "" Then X += "Nama, "
            If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
            If Cb_Warga.Text.Trim = "" Or Cb_Warga.Text.Trim = "-" Then X += "Warganegara, "
            'Add Mandatory for brith date & brith place 17102013-OSH 
            If Tx_Tkh_Lahir.Text.Trim = "" Then X += "Tarikh Lahir, "
            If Tx_Tp_Lahir.Text.Trim = "" Then X += "Tempat Lahir , "
            If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
            If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
            If Cb_Agama.Text.Trim = "" Then X += "Agama, "
            If Cb_Kahwin.Text.Trim = "" Then X += "Taraf Perkahwinan, "
            If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
            If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
            If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
            If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
            If Tx_Tel.Text.Trim = "" Then X += "No. Telefon, "
            If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
            If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
            If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
            'Original Comment 22122020 - OSH
            'If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
            'Fix loophole date 00000/00/00 pass through 24122020 - OSH   
            'If Tx_M_Latihan.Text.Trim = "" Or Not IsDate(Tx_M_Latihan.Text.Trim) Or Chk_Tkh(Tx_M_Latihan.Text) = "NULL" Then X += "Tarikh Mula Latihan, "
            'Remove Check Date Format 03092021 - OSH 
            'If Tx_M_Latihan.Text.Trim = "" Or Not IsDate(Tx_M_Latihan.Text.Trim) Then X += "Tarikh Mula Latihan, "
            If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
            If X.Trim = "" Then Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

            'Check No KP length
            X = ""
            With Cb_NoKP
                If .SelectedIndex = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
                If .SelectedIndex = 0 Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"
            End With
            If X.Trim = "" Then Else Msg(Me, "Maklumat No. Kad Pengenalan tidak " & X) : Tx_NoKP.Focus() : Exit Sub

        'Check Term and Condition Agreement 30082022 -OSH  
        'If ChkSah.Checked = False Then
        '    Msg(Me, "Jika Maklumat Pelatih " & P & " Yang Didaftarkan Adalah Benar dan Sahih. Sila Perakukan.. ") : ChkSah.Focus() : Exit Sub

        'End If

        'Recheck 5 credit policy on date 31 july 2013 - 17082013 - OSH
        'If Tx_M_Latihan.Text = "31/07/2010" Then
        '    If Chk_Subjek() = True Msg(Me, "Subjek 5 Kredit SPM Tidak Ditepati.") : Exit Sub
        '    End If

        'Dim dt1, dt2 As DateTime

        'Comment 03012012- query secure by categoery -OSH
        'Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"


        'Add subjects record checker 12112018 - OSH
        'Cmd.CommandText = "select nokp from pelatih_kelayakan where nokp = '" & Tx_NoKP.Text & "'"
        'Rdr = Cmd.ExecuteReader()
        'If Rdr.Read Then
        '    Msg(Me, "Rekod Mata Pelajaran Pelatih Ini Telah Ada!")
        '    Rdr.Close()
        '    Exit Sub
        'End If
        'Rdr.Close()

        'Add 03012012/ 08012013- query secure by categoery -OSH
        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "' AND j_kursus='" & Cb_Kursus.SelectedValue & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        'Update personal old records 01102019
        'Dim h1 As Integer
        Dim SQL_U As String
        SQL_U = ""

        If Cb_Kursus.SelectedValue = "1" Or Cb_Kursus.SelectedValue = "8" Then ' DIPLOMA
            Cmd.CommandText = "select j_kursus from  pelatih where status is null and j_kursus in ('2','3') and nokp  = '" & Tx_NoKP.Text & "'  "
            Rdr = Cmd.ExecuteReader()
            'If Rdr.Read Then
            While Rdr.Read
                SQL_U += "update pelatih set status = 1 where status is null and j_kursus = '" & Rdr(0) & "' and nokp  = '" & Tx_NoKP.Text.Trim & "'"
                SQL_U += "insert into kj_pelatih_kemaskini_log (nokp, lama,  baru, log_id, log_tkh) values ( '" & Tx_NoKP.Text.Trim & "', '" & Rdr(0) & "' ,'" & Cb_Kursus.SelectedValue & "','" & Session("Id_PG") & "',getdate()) "
            End While
            Rdr.Close()
            'End If

        ElseIf Cb_Kursus.SelectedValue = "4" Then 'MIDWIFE 
            Cmd.CommandText = "select j_kursus from  pelatih where status is null and j_kursus in ('1','5') and nokp  = '" & Tx_NoKP.Text & "'  "
            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                SQL_U += "update pelatih set status = 1 where status is null and j_kursus = '" & Rdr(0) & "' and nokp  = '" & Tx_NoKP.Text.Trim & "'"
                SQL_U += "insert into kj_pelatih_kemaskini_log (nokp, lama,  baru, log_id, log_tkh) values ( '" & Tx_NoKP.Text.Trim & "', '" & Rdr(0) & "' ,'" & Cb_Kursus.SelectedValue & "','" & Session("Id_PG") & "',getdate()) "
            End While
            'Add 
            Rdr.Close()
            'ElseIf Cb_Kursus.SelectedValue = "8" Then 'DEGREE
            '    Cmd.CommandText = "select j_kursus from  pelatih where status is null and j_kursus in ('2','3') and nokp  = '" & Tx_NoKP.Text & "'  "
            '    Rdr = Cmd.ExecuteReader()
            '    If Rdr.Read Then
            '        SQL_U = "update pelatih set status = 1 where status is null and j_kursus = '" & Rdr(0) & "' and nokp  = '" & Tx_NoKP.Text.Trim & "'"
            '        SQL_U += "insert into kj_pelatih_kemaskini_log (nokp, lama,  baru, log_id, log_tkh) values ( '" & Tx_NoKP.Text.Trim & "', '" & Rdr(0) & "' ,'" & Cb_Kursus.SelectedValue & "','" & Session("Id_PG") & "',getdate()) "
            '        Rdr.Close()
            'End If
        End If

        'SQL_U += "update pelatih set status = 1 where status is null and j_kursus = '" & h1 & "'  and nokp  = '" & Tx_NoKP.Text & "'"
        If SQL_U <> "" Then
            Cmd.CommandText = SQL_U
            Cmd.ExecuteNonQuery()
        End If

        Dim SQL As String
        'add SQL variable asign nothing 22102013-OSH
        SQL = Nothing


        Try
            'Add datetime covertion 09092021 - OSH 
            Dim y, y1 As DateTime
            Dim z, z1 As String

            'Tarikh Lahir
            z = Tx_Tkh_Lahir.Text.Trim
            If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
                y = DateTime.ParseExact(z, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z = y.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z = "'" & z & "'"
            Else
                z = "NULL"
            End If


            'Tarikh Mula Latihan
            z1 = Tx_M_Latihan.Text.Trim
            If Tx_M_Latihan.Text.Trim <> String.Empty Then
                y1 = DateTime.ParseExact(z1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z1 = y1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z1 = "'" & z1 & "'"
            Else
                z1 = "NULL"
            End If


            'Add Choose type id 11081013-OSH
            'Fixing select value 19082013 -OSH
            'If Cb_NoKP.SelectedValue = "0" Or Cb_NoKP.SelectedValue = "2" Then
            'Comment Ori 19082013- OSH
            If Cb_NoKP.SelectedIndex = "0" Or Cb_NoKP.SelectedIndex = "2" Then
                'Add insert Brith Date & Brith Place query 26032014 -OSH 
                SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                'Comment Ori 26032014- OSH
                'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                SQL += Cb_Kursus.SelectedItem.Value & ","
                If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
                SQL += "" & Cb_Kelayakan_Taraf.SelectedValue & ","
                SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "'," ' Add ARMY Number 26032014 -OSH
                'Fixing select value 19082013 -OSH
                'SQL += Cb_NoKP.SelectedValue & ","
                'Comment Ori 19082013- OSH
                SQL += Cb_NoKP.SelectedIndex & ","
                SQL += Cb_Warga.SelectedItem.Value & ","
                SQL += z & ","   'Fix Date Flip issue 09092021 - OSH 
                'Fix Date Flip issue 07092021 - OSH 
                'If Tx_Tkh_Lahir.Text <> String.Empty And IsDate(Tx_M_Latihan.Text.Trim) Then
                '    dt1 = Tx_Tkh_Lahir.Text : SQL += "'" & dt1.ToString("yyyy-MM-dd") & "',"
                'End If
                'Add Brith Date 16092013 - OSH
                'SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & ","
                'Comment Original 14062022 -OSH 
                'Add Brith Place 16092013 - OSH
                'SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                ' Fix Apostrophe Texts Issue 14062022 OSH 
                SQL += "'" & Apo(Tx_Tp_Lahir.Text.Trim) & "',"
                SQL += Cb_Jantina.SelectedIndex & ","
                SQL += Cb_Bangsa.SelectedIndex & ","
                SQL += Cb_Agama.SelectedIndex & ","
                SQL += Cb_Kahwin.SelectedIndex & ","
                SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Tx_Tel.Text.Trim & "',"
                'Comment Original 04072023 -OSH
                'SQL += "'" & Tx_Emel.Text.Trim & "',"
                'Casting email to lower case 04072023 -OSH
                SQL += "'" & Tx_Emel.Text.Trim.ToLower & "',"
                SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
                SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                SQL += z1 & ","   'Fix Date Flip issue 09092021 - OSH 
                'Fix Date Flip issue 07092021 - OSH 
                'If Tx_M_Latihan.Text.Trim <> String.Empty And IsDate(Tx_M_Latihan.Text.Trim) Then
                '    dt2 = Tx_M_Latihan.Text : SQL += "'" & dt2.ToString("yyyy-MM-dd") & "',"  'SQL += "'" & dt2.ToString("dd'/'MM'/'yyyy") & "',"  
                'End If
                'SQL += "'" & Tx_M_Latihan.Text & "'," 'Comment Original 22122020 - OSH 
                'SQL += Chk_Tkh(Tx_M_Latihan.Text) & "," 'Comment Original 22122020 - OSH 
                'Fix loophole date 00000/00/00 pass through 22122020 - OSH  
                'If Not String.IsNullOrEmpty(Chk_Tkh(Tx_M_Latihan.Text)) Then
                '    SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
                'Else
                '    Msg(Me, "Sila isikan tarikh mula latihan")
                'End If
                SQL += SSemak(0) & ","
                SQL += SSemak(1) & ","
                SQL += SSemak(2) & ","
                SQL += SSemak(3) & ","
                SQL += SSemak(4) & ","
                SQL += SSemak(5) & ","
                SQL += SSemak(6) & ","
                SQL += SSemak(7) & ","
                SQL += "'" & Session("Id_PG") & "',"
                SQL += "getdate()"
                SQL += ")"
                'Add AMRY ID SQL INSERT 11082013-OSH
                'Fixing select value 19082013 -OSH
                'ElseIf Cb_NoKP.SelectedValue = "1" Then
                'Comment Ori 19082013- OSH
            ElseIf Cb_NoKP.SelectedIndex = "1" Then
                'Add insert Brith Date & Brith Place query 16092013 -OSH 
                SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                'Comment Ori 160920213 -OSH
                'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                SQL += Cb_Kursus.SelectedItem.Value & ","
                If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
                SQL += "" & Cb_Kelayakan_Taraf.SelectedValue & ","
                SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "',"
                'Fixing select value 19082013 -OSH
                'SQL += Cb_NoKP.SelectedValue & ","
                'Comment Ori 19082013- OSH
                SQL += Cb_NoKP.SelectedIndex & ","
                SQL += Cb_Warga.SelectedItem.Value & ","
                SQL += y1 & ","   'Fix Date Flip issue 09092021 - OSH 
                'Fix Date Flip issue 07092021 - OSH 
                'If Tx_Tkh_Lahir.Text <> String.Empty Then
                '    dt1 = Tx_Tkh_Lahir.Text : SQL += "'" & dt1.ToString("yyy-MM-dd") & "',"
                'End If
                'Add Brith Date 16092013 - OSH
                'SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & ","
                'Add Brith Place 16092013 - OSH
                SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                SQL += Cb_Jantina.SelectedIndex & ","
                SQL += Cb_Bangsa.SelectedIndex & ","
                SQL += Cb_Agama.SelectedIndex & ","
                SQL += Cb_Kahwin.SelectedIndex & ","
                SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Tx_Tel.Text.Trim & "',"
                SQL += "'" & Tx_Emel.Text.Trim & "',"
                SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
                SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                SQL += z1 & ","   'Fix Date Flip issue 09092021 - OSH 
                'If Tx_M_Latihan.Text <> String.Empty And IsDate(Tx_M_Latihan.Text.Trim) Then
                '    dt2 = Tx_M_Latihan.Text : SQL += "'" & dt2.ToString("yyyy-MM-dd") & "'," 'Fix Date Flip issue 07092021 - OSH 
                'End If
                'SQL += "'" & Tx_M_Latihan.Text & "," 'Remove 03092021 - OSH
                'SQL += Chk_Tkh(Tx_M_Latihan.Text) & "," 'Comment Original 22122020 - OSH 
                'Fix loophole date 00000/00/00 pass through 22122020 - OSH  
                'If Not String.IsNullOrEmpty(Chk_Tkh(Tx_M_Latihan.Text)) Then
                '    SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
                'Else
                '    Msg(Me, "Sila isikan tarikh mula latihan")
                'End If
                SQL += SSemak(0) & ","
                SQL += SSemak(1) & ","
                SQL += SSemak(2) & ","
                SQL += SSemak(3) & ","
                SQL += SSemak(4) & ","
                SQL += SSemak(5) & ","
                SQL += SSemak(6) & ","
                SQL += SSemak(7) & ","
                SQL += "'" & Session("Id_PG") & "',"
                SQL += "getdate()"
                SQL += ")"
            End If
            'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
            'Cmd.CommandText = SQL
            'Cmd.ExecuteNonQuery()

            'If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then
            'Else


            If Panel6.Visible = True Then 'muet
                'Improve audit trail 03102019 - OSH 
                If Cb_KptsMuet.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','15','" & Cb_KptsMuet.SelectedItem.Text & "', getdate())" & vbCrLf
                'Comment Original 03102019 - OSH
                'If Cb_KptsMuet.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','15','" & Cb_KptsMuet.SelectedItem.Text & "')" & vbCrLf
            End If
            If Panel5.Visible = True Then 'BI luar/setaraf
                'Improve audit trail 03102019 - OSH 
                If Cb_Luar.SelectedIndex = 1 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-11','" & Cb_KptsLuar.Text.ToUpper & "', getdate())" & vbCrLf
                If Cb_Luar.SelectedIndex = 2 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-12','" & Cb_KptsLuar.Text.ToUpper & "', getdate())" & vbCrLf
                If Cb_Luar.SelectedIndex = 3 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-13','" & Cb_KptsLuar.Text.ToUpper & "', getdate())" & vbCrLf

                'Comment Original 03102019 - OSH
                'If Cb_Luar.SelectedIndex = 1 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-11','" & Cb_KptsLuar.Text.ToUpper & "')" & vbCrLf
                'If Cb_Luar.SelectedIndex = 2 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-12','" & Cb_KptsLuar.Text.ToUpper & "')" & vbCrLf
                'If Cb_Luar.SelectedIndex = 3 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-13','" & Cb_KptsLuar.Text.ToUpper & "')" & vbCrLf
            End If
            If Panel4.Visible = True Then 'matrik
                'Improve audit trail 03102019 - OSH 
                If Cb_SbjM.SelectedIndex = 1 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-21','" & Cb_KptsM.Text.ToUpper & "', getdate())" & vbCrLf
                If Cb_SbjM.SelectedIndex = 2 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-22','" & Cb_KptsM.Text.ToUpper & "', getdate())" & vbCrLf

                'Comment Original 03102019 - OSH
                'If Cb_SbjM.SelectedIndex = 1 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-21','" & Cb_KptsM.Text.ToUpper & "')" & vbCrLf
                'If Cb_SbjM.SelectedIndex = 2 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-22','" & Cb_KptsM.Text.ToUpper & "')" & vbCrLf
            End If
            If Panel3.Visible = True Then 'stpm
                'Improve audit trail 03102019 - OSH 
                If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "', getdate())" & vbCrLf


                'Comment Original 03102019 - OSH
                'If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "')" & vbCrLf
                'If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "')" & vbCrLf
                'If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "')" & vbCrLf
                'If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "')" & vbCrLf
                'If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "')" & vbCrLf

                If Cb_Aliran.SelectedIndex = 2 Then
                    'Improve audit trail 03102019 - OSH 
                    If Cb_KptsT6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-3','" & Cb_KptsT6.SelectedItem.Text & "', getdate())" & vbCrLf
                    If Cb_KptsT7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-4','" & Cb_KptsT7.SelectedItem.Text & "', getdate())" & vbCrLf

                    'Comment Original 03102019 - OSH
                    'If Cb_KptsT6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-3','" & Cb_KptsT6.SelectedItem.Text & "')" & vbCrLf
                    'If Cb_KptsT7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-4','" & Cb_KptsT7.SelectedItem.Text & "')" & vbCrLf
                End If
            End If
            If Panel2.Visible = True Then 'spm

                'Improve audit trail 03102019 - OSH 
                If Cb_Kpts1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-1','" & Cb_Kpts1.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-2','" & Cb_Kpts2.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-3','" & Cb_Kpts3.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','-4','" & Cb_Kpts4.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj7.SelectedItem.Value & "','" & Cb_Kpts7.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts8.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj8.SelectedItem.Value & "','" & Cb_Kpts8.SelectedItem.Text & "', getdate())" & vbCrLf
                If Cb_Kpts9.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj9.SelectedItem.Value & "','" & Cb_Kpts9.SelectedItem.Text & "', getdate())" & vbCrLf

                'Comment Original 03102019 - OSH
                '    If Cb_Kpts1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-1','" & Cb_Kpts1.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-2','" & Cb_Kpts2.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-3','" & Cb_Kpts3.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-4','" & Cb_Kpts4.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj7.SelectedItem.Value & "','" & Cb_Kpts7.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts8.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj8.SelectedItem.Value & "','" & Cb_Kpts8.SelectedItem.Text & "')" & vbCrLf
                '    If Cb_Kpts9.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj9.SelectedItem.Value & "','" & Cb_Kpts9.SelectedItem.Text & "')" & vbCrLf
            End If

            ' End If

            If SQL = "" Then
            Else
                'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")

                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
            End If

            Cn.Close()
            Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
            Session("Msg_Isi") = "Rekod Telah Dihantar..."
            Response.Redirect("Mesej.aspx")
            'Msg(Me, "Rekod Telah Dihantar...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    'Comment Original 03092021 - OSH 
    'Function Chk_Tkh(ByVal X As String)
    '    If IsDate(X) Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
    '    Return X
    'End Function
    'Fix Date Flip issue 03092021 - OSH 
    'Function Chk_Tkh(ByVal X As String)
    '    If IsDate(X) Then X = Format(CDate(X), "dd/MM/yyyy") : X = "'" & X & "'" Else X = "NULL"
    '    Return X
    'End Function

    Function Chk_Subjek()
        If Cb_Kelayakan.Items.Count = 0 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        If Cb_Kursus.SelectedIndex = 8 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        Chk_Subjek = True
        Dim L, CL, PL As Int16, semak As String = "", Total As Double = 0, Tot As Double = 0
        'Dim SC, EL As Boolean
        Dim SC As Boolean

        If Session("POLISI") = "LAMA" Then
            'Add Check Diploma SPM policy 2010 16092013- OSH
            If Cb_Kursus.SelectedIndex = 1 And Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                'Comment Ori 16092013- OSH
                'If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then L = L + 1
            ElseIf Cb_Kelayakan.SelectedItem.Text = "PMR" Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1
            ElseIf Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT6.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT7.SelectedIndex > 0 Then L = L + 1
                'comment Original 24082018 - OSH
                'ElseIf Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
                '    If Cb_KptsMuet.SelectedIndex > 0 Then L = L + 2
                '    If Cb_SbjM.SelectedIndex = 1 Then If CDbl(Cb_KptsM.Text) >= 2.0 Then L = L + 1
                '    If Cb_SbjM.SelectedIndex = 2 Then If CDbl(Cb_KptsM.Text) >= 2.0 Then L = L + 1
            ElseIf Cb_Kelayakan.SelectedItem.Text = "SRP" Then
                L = 3
            End If

            If Cb_Kursus.SelectedIndex = 4 Then
                L = 2
                If Cb_Sbj5.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj6.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj7.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj8.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj9.SelectedItem.Text = "MUET" Then L = L + 1
            End If

            If L > 2 Then semak = "L" Else semak = "G"
        End If

        If Session("POLISI") = "BARU" Then
            If Cb_Kelayakan.SelectedItem.Text = "SRP" Then semak = "L"
            If Cb_Kelayakan.SelectedItem.Text = "PMR" Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1
                If L > 2 Then semak = "L" Else semak = "G"
            End If
            'Add Check Diploma SPM policy 2010 16092013- OSH
            If Cb_Kursus.SelectedIndex = 1 And Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then 'spm local
                'Comment Ori 16092013- OSH
                'If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then 'spm local
                'Retune 5 Credit Policy 16082013 - OSH
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 9 Then PL = PL + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 9 Then PL = PL + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj5) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj6) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj7) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj8) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj9) = True Then CL = CL + 1 Else L = L + 1
                If CL = 0 Then semak = "G" Else semak = "L"
                If semak = "L" Then Total = PL + CL + L
                If Total > 4 And Cb_Kpts3.SelectedIndex < 8 Then semak = "L" Else semak = "G"

                'Comment Ori 17082013 - OSH
                'If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 10 Then PL = PL + 1
                'If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1
                'If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                'If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1
                'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj5) = True Then CL = CL + 1 Else L = L + 1
                'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj6) = True Then CL = CL + 1 Else L = L + 1
                'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj7) = True Then CL = CL + 1 Else L = L + 1
                'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj8) = True Then CL = CL + 1 Else L = L + 1
                'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj9) = True Then CL = CL + 1 Else L = L + 1
                'If PL = 2 Then semak = "L" Else semak = "G"
                'If semak = "L" Then If CL = 0 Then semak = "G" Else semak = "L"
                'If semak = "L" Then Total = PL + CL + L
                'If Total > 6 Then semak = "L" Else semak = "G"
            End If
            If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then 'spm staraf/luar negara              
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 550 Then PL = PL + 1 ' BI staraf/luar negara
                If Cb_Luar.SelectedIndex = 3 And Cb_KptsLuar.Text <> "" Then If Cb_KptsLuar.Text.ToUpper <> "G" Then PL = PL + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1

                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj5) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj6) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj7) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj8) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj9) = True Then CL = CL + 1 Else L = L + 1
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If CL = 0 Then semak = "G" Else semak = "L"
                If semak = "L" Then Total = PL + CL + L
                If Total > 5 Then semak = "L" Else semak = "G"
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then 'stpm local
                If Cb_KptsMuet.SelectedIndex > 0 Then PL = PL + 1

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L

                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
                If Cb_Aliran.SelectedIndex = 2 Then
                    If Cb_KptsT6.SelectedIndex > 0 And Cb_KptsT6.SelectedIndex < 8 Then CL = CL + 1
                    If Cb_KptsT7.SelectedIndex > 0 And Cb_KptsT7.SelectedIndex < 8 Then CL = CL + 1
                    If semak = "L" Then If CL = 2 Then semak = "L" Else semak = "G"
                End If
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
                If Cb_Luar.SelectedIndex = 0 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 550 Then PL = PL + 1 'BI Setaraf/Luar Negara                

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
            End If
            'Commment Original 24082018 - OSH
            'If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
            '    If Cb_KptsMuet.SelectedIndex > 0 Then L = L + 1
            '    If Cb_SbjM.SelectedIndex = 1 Then If CDbl(Cb_KptsM.Text) >= 2.5 Then L = L + 1
            '    If Cb_SbjM.SelectedIndex = 2 Then If CDbl(Cb_KptsM.Text) >= 3.0 Then L = L + 1
            '    If L > 1 Then semak = "L" Else semak = "G"
            'End If

            'Add Skip Check Subject for CN, AN and Diploma Covention Policy 2010  01102019 -OSH
            If Cb_Kursus.SelectedValue = 2 Or Cb_Kursus.SelectedValue = 3 Or Cb_Kursus.SelectedValue = 8 Then
                'Add Skip Check Subject for CM and AN Policy 2010  16092013 -OSH
                'If Cb_Kursus.SelectedIndex = 2 Or Cb_Kursus.SelectedIndex = 3 Then
                semak = "L"
            End If
            If semak = "L" Then
                Chk_Subjek = False
            Else
                Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
                Chk_Subjek = True
            End If


            Return Chk_Subjek
        End If

        'ADD NEW SOP 2015 RULE 19092018 - OSH
        If Session("POLISI") = "NEO" Then
            'Cert
            If (Cb_Kursus.SelectedValue = "2" Or Cb_Kursus.SelectedValue = "3") And Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then CL = CL + 1 ' malay
                'Adjust polisi 2015 pass range Ticket #235899 27112020 - OSH
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then L = L + 1 'english
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 10 Then L = L + 1 'math 
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 10 Then L = L + 1 'science
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 10 Then If Chk_Sbj_Cert_2015(Cb_Sbj5) = True Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 10 Then If Chk_Sbj_Cert_2015(Cb_Sbj6) = True Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 10 Then If Chk_Sbj_Cert_2015(Cb_Sbj7) = True Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 10 Then If Chk_Sbj_Cert_2015(Cb_Sbj8) = True Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 10 Then If Chk_Sbj_Cert_2015(Cb_Sbj9) = True Then L = L + 1

                'Comment Original 27112020 - OSH 
                'If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 9 Then L = L + 1 'english
                'If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 9 Then L = L + 1 'math 
                'If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 9 Then L = L + 1 'science
                'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 9 Then If Chk_Sbj_Cert_2015(Cb_Sbj5) = True Then L = L + 1
                'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 9 Then If Chk_Sbj_Cert_2015(Cb_Sbj6) = True Then L = L + 1
                'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 9 Then If Chk_Sbj_Cert_2015(Cb_Sbj7) = True Then L = L + 1
                'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 9 Then If Chk_Sbj_Cert_2015(Cb_Sbj8) = True Then L = L + 1
                'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 9 Then If Chk_Sbj_Cert_2015(Cb_Sbj9) = True Then L = L + 1
                If CL = 1 Then
                    If L = 3 Then
                        Total = CL + L
                        If Total = 4 Then
                            semak = "L"
                        Else
                            semak = "G"
                            Msg(Me, "SUBJEK TIADA MENEPATI SYARAT KEMASUKAN 2015 - PERINGKAT SIJIL ") ' Qualify cert level intake  
                        End If
                    Else
                        Msg(Me, "SUBJEK TIADA MENEPATI SYARAT KEMASUKAN 2015 - PERINGKAT SIJIL ") ' min pass of 3 subjects
                    End If
                Else
                    Msg(Me, "SUBJEK TIADA MENEPATI SYARAT KEMASUKAN 2015 - PERINGKAT SIJIL ") 'min credit 
                End If
            End If
            'Diploma with SPM
            If Cb_Kursus.SelectedValue = 1 And Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then

                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then CL = CL + 1 'malay 
                'Improve check english rule policy 2015 03102019 - OSH 
                If Cb_Kpts2.SelectedIndex > 7 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1 'english pass
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then PL = PL + 1 'english credit
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then CL = CL + 1 ' math  
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CLP = CLP + 1 ' science 

                'Check Biology Credit or Pass 02102019 - OSH
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then 'credit
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj5) = True Then Policy_15(1)
                    If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj5) = False Then LP = LP + 1
                ElseIf Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 9 Then 'pass
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj5) = True Then Policy_15(2)
                    If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj5) = False Then LP = LP + 1
                End If
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then 'credit
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj6) = True Then Policy_15(1)
                    If Chk_Sbj_Math_2018(Cb_Sbj6) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj6) = False Then LP = LP + 1
                ElseIf Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 9 Then 'pass
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj6) = True Then Policy_15(2)
                    If Chk_Sbj_Math_2018(Cb_Sbj6) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj6) = False Then LP = LP + 1
                End If
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then 'credit
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj7) = True Then Policy_15(1)
                    If Chk_Sbj_Math_2018(Cb_Sbj7) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj7) = False Then LP = LP + 1
                ElseIf Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 9 Then 'pass
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj7) = True Then Policy_15(2)
                    If Chk_Sbj_Math_2018(Cb_Sbj7) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj7) = False Then LP = LP + 1
                End If
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then 'credit
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj8) = True Then Policy_15(1)
                    If Chk_Sbj_Math_2018(Cb_Sbj8) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj8) = False Then LP = LP + 1
                ElseIf Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 9 Then 'pass
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj8) = True Then Policy_15(2)
                    If Chk_Sbj_Math_2018(Cb_Sbj8) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj8) = False Then LP = LP + 1
                End If
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then 'credit
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj9) = True Then Policy_15(1)
                    If Chk_Sbj_Math_2018(Cb_Sbj9) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj9) = False Then LP = LP + 1
                ElseIf Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 9 Then 'pass
                    If Chk_Sbj_Dip_Pass_2015(Cb_Sbj9) = True Then Policy_15(2)
                    If Chk_Sbj_Math_2018(Cb_Sbj9) = True Then CL = CL + 1
                    If Chk_Sbj_Credit_2018(Cb_Sbj9) = False Then LP = LP + 1
                End If

                ''Biology - Credit 
                'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 6 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj5) = True Then CLP = CLP + 1 Else If Chk_Sbj_Dip_Credit_2015(Cb_Sbj5) = False Then L = L + 1
                'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 6 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj6) = True Then CLP = CLP + 1 Else If Chk_Sbj_Dip_Credit_2015(Cb_Sbj6) = False Then L = L + 1
                'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 6 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj7) = True Then CLP = CLP + 1 Else If Chk_Sbj_Dip_Credit_2015(Cb_Sbj7) = False Then L = L + 1
                'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 6 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj8) = True Then CLP = CLP + 1 Else If Chk_Sbj_Dip_Credit_2015(Cb_Sbj8) = False Then L = L + 1
                'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 6 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj9) = True Then CLP = CLP + 1 Else If Chk_Sbj_Dip_Credit_2015(Cb_Sbj9) = False Then L = L + 1

                ''Biology - Pass
                'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj5) = True Then Policy_15(1)
                'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj6) = True Then Policy_15(1)
                'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj7) = True Then Policy_15(1)
                'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj8) = True Then Policy_15(1)
                'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj_Dip_Pass_2015(Cb_Sbj9) = True Then Policy_15(1)


                'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 6 Then If Chk_Dip_Credit_2015(Cb_Sbj5) = True Then CLP = CLP + 1 Else If Chk_Dip_Credit_2015(Cb_Sbj5) = False Then L = L + 1
                'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 6 Then If Chk_Dip_Credit_2015(Cb_Sbj6) = True Then CLP = CLP + 1 Else If Chk_Dip_Credit_2015(Cb_Sbj6) = False Then L = L + 1
                'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 6 Then If Chk_Dip_Credit_2015(Cb_Sbj7) = True Then CLP = CLP + 1 Else If Chk_Dip_Credit_2015(Cb_Sbj7) = False Then L = L + 1
                'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 6 Then If Chk_Dip_Credit_2015(Cb_Sbj8) = True Then CLP = CLP + 1 Else If Chk_Dip_Credit_2015(Cb_Sbj8) = False Then L = L + 1
                'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 6 Then If Chk_Dip_Credit_2015(Cb_Sbj9) = True Then CLP = CLP + 1 Else If Chk_Dip_Credit_2015(Cb_Sbj9) = False Then L = L + 1

                'Comment Original 27092019 - OSH
                'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 6 Then If Chk_Sbj_Dip_Credit_2015(Cb_Sbj5) = True Then L = L + 1
                'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 6 Then If Chk_Sbj_Dip_Credit_2015(Cb_Sbj6) = True Then L = L + 1
                'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 6 Then If Chk_Sbj_Dip_Credit_2015(Cb_Sbj7) = True Then L = L + 1
                'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 6 Then If Chk_Sbj_Dip_Credit_2015(Cb_Sbj8) = True Then L = L + 1
                'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 6 Then If Chk_Sbj_Dip_Credit_2015(Cb_Sbj9) = True Then L = L + 1

                'Improve Final Check Score 03102019 - OSH 
                If (CL + PL) > 0 Then
                    If PL = 0 Then 'PASS
                        Total = CL + CLP + LP
                    ElseIf PL = 1 Then 'CREDIT
                        Total = CL + PL + CLP + LP
                    End If
                End If
                'If CL = 0 Then semak = "G" Else semak = "L"
                'If semak = "L" Then Total = PL + CL + L
                If Total > 4 And Cb_Kpts3.SelectedIndex < 8 Then semak = "L" Else semak = "G"


            End If
            If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then 'spm staraf/luar negara              
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 550 Then PL = PL + 1 ' BI staraf/luar negara
                If Cb_Luar.SelectedIndex = 3 And Cb_KptsLuar.Text <> "" Then If Cb_KptsLuar.Text.ToUpper <> "G" Then PL = PL + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1

                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj5) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj6) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj7) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj8) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj(Cb_Sbj9) = True Then CL = CL + 1 Else L = L + 1
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If CL = 0 Then semak = "G" Else semak = "L"
                If semak = "L" Then Total = PL + CL + L
                If Total > 5 Then semak = "L" Else semak = "G"
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then 'stpm local
                If Cb_KptsMuet.SelectedIndex > 0 Then PL = PL + 1

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L

                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
                If Cb_Aliran.SelectedIndex = 2 Then
                    If Cb_KptsT6.SelectedIndex > 0 And Cb_KptsT6.SelectedIndex < 8 Then CL = CL + 1
                    If Cb_KptsT7.SelectedIndex > 0 And Cb_KptsT7.SelectedIndex < 8 Then CL = CL + 1
                    If semak = "L" Then If CL = 2 Then semak = "L" Else semak = "G"
                End If
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
                If Cb_Luar.SelectedIndex = 0 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 550 Then PL = PL + 1 'BI Setaraf/Luar Negara                

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
            End If

            'Add Skip Check Subject for Diploma Covention Policy 2015  01102019 -OSH
            If Cb_Kursus.SelectedValue = "8" And Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                semak = "L"
            End If

            'Final Check - Load Details Form 27092019 - OSH
            If semak = "L" Then
                Chk_Subjek = False
            Else
                'Improve Massege 03102019 - OSH 
                Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan kemasukan - 2015.")
                'Comment Original 03102019 - OSH 
                'Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
                Chk_Subjek = True
            End If

            Return Chk_Subjek
        End If

        'STANDARD CRITERIA ADDMISSION POLICY JULY 2018 16072019 - OSH
        If Session("POLISI") = "GAMMA" Then

            'CERTIFICATE LEVEL
            If (Cb_Kursus.SelectedValue = 2 Or Cb_Kursus.SelectedValue = 3) And Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then CL = CL + 1 'Malay
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 10 Then PL = PL + 1 'English

                'MATH/ ADD MATH
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 10 Then PL = PL + 1
                If PL = 1 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 10 Then If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then PL = PL + 1
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 10 Then If Chk_Sbj_Math_2018(Cb_Sbj6) = True Then PL = PL + 1
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 10 Then If Chk_Sbj_Math_2018(Cb_Sbj7) = True Then PL = PL + 1
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 10 Then If Chk_Sbj_Math_2018(Cb_Sbj8) = True Then PL = PL + 1
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 10 Then If Chk_Sbj_Math_2018(Cb_Sbj9) = True Then PL = PL + 1
                End If

                'GENERAL SCIENCE /PURE SCIENCE 
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 10 Then PL = PL + 1
                If PL = 2 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 10 Then If Chk_Sbj_Science_2018(Cb_Sbj5) = True Then PL = PL + 1
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 10 Then If Chk_Sbj_Science_2018(Cb_Sbj6) = True Then PL = PL + 1
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 10 Then If Chk_Sbj_Science_2018(Cb_Sbj7) = True Then PL = PL + 1
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 10 Then If Chk_Sbj_Science_2018(Cb_Sbj8) = True Then PL = PL + 1
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 10 Then If Chk_Sbj_Science_2018(Cb_Sbj9) = True Then PL = PL + 1
                End If

                If CL = 1 Then
                    If PL > 2 Then
                        If (CL + PL) > 3 Then 'Fix miniumun subjects addmission score 10092020 - OSH 
                            'If (CL + PL) = 4 Then 'Comment Original 10092020 - OSH 
                            semak = "L"
                        Else
                            Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum kemasukan 2018 - peringkat sijil! Sila semak maklumat kelayakan.") : semak = "G"
                        End If
                    Else
                        Msg(Me, "subjek bahasa inggeris, matematik atau sains tidak melepasi syarat minimum kemasukan 2018 - peringkat sijil! Sila semak maklumat kelayakan.")
                    End If
                Else
                    Msg(Me, "subjek bahasa melayu tidak melepasi syarat minimum kemasukan 2018 - peringkat sijil! Sila semak maklumat kelayakan.")
                End If
            End If ' end of cert 

            'Add Check Diploma SPM policy 2018 16072019- OSH
            If Cb_Kursus.SelectedIndex = 1 And Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then 'spm local

                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then CL = CL + 1 ' malay
                'Fix minimum grade level 24102019 - OSH 
                If Cb_Kpts2.SelectedIndex > 7 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1 : SC = False 'english - pass

                'Comment Original 24102019 - OSH 
                'If Cb_Kpts2.SelectedIndex > 7 And Cb_Kpts2.SelectedIndex < 9 Then PL = PL + 1 : SC = False 'english - pass
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then PL = PL + 1 : SC = True 'english - credit

                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then CL = CL + 1 'math
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1 'science
                If CL > 0 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj_Credit_2018(Cb_Sbj5) = True Then CL = CL + 1 Else If Chk_Sbj_Extra_2018(Cb_Sbj5) = True Then L = L + 1
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj_Credit_2018(Cb_Sbj6) = True Then CL = CL + 1 Else If Chk_Sbj_Extra_2018(Cb_Sbj6) = True Then L = L + 1
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj_Credit_2018(Cb_Sbj7) = True Then CL = CL + 1 Else If Chk_Sbj_Extra_2018(Cb_Sbj7) = True Then L = L + 1
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj_Credit_2018(Cb_Sbj8) = True Then CL = CL + 1 Else If Chk_Sbj_Extra_2018(Cb_Sbj8) = True Then L = L + 1
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj_Credit_2018(Cb_Sbj9) = True Then CL = CL + 1 Else If Chk_Sbj_Extra_2018(Cb_Sbj9) = True Then L = L + 1
                End If



                If CL > 2 Then       ' must credit malay, maths and science subjects
                    If PL = 1 Then   ' must pass english subject

                        If SC = False Then 'pass english
                            'Fix mininum 2 credit subjects 24102019 - OSH 
                            If L > 1 Then 'minimum 2 credit subjects or more 
                                'Comment Original 24102019 - OSH 
                                'If L > 2 Then 'minimum 2 credit subjects or more 
                                If (CL + PL + L) > 5 Then
                                    semak = "L"
                                Else
                                    Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum kemasukan 2018 - peringkat diploma! Sila semak maklumat kelayakan.")
                                    semak = "G"
                                End If
                            Else
                                Msg(Me, "subjek keputusan akademik (item 5 sehingga 9)tidak melepasi syarat minimum kemasukan 2018 - peringkat diploma! Sila semak maklumat kelayakan.")
                            End If
                        ElseIf SC = True Then 'credit english 
                            If (CL + PL + L) > 4 Then
                                semak = "L"
                            Else
                                Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum kemasukan 2018 - peringkat diploma! Sila semak maklumat kelayakan.")
                                semak = "G"
                            End If
                        End If
                    Else
                        Msg(Me, "subjek bahasa inggeris tidak melepasi syarat minimum kemasukan 2018 - peringkat diploma! Sila semak maklumat kelayakan.")
                    End If
                Else
                    Msg(Me, "subjek bahasa melayu, matematik atau sains tidak melepasi syarat minimum kemasukan 2018 - peringkat diploma! Sila semak maklumat kelayakan.")
                End If

                If semak = "L" Then
                    Chk_Subjek = False
                Else
                    Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
                    Chk_Subjek = True
                End If

                Return Chk_Subjek
            End If

            'INTERNATIONAL STUDENT - DIPLOMA
            If Cb_Kursus.SelectedIndex = 1 And Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
                'If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1 ' IETLS
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 515 Then PL = PL + 1 ' TOEFL PBT

                'Add SOP 2018 RULES - TOEFL CBT  17072019 - OSH
                If Cb_Luar.SelectedIndex = 3 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 215 Then PL = PL + 1 ' TOEFL CBT

                'Add SOP 2018 RULES - TOEFL CBT  18072019 - OSH
                If Cb_Luar.SelectedIndex = 4 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 80 Then PL = PL + 1 ' TOEFL iBT

                'Comment Original 17072019 - OSH 
                'If Cb_Luar.SelectedIndex = 3 And Cb_KptsLuar.Text <> "" Then If Cb_KptsLuar.Text.ToUpper <> "G" Then PL = PL + 1
                ' If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then CL = CL + 1 'math 
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1 'science 
                If CL >= 0 Then
                    'Comment Original 08012020 -OSH 
                    'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj5) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj5) = True Then L = L + 1
                    'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj6) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj6) = True Then L = L + 1
                    'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj7) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj7) = True Then L = L + 1
                    'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj8) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj8) = True Then L = L + 1
                    'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj9) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj9) = True Then L = L + 1

                    'Fix check extra subjects 
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj5) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj5) = False Then L = L + 1
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj6) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj6) = False Then L = L + 1
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj7) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj7) = False Then L = L + 1
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj8) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj8) = False Then L = L + 1
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj9) = True Then CL = CL + 1 Else If Chk_Sbj_Credit_2018(Cb_Sbj9) = False Then L = L + 1
                End If

                If PL = 1 Then semak = "L" Else semak = "G" 'PASS English 
                If semak = "L" Then If CL = 0 And L < 3 Then semak = "G" Else semak = "L" ' min credit 2 (primary) + 3 (extra) 
                If semak = "L" Then Total = PL + CL + L
                If Total > 5 Then semak = "L" Else Msg(Me, "Kelayakan pelatih antarabangsa tidak melepasi syarat minimum kemasukan 2018 - peringkat diploma! Sila semak maklumat kelayakan.") : semak = "G"
            End If

            'LOCAL STUDENT - DEGREE
            If Cb_Kursus.SelectedIndex = 5 And Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedValue = 0 Then
                If Cb_KptsMuet.SelectedIndex > 3 Then PL = PL + 1 Else Msg(Me, "Keputusan MUET tidak melepasi syarat minimum minimum kemasukan 2018") 'min band 3

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L

                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
                If Cb_Aliran.SelectedIndex = 2 Then
                    If Cb_KptsT6.SelectedIndex > 0 And Cb_KptsT6.SelectedIndex < 8 Then CL = CL + 1
                    If Cb_KptsT7.SelectedIndex > 0 And Cb_KptsT7.SelectedIndex < 8 Then CL = CL + 1
                    If semak = "L" Then If CL = 2 Then semak = "L" Else semak = "G"
                End If
            End If



            'INTERNATIONAL STUDENT - DEGREE
            If Cb_Kursus.SelectedIndex = 5 And Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedValue = 1 Then
                If Cb_KptsMuet.SelectedIndex > 0 Then PL = PL + 1

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L

                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
                If Cb_Aliran.SelectedIndex = 2 Then
                    If Cb_KptsT6.SelectedIndex > 0 And Cb_KptsT6.SelectedIndex < 8 Then CL = CL + 1
                    If Cb_KptsT7.SelectedIndex > 0 And Cb_KptsT7.SelectedIndex < 8 Then CL = CL + 1
                    If semak = "L" Then If CL = 2 Then semak = "L" Else semak = "G"
                End If
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
                If Cb_Luar.SelectedIndex = 0 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 550 Then PL = PL + 1 'BI Setaraf/Luar Negara                

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
            End If

          

            'Commment Original 24082018 - OSH
            'If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
            '    If Cb_KptsMuet.SelectedIndex > 0 Then L = L + 1
            '    If Cb_SbjM.SelectedIndex = 1 Then If CDbl(Cb_KptsM.Text) >= 2.5 Then L = L + 1
            '    If Cb_SbjM.SelectedIndex = 2 Then If CDbl(Cb_KptsM.Text) >= 3.0 Then L = L + 1
            '    If L > 1 Then semak = "L" Else semak = "G"
            'End If


            'Add Skip Check Subject for Diploma Covention Policy 2018  01102019 -OSH
            If Cb_Kursus.SelectedValue = "8" And Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                semak = "L"
            End If

            If semak = "L" Then
                Chk_Subjek = False
            Else
                Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
                Chk_Subjek = True
            End If

            Return Chk_Subjek
            'End If


            'If semak = "L" Then
            '    Chk_Subjek = False
            'Else
            '    Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan. - 2015")
            '    Chk_Subjek = True
            'End If
        End If

    End Function

    Public Sub Policy_15(ByVal I As Integer)
        Dim q As Integer

        If I = 1 Then 'Credit in Biology
            If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj5) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj5) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj6) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj6) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj7) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj7) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj8) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj8) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj9) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj9) = True Then CLP = CLP + 1
            End If

            If CLP = 0 And q > 0 Then
                CLP = CLP + 1
            End If

        ElseIf I = 2 Then 'Pass in Biology

            If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj5) = True Then CLP = CLP + 1
            If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj6) = True Then CLP = CLP + 1
            If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj7) = True Then CLP = CLP + 1
            If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj8) = True Then CLP = CLP + 1
            If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj9) = True Then CLP = CLP + 1

            'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj5) = True Then CLP = CLP + 1 Else LP = LP + 1
            'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj6) = True Then CLP = CLP + 1 Else LP = LP + 1
            'If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj7) = True Then CLP = CLP + 1 Else LP = LP + 1
            'If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj8) = True Then CLP = CLP + 1 Else LP = LP + 1
            'If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj9) = True Then CLP = CLP + 1 Else LP = LP + 1
        End If
    End Sub

    Function Chk_Sbj(ByVal cb As DropDownList)
        Chk_Sbj = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj = True : Exit Function
        'Add Subject valid still september 2013 - 23072014- OSH
        If cb.SelectedItem.Text = "APPLIED SCIENCE" Then Chk_Sbj = True : Exit Function
        'Add Subject Addtional Sciene - 21102014- OSH
        If cb.SelectedItem.Text = "ADDITIONAL SCIENCE" Then Chk_Sbj = True : Exit Function

        Return Chk_Sbj
    End Function

    Function Chk_Sbj_Cert_2015(ByVal cb As DropDownList)
        Chk_Sbj_Cert_2015 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Cert_2015 = True : Exit Function
        If cb.SelectedItem.Text = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Cert_2015 = True : Exit Function

        Return Chk_Sbj_Cert_2015
    End Function
    Function Chk_Sbj_Dip_Credit_2015(ByVal cb As DropDownList)
        Chk_Sbj_Dip_Credit_2015 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj_Dip_Credit_2015 = True : Exit Function

        Return Chk_Sbj_Dip_Credit_2015
    End Function
    Function Chk_Sbj_Dip_Pass_2015(ByVal cb As DropDownList)
        Chk_Sbj_Dip_Pass_2015 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Dip_Pass_2015 = True : Exit Function

        Return Chk_Sbj_Dip_Pass_2015
    End Function

    Function Chk_Sbj_Math_2018(ByVal cb As DropDownList)
        Chk_Sbj_Math_2018 = False

        If cb.SelectedItem.Text = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Math_2018 = True : Exit Function
        Return Chk_Sbj_Math_2018
    End Function

    Function Chk_Sbj_Science_2018(ByVal cb As DropDownList)
        Chk_Sbj_Science_2018 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Science_2018 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj_Science_2018 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj_Science_2018 = True : Exit Function
        Return Chk_Sbj_Science_2018
    End Function
    Function Chk_Sbj_Extra_2018(ByVal cb As DropDownList)
        Chk_Sbj_Extra_2018 = False

        If cb.SelectedItem.Text <> "MATEMATIK TAMBAHAN" Then Chk_Sbj_Extra_2018 = True : Exit Function
        If cb.SelectedItem.Text <> "BIOLOGI" Then Chk_Sbj_Extra_2018 = True : Exit Function
        If cb.SelectedItem.Text <> "KIMIA" Then Chk_Sbj_Extra_2018 = True : Exit Function
        If cb.SelectedItem.Text <> "FIZIK" Then Chk_Sbj_Extra_2018 = True : Exit Function
        Return Chk_Sbj_Extra_2018
    End Function
    Function Chk_Sbj_Credit_2018(ByVal cb As DropDownList)
        Chk_Sbj_Credit_2018 = False

        If cb.SelectedItem.Text = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Credit_2018 = True : Exit Function
        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Credit_2018 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj_Credit_2018 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj_Credit_2018 = True : Exit Function
        Return Chk_Sbj_Credit_2018
    End Function

    Function Chk_Sbj_Extra_I_DIP_2018(ByVal cb As DropDownList)
        Chk_Sbj_Extra_I_DIP_2018 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Extra_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj_Extra_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj_Extra_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "APPLIED SCIENCE" Then Chk_Sbj_Extra_I_DIP_2018 = True : Exit Function
        Return Chk_Sbj_Extra_I_DIP_2018
    End Function

    Function Chk_Sbj_Credit_I_DIP_2018(ByVal cb As DropDownList)
        Chk_Sbj_Credit_I_DIP_2018 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "APPLIED SCIENCE" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        Return Chk_Sbj_Credit_I_DIP_2018
    End Function

    Function Chk_Staff()
        'KPSL
        Dim A, T, J As Integer

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'PENDAFTARAN PENUH
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where ret=0 and apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        'Comment Original 17072019 - OSH
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        'Add SOP 2018 rules, active apc only and reg types 17092019 - OSH 
        'Cmd.CommandText = "SELECT nokp, apc_tahun, j_daftar FROM jt_penuh_apc where apc_tahun <= year(getdate()) and ret = 0 and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun desc"
        'Fix apc year counter 23032022  - OSH 
        Cmd.CommandText = "SELECT nokp, apc_tahun, j_daftar FROM jt_penuh_apc where apc_tahun <= '" & CInt(Cb_Sesi_Thn.Text) & "' and ret = 0 and nokp='" & Tx_NoKP.Text & "' GROUP BY nokp, apc_tahun, j_daftar ORDER BY apc_tahun desc"

        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            'T = Rdr(1)
            'J = Rdr(2) 'Type of registration
            'Assign values to variable 03042023 - OSH 
            T = CInt(Rdr("apc_tahun"))
            J = CInt(Rdr("j_daftar"))
            If T >= CInt(Cb_Sesi_Thn.Text) - 1 And T <= CInt(Cb_Sesi_Thn.Text) Then
                A += 1 'Counter
            End If
        End While

        Rdr.Close()
        Cn.Close()


        'ADD Change 5 years work EXP to 3 years working EXP 29102013- OSH
        'If A > 2 And T = CInt(Year(Now)) Then
        'Comment Ori  5 YEAR WORKING EXP 29102013 -OSH
        'If A > 4 And T = CInt(Year(Now)) Then

        'ADD REG TYPES CHECK 17072019 - OSH 
        'If A > 2 And J > 1 And T = CInt(Year(Now)) Then
        'IMPROVE 2 YEARS APC CONTIOUS REQUIREMENT 17012023 -OSH 
        'If A > 1 And J > 1 Then
        'Fix Check Registration Type 03042023 - OSH 
        If A > 1 And J > 1 Then
            Chk_Staff = False
        Else
            Msg(Me, "Pengalaman bekerja tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
            Chk_Staff = True
        End If
        Return Chk_Staff
    End Function

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        Dim X As String = ""
        If Cb_Kursus.SelectedValue = 4 Then Panel1.Visible = True : Exit Sub
        If Cb_Sesi_Bln.SelectedIndex < 1 Then X += "Sesi Pengambilan (Bulan), "
        If Cb_Sesi_Thn.SelectedIndex < 1 Then X += "Sesi Pengambilan (Tahun), "
        If Panel3.Visible = True And Cb_Aliran.Enabled = True Then If Cb_Aliran.SelectedIndex = 0 Then X += "Aliran, "
        If Panel6.Visible = True Then If Cb_KptsMuet.SelectedIndex = 0 Then X += "Keputusan MUET, "
        If Cb_Luar.SelectedIndex > 0 And Cb_KptsLuar.Text = "" Then X += "Keputusan Bahasa Inggeris(Calon Setaraf/Luar Negara), "
        If Cb_SbjM.SelectedIndex > 0 And Cb_KptsM.Text = "" Then X += "Matrikulasi/Asasi, "
        If Cb_Aliran.SelectedIndex = 2 Then If Cb_KptsT6.SelectedIndex = 0 Then X += "SPM (Matematik), "
        If Cb_Aliran.SelectedIndex = 2 Then If Cb_KptsT7.SelectedIndex = 0 Then X += "SPM (Sains), "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        If Chk_Subjek() = True Then Exit Sub

        'Comment Original 08112023 - OSH 
        'Panel1.Visible = True : PanelSemak.Visible = True

        'Enable Confirmation Panel 08112023 - OSH
        Panel1.Visible = True : Panel8.Visible = True : PanelSemak.Visible = True


        Cb_Muet.Visible = False : Cb_Muetx.Visible = True : Cb_Muetx.Text = Cb_Muet.Text 'muet
        Cb_Luar.Visible = False : Cb_Luarx.Visible = True : Cb_Luarx.Text = Cb_Luar.SelectedItem.Text 'bi luar negara/setaraf
        Cb_SbjM.Visible = False : Cb_SbjMx.Visible = True : Cb_SbjMx.Text = Cb_SbjM.SelectedItem.Text 'matrik/asasi

        Cb_SbjT1.Visible = False : Cb_SbjT1x.Visible = True : Cb_SbjT1x.Text = Cb_SbjT1.SelectedItem.Text
        Cb_SbjT2.Visible = False : Cb_SbjT2x.Visible = True : Cb_SbjT2x.Text = Cb_SbjT2.SelectedItem.Text
        Cb_SbjT3.Visible = False : Cb_SbjT3x.Visible = True : Cb_SbjT3x.Text = Cb_SbjT3.SelectedItem.Text
        Cb_SbjT4.Visible = False : Cb_SbjT4x.Visible = True : Cb_SbjT4x.Text = Cb_SbjT4.SelectedItem.Text
        Cb_SbjT5.Visible = False : Cb_SbjT5x.Visible = True : Cb_SbjT5x.Text = Cb_SbjT5.SelectedItem.Text 'stpm

        Cb_Sbj5.Visible = False : Cb_Sbj5x.Visible = True : Cb_Sbj5x.Text = Cb_Sbj5.SelectedItem.Text
        Cb_Sbj6.Visible = False : Cb_Sbj6x.Visible = True : Cb_Sbj6x.Text = Cb_Sbj6.SelectedItem.Text
        Cb_Sbj7.Visible = False : Cb_Sbj7x.Visible = True : Cb_Sbj7x.Text = Cb_Sbj7.SelectedItem.Text
        Cb_Sbj8.Visible = False : Cb_Sbj8x.Visible = True : Cb_Sbj8x.Text = Cb_Sbj8.SelectedItem.Text
        Cb_Sbj9.Visible = False : Cb_Sbj9x.Visible = True : Cb_Sbj9x.Text = Cb_Sbj9.SelectedItem.Text 'spm

        'Add Diasble Course Selection After Qualification Check  16082013- OSH
        Cb_Kursus.Enabled = False : Cb_Kelayakan.Enabled = False : Cb_Kelayakan_Taraf.Enabled = False
        'Comment Ori 16082013- OSH
        'Cb_Kelayakan.Enabled = False : Cb_Kelayakan_Taraf.Enabled = False
        Cb_Sesi_Bln.Enabled = False : Cb_Sesi_Thn.Enabled = False
        Panel2.Enabled = False : Panel3.Enabled = False : Panel4.Enabled = False
        Panel5.Enabled = False : Panel6.Enabled = False : PanelSemak.Enabled = False
    End Sub

    Protected Sub Cb_NoKP_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_NoKP.SelectedIndexChanged
        Tx_NoKP.Text = ""
        'Comment Original 15012020 - OSH 
        'With Cb_NoKP
        '    'Add Army textbox Control 10082013-OSH
        '    If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)
        '    'Add Army textbox Control 10082013-OSH
        '    If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1)
        '    'Comment Ori 10082013-OSH
        '    'If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : Fn_Negara(1)
        '    'Add Army textbox Control 10082013-OSH
        '    If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0)
        '    If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        'End With

        'Switch states to country 2012019 - OSH 
        'With Cb_NoKP
        '    If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1)
        '    If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 8 : Fn_Negara(1) : Fn_Negeri(1)
        '    If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0)
        '    If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        'End With


        With Cb_NoKP
            If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
            If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan" : Tx_Tentera_TextBoxWatermarkExtender.Enabled = True : Tx_Tentera_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_Tentera_TextBoxWatermarkExtender.WatermarkText = "Nombor Tentera"
            If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Passport"
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
    End Sub

    Public Sub Fn_Negara(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"
        Else
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        Cb_W_Negara.Items.Clear()
        Cb_W_Negara.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negara.Items.Add(Rdr(0))
            Cb_W_Negara.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    'Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
    '    Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
    '    Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

    '    Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"
    '    Rdr = Cmd.ExecuteReader()
    '    If Rdr.Read Then
    '        Msg(Me, "Rekod Pelatih Ini Telah Ada!")
    '        Rdr.Close()
    '        Exit Sub
    '    End If
    '    Rdr.Close()

    '    Try
    '        Cmd.CommandText = "select * from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
    '        Rdr = Cmd.ExecuteReader()
    '        If Rdr.Read Then
    '            If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama")
    '            If Not IsDBNull(Rdr("warganegara")) Then If Rdr("warganegara") > 0 Then Cb_Warga.SelectedValue = Rdr("warganegara")
    '            If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") > 0 Then Cb_Jantina.SelectedValue = Rdr("jantina")
    '            If Not IsDBNull(Rdr("bangsa")) Then If Rdr("bangsa") > 0 Then Cb_Bangsa.SelectedValue = Rdr("bangsa")
    '            If Not IsDBNull(Rdr("agama")) Then If Rdr("agama") > 0 Then Cb_Agama.SelectedValue = Rdr("agama")
    '            If Not IsDBNull(Rdr("t_kahwin")) Then If Rdr("t_kahwin") > 0 Then Cb_Kahwin.SelectedValue = Rdr("t_kahwin")
    '            If Not IsDBNull(Rdr("tp_alamat")) Then Tx_TP_Alamat.Text = Rdr("tp_alamat")
    '            If Not IsDBNull(Rdr("tp_poskod")) Then Tx_TP_Poskod.Text = Rdr("tp_poskod")
    '            If Not IsDBNull(Rdr("tp_bandar")) Then Tx_TP_Bandar.Text = Rdr("tp_bandar")
    '            If Not IsDBNull(Rdr("tp_negeri")) Then If Rdr("tp_negeri") > 0 Then Cb_TP_Negeri.SelectedValue = Rdr("tp_negeri")
    '            If Not IsDBNull(Rdr("tel_hp")) Then Tx_Tel.Text = Rdr("tel_hp")
    '            If Not IsDBNull(Rdr("emel")) Then Tx_Emel.Text = Rdr("emel")
    '        Else
    '            Msg(Me, "Rekod Tidak Wujud")
    '        End If
    '        Rdr.Close()
    '    Catch ex As Exception
    '        Msg(Me, ex.Message)
    '    End Try
    '    Cn.Close()
    'End Sub

    Protected Sub Cb_Aliran_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Aliran.SelectedIndexChanged
        Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_Aliran.SelectedIndex = 2 Then
            Textbox13.Visible = True : Textbox22.Visible = True : TextBox23.Visible = True
            Cb_SbjT6.Visible = True : Cb_KptsT6.Visible = True
            Cb_SbjT7.Visible = True : Cb_KptsT7.Visible = True
        Else
            Textbox13.Visible = False : Textbox22.Visible = False : TextBox23.Visible = False
            Cb_SbjT6.Visible = False : Cb_KptsT6.Visible = False
            Cb_SbjT7.Visible = False : Cb_KptsT7.Visible = False
        End If
    End Sub

    Protected Sub Cb_Luar_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Luar.SelectedIndexChanged
        Cb_KptsLuar.Text = ""
        If Cb_Luar.SelectedIndex = 0 Then Cb_Kpts2.Enabled = True Else Cb_Kpts2.Enabled = False
    End Sub

    'Private Sub Cb_Kelayakan_Taraf_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Cb_Kelayakan_Taraf.SelectedIndexChanged
    Protected Sub Cb_Kelayakan_Taraf_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan_Taraf.SelectedIndexChanged
        'Comment Original 22072019 - OSH 
        'Panel1.Visible = False : Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : PanelSemak.Visible = True
        'Panel1.Enabled = True : Panel2.Enabled = True : Panel3.Enabled = True : Panel4.Enabled = True : Panel5.Enabled = True : Panel6.Enabled = True : PanelSemak.Enabled = True
        ''Cb_KptsMuet.SelectedIndex = 0
        'Cb_Luar.SelectedIndex = 0 : Cb_KptsLuar.Text = "" : Cb_KptsM.Text = "" : Cb_Aliran.SelectedIndex = 0
        'Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_Kelayakan_Taraf.SelectedValue = 1 Then
            If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                Panel2.Visible = True : Isi_Subjek(2)
                Cb_Kpts1.Enabled = False ': Cb_Kpts2.Enabled = False
                Panel5.Visible = True
                Cb_Luar.Items.Clear()
                Cb_Luar.Items.Add("(PILIHAN)")
                Cb_Luar.Items.Add("IELTS")
                'Comment Original 17072019 - OSH 
                'Cb_Luar.Items.Add("TOEFL")
                'Cb_Luar.Items.Add("ENGLISH GCE O LEVEL")

                'Add SOP 2018 rules 17072019 - OSH
                Cb_Luar.Items.Add("TOEFL PBT")
                Cb_Luar.Items.Add("TOEFL CBT")
                Cb_Luar.Items.Add("TOEFL iBT")
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                Panel3.Visible = True : Isi_Subjek(3)
                Cb_Aliran.Enabled = False
                Panel5.Visible = True
                Cb_Luar.Items.Clear()
                Cb_Luar.Items.Add("(PILIHAN)")
                Cb_Luar.Items.Add("IELTS")
                'Comment Original 17072019 - OSH 
                'Cb_Luar.Items.Add("TOEFL")

                'Add SOP 2018 rules 19072019 - OSH
                Cb_Luar.Items.Add("TOEFL PBT")
                Cb_Luar.Items.Add("TOEFL CBT")
                Cb_Luar.Items.Add("TOEFL iBT")
            End If

        Else
            If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                Cb_Kpts1.Enabled = True ': Cb_Kpts2.Enabled = True
                'Comment Original 25082020 - OSH 
                'Panel2.Visible = True : Isi_Subjek(2)
                'Improve - disable internation english panel when local SPM only
                Panel5.Visible = False : Panel2.Visible = True : Isi_Subjek(2)
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                Panel3.Visible = True : Isi_Subjek(3)
                Cb_Aliran.Enabled = True
                Panel6.Visible = True
            End If
        End If
    End Sub
    Function Chk_Dip_Credit_2015(ByVal cb As DropDownList)
        Chk_Dip_Credit_2015 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Dip_Credit_2015 = True : Exit Function

        Return Chk_Dip_Credit_2015
    End Function

    Function Chk_Pure_Dip_Credit_2015(ByVal cb As DropDownList)
        Chk_Pure_Dip_Credit_2015 = False

        If cb.SelectedItem.Text = "KIMIA" Then Chk_Pure_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Pure_Dip_Credit_2015 = True : Exit Function

        Return Chk_Pure_Dip_Credit_2015
    End Function

    Function Chk_Record()
        Dim A As Integer

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Add SOP 2018 rules, exist registion record 21102019 - OSH  
        Cmd.CommandText = "SELECT nokp from jt_penuh where j_daftar = 1 and nokp='" & Tx_NoKP.Text & "' "
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A = A + 1
        End While
        Rdr.Close()
        Cn.Close()
        'ADD REG TYPES CHECK 17072019 - OSH 
        If A > 0 Then
            Chk_Record = False
        Else
            Msg(Me, "Pelatih tidak berdaftar denagan Lembaga Jururawat Malaysia ! Sila semak maklumat kelayakan.")
            Chk_Record = True
        End If
        Return Chk_Record
    End Function

    Function Chk_Staff_Midwife()
        'Dim A, T, J As Integer
        'Dim A, T As Integer
        Dim A As Integer


        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'APC 
        'Fix SOP 2015 rules, active apc only and min 2 year experince  17092019 - OSH 
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where apc_tahun <= year(getdate()) and ret = 0 and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        'Fix Sorting to enrolled year 31012023 - OSH
        Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where j_daftar = 1 and apc_tahun <= '" & Cb_Sesi_Thn.Text.Trim & "' and ret = 0 and nokp='" & Tx_NoKP.Text.Trim & "'  ORDER BY apc_tahun desc"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A += 1
            'Comment Original 03042023 - OSH 
            'T = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
        'ADD REG TYPES CHECK 17072019 - OSH 
        'If A > 1 And T = CInt(Year(Now)) Then
        'ADD Change 5 years work EXP to 3 years working EXP 29102013- OSH
        'If A > 2 And T = CInt(Year(Now)) Then
        'Comment Ori  5 YEAR WORKING EXP 29102013 -OSH
        'If A > 4 And T = CInt(Year(Now)) Then
        'Fix Require APCs from enrolled year 31012023 -OSH
        ' If A > 1 And T = CInt(Cb_Sesi_Thn.Text) Then
        'Fix minimum APCs from enrolled year 31012023 -OSH
        'If A > 2 And T = CInt(Cb_Sesi_Thn.Text) Then
        'Fix minimum working experices - APCs from enrolled year 31012023 -OSH
        If A > 2 Then
            Chk_Staff_Midwife = False
        Else
            Msg(Me, "Pengalaman bekerja tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
            Chk_Staff_Midwife = True
        End If
        Return Chk_Staff_Midwife
    End Function

    Protected Sub ChkSah_CheckedChanged(sender As Object, e As EventArgs) Handles ChkSah.CheckedChanged
        'Enable Submit Button When Declaration Is Confirm  20042023 - OSH
        If ChkSah.Checked = True Then
            Panel1.Enabled = False : cmdHantar.Enabled = True
        ElseIf ChkSah.Checked = False Then
            'Comment Original 08112023 - OSH 
            'cmdHantar.Enabled = False
            'Fix Details Panel Enable 08112023 - OSH 
            Panel1.Enabled = True : cmdHantar.Enabled = False
        End If
    End Sub


    'Add states switch to country for internations student 20122019 - OSH  
    Public Sub Fn_Negeri(ByVal X As Int16)
        Dim Cn5 As New OleDbConnection : Dim Cmd5 As New OleDbCommand : Dim Rdr5 As OleDbDataReader
        Cn5.ConnectionString = ServerId : Cn5.Open() : Cmd5.Connection = Cn5

        'WARGANEGARA
        If X = 1 Then
            'Comment Original 25082020 - OSH 
            'Lb_States.Text = "NEGERI" : Lb_States.Enabled = False : Cb_W_Negeri.Enabled = False

            'Fix disable states dropdownlist issue 25082020 - OSH  
            Lb_States.Text = "NEGERI" : Lb_States.Enabled = False : Cb_W_Negeri.Enabled = True
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("")
            'Comment Original 21082020 - OSH 
            'Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri order by dc_negeri"

            'Improve query exclude 'luar negeri' from states populate 21082020 - OSH
            Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri where dc_negeri <> 'LUAR NEGERA' order by dc_negeri"
        Else
            Lb_States.Text = "NEGARA" : Lb_States.Enabled = False
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False
            Cmd5.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr5 = Cmd5.ExecuteReader()
        Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
        'Comment Original 25082020 - OSH 
        'Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False

        While Rdr5.Read
            Cb_TP_Negeri.Items.Add(Rdr5(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr5(1)
            Cb_W_Negeri.Items.Add(Rdr5(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr5(1)
        End While
        Rdr5.Close()
        Cn5.Close()
    End Sub
End Class