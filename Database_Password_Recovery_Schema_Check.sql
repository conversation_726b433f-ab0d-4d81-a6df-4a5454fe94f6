-- DATABASE SCHEMA VERIFICATION FOR PASSWORD RECOVERY FIX
-- Run this script to ensure the required columns exist in the pn_pengguna table

-- Check if required columns exist
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pn_pengguna' 
AND COLUMN_NAME IN ('is_temporary', 'force_change', 'tarikh_tukar_katalaluan', 'salt', 'password_migrated')
ORDER BY COLUMN_NAME;

-- If columns don't exist, uncomment and run these ALTER statements:

-- Add is_temporary column (tracks temporary passwords from recovery)
-- ALTER TABLE pn_pengguna ADD is_temporary BIT DEFAULT 0;

-- Add force_change column (forces password change on next login)  
-- ALTER TABLE pn_pengguna ADD force_change BIT DEFAULT 0;

-- Add password change date tracking
-- ALTER TABLE pn_pengguna ADD tarikh_tukar_katalaluan DATETIME;

-- Check current user records to see password recovery status
SELECT 
    id_pg,
    pwd,
    CASE WHEN salt IS NULL OR salt = '' THEN 'NO SALT' ELSE 'HAS SALT' END as salt_status,
    CASE WHEN password_migrated = 1 THEN 'MIGRATED' ELSE 'PLAIN TEXT' END as pwd_type,
    CASE WHEN is_temporary = 1 THEN 'TEMPORARY' ELSE 'PERMANENT' END as pwd_temp_status,
    CASE WHEN force_change = 1 THEN 'REQUIRED' ELSE 'NOT REQUIRED' END as change_required,
    tarikh_tukar_katalaluan as last_password_change,
    email,
    status
FROM pn_pengguna 
WHERE status = 1 
ORDER BY id_pg;

-- Test query to simulate password recovery lookup
-- Replace 'your_user_id' with actual user ID for testing
/*
SELECT 
    id_pg,
    pwd,
    salt,
    password_migrated,
    is_temporary,
    force_change,
    email,
    modul,
    akses,
    status
FROM pn_pengguna 
WHERE id_pg = 'your_user_id' AND status = 1;
*/
