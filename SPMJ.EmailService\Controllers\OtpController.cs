using Microsoft.AspNetCore.Mvc;
using SPMJ.EmailService.Models;
using SPMJ.EmailService.Services;

namespace SPMJ.EmailService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class OtpController : ControllerBase
{
    private readonly IOtpService _otpService;
    private readonly ILogger<OtpController> _logger;

    public OtpController(IOtpService otpService, ILogger<OtpController> logger)
    {
        _otpService = otpService;
        _logger = logger;
    }

    /// <summary>
    /// Generate OTP for user authentication
    /// </summary>
    [HttpPost("generate")]
    public async Task<ActionResult<OtpResponse>> GenerateOtp([FromBody] OtpRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.UserId))
            {
                return BadRequest(new OtpResponse
                {
                    Success = false,
                    Message = "ID Pengguna diperlukan"
                });
            }

            // Validate purpose
            var validPurposes = new[] { "LOGIN", "PASSWORD_RESET", "ADMIN_ACTION" };
            if (!validPurposes.Contains(request.Purpose.ToUpper()))
            {
                return BadRequest(new OtpResponse
                {
                    Success = false,
                    Message = "Tujuan OTP tidak sah"
                });
            }

            request.Purpose = request.Purpose.ToUpper();
            var result = await _otpService.GenerateOtpAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GenerateOtp");
            return StatusCode(500, new OtpResponse
            {
                Success = false,
                Message = "Ralat sistem dalaman"
            });
        }
    }

    /// <summary>
    /// Validate OTP code
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<ApiResponse<bool>>> ValidateOtp([FromBody] OtpValidationRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.UserId) || string.IsNullOrEmpty(request.OtpCode))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "ID Pengguna dan kod OTP diperlukan",
                    Data = false
                });
            }

            if (request.OtpCode.Length != 6 || !request.OtpCode.All(char.IsDigit))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Kod OTP mestilah 6 digit nombor",
                    Data = false
                });
            }

            // Validate purpose
            var validPurposes = new[] { "LOGIN", "PASSWORD_RESET", "ADMIN_ACTION" };
            if (!validPurposes.Contains(request.Purpose.ToUpper()))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Tujuan OTP tidak sah",
                    Data = false
                });
            }

            request.Purpose = request.Purpose.ToUpper();
            var result = await _otpService.ValidateOtpAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ValidateOtp");
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "Ralat sistem dalaman",
                Data = false
            });
        }
    }

    /// <summary>
    /// Cleanup expired OTPs (maintenance endpoint)
    /// </summary>
    [HttpPost("cleanup")]
    public async Task<ActionResult<ApiResponse<bool>>> CleanupExpiredOtps()
    {
        try
        {
            var result = await _otpService.CleanupExpiredOtpsAsync();
            return Ok(new ApiResponse<bool>
            {
                Success = result,
                Message = result ? "Cleanup completed successfully" : "Cleanup failed",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CleanupExpiredOtps");
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "Ralat sistem dalaman",
                Data = false
            });
        }
    }
}
