-- =====================================================================================
-- SPMJ KOLEJ-PDSA PRODUCTION ROLLBACK SCRIPT FOR kj_pengguna TABLE
-- =====================================================================================
-- Purpose: Remove columns added by the enhancement script if rollback is needed
-- Target Table: kj_pengguna
-- Date: 2025-06-27
-- Version: 1.0 PRODUCTION ROLLBACK
-- ⚠️  WARNING: THIS WILL PERMANENTLY DELETE DATA IN THE ADDED COLUMNS
-- =====================================================================================

-- Enable error handling
SET NOCOUNT ON;
SET XACT_ABORT ON;

PRINT '====================================================================================='
PRINT '⚠️  SPMJ KOLEJ-PDSA PRODUCTION ROLLBACK SCRIPT - kj_pengguna TABLE'
PRINT '====================================================================================='
PRINT '⚠️  WARNING: THIS SCRIPT WILL PERMANENTLY DELETE ENHANCEMENT DATA!'
PRINT '⚠️  ENSURE YOU HAVE A COMPLETE BACKUP BEFORE PROCEEDING!'
PRINT ''
PRINT 'Starting rollback at: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================================================
-- SAFETY CHECK: REQUIRE MANUAL CONFIRMATION
-- =====================================================================================
PRINT '🛑 SAFETY CHECK: Manual confirmation required'
PRINT ''
PRINT 'To proceed with rollback, you must manually comment out the following line:'
PRINT 'RAISERROR(''Rollback script requires manual confirmation'', 16, 1);'
PRINT ''

-- Uncomment the next line to enable rollback (SAFETY MEASURE)
RAISERROR('Rollback script requires manual confirmation - comment out this line to proceed', 16, 1);

-- =====================================================================================
-- STEP 1: BACKUP CURRENT STATE
-- =====================================================================================
PRINT 'Step 1: Creating backup of current enhanced table...'

-- Create backup with timestamp
DECLARE @backup_table_name VARCHAR(100) = 'kj_pengguna_enhanced_backup_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss');

DECLARE @backup_sql NVARCHAR(MAX) = 'SELECT * INTO [' + @backup_table_name + '] FROM [kj_pengguna]';
EXEC sp_executesql @backup_sql;

PRINT '✅ Backup created: ' + @backup_table_name;

-- =====================================================================================
-- STEP 2: DROP INDEXES CREATED BY ENHANCEMENT
-- =====================================================================================
PRINT 'Step 2: Dropping enhancement indexes...'

-- Drop email index
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_kj_pengguna_email')
BEGIN
    DROP INDEX [IX_kj_pengguna_email] ON [dbo].[kj_pengguna];
    PRINT '✅ Dropped index IX_kj_pengguna_email'
END
ELSE
BEGIN
    PRINT '⚠️  Index IX_kj_pengguna_email not found - skipping'
END

-- Drop account locked index
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_kj_pengguna_account_locked')
BEGIN
    DROP INDEX [IX_kj_pengguna_account_locked] ON [dbo].[kj_pengguna];
    PRINT '✅ Dropped index IX_kj_pengguna_account_locked'
END
ELSE
BEGIN
    PRINT '⚠️  Index IX_kj_pengguna_account_locked not found - skipping'
END

-- =====================================================================================
-- STEP 3: REMOVE COLUMN DESCRIPTIONS
-- =====================================================================================
PRINT 'Step 3: Removing column descriptions...'

-- Remove extended properties
BEGIN TRY
    EXEC sp_dropextendedproperty 
        @name = 'MS_Description',
        @level0type = 'SCHEMA', @level0name = 'dbo',
        @level1type = 'TABLE', @level1name = 'kj_pengguna',
        @level2type = 'COLUMN', @level2name = 'email';
    PRINT '✅ Removed email column description'
END TRY
BEGIN CATCH
    PRINT '⚠️  Email column description not found - skipping'
END CATCH

BEGIN TRY
    EXEC sp_dropextendedproperty 
        @name = 'MS_Description',
        @level0type = 'SCHEMA', @level0name = 'dbo',
        @level1type = 'TABLE', @level1name = 'kj_pengguna',
        @level2type = 'COLUMN', @level2name = 'salt';
    PRINT '✅ Removed salt column description'
END TRY
BEGIN CATCH
    PRINT '⚠️  Salt column description not found - skipping'
END CATCH

BEGIN TRY
    EXEC sp_dropextendedproperty 
        @name = 'MS_Description',
        @level0type = 'SCHEMA', @level0name = 'dbo',
        @level1type = 'TABLE', @level1name = 'kj_pengguna',
        @level2type = 'COLUMN', @level2name = 'pwd_encrypted';
    PRINT '✅ Removed pwd_encrypted column description'
END TRY
BEGIN CATCH
    PRINT '⚠️  pwd_encrypted column description not found - skipping'
END CATCH

-- =====================================================================================
-- STEP 4: DROP ENHANCEMENT COLUMNS
-- =====================================================================================
PRINT 'Step 4: Dropping enhancement columns...'

-- Drop email column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'email')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [email];
    PRINT '✅ Dropped email column'
END
ELSE
BEGIN
    PRINT '⚠️  email column not found - skipping'
END

-- Drop salt column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'salt')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [salt];
    PRINT '✅ Dropped salt column'
END
ELSE
BEGIN
    PRINT '⚠️  salt column not found - skipping'
END

-- Drop pwd_encrypted column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_encrypted')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [pwd_encrypted];
    PRINT '✅ Dropped pwd_encrypted column'
END
ELSE
BEGIN
    PRINT '⚠️  pwd_encrypted column not found - skipping'
END

-- Drop pwd_migrated column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_migrated')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [pwd_migrated];
    PRINT '✅ Dropped pwd_migrated column'
END
ELSE
BEGIN
    PRINT '⚠️  pwd_migrated column not found - skipping'
END

-- Drop failed_login_attempts column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'failed_login_attempts')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [failed_login_attempts];
    PRINT '✅ Dropped failed_login_attempts column'
END
ELSE
BEGIN
    PRINT '⚠️  failed_login_attempts column not found - skipping'
END

-- Drop account_locked column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'account_locked')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [account_locked];
    PRINT '✅ Dropped account_locked column'
END
ELSE
BEGIN
    PRINT '⚠️  account_locked column not found - skipping'
END

-- Drop last_successful_login column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'last_successful_login')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [last_successful_login];
    PRINT '✅ Dropped last_successful_login column'
END
ELSE
BEGIN
    PRINT '⚠️  last_successful_login column not found - skipping'
END

-- Drop last_failed_login column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'last_failed_login')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [last_failed_login];
    PRINT '✅ Dropped last_failed_login column'
END
ELSE
BEGIN
    PRINT '⚠️  last_failed_login column not found - skipping'
END

-- Drop locked_date column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'locked_date')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [locked_date];
    PRINT '✅ Dropped locked_date column'
END
ELSE
BEGIN
    PRINT '⚠️  locked_date column not found - skipping'
END

-- Drop pwd_last_changed column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_last_changed')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [pwd_last_changed];
    PRINT '✅ Dropped pwd_last_changed column'
END
ELSE
BEGIN
    PRINT '⚠️  pwd_last_changed column not found - skipping'
END

-- Drop force_change_required column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'force_change_required')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [force_change_required];
    PRINT '✅ Dropped force_change_required column'
END
ELSE
BEGIN
    PRINT '⚠️  force_change_required column not found - skipping'
END

-- Drop migration_date column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'migration_date')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] DROP COLUMN [migration_date];
    PRINT '✅ Dropped migration_date column'
END
ELSE
BEGIN
    PRINT '⚠️  migration_date column not found - skipping'
END

-- =====================================================================================
-- STEP 5: FINAL VERIFICATION
-- =====================================================================================
PRINT 'Step 5: Final verification...'

-- Check if any enhancement columns still exist
DECLARE @remaining_columns TABLE (column_name VARCHAR(50));

INSERT INTO @remaining_columns (column_name)
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'kj_pengguna'
  AND COLUMN_NAME IN (
    'email', 'salt', 'pwd_encrypted', 'pwd_migrated',
    'failed_login_attempts', 'account_locked', 'last_successful_login',
    'last_failed_login', 'locked_date', 'pwd_last_changed',
    'force_change_required', 'migration_date'
  );

DECLARE @remaining_count INT = (SELECT COUNT(*) FROM @remaining_columns);

IF @remaining_count = 0
BEGIN
    PRINT '✅ All enhancement columns successfully removed from kj_pengguna table'
    
    -- Display final statistics
    DECLARE @total_columns INT = (
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'kj_pengguna'
    );
    
    DECLARE @total_records INT = (SELECT COUNT(*) FROM kj_pengguna);
    
    PRINT '📊 Final Statistics:'
    PRINT '   • Total columns in kj_pengguna: ' + CAST(@total_columns AS VARCHAR(10))
    PRINT '   • Total records in kj_pengguna: ' + CAST(@total_records AS VARCHAR(10))
    PRINT '   • Backup table created: ' + @backup_table_name
END
ELSE
BEGIN
    PRINT '❌ Some enhancement columns still exist:'
    SELECT column_name FROM @remaining_columns;
    RAISERROR('Rollback incomplete. Some columns could not be removed.', 16, 1);
END

-- =====================================================================================
-- COMPLETION MESSAGE
-- =====================================================================================
PRINT ''
PRINT '====================================================================================='
PRINT '✅ SPMJ KOLEJ-PDSA PRODUCTION ROLLBACK COMPLETED!'
PRINT '====================================================================================='
PRINT 'Rollback completion time: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT 'ROLLBACK SUMMARY:'
PRINT '• Removed all enhancement columns from kj_pengguna table'
PRINT '• Dropped performance indexes created by enhancement'
PRINT '• Removed column documentation'
PRINT '• Created backup table: ' + @backup_table_name
PRINT ''
PRINT 'IMPORTANT NOTES:'
PRINT '• All enhancement data has been permanently deleted'
PRINT '• Enhanced login functionality will no longer work'
PRINT '• Backup table contains all data before rollback'
PRINT '• You may need to revert application code changes'
PRINT ''
PRINT 'NEXT STEPS:'
PRINT '1. Revert KOLEJ-PDSA application code to previous version'
PRINT '2. Test basic login functionality'
PRINT '3. Consider keeping backup table for future reference'
PRINT '4. Document rollback reason for future planning'
PRINT ''
PRINT '⚠️  System has been rolled back to basic login functionality'
PRINT '====================================================================================='

SET NOCOUNT OFF;
