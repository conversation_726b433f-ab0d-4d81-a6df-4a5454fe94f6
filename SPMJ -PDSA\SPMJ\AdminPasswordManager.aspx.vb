Imports System.Data.OleDb
Imports System.Configuration
Imports System.Net.Mail

Partial Public Class AdminPasswordManager
    Inherits System.Web.UI.Page

    Private emailServiceClient As EmailServiceClient
    Private currentUserInfo As UserInfo = Nothing

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Check admin access
        If Session("Id_PG") Is Nothing Then
            Session.Abandon()
            Response.Redirect("p0_Login.aspx")
            Return
        End If
        
        ' Initialize email service client
        Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
        If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
        emailServiceClient = New EmailServiceClient(serviceUrl)

        ' Check if user has admin privileges (you may need to adjust this based on your authorization system)
        If Not IsUserAdmin() Then
            Response.Redirect("blank.aspx")
            Return
        End If
    End Sub

    Protected Sub ButtonSearchUser_Click(sender As Object, e As EventArgs) Handles ButtonSearchUser.Click
        Try
            If String.IsNullOrEmpty(TextBoxSearchUser.Text) OrElse TextBoxSearchUser.Text.Trim() = "" Then
                ShowMessage("Sila masukkan ID pengguna", "danger")
                TextBoxSearchUser.Focus()
                Return
            End If

            Dim userInfo As UserInfo = GetUserInfo(TextBoxSearchUser.Text.Trim())
            If userInfo IsNot Nothing Then
                ' Display user information
                currentUserInfo = userInfo
                DisplayUserInfo(userInfo)
                PanelUserInfo.Visible = True
                PanelActions.Visible = True
                PanelGeneratedPassword.Visible = False

                ' Pre-populate email if available
                TextBoxUserEmail.Text = userInfo.Email
            Else
                ShowMessage("Pengguna tidak dijumpai", "danger")
                PanelUserInfo.Visible = False
                PanelActions.Visible = False
                PanelGeneratedPassword.Visible = False
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "danger")
        End Try
    End Sub

    Protected Sub ButtonCreatePassword_Click(sender As Object, e As EventArgs) Handles ButtonCreatePassword.Click
        Try
            If currentUserInfo Is Nothing Then
                ShowMessage("Sila cari pengguna terlebih dahulu", "danger")
                Return
            End If

            If String.IsNullOrEmpty(TextBoxUserEmail.Text) OrElse TextBoxUserEmail.Text.Trim() = "" Then
                ShowMessage("Sila masukkan alamat email", "danger")
                TextBoxUserEmail.Focus()
                Return
            End If

            ' Validate email format
            If Not IsValidEmail(TextBoxUserEmail.Text) Then
                ShowMessage("Format alamat email tidak sah", "danger")
                TextBoxUserEmail.Focus()
                Return
            End If

            ' Generate or use provided password
            Dim tempPassword As String = TextBoxNewPassword.Text.Trim()
            If String.IsNullOrEmpty(tempPassword) Then
                tempPassword = GenerateTemporaryPassword()
            ElseIf tempPassword.Length < 6 Then
                ShowMessage("Kata laluan mestilah sekurang-kurangnya 6 aksara", "danger")
                TextBoxNewPassword.Focus()
                Return
            End If

            ' Check if email service is available
            If Not emailServiceClient.CheckHealth() Then
                ShowMessage("Sistem email tidak tersedia. Kata laluan tidak dapat dihantar melalui email.", "danger")
                Return
            End If

            ' Create admin password via email service
            Dim response = emailServiceClient.CreateAdminPassword(
                currentUserInfo.UserId,
                TextBoxUserEmail.Text.Trim(),
                tempPassword,
                Session("Id_PG").ToString()
            )

            If response.Success Then
                ' Display generated password
                LabelGeneratedPassword.Text = tempPassword
                PanelGeneratedPassword.Visible = True
                
                ShowMessage("Kata laluan telah berjaya ditetapkan dan email dihantar ke " & MaskEmail(TextBoxUserEmail.Text), "success")
                
                ' Clear form
                TextBoxNewPassword.Text = ""
            Else
                ShowMessage("Gagal menetapkan kata laluan: " & response.Message, "danger")
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "danger")
        End Try
    End Sub

    Protected Sub ButtonResetPassword_Click(sender As Object, e As EventArgs) Handles ButtonResetPassword.Click
        Try
            If currentUserInfo Is Nothing Then
                ShowMessage("Sila cari pengguna terlebih dahulu", "danger")
                Return
            End If

            Dim emailToUse As String
            If String.IsNullOrEmpty(TextBoxUserEmail.Text) OrElse TextBoxUserEmail.Text.Trim() = "" Then
                emailToUse = currentUserInfo.Email
            Else
                emailToUse = TextBoxUserEmail.Text.Trim()
            End If
            
            If String.IsNullOrEmpty(emailToUse) OrElse emailToUse.Trim() = "" Then
                ShowMessage("Alamat email diperlukan untuk reset kata laluan", "danger")
                TextBoxUserEmail.Focus()
                Return
            End If

            ' Validate email format
            If Not IsValidEmail(emailToUse) Then
                ShowMessage("Format alamat email tidak sah", "danger")
                TextBoxUserEmail.Focus()
                Return
            End If

            ' Check if email service is available
            If Not emailServiceClient.CheckHealth() Then
                ShowMessage("Sistem email tidak tersedia. Kata laluan tidak dapat direset.", "danger")
                Return
            End If

            ' Reset password via email service
            Dim response = emailServiceClient.ResetAdminPassword(
                currentUserInfo.UserId,
                emailToUse,
                Session("Id_PG").ToString()
            )

            If response.Success Then
                ' Display generated password if provided
                If Not String.IsNullOrEmpty(response.Data) Then
                    LabelGeneratedPassword.Text = response.Data
                    PanelGeneratedPassword.Visible = True
                End If
                
                ShowMessage("Kata laluan telah berjaya direset dan email dihantar ke " & MaskEmail(emailToUse), "success")
            Else
                ShowMessage("Gagal reset kata laluan: " & response.Message, "danger")
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "danger")
        End Try
    End Sub

    Private Function GetUserInfo(userId As String) As UserInfo
        Try
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            Cmd.CommandText = "SELECT id_pg, nama, email, status, modul, akses FROM pn_pengguna WHERE id_pg = ?"
            Cmd.Parameters.AddWithValue("@id_pg", userId)
            
            Dim reader As OleDbDataReader = Cmd.ExecuteReader()
            If reader.Read() Then
                Dim userInfo As New UserInfo()
                userInfo.UserId = reader("id_pg").ToString()
                userInfo.Name = If(IsDBNull(reader("nama")), "", reader("nama").ToString())
                userInfo.Email = If(IsDBNull(reader("email")), "", reader("email").ToString())
                userInfo.Status = If(IsDBNull(reader("status")), "", reader("status").ToString())
                userInfo.Modul = If(IsDBNull(reader("modul")), "", reader("modul").ToString())
                userInfo.Akses = If(IsDBNull(reader("akses")), "", reader("akses").ToString())
                
                reader.Close()
                Cn.Close()
                Return userInfo
            End If
            
            reader.Close()
            Cn.Close()
            Return Nothing
            
        Catch ex As Exception
            Return Nothing
        End Try
    End Function

    Private Sub DisplayUserInfo(userInfo As UserInfo)
        LabelUserId.Text = userInfo.UserId
        LabelUserName.Text = If(String.IsNullOrEmpty(userInfo.Name), "Tiada nama", userInfo.Name)
        LabelUserEmail.Text = If(String.IsNullOrEmpty(userInfo.Email), "Tiada email", userInfo.Email)
        
        Dim statusText As String
        Select Case userInfo.Status
            Case "1"
                statusText = "Aktif"
            Case "0"
                statusText = "Tidak Aktif"
            Case Else
                statusText = "Tidak Diketahui"
        End Select
        LabelUserStatus.Text = statusText
    End Sub

    Private Function IsUserAdmin() As Boolean
        ' Check if current user has admin privileges
        ' You may need to adjust this based on your authorization system
        Try
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand

            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            Cmd.CommandText = "SELECT akses FROM pn_pengguna WHERE id_pg = ? AND status = '1'"
            Cmd.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())

            Dim result = Cmd.ExecuteScalar()
            Cn.Close()

            If result IsNot Nothing Then
                Dim akses As String = result.ToString()
                ' Assuming admin access is indicated by certain pattern in akses field
                Return akses.Contains("admin") OrElse akses.Contains("1") ' Adjust based on your system
            End If

            Return False
        Catch
            Return False
        End Try
    End Function
    Private Function GenerateTemporaryPassword() As String
        Const chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        Dim random As New Random()
        Dim result As String = ""
        For i As Integer = 0 To 7
            result += chars(random.Next(chars.Length))
        Next
        Return result
    End Function

    Private Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr As New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function

    Private Function MaskEmail(email As String) As String
        Try
            If String.IsNullOrEmpty(email) OrElse Not email.Contains("@") Then
                Return email
            End If

            Dim parts = email.Split("@"c)
            Dim localPart = parts(0)
            Dim domainPart = parts(1)

            If localPart.Length <= 2 Then
                Return email
            End If

            ' Show first and last character of local part, mask the middle
            Dim masked = localPart.Substring(0, 1) & 
                        New String("*"c, localPart.Length - 2) & 
                        localPart.Substring(localPart.Length - 1, 1) & 
                        "@" & domainPart

            Return masked
        Catch
            Return email
        End Try
    End Function

    Private Sub ShowMessage(message As String, type As String)
        LabelMessage.Text = message
        PanelMessage.Visible = True
        
        ' Set CSS class based on type
        Select Case type.ToLower()
            Case "success"
                alertDiv.Attributes("class") = "alert alert-success"
            Case "danger", "error"
                alertDiv.Attributes("class") = "alert alert-danger"
            Case "info"
                alertDiv.Attributes("class") = "alert alert-info"
            Case Else
                alertDiv.Attributes("class") = "alert alert-info"
        End Select
    End Sub

End Class

Public Class UserInfo
    Private _userId As String = ""
    Private _name As String = ""
    Private _email As String = ""
    Private _status As String = ""
    Private _modul As String = ""
    Private _akses As String = ""
    
    Public Property UserId() As String
        Get
            Return _userId
        End Get
        Set(value As String)
            _userId = value
        End Set
    End Property
    
    Public Property Name() As String
        Get
            Return _name
        End Get
        Set(value As String)
            _name = value
        End Set
    End Property
    
    Public Property Email() As String
        Get
            Return _email
        End Get
        Set(value As String)
            _email = value
        End Set
    End Property
    
    Public Property Status() As String
        Get
            Return _status
        End Get
        Set(value As String)
            _status = value
        End Set
    End Property
    
    Public Property Modul() As String
        Get
            Return _modul
        End Get
        Set(value As String)
            _modul = value
        End Set
    End Property
    
    Public Property Akses() As String
        Get
            Return _akses
        End Get
        Set(value As String)
            _akses = value
        End Set
    End Property
End Class
