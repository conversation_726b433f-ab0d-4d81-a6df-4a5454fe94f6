# SPMJ-PDSA Login & Password Recovery Test Script
Write-Host "SPMJ-PDSA Login & Password Recovery Test Suite" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host ""

$EmailServiceUrl = "http://localhost:5000"
$TestResults = @()

function Test-EmailService {
    Write-Host "1. Testing Email Microservice Connectivity..." -ForegroundColor Yellow
    
    try {
        $response = Invoke-WebRequest -Uri "$EmailServiceUrl/api/health" -Method GET -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ Email service is running and healthy" -ForegroundColor Green
            $script:TestResults += "Email Service: PASS"
            return $true
        } else {
            Write-Host "   ❌ Email service health check failed (Status: $($response.StatusCode))" -ForegroundColor Red
            $script:TestResults += "Email Service: FAIL - Bad status code"
            return $false
        }
    } catch {
        Write-Host "   ❌ Cannot connect to email service at $EmailServiceUrl" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += "Email Service: FAIL - Connection error"
        return $false
    }
}

function Test-DatabaseConnectivity {
    Write-Host ""
    Write-Host "2. Testing Database Connectivity..." -ForegroundColor Yellow
    
    try {
        # Test basic SQL Server connection
        $connectionString = "Server=localhost;Database=SPMJ_PDSA;Integrated Security=true;Connection Timeout=5;"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        # Test pn_pengguna table structure
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna'"
        $columnCount = $command.ExecuteScalar()
        
        if ($columnCount -gt 0) {
            Write-Host "   ✅ Database connection successful" -ForegroundColor Green
            Write-Host "   ✅ pn_pengguna table found with $columnCount columns" -ForegroundColor Green
            $script:TestResults += "Database Connectivity: PASS"
            
            # Check for required columns
            $requiredColumns = @('id_pg', 'pwd', 'salt', 'password_migrated', 'is_temporary', 'force_change', 'email')
            $command.CommandText = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME IN ('" + ($requiredColumns -join "','") + "')"
            $reader = $command.ExecuteReader()
            $foundColumns = @()
            while ($reader.Read()) {
                $foundColumns += $reader["COLUMN_NAME"]
            }
            $reader.Close()
            
            $missingColumns = $requiredColumns | Where-Object { $_ -notin $foundColumns }
            if ($missingColumns.Count -eq 0) {
                Write-Host "   ✅ All required columns present" -ForegroundColor Green
            } else {
                Write-Host "   ⚠️  Missing columns: $($missingColumns -join ', ')" -ForegroundColor Yellow
            }
            
        } else {
            Write-Host "   ❌ pn_pengguna table not found" -ForegroundColor Red
            $script:TestResults += "Database Connectivity: FAIL - Table not found"
        }
        
        $connection.Close()
        return $true
        
    } catch {
        Write-Host "   ❌ Database connection failed" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += "Database Connectivity: FAIL - Connection error"
        return $false
    }
}

function Test-WebConfig {
    Write-Host ""
    Write-Host "3. Testing Web.config Configuration..." -ForegroundColor Yellow
    
    $webConfigPath = "SPMJ -PDSA\SPMJ\Web.config"
    
    if (Test-Path $webConfigPath) {
        Write-Host "   ✅ Web.config file found" -ForegroundColor Green
        
        $webConfigContent = Get-Content $webConfigPath -Raw
        
        # Check for required app settings
        $requiredSettings = @('EmailServiceUrl', 'IP_App', 'dB')
        $foundSettings = @()
        
        foreach ($setting in $requiredSettings) {
            if ($webConfigContent -match "key=`"$setting`"") {
                $foundSettings += $setting
                Write-Host "   ✅ $setting configuration found" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $setting configuration missing" -ForegroundColor Red
            }
        }
        
        if ($foundSettings.Count -eq $requiredSettings.Count) {
            $script:TestResults += "Web.config: PASS"
        } else {
            $script:TestResults += "Web.config: PARTIAL - Missing settings"
        }
        
    } else {
        Write-Host "   ❌ Web.config file not found at $webConfigPath" -ForegroundColor Red
        $script:TestResults += "Web.config: FAIL - File not found"
    }
}

function Test-CodeFiles {
    Write-Host ""
    Write-Host "4. Testing Code Files..." -ForegroundColor Yellow
    
    $codeFiles = @(
        "SPMJ -PDSA\SPMJ\p0_Login.aspx.vb",
        "SPMJ -PDSA\SPMJ\EmailServiceClient.vb",
        "SPMJ -PDSA\SPMJ\PasswordHelper.vb",
        "SPMJ -PDSA\SPMJ\SPMJ_Mod.vb"
    )
    
    $allFilesExist = $true
    
    foreach ($file in $codeFiles) {
        if (Test-Path $file) {
            Write-Host "   ✅ $file exists" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $file missing" -ForegroundColor Red
            $allFilesExist = $false
        }
    }
    
    if ($allFilesExist) {
        $script:TestResults += "Code Files: PASS"
    } else {
        $script:TestResults += "Code Files: FAIL - Missing files"
    }
}

function Show-TestResults {
    Write-Host ""
    Write-Host "Test Results Summary:" -ForegroundColor Green
    Write-Host "===================" -ForegroundColor Green
    
    foreach ($result in $script:TestResults) {
        if ($result -like "*PASS*") {
            Write-Host "   ✅ $result" -ForegroundColor Green
        } elseif ($result -like "*PARTIAL*") {
            Write-Host "   ⚠️  $result" -ForegroundColor Yellow
        } else {
            Write-Host "   ❌ $result" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    
    # Check if email service needs to be started
    if ($script:TestResults -contains "Email Service: FAIL - Connection error") {
        Write-Host "🚀 TO START EMAIL SERVICE:" -ForegroundColor Yellow
        Write-Host "   Run: Start-EmailService.bat" -ForegroundColor White
        Write-Host "   Or: cd SPMJ.EmailService && dotnet run --urls `"http://localhost:5000`"" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Start email microservice if not running" -ForegroundColor White
    Write-Host "2. Test login functionality in browser" -ForegroundColor White
    Write-Host "3. Test password recovery feature" -ForegroundColor White
    Write-Host "4. Monitor debug output for issues" -ForegroundColor White
}

# Run all tests
Test-EmailService
Test-DatabaseConnectivity
Test-WebConfig
Test-CodeFiles
Show-TestResults
