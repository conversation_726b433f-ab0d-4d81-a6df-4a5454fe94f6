﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm71
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim List_Data As New DataSet

        'Improve sorting by name, month and year 04122023 -OSH
        Dim SQL As String = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end 'SESI (BULAN)', p.sesi_tahun 'SESI (TAHUN)' from pelatih p where  p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by p.sesi_tahun desc, cast (p.sesi_bulan as int ) desc,  p.nama"

        'Fix wildcard isues not filter by month and year 22112023 -OSH 
        'Dim SQL As String = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end 'SESI (BULAN)', p.sesi_tahun 'SESI (TAHUN)' from pelatih p where  p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by p.nama"

        'Comment Oiginal 22112023 - OSH
        'Dim SQL As String = "SELECT p.Nama, p.NoKP, case p.sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end  'SESI (BULAN)', p.sesi_tahun 'SESI (TAHUN)',t.Dc_TAJAAN 'TAJAAN', p.status FROM dbo.PELATIH p INNER JOIN dbo.PN_TAJAAN t ON  ( p.Tajaan = t.Id_TAJAAN) where  p.id_kolej = " & Cb_Kolej.SelectedValue & " And j_kursus = " & Cb_Kursus.SelectedValue & " And " & X & " order by p.nama"

        'Comment Original 14082023 - OSH 
        'Dim SQL As String = "Select p.Nama, p.NoKP, Case p.sesi_bulan When 1 Then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end  'SESI (BULAN)', p.sesi_tahun 'SESI (TAHUN)',t.Dc_TAJAAN 'TAJAAN' FROM dbo.PELATIH p INNER JOIN dbo.PN_TAJAAN t ON  ( p.Tajaan = t.Id_TAJAAN) where  p.id_kolej = " & Cb_Kolej.SelectedValue & " And j_kursus = " & Cb_Kursus.SelectedValue & " And " & X & " order by p.nama"

        'add new query with s'ship 09082012
        'Dim SQL As String = "Select p.Nama, p.NoKP, Case p.sesi_bulan When 1 Then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end  'SESI (BULAN)', p.sesi_tahun 'SESI (TAHUN)',t.Dc_TAJAAN 'TAJAAN' FROM dbo.PELATIH p INNER JOIN dbo.PN_TAJAAN t ON  ( p.Tajaan = t.Id_TAJAAN) where " & Cb_Sesi.SelectedValue & " p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by p.nama"

        'comment make way new improvent query 09082012 
        'Dim SQL As String = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end 'SESI (BULAN)', p.sesi_tahun 'SESI (TAHUN)' from pelatih p where " & Cb_Sesi.SelectedValue & " p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by p.nama"
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)

        List_Adp.Fill(List_Data, "pelatih")
        Gd.DataSource = List_Data.Tables("pelatih")
        DataBind()
        Gd.Visible = True
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then Else e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Gd.SelectedIndexChanged

        Dim m As Integer

        'Add Fitler massege more than 5 year 17082013- OSH
        'If Gd.SelectedRow.Cells(5).Text >= Year(Now) - 5 And Gd.SelectedRow.Cells(5).Text <= Year(Now) Then
        Session("nokp") = Gd.SelectedRow.Cells(3).Text
        'add session type course 31052012 
        Session("j_kursus") = Cb_Kursus.SelectedValue
        'Comment Original 04022020 - OSH 
        'degree 26082019 - OSH 
        'If Cb_Kursus.SelectedValue = "5" Then
        '    Response.Redirect("pelatih_pinda2.aspx")

        'Fix select policy form load 04022020 - OSH 
        If Gd.SelectedRow.Cells(5).Text = "JANUARI" Then
            m = 1
        ElseIf Gd.SelectedRow.Cells(5).Text = "FEBRUARI" Then
            m = 2
        ElseIf Gd.SelectedRow.Cells(5).Text = "MAC" Then
            m = 3
        ElseIf Gd.SelectedRow.Cells(5).Text = "APRIL" Then
            m = 4
        ElseIf Gd.SelectedRow.Cells(5).Text = "MEI" Then
            m = 5
        ElseIf Gd.SelectedRow.Cells(5).Text = "JUN" Then
            m = 6
        ElseIf Gd.SelectedRow.Cells(5).Text = "JULAI" Then
            m = 7
        ElseIf Gd.SelectedRow.Cells(5).Text = "OGOS" Then
            m = 8
        ElseIf Gd.SelectedRow.Cells(5).Text = "SEPTEMBER" Then
            m = 9
        ElseIf Gd.SelectedRow.Cells(5).Text = "OKTOBER" Then
            m = 10
        ElseIf Gd.SelectedRow.Cells(5).Text = "NOVEMBER" Then
            m = 11
        ElseIf Gd.SelectedRow.Cells(5).Text = "DISEMBER" Then
            m = 12
        End If

        'Comment Original 26042022 - OSH 
        'If Cb_Kursus.SelectedValue = "5" And Gd.SelectedRow.Cells(5).Text > 2018 Then
        '    Response.Redirect("pelatih_pinda2.aspx")
        'ElseIf Cb_Kursus.SelectedValue = "5" And Gd.SelectedRow.Cells(5).Text > 2017 And m > 5 Then
        '    Response.Redirect("pelatih_pinda2.aspx")
        'Else
        '    'Redirect pick record to new policy form 16082013 -OSH
        '    Response.Redirect("pelatih_pinda1.aspx")
        '    'Comment Ori 16082013 -OSH
        '    'Response.Redirect("pelatih_pinda.aspx")
        'End If
        'Else
        'Msg(Me, "Rekod Tidak Boleh Dibuka, Melebihi Tempoh Pengajian.")
        'End If

        'Fix Load Error for Midwife  and KPSL report by fatimah school 15062022 - OSH
        If Session("j_kursus") = "4" Or Session("j_kursus") = "8" Then
            Response.Redirect("pelatih_pinda1.aspx")
        End If

        'Temp
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Lock_IGCSE As Boolean 'Check Switch To Non-IGCSE 1410222 - OSH

        Cmd.CommandText = "SELECT KELAYAKAN FROM PELATIH WHERE STATUS IS NULL AND NOKP =  '" & Session("nokp") & "'"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Not IsDBNull(Rdr(0)) And Rdr(0) = "IGCSE" Then
                'Comment Original 13122023 -OSH 
                'Lock_IGCSE = True : Response.Redirect("pelatih_pinda3.aspx")
                'improve Edit Form 13122023 - OSH 
                Lock_IGCSE = True : Response.Redirect("pelatih_pinda3b.aspx")
            End If
        End While
        Rdr.Close()

        If Lock_IGCSE = False Then
            'Cmd.CommandText = "SELECT Id_JENIS FROM PELATIH_KELAYAKAN WHERE NOKP =  '" & Session("nokp") & "'"
            'Load Old Format 17112023 - OSH 
            'Cmd.CommandText = "SELECT Id_JENIS FROM PELATIH_KELAYAKAN WHERE NOKP =  '" & Session("nokp") & "' AND ID_SUBJEK = '-1'"
            'Improve determine old format 22112023 - OSH
            Cmd.CommandText = "SELECT Id_JENIS FROM PELATIH_KELAYAKAN WHERE NOKP =  '" & Session("nokp") & "' AND ID_SUBJEK < 0"
            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                If Not IsDBNull(Rdr(0)) Then
                    'Comment Original 06012023 - OSH
                    'Response.Redirect("pelatih_pinda2.aspx")
                    'Improve form 06012023 - OSH 
                    Response.Redirect("pelatih_pinda2a.aspx")
                    'Comment Original 16112023 - OSH
                    'Response.Redirect("pelatih_pinda2b.aspx")
                Else
                    Response.Redirect("pelatih_pinda1.aspx")
                End If
            End While
            Rdr.Close()

            'Load New Open Format 17112023 - OSH 
            Cmd.CommandText = "SELECT Id_JENIS FROM PELATIH_KELAYAKAN WHERE NOKP =  '" & Session("nokp") & "' AND ID_SUBJEK > 0 "
            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                If Not IsDBNull(Rdr(0)) Then
                    Response.Redirect("pelatih_pinda2b.aspx")
                Else
                    Response.Redirect("pelatih_pinda1.aspx")
                End If
            End While
            Rdr.Close()
        End If
        Cn.Close()
    End Sub

    Private Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Comment Original 05042022 -OSH 
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        'NEW lANDING pAGE 05042022 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login_j.aspx")
        'KOLEJ
        Cb_Kolej.Items.Add(Session("Dc_KOLEJ"))
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Session("Id_KOLEJ")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KURSUS
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select distinct sesi_bulan, sesi_tahun, case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end from pelatih where id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus=" & Cb_Kursus.SelectedValue & " order by sesi_tahun, sesi_bulan"
        Rdr = Cmd.ExecuteReader()
        Cb_Sesi.Items.Clear()
        Cb_Sesi.Items.Add("SEMUA")
        Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Sesi.Items.Add(Rdr(1) & " - " & Rdr(2))
            Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = " p.sesi_bulan = " & Rdr(0) & " and p.sesi_tahun = " & Rdr(1) & " and "
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmdHantar1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar1.Click
        'Add Fixing Course Validation 05082013 -OSH 
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" Or Cb_Kursus.SelectedValue <= "0" Then Exit Sub
        'Comment Ori 05082013 - OSH
        ' If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" Then Exit Sub
        'Comment 17082021 - OSH 
        'Cari("p.nama like '" & Tx_Nama.Text.Trim & "%' and p.nokp like '" & Tx_NoKP.Text.Trim & "%'")
        'Fix Name with ' 17082021 -OSH
        Cari("p.nama like '" & Apo(Tx_Nama.Text.Trim) & "%' and p.nokp like '" & Tx_NoKP.Text.Trim & "%'")
    End Sub
    Private Sub Gd_RowDataBound(sender As Object, e As GridViewRowEventArgs) Handles Gd.RowDataBound
        'highlight pass and 4 strike-out 14082023 - OSH
        Try
            If e.Row.RowType = DataControlRowType.DataRow Then
                If (e.Row.Cells(5).Text = "1") Then
                    e.Row.BackColor = Drawing.Color.LightGoldenrodYellow
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub
End Class