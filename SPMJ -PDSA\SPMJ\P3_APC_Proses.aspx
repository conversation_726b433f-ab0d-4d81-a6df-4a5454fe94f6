﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P3_APC_Proses.aspx.vb" Inherits="SPMJ.WebForm19" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style2
        {
            height: 64px;
        }
        .style5
        {
        }
        .style15
        {
            height: 21px;
            width: 123px;
        }
        .style37
        {
            height: 24px;
        }
        .style41
        {
            width: 137px;
            height: 21px;
        }
        .style42
        {
            width: 14px;
        }
        .style43
        {
            width: 217px;
        }
        .style44
        {
            width: 15px;
        }
        .style45
        {
            width: 97px;
        }
        .style50
        {
            width: 137px;
            height: 22px;
        }
        .style51
        {
            height: 22px;
            width: 123px;
        }
        .style52
        {
            width: 137px;
            height: 20px;
        }
        .style53
        {
            height: 20px;
            width: 123px;
        }
        .style54
        {
            width: 137px;
            height: 19px;
        }
        .style55
        {
            height: 19px;
            width: 123px;
        }
        .style56
        {
            width: 137px;
            height: 13px;
        }
        .style57
        {
            height: 13px;
            width: 123px;
        }
        .style58
        {
            width: 137px;
            height: 16px;
        }
        .style59
        {
            height: 16px;
            width: 123px;
        }
        .style60
        {
            width: 137px;
            height: 18px;
        }
        .style61
        {
            height: 18px;
            width: 123px;
        }
        .style62
        {
            height: 18px;
        }
        .style63
        {
            width: 137px;
            height: 3px;
        }
        .style64
        {
            height: 3px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr style="width: 10%">
            <td width="100"></td>
            <td width="600" colspan="2">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td width="100">&nbsp;</td>
        </tr>
        <tr>
            <td class="style37"></td>
            <td colspan="2" align="center" bgcolor="#719548" class="style37" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;">
                proses pembaharuan apc</td>
            <td class="style37"></td>
        </tr>
        <tr>
            <td class="style37"></td>
            <td colspan="2" align="right" bgcolor="#719548">
                                                <asp:Button ID="cmdxls" runat="server" 
                    Font-Names="Arial" Font-Size="8pt" 
                                                    Height="20px" tabIndex="3" Text="EXCEL" 
                    Width="96px" />
                                            </td>
            <td class="style37"></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" GridLines="Horizontal" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:CommandField ButtonType="Button" SelectText="X" ShowSelectButton="True">
                                    <ControlStyle Font-Names="Arial" Font-Size="8pt" ForeColor="#CC3300" 
                                        Width="30px" />
                                </asp:CommandField>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="False" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                </asp:UpdatePanel>
            </td>
            <td>&nbsp;</td>
        </tr>
                                 <tr>
                        <td class="style2">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style2" 
                                         
                                         style="border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px;">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" 
                                         style="border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-right-style: solid; border-right-width: 1px;">
                            &nbsp;</td>
                        <td>
                            &nbsp;</td>
                    </tr>
                                 <tr>
                        <td class="style2">
                                     </td>
                        <td bgcolor="#ffffff" class="style2" 
                                         
                                         style="border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px;" 
                                         colspan="2">
                            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                <ContentTemplate>
                                
                                    <table cellpadding="-1" cellspacing="-1" 
    
                                        
                                        style="border-color: #7EA851; width:100%; border-bottom-width: 1px; border-right-width: 1px; border-right-style: Solid;">
                                        <tr>
                                            <td class="style44" rowspan="11" width="20PX">
                                                &nbsp;</td>
                                            <td class="style52">
                                                &nbsp; SEKTOR</td>
                                            <td class="style53" width="280px">
                                                <asp:UpdatePanel ID="UpdatePanel6" runat="server">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="Cb_Sektor" runat="server" AutoPostBack="True" 
                                                            CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                                                            style="margin-left: 0px" Width="150px">
                                                            <asp:ListItem></asp:ListItem>
                                                            <asp:ListItem Value="1">KERAJAAN</asp:ListItem>
                                                            <asp:ListItem Value="2">SWASTA</asp:ListItem>
                                                        </asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </td>
                                            <td class="style42" rowspan="8" width="20PX">
                                                &nbsp;</td>
                                            <td class="style43" rowspan="8" valign="top" align="left" width="240">
                                                <table cellpadding="-1" cellspacing="-1" width="240">
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp; TAHUN APC&nbsp;</td>
                                                        <td valign="top">
                                                            <asp:UpdatePanel ID="UpdatePanel9" runat="server">
                                                                <ContentTemplate>
                                                                    <asp:DropDownList ID="Cb_Tahun" runat="server" AutoPostBack="True" 
                                                                        CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                                                                        style="margin-left: 0px" Width="115px">
                                                                        <asp:ListItem>SEMASA</asp:ListItem>
                                                                        <asp:ListItem>HADAPAN</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp;</td>
                                                        <td>
                                                            &nbsp;</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" bgcolor="#99CC00" class="style62" style="color: #FFFFFF">
                                                            &nbsp; A. FEE APC</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp;&nbsp;TARIKH APC&nbsp;</td>
                                                        <td align="right">
                                                            <asp:TextBox ID="Tx_Tkh" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_Tkh_MaskedEditExtender" runat="server" 
                                                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh" 
                                                                UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Tkh_CalendarExtender" runat="server" 
                                                                Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                TargetControlID="Tx_Tkh">
                                                            </cc1:CalendarExtender>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp; NO. RESIT</td>
                                                        <td align="right">
                                                            <asp:TextBox ID="Tx_NoResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp; TARIKH RESIT</td>
                                                        <td align="right">
                                                            <asp:TextBox ID="Tx_TkhResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_TkhResit_MaskedEditExtender" runat="server" 
                                                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_TkhResit" 
                                                                UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_TkhResit_CalendarExtender" runat="server" 
                                                                Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                TargetControlID="Tx_TkhResit">
                                                            </cc1:CalendarExtender>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp; AMAUN</td>
                                                        <td align="right">
                                                            <asp:TextBox ID="Tx_Amaun" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp;</td>
                                                        <td>
                                                            &nbsp;</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" bgcolor="#CC3300" class="style62" style="color: #FFFFFF">
                                                            &nbsp;&nbsp; b. FEE LEWAT</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp;&nbsp; NO. RESIT</td>
                                                        <td align="right">
                                                            <asp:TextBox ID="Tx_L_NoResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp;&nbsp; TARIKH RESIT</td>
                                                        <td align="right">
                                                            <asp:TextBox ID="Tx_L_TkhResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_L_TkhResit_MaskedEditExtender" runat="server" 
                                                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" Mask="99/99/9999" MaskType="Date" 
                                                                TargetControlID="Tx_L_TkhResit" UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_L_TkhResit_CalendarExtender" runat="server" 
                                                                Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                TargetControlID="Tx_L_TkhResit">
                                                            </cc1:CalendarExtender>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="style45">
                                                            &nbsp;&nbsp; AMAUN</td>
                                                        <td align="right">
                                                            <asp:TextBox ID="Tx_L_Amaun" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                            <td class="style5" rowspan="11" width="20px">
                                                &nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td class="style50">
                                                &nbsp; NEGERI</td>
                                            <td class="style51">
                                                <asp:UpdatePanel ID="UpdatePanel5" runat="server">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="Cb_Negeri" runat="server" AutoPostBack="True" 
                                                            CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                                                            style="margin-left: 0px" Width="256px">
                                                        </asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; TEMPAT AMALAN&nbsp;</td>
                                            <td class="style15">
                                                <asp:UpdatePanel ID="UpdatePanel7" runat="server">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="Cb_Tpt_Amalan" runat="server" AutoPostBack="True" 
                                                            CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                                                            style="margin-left: 0px" Width="256px">
                                                        </asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41" valign="top">
                                                &nbsp; ALAMAT AMALAN</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_Amalan_Alamat" runat="server" CssClass="std" Height="65px" 
                                                    ReadOnly="True" TextMode="MultiLine" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style54">
                                                &nbsp; POSKOD</td>
                                            <td class="style55">
                                                <asp:TextBox ID="Tx_Amalan_Poskod" runat="server" CssClass="std" 
                                                    ReadOnly="True" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style56">
                                                &nbsp; BANDAR</td>
                                            <td class="style57">
                                                <asp:TextBox ID="Tx_Amalan_Bandar" runat="server" CssClass="std" 
                                                    ReadOnly="True" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style58">
                                                &nbsp; NO. TELEFON</td>
                                            <td class="style59">
                                                <asp:TextBox ID="Tx_Amalan_Tel" runat="server" CssClass="std" ReadOnly="True" 
                                                    Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style60">
                                                &nbsp; NO. FAKS</td>
                                            <td class="style61">
                                                <asp:TextBox ID="Tx_Amalan_Fax" runat="server" CssClass="std" ReadOnly="True" 
                                                    Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style63">
                                            </td>
                                            <td class="style64">
                                                &nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td class="style63">
                                                &nbsp;</td>
                                            <td class="style64">
                                                &nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td class="style60">
                                                <asp:UpdatePanel ID="UpdatePanel8" runat="server">
                                                    <ContentTemplate>
                                                        <asp:CheckBox ID="chk1" runat="server" CssClass="menu_small" Font-Names="Arial" 
                                                            Font-Size="8pt" ForeColor="#666666" Text="kekalkan senarai nama" />
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </td>
                                            <td>
                                                <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                    Height="20px" tabIndex="3" Text="SIMPAN" Width="96px" />
                                                <br />
                                            </td>
                                        </tr>
                                    </table>
                                </ContentTemplate>
                            </asp:UpdatePanel>                            
                                     </td>
                        <td class="style2">
                                     </td>
                    </tr>
    
    
<tr>
            <td>&nbsp;</td>
            <td width="600" colspan="2" align="center" 
                
                style="border-right-style: solid; border-width: 1px; border-color: #7EA851; border-left-style: solid; background-color: #FFFFFF; border-bottom-style: solid;">
                &nbsp;</td>
            <td>&nbsp;</td>
        </tr>
    
    
<tr>
            <td>&nbsp;</td>
            <td width="600" colspan="2" align="center">
                &nbsp;</td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div>
    </asp:Content>
