Imports System
Imports System.Data
Imports System.Data.OleDb
Imports System.Configuration
Imports System.Web
Imports System.Web.UI
Imports System.Web.UI.WebControls

''' <summary>
''' PN_AdminPasswordManager - Pure .NET Framework 3.5.1 VB.NET Implementation
''' Admin password management page for SPMJ system
''' </summary>
Partial Public Class PN_AdminPasswordManager
    Inherits System.Web.UI.Page

    ' Page-level variables
    Private currentUser As UserData = Nothing

    ''' <summary>
    ''' Page Load Event - .NET 3.5.1 Standard
    ''' </summary>    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            ' Security check - ensure user is logged in
            If Session("Id_PG") Is Nothing OrElse Session("Id_PG").ToString().Trim() = "" Then
                Session.Abandon()
                Response.Redirect("p0_Login.aspx")
                Return
            End If

            ' Admin privilege check
            If Not CheckAdminPrivileges() Then
                ShowMessage("Akses ditolak. Anda tidak mempunyai kebenaran untuk menggunakan fungsi ini.", "error")
                Response.Redirect("blank.aspx")
                Return
            End If

            ' Initialize page on first load
            If Not Page.IsPostBack Then
                InitializePage()
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "error")
        End Try
    End Sub
    '    End Sub

    '    ''' <summary>
    '    ''' Initialize page components
    '    ''' </summary>
    '    Private Sub InitializePage()
    '        Try
    '            ClearAllPanels()
    '            ShowMessage("Sila masukkan ID pengguna untuk mula mencari.", "info")
    '        Catch ex As Exception
    '            ShowMessage("Ralat memulakan halaman: " & ex.Message, "error")
    '        End Try
    '    End Sub

    '    ''' <summary>
    '    ''' Search User Button Click Event
    '    ''' </summary>
    '    Protected Sub btnSearchUser_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSearchUser.Click
    '        Try
    '            ClearAllPanels()

    '            ' Validate input
    '            If String.IsNullOrEmpty(txtSearchUser.Text) OrElse txtSearchUser.Text.Trim() = "" Then
    '                ShowMessage("Sila masukkan ID pengguna.", "error")
    '                txtSearchUser.Focus()
    '                Return
    '            End If

    '            ' Search for user
    '            Dim userData As UserData = SearchUserInDatabase(txtSearchUser.Text.Trim())

    '            If userData IsNot Nothing Then
    '                currentUser = userData
    '                DisplayUserInformation(userData)
    '                ShowPasswordActions()

    '                ' Pre-populate email if available
    '                If Not String.IsNullOrEmpty(userData.Email) Then
    '                    txtUserEmail.Text = userData.Email
    '                End If

    '                ShowMessage("Pengguna dijumpai. Sila pilih tindakan yang diperlukan.", "success")
    '            Else
    '                ShowMessage("Pengguna dengan ID '" & txtSearchUser.Text.Trim() & "' tidak dijumpai dalam sistem.", "error")
    '            End If

    '        Catch ex As Exception
    '            ShowMessage("Ralat mencari pengguna: " & ex.Message, "error")
    '        End Try
    '    End Sub

    '    ''' <summary>
    '    ''' Set Password Button Click Event
    '    ''' </summary>
    '    Protected Sub btnSetPassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSetPassword.Click
    '        Try
    '            If currentUser Is Nothing Then
    '                ShowMessage("Sila cari pengguna terlebih dahulu.", "error")
    '                Return
    '            End If

    '            ' Validate email if provided
    '            If Not String.IsNullOrEmpty(txtUserEmail.Text.Trim()) Then
    '                If Not IsValidEmailFormat(txtUserEmail.Text.Trim()) Then
    '                    ShowMessage("Format email tidak sah.", "error")
    '                    txtUserEmail.Focus()
    '                    Return
    '                End If
    '            End If

    '            ' Get or generate password
    '            Dim newPassword As String = txtNewPassword.Text.Trim()
    '            If String.IsNullOrEmpty(newPassword) Then
    '                newPassword = GenerateRandomPassword()
    '            ElseIf newPassword.Length < 6 Then
    '                ShowMessage("Kata laluan mesti sekurang-kurangnya 6 aksara.", "error")
    '                txtNewPassword.Focus()
    '                Return
    '            End If

    '            ' Update password in database
    '            If UpdateUserPassword(currentUser.UserId, newPassword) Then
    '                ShowPasswordResult(newPassword)
    '                ShowMessage("Kata laluan telah berjaya ditetapkan untuk pengguna " & currentUser.UserId & ".", "success")
    '                ClearPasswordInputs()
    '            Else
    '                ShowMessage("Gagal menetapkan kata laluan. Sila cuba lagi.", "error")
    '            End If

    '        Catch ex As Exception
    '            ShowMessage("Ralat menetapkan kata laluan: " & ex.Message, "error")
    '        End Try
    '    End Sub

    '    ''' <summary>
    '    ''' Reset Password Button Click Event
    '    ''' </summary>
    '    Protected Sub btnResetPassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnResetPassword.Click
    '        Try
    '            If currentUser Is Nothing Then
    '                ShowMessage("Sila cari pengguna terlebih dahulu.", "error")
    '                Return
    '            End If

    '            ' Generate new password
    '            Dim newPassword As String = GenerateRandomPassword()

    '            ' Update password in database
    '            If UpdateUserPassword(currentUser.UserId, newPassword) Then
    '                ShowPasswordResult(newPassword)
    '                ShowMessage("Kata laluan telah berjaya direset untuk pengguna " & currentUser.UserId & ".", "success")
    '                ClearPasswordInputs()
    '            Else
    '                ShowMessage("Gagal reset kata laluan. Sila cuba lagi.", "error")
    '            End If

    '        Catch ex As Exception
    '            ShowMessage("Ralat reset kata laluan: " & ex.Message, "error")
    '        End Try
    '    End Sub

    '    ''' <summary>
    '    ''' Search user in database
    '    ''' </summary>
    '    Private Function SearchUserInDatabase(ByVal userId As String) As UserData
    '        Dim connection As OleDbConnection = Nothing
    '        Dim command As OleDbCommand = Nothing
    '        Dim reader As OleDbDataReader = Nothing

    '        Try
    '            connection = New OleDbConnection(SPMJ_Mod.ServerId)
    '            connection.Open()

    '            command = New OleDbCommand()
    '            command.Connection = connection
    '            command.CommandText = "SELECT id_pg, nama, email, status, modul, akses FROM pn_pengguna WHERE id_pg = ?"
    '            command.Parameters.AddWithValue("@id_pg", userId)

    '            reader = command.ExecuteReader()

    '            If reader.Read() Then
    '                Dim userData As New UserData()
    '                userData.UserId = GetSafeStringValue(reader, "id_pg")
    '                userData.Name = GetSafeStringValue(reader, "nama")
    '                userData.Email = GetSafeStringValue(reader, "email")
    '                userData.Status = GetSafeStringValue(reader, "status")
    '                userData.UserModule = GetSafeStringValue(reader, "modul")
    '                userData.Access = GetSafeStringValue(reader, "akses")
    '                Return userData
    '            End If

    '            Return Nothing

    '        Catch ex As Exception
    '            ShowMessage("Ralat mengakses pangkalan data: " & ex.Message, "error")
    '            Return Nothing
    '        Finally
    '            If reader IsNot Nothing Then reader.Close()
    '            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
    '        End Try
    '    End Function

    '    ''' <summary>
    '    ''' Update user password in database
    '    ''' </summary>
    '    Private Function UpdateUserPassword(ByVal userId As String, ByVal newPassword As String) As Boolean
    '        Dim connection As OleDbConnection = Nothing
    '        Dim command As OleDbCommand = Nothing

    '        Try
    '            connection = New OleDbConnection(SPMJ_Mod.ServerId)
    '            connection.Open()

    '            command = New OleDbCommand()
    '            command.Connection = connection

    '            ' Hash the password (simple MD5 for .NET 3.5.1 compatibility)
    '            Dim hashedPassword As String = HashPasswordMD5(newPassword)

    '            command.CommandText = "UPDATE pn_pengguna SET katalaluan = ? WHERE id_pg = ?"
    '            command.Parameters.AddWithValue("@password", hashedPassword)
    '            command.Parameters.AddWithValue("@id_pg", userId)

    '            Dim rowsAffected As Integer = command.ExecuteNonQuery()
    '            Return rowsAffected > 0

    '        Catch ex As Exception
    '            ShowMessage("Ralat mengemas kini kata laluan: " & ex.Message, "error")
    '            Return False
    '        Finally
    '            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
    '        End Try
    '    End Function

    '    ''' <summary>
    '    ''' Check if current user has admin privileges
    '    ''' </summary>
    '    Private Function CheckAdminPrivileges() As Boolean
    '        Dim connection As OleDbConnection = Nothing
    '        Dim command As OleDbCommand = Nothing

    '        Try
    '            connection = New OleDbConnection(SPMJ_Mod.ServerId)
    '            connection.Open()

    '            command = New OleDbCommand()
    '            command.Connection = connection
    '            command.CommandText = "SELECT akses FROM pn_pengguna WHERE id_pg = ? AND status = '1'"
    '            command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())

    '            Dim accessLevel As Object = command.ExecuteScalar()
    '            If accessLevel IsNot Nothing Then
    '                Dim access As String = accessLevel.ToString().ToLower()
    '                Return access.Contains("admin") OrElse access.Contains("1") OrElse access.Contains("pengurusan")
    '            End If

    '            Return False

    '        Catch
    '            Return False
    '        Finally
    '            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
    '        End Try
    '    End Function

    '    ''' <summary>
    '    ''' Generate random password - .NET 3.5.1 compatible
    '    ''' </summary>
    '    Private Function GenerateRandomPassword() As String
    '        Const passwordChars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    '        Dim random As New Random()
    '        Dim password As String = ""

    '        For i As Integer = 1 To 8
    '            password = password & passwordChars.Substring(random.Next(passwordChars.Length), 1)
    '        Next

    '        Return password
    '    End Function

    '    ''' <summary>
    '    ''' Hash password using MD5 - .NET 3.5.1 compatible
    '    ''' </summary>
    '    Private Function HashPasswordMD5(ByVal password As String) As String
    '        Try
    '            Dim md5Provider As New System.Security.Cryptography.MD5CryptoServiceProvider()
    '            Dim inputBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(password)
    '            Dim hashBytes As Byte() = md5Provider.ComputeHash(inputBytes)

    '            Dim hashString As String = ""
    '            For Each b As Byte In hashBytes
    '                hashString = hashString & b.ToString("x2")
    '            Next

    '            Return hashString
    '        Catch
    '            ' Fallback to plain text if hashing fails
    '            Return password
    '        End Try
    '    End Function

    '    ''' <summary>
    '    ''' Validate email format - .NET 3.5.1 compatible
    '    ''' </summary>
    '    Private Function IsValidEmailFormat(ByVal email As String) As Boolean
    '        Try
    '            If String.IsNullOrEmpty(email) Then Return False
    '            Return email.Contains("@") AndAlso email.Contains(".") AndAlso email.Length >= 5
    '        Catch
    '            Return False
    '        End Try
    '    End Function

    '    ''' <summary>
    '    ''' Get safe string value from database reader
    '    ''' </summary>
    '    Private Function GetSafeStringValue(ByVal reader As OleDbDataReader, ByVal columnName As String) As String
    '        Try
    '            Dim value As Object = reader(columnName)
    '            If value Is DBNull.Value OrElse value Is Nothing Then
    '                Return ""
    '            Else
    '                Return value.ToString()
    '            End If
    '        Catch
    '            Return ""
    '        End Try
    '    End Function

    '    ''' <summary>
    '    ''' Display user information
    '    ''' </summary>
    '    Private Sub DisplayUserInformation(ByVal userData As UserData)
    '        lblUserId.Text = userData.UserId
    '        lblUserName.Text = IIf(String.IsNullOrEmpty(userData.Name), "Tiada nama", userData.Name).ToString()
    '        lblUserEmail.Text = IIf(String.IsNullOrEmpty(userData.Email), "Tiada email", userData.Email).ToString()

    '        ' Format status
    '        Dim statusText As String = "Tidak Diketahui"
    '        Select Case userData.Status
    '            Case "1"
    '                statusText = "Aktif"
    '            Case "0"
    '                statusText = "Tidak Aktif"
    '        End Select
    '        lblUserStatus.Text = statusText

    '        pnlUserInfo.Visible = True
    '    End Sub

    '    ''' <summary>
    '    ''' Show password action controls
    '    ''' </summary>
    '    Private Sub ShowPasswordActions()
    '        pnlPasswordActions.Visible = True
    '    End Sub

    '    ''' <summary>
    '    ''' Show password result
    '    ''' </summary>
    '    Private Sub ShowPasswordResult(ByVal password As String)
    '        lblGeneratedPassword.Text = password
    '        pnlPasswordResult.Visible = True
    '    End Sub

    '    ''' <summary>
    '    ''' Clear all panels
    '    ''' </summary>
    '    Private Sub ClearAllPanels()
    '        pnlUserInfo.Visible = False
    '        pnlPasswordActions.Visible = False
    '        pnlPasswordResult.Visible = False
    '    End Sub

    '    ''' <summary>
    '    ''' Clear password input fields
    '    ''' </summary>
    '    Private Sub ClearPasswordInputs()
    '        txtNewPassword.Text = ""
    '    End Sub

    '    ''' <summary>
    '    ''' Show message to user
    '    ''' </summary>
    '    Private Sub ShowMessage(ByVal message As String, ByVal messageType As String)
    '        lblMessage.Text = message
    '        pnlMessage.Visible = True

    '        ' Set CSS class based on message type
    '        Select Case messageType.ToLower()
    '            Case "success"
    '                divMessage.Attributes("class") = "message-box message-success"
    '            Case "error"
    '                divMessage.Attributes("class") = "message-box message-error"
    '            Case "info"
    '                divMessage.Attributes("class") = "message-box message-info"
    '            Case Else
    '                divMessage.Attributes("class") = "message-box message-info"
    '        End Select
    '    End Sub

    'End Class

    '''' <summary>
    '''' User data structure - .NET 3.5.1 compatible
    '''' </summary>
    'Public Class UserData
    '    Private _userId As String = ""
    '    Private _name As String = ""
    '    Private _email As String = ""
    '    Private _status As String = ""
    '    Private _module As String = ""
    '    Private _access As String = ""

    '    Public Property UserId() As String
    '        Get
    '            Return _userId
    '        End Get
    '        Set(ByVal value As String)
    '            _userId = value
    '        End Set
    '    End Property

    '    Public Property Name() As String
    '        Get
    '            Return _name
    '        End Get
    '        Set(ByVal value As String)
    '            _name = value
    '        End Set
    '    End Property

    '    Public Property Email() As String
    '        Get
    '            Return _email
    '        End Get
    '        Set(ByVal value As String)
    '            _email = value
    '        End Set
    '    End Property

    '    Public Property Status() As String
    '        Get
    '            Return _status
    '        End Get
    '        Set(ByVal value As String)
    '            _status = value
    '        End Set
    '    End Property

    '    Public Property UserModule() As String
    '        Get
    '            Return _module
    '        End Get
    '        Set(ByVal value As String)
    '            _module = value
    '        End Set
    '    End Property

    '    Public Property Access() As String
    '        Get
    '            Return _access
    '        End Get

    '        Set(ByVal value As String)
    '            _access = value
    '        End Set
    '    End Property
End Class
