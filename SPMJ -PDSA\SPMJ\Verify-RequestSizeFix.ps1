# SPMJ Request Size Verification Script
# This script helps verify the Web.config changes for request size limits

Write-Host "=== SPMJ Request Size Configuration Verification ===" -ForegroundColor Green
Write-Host ""

$webConfigPath = "Web.config"

if (-not (Test-Path $webConfigPath)) {
    Write-Host "ERROR: Web.config not found in current directory!" -ForegroundColor Red
    Write-Host "Please run this script from the SPMJ application root directory." -ForegroundColor Yellow
    exit 1
}

Write-Host "Reading Web.config..." -ForegroundColor Cyan

try {
    [xml]$webConfig = Get-Content $webConfigPath
    
    # Check system.web httpRuntime settings
    $httpRuntime = $webConfig.configuration.'system.web'.httpRuntime
    if ($httpRuntime) {
        Write-Host "✓ Found system.web httpRuntime configuration" -ForegroundColor Green
        Write-Host "  - maxRequestLength: $($httpRuntime.maxRequestLength) KB" -ForegroundColor White
        Write-Host "  - requestLengthDiskThreshold: $($httpRuntime.requestLengthDiskThreshold) KB" -ForegroundColor White
        Write-Host "  - executionTimeout: $($httpRuntime.executionTimeout) seconds" -ForegroundColor White
        
        if ($httpRuntime.maxRequestLength -eq "51200") {
            Write-Host "  ✓ Request size limit correctly set to 50MB" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ Request size limit not set to expected 50MB (51200 KB)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠ httpRuntime configuration not found in system.web" -ForegroundColor Yellow
    }
    
    Write-Host ""
    
    # Check system.webServer requestFiltering settings
    $requestLimits = $webConfig.configuration.'system.webServer'.security.requestFiltering.requestLimits
    if ($requestLimits) {
        Write-Host "✓ Found system.webServer requestFiltering configuration" -ForegroundColor Green
        Write-Host "  - maxAllowedContentLength: $($requestLimits.maxAllowedContentLength) bytes" -ForegroundColor White
        
        if ($requestLimits.maxAllowedContentLength -eq "52428800") {
            Write-Host "  ✓ IIS request size limit correctly set to 50MB" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ IIS request size limit not set to expected 50MB (52428800 bytes)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠ requestFiltering configuration not found in system.webServer" -ForegroundColor Yellow
    }
    
    Write-Host ""
    
    # Check MaxHttpCollectionKeys setting
    $maxHttpKeys = $webConfig.configuration.appSettings.add | Where-Object { $_.key -eq "aspnet:MaxHttpCollectionKeys" }
    if ($maxHttpKeys) {
        Write-Host "✓ Found aspnet:MaxHttpCollectionKeys setting" -ForegroundColor Green
        Write-Host "  - Value: $($maxHttpKeys.value)" -ForegroundColor White
    } else {
        Write-Host "⚠ aspnet:MaxHttpCollectionKeys setting not found" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "=== Summary ===" -ForegroundColor Green
    Write-Host "Configuration appears to be set up for handling large requests." -ForegroundColor White
    Write-Host "The 'request entity is too large' error should be resolved." -ForegroundColor White
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Restart IIS/Application Pool" -ForegroundColor White
    Write-Host "2. Test PILIH buttons on maintenance pages" -ForegroundColor White
    Write-Host "3. Monitor application performance" -ForegroundColor White
    
} catch {
    Write-Host "ERROR: Failed to read or parse Web.config" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
