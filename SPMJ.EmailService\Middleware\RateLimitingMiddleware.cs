using Microsoft.Extensions.Caching.Memory;

namespace SPMJ.EmailService.Middleware
{
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IMemoryCache _cache;
        private readonly ILogger<RateLimitingMiddleware> _logger;
        private readonly int _requestsPerMinute;

        public RateLimitingMiddleware(RequestDelegate next, IMemoryCache cache, 
            IConfiguration configuration, ILogger<RateLimitingMiddleware> logger)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
            _requestsPerMinute = configuration.GetValue<int>("ApiSettings:RateLimitPerMinute", 60);
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var clientIp = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            var cacheKey = $"rate_limit_{clientIp}";
            
            // Skip rate limiting for health check
            if (context.Request.Path.ToString().ToLower().Contains("/health"))
            {
                await _next(context);
                return;
            }

            var requestCount = _cache.Get<int>(cacheKey);
            
            if (requestCount >= _requestsPerMinute)
            {
                _logger.LogWarning("Rate limit exceeded for {ClientIP}. Requests: {RequestCount}", 
                    clientIp, requestCount);
                
                context.Response.StatusCode = 429; // Too Many Requests
                await context.Response.WriteAsync("Rate limit exceeded. Try again later.");
                return;
            }

            // Increment counter
            _cache.Set(cacheKey, requestCount + 1, TimeSpan.FromMinutes(1));
            
            await _next(context);
        }
    }
}
