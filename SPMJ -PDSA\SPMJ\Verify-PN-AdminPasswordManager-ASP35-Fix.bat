@echo off
echo ===============================================================================
echo PN_AdminPasswordManager ASP.NET 3.5 Compatibility Fix - Final Verification
echo ===============================================================================
echo.

echo [STEP 1] Clearing ASP.NET temporary files...
echo.
if exist "%WINDOWS%\Microsoft.NET\Framework\v2.0.50727\Temporary ASP.NET Files\*" (
    for /d %%i in ("%WINDOWS%\Microsoft.NET\Framework\v2.0.50727\Temporary ASP.NET Files\*") do (
        echo Clearing: %%i
        rmdir /s /q "%%i" 2>nul
    )
)

echo.
echo [STEP 2] Clearing project bin folder...
cd /d "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ"
if exist "bin\SPMJ.dll" del "bin\SPMJ.dll"
if exist "bin\SPMJ.pdb" del "bin\SPMJ.pdb"

echo.
echo [STEP 3] Checking file structure...
echo.
echo ASPX Page:
if exist "PN_AdminPasswordManager.aspx" (
    echo    ✓ PN_AdminPasswordManager.aspx - EXISTS
    findstr /C:"Inherits=\"PN_AdminPasswordManager\"" PN_AdminPasswordManager.aspx >nul
    if %ERRORLEVEL%==0 (
        echo    ✓ Page directive correctly references PN_AdminPasswordManager
    ) else (
        echo    ✗ Page directive issue detected
    )
) else (
    echo    ✗ PN_AdminPasswordManager.aspx - MISSING
)

echo.
echo Code-behind:
if exist "PN_AdminPasswordManager.aspx.vb" (
    echo    ✓ PN_AdminPasswordManager.aspx.vb - EXISTS
    findstr /C:"Partial Public Class PN_AdminPasswordManager" PN_AdminPasswordManager.aspx.vb >nul
    if %ERRORLEVEL%==0 (
        echo    ✓ Class declaration found
    ) else (
        echo    ✗ Class declaration issue
    )
    findstr /C:"Namespace" PN_AdminPasswordManager.aspx.vb >nul
    if %ERRORLEVEL%==0 (
        echo    ✗ WARNING: Namespace still present (should be removed for ASP.NET 3.5)
    ) else (
        echo    ✓ No namespace (correct for ASP.NET 3.5)
    )
) else (
    echo    ✗ PN_AdminPasswordManager.aspx.vb - MISSING
)

echo.
echo Designer:
if exist "PN_AdminPasswordManager.aspx.designer.vb" (
    echo    ✓ PN_AdminPasswordManager.aspx.designer.vb - EXISTS
    findstr /C:"Protected WithEvents" PN_AdminPasswordManager.aspx.designer.vb >nul
    if %ERRORLEVEL%==0 (
        echo    ✓ Control declarations found
    ) else (
        echo    ✗ Control declaration issue
    )
) else (
    echo    ✗ PN_AdminPasswordManager.aspx.designer.vb - MISSING
)

echo.
echo [STEP 4] Attempting compilation...
echo.
"%WINDIR%\Microsoft.NET\Framework\v3.5\MSBuild.exe" SPMJ.vbproj /p:Configuration=Debug /p:Platform=AnyCPU /verbosity:minimal

echo.
echo [STEP 5] Checking compilation result...
echo.
if exist "bin\SPMJ.dll" (
    echo ===============================================================================
    echo    ✓ SUCCESS: Compilation completed successfully!
    echo ===============================================================================
    echo.
    echo    PN_AdminPasswordManager should now work without parser errors.
    echo    
    echo    Next steps:
    echo    1. Restart your web application or IIS
    echo    2. Access the page at: /PN_AdminPasswordManager.aspx
    echo    3. Verify that all controls are working properly
    echo.
    echo    If you still get parser errors:
    echo    - Open the project in Visual Studio 2017
    echo    - Clean and rebuild the entire solution
    echo    - Check that the web.config is properly configured for .NET 3.5
    echo.
) else (
    echo ===============================================================================
    echo    ✗ COMPILATION FAILED
    echo ===============================================================================
    echo.
    echo    This indicates there may still be syntax errors in the code.
    echo    Please check the MSBuild output above for specific error messages.
    echo    
    echo    Common fixes:
    echo    1. Open Visual Studio 2017 and check for compilation errors
    echo    2. Ensure all references are properly configured
    echo    3. Verify the EmailServiceClient class exists and is accessible
    echo.
)

echo.
echo Press any key to continue...
pause >nul
