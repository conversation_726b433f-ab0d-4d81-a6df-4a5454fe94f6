# SPMJ Email Service Integration Testing Script
# This script validates the complete email service integration

param(
    [string]$EmailServiceUrl = "http://localhost:5000",
    [string]$SPMJUrl = "http://localhost:8080",
    [switch]$SkipDatabaseCheck,
    [switch]$TestEmailOnly,
    [switch]$Verbose
)

Write-Host "🚀 SPMJ Email Service Integration Test" -ForegroundColor Green
Write-Host "=" * 50

# Function to test API endpoint
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Body = @{},
        [string]$Description
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    
    try {
        if ($Method -eq "GET") {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -TimeoutSec 10
        } else {
            $jsonBody = $Body | ConvertTo-Json
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $jsonBody -ContentType "application/json" -TimeoutSec 10
        }
        
        Write-Host "✅ SUCCESS: $Description" -ForegroundColor Green
        if ($Verbose) {
            Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
        }
        return $true
    }
    catch {
        Write-Host "❌ FAILED: $Description" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test database connectivity
function Test-DatabaseConnection {
    Write-Host "`n📊 Testing Database Connection..." -ForegroundColor Cyan
    
    $testUrl = "$EmailServiceUrl/api/health"
    return Test-ApiEndpoint -Url $testUrl -Description "Database Health Check"
}

# Function to test email service APIs
function Test-EmailServiceAPIs {
    Write-Host "`n📧 Testing Email Service APIs..." -ForegroundColor Cyan
    
    $results = @{}
    
    # Test Health Check
    $results.Health = Test-ApiEndpoint -Url "$EmailServiceUrl/api/health" -Description "Health Check API"
    
    # Test Swagger Documentation
    $results.Swagger = Test-ApiEndpoint -Url "$EmailServiceUrl/swagger/index.html" -Description "Swagger Documentation"
    
    # Test Password Reset Request (without actual email sending)
    $resetBody = @{
        Email = "<EMAIL>"
    }
    $results.PasswordReset = Test-ApiEndpoint -Url "$EmailServiceUrl/api/password/request-reset" -Method "POST" -Body $resetBody -Description "Password Reset Request API"
    
    # Test OTP Generation (without actual email sending)
    $otpBody = @{
        UserId = "test-user-id"
        Email = "<EMAIL>"
    }
    $results.OtpGeneration = Test-ApiEndpoint -Url "$EmailServiceUrl/api/otp/generate" -Method "POST" -Body $otpBody -Description "OTP Generation API"
    
    return $results
}

# Function to test .NET 3.5 integration
function Test-SPMJIntegration {
    Write-Host "`n🔗 Testing SPMJ .NET 3.5 Integration..." -ForegroundColor Cyan
    
    $results = @{}
    
    # Test if SPMJ application is accessible
    try {
        $response = Invoke-WebRequest -Uri $SPMJUrl -TimeoutSec 10 -UseBasicParsing
        $results.SPMJAccessible = $true
        Write-Host "✅ SUCCESS: SPMJ Application is accessible" -ForegroundColor Green
    }
    catch {
        $results.SPMJAccessible = $false
        Write-Host "❌ FAILED: SPMJ Application not accessible at $SPMJUrl" -ForegroundColor Red
        Write-Host "Note: Make sure IIS/IIS Express is running" -ForegroundColor Yellow
    }
    
    # Test specific pages if SPMJ is accessible
    if ($results.SPMJAccessible) {
        $pages = @(
            @{ Url = "$SPMJUrl/PasswordResetModern.aspx"; Name = "Password Reset Page" },
            @{ Url = "$SPMJUrl/OtpVerification.aspx"; Name = "OTP Verification Page" },
            @{ Url = "$SPMJUrl/AdminPasswordManager.aspx"; Name = "Admin Password Manager" }
        )
        
        foreach ($page in $pages) {
            try {
                $response = Invoke-WebRequest -Uri $page.Url -TimeoutSec 10 -UseBasicParsing
                $results[$page.Name] = $true
                Write-Host "✅ SUCCESS: $($page.Name) is accessible" -ForegroundColor Green
            }
            catch {
                $results[$page.Name] = $false
                Write-Host "❌ FAILED: $($page.Name) not accessible" -ForegroundColor Red
            }
        }
    }
    
    return $results
}

# Function to check configuration files
function Test-Configuration {
    Write-Host "`n⚙️ Testing Configuration..." -ForegroundColor Cyan
    
    $results = @{}
    
    # Check Email Service appsettings.json
    $appSettingsPath = "d:\source_code\.NET 3.5. - O\SPMJ -PDSA\SPMJ.EmailService\appsettings.json"
    if (Test-Path $appSettingsPath) {
        try {
            $config = Get-Content $appSettingsPath | ConvertFrom-Json
            $results.EmailServiceConfig = $true
            Write-Host "✅ SUCCESS: Email Service configuration found" -ForegroundColor Green
            
            # Check critical settings
            if ($config.EmailSettings.SmtpServer -eq "smtp.gmail.com" -and 
                $config.EmailSettings.Username -eq "<EMAIL>") {
                Write-Host "⚠️  WARNING: Email settings still use default values" -ForegroundColor Yellow
                Write-Host "   Please update SMTP settings in appsettings.json" -ForegroundColor Yellow
            }
        }
        catch {
            $results.EmailServiceConfig = $false
            Write-Host "❌ FAILED: Invalid Email Service configuration" -ForegroundColor Red
        }
    } else {
        $results.EmailServiceConfig = $false
        Write-Host "❌ FAILED: Email Service configuration not found" -ForegroundColor Red
    }
    
    # Check SPMJ Web.config
    $webConfigPath = "d:\source_code\.NET 3.5. - O\SPMJ -PDSA\SPMJ\Web.config"
    if (Test-Path $webConfigPath) {
        try {
            $webConfig = [xml](Get-Content $webConfigPath)
            $emailServiceSetting = $webConfig.configuration.appSettings.add | Where-Object { $_.key -eq "EmailServiceUrl" }
            
            if ($emailServiceSetting) {
                $results.SPMJConfig = $true
                Write-Host "✅ SUCCESS: SPMJ Web.config has email service settings" -ForegroundColor Green
            } else {
                $results.SPMJConfig = $false
                Write-Host "❌ FAILED: EmailServiceUrl not found in Web.config" -ForegroundColor Red
            }
        }
        catch {
            $results.SPMJConfig = $false
            Write-Host "❌ FAILED: Error reading SPMJ Web.config" -ForegroundColor Red
        }
    } else {
        $results.SPMJConfig = $false
        Write-Host "❌ FAILED: SPMJ Web.config not found" -ForegroundColor Red
    }
    
    return $results
}

# Function to display deployment status
function Show-DeploymentStatus {
    Write-Host "`n📋 Deployment Status Summary" -ForegroundColor Cyan
    Write-Host "=" * 30
    
    # Check if database migration script exists
    $migrationScript = "d:\source_code\.NET 3.5. - O\SPMJ -PDSA\SPMJ.EmailService\Database_EmailService_Migration.sql"
    if (Test-Path $migrationScript) {
        Write-Host "✅ Database migration script ready" -ForegroundColor Green
    } else {
        Write-Host "❌ Database migration script missing" -ForegroundColor Red
    }
    
    # Check if deployment script exists
    $deployScript = "d:\source_code\.NET 3.5. - O\SPMJ -PDSA\Deploy-EmailService.ps1"
    if (Test-Path $deployScript) {
        Write-Host "✅ Deployment script ready" -ForegroundColor Green
    } else {
        Write-Host "❌ Deployment script missing" -ForegroundColor Red
    }
    
    # Check if documentation exists
    $deployGuide = "d:\source_code\.NET 3.5. - O\SPMJ -PDSA\SPMJ.EmailService\DEPLOYMENT_GUIDE.md"
    if (Test-Path $deployGuide) {
        Write-Host "✅ Deployment guide available" -ForegroundColor Green
    } else {
        Write-Host "❌ Deployment guide missing" -ForegroundColor Red
    }
    
    Write-Host "`n📝 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Execute database migration script" -ForegroundColor White
    Write-Host "2. Configure SMTP settings in appsettings.json" -ForegroundColor White
    Write-Host "3. Update user email addresses in pn_pengguna table" -ForegroundColor White
    Write-Host "4. Deploy microservice to production environment" -ForegroundColor White
    Write-Host "5. Test end-to-end integration" -ForegroundColor White
}

# Main execution
$totalTests = 0
$passedTests = 0

Write-Host "Email Service URL: $EmailServiceUrl" -ForegroundColor Gray
Write-Host "SPMJ Application URL: $SPMJUrl" -ForegroundColor Gray
Write-Host ""

# Test database connection
if (-not $SkipDatabaseCheck) {
    $dbResult = Test-DatabaseConnection
    $totalTests++
    if ($dbResult) { $passedTests++ }
}

# Test email service APIs
$emailResults = Test-EmailServiceAPIs
$totalTests += $emailResults.Count
$passedTests += ($emailResults.Values | Where-Object { $_ -eq $true }).Count

# Test SPMJ integration (unless testing email only)
if (-not $TestEmailOnly) {
    $spmjResults = Test-SPMJIntegration
    $totalTests += $spmjResults.Count
    $passedTests += ($spmjResults.Values | Where-Object { $_ -eq $true }).Count
}

# Test configuration
$configResults = Test-Configuration
$totalTests += $configResults.Count
$passedTests += ($configResults.Values | Where-Object { $_ -eq $true }).Count

# Show deployment status
Show-DeploymentStatus

# Final summary
Write-Host "`n🎯 Test Summary" -ForegroundColor Cyan
Write-Host "=" * 20
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $($totalTests - $passedTests)" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 1))%" -ForegroundColor Yellow

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 All tests passed! Integration is ready for production." -ForegroundColor Green
} elseif ($passedTests / $totalTests -gt 0.8) {
    Write-Host "`n⚠️  Most tests passed. Check failed items above." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ Multiple issues detected. Review configuration and setup." -ForegroundColor Red
}

Write-Host "`nFor detailed deployment instructions, see:" -ForegroundColor Gray
Write-Host "DEPLOYMENT_GUIDE.md in the SPMJ.EmailService folder" -ForegroundColor Gray
