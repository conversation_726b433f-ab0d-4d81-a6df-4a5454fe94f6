# 🚀 SPMJ KOLEJ-PDSA PRODUCTION DEPLOYMENT GUIDE

## 📋 **PRE-DEPLOYMENT CHECKLIST**

### **✅ Prerequisites**
- [ ] **Database Backup**: Complete backup of SPMJ_PDSA database
- [ ] **Application Backup**: Backup of current application files
- [ ] **OTP Microservice**: Email service running and tested
- [ ] **Maintenance Window**: Scheduled downtime for deployment
- [ ] **Rollback Plan**: Tested rollback procedures ready

### **✅ Required Files**
- [ ] `PRODUCTION_ALTER_SCRIPT_kj_pengguna.sql` - Main enhancement script
- [ ] `PRODUCTION_ROLLBACK_SCRIPT_kj_pengguna.sql` - Rollback script (if needed)
- [ ] Enhanced application files (Login_J.aspx, Login_J.aspx.vb, etc.)
- [ ] Updated web.config with email service settings

---

## 🗄️ **DATABASE DEPLOYMENT**

### **Step 1: Create Backup**
```sql
-- Create full database backup
BACKUP DATABASE [SPMJ_PDSA] 
TO DISK = 'C:\Backup\SPMJ_PDSA_PreEnhancement_20250627.bak'
WITH FORMAT, INIT, COMPRESSION;

-- Create table-specific backup
SELECT * INTO kj_pengguna_backup_20250627 FROM kj_pengguna;
```

### **Step 2: Execute Enhancement Script**
```bash
# Using SQL Server Management Studio:
# 1. Open PRODUCTION_ALTER_SCRIPT_kj_pengguna.sql
# 2. Connect to production database
# 3. Execute script
# 4. Verify all steps completed successfully

# Using Command Line:
sqlcmd -S [ServerName] -d SPMJ_PDSA -E -i "PRODUCTION_ALTER_SCRIPT_kj_pengguna.sql"
```

### **Step 3: Verify Database Changes**
```sql
-- Verify all required columns exist
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'kj_pengguna' 
  AND COLUMN_NAME IN (
    'email', 'salt', 'pwd_encrypted', 'pwd_migrated',
    'failed_login_attempts', 'account_locked'
  )
ORDER BY COLUMN_NAME;

-- Check record count (should be unchanged)
SELECT COUNT(*) as total_users FROM kj_pengguna;

-- Verify indexes created
SELECT name, type_desc FROM sys.indexes 
WHERE object_id = OBJECT_ID('kj_pengguna') 
  AND name LIKE 'IX_kj_pengguna_%';
```

---

## 💻 **APPLICATION DEPLOYMENT**

### **Step 1: Deploy Application Files**
```bash
# Backup current application
xcopy "C:\inetpub\wwwroot\SPMJ" "C:\Backup\SPMJ_App_Backup_20250627\" /E /I

# Deploy enhanced files
copy "Login_J.aspx" "C:\inetpub\wwwroot\SPMJ\"
copy "Login_J.aspx.vb" "C:\inetpub\wwwroot\SPMJ\"
copy "OtpVerification.aspx" "C:\inetpub\wwwroot\SPMJ\"
copy "OtpVerification.aspx.vb" "C:\inetpub\wwwroot\SPMJ\"
# ... other enhanced files
```

### **Step 2: Update Configuration**
```xml
<!-- Add to web.config appSettings section -->
<add key="EmailServiceUrl" value="http://localhost:5000" />
<add key="OtpEnabled" value="true" />
<add key="OtpExpirationMinutes" value="10" />
```

### **Step 3: Restart Application**
```bash
# Restart IIS Application Pool
iisreset /restart

# Or restart specific application pool
%windir%\system32\inetsrv\appcmd recycle apppool "DefaultAppPool"
```

---

## 🔧 **OTP MICROSERVICE DEPLOYMENT**

### **Step 1: Verify Service Status**
```bash
# Check if service is running
netstat -an | findstr :5000

# Test health endpoint
curl http://localhost:5000/health
# Expected response: {"status":"healthy","timestamp":"..."}
```

### **Step 2: Start Service (if not running)**
```bash
# Navigate to service directory
cd "C:\SPMJ.EmailService"

# Start service
dotnet run --launch-profile Production
```

### **Step 3: Configure Service Settings**
```json
// appsettings.Production.json
{
  "EmailSettings": {
    "SmtpServer": "your-smtp-server.com",
    "SmtpPort": 587,
    "Username": "<EMAIL>",
    "Password": "your-app-password",
    "FromEmail": "<EMAIL>",
    "FromName": "SPMJ KOLEJ-PDSA System"
  },
  "OtpSettings": {
    "ExpirationMinutes": 10,
    "CodeLength": 6,
    "MaxAttempts": 3
  }
}
```

---

## 🧪 **POST-DEPLOYMENT TESTING**

### **Step 1: Basic System Tests**
```bash
# Test database connectivity
# Navigate to: http://your-server/QuickTestKolejLogin.aspx
# Click "Run Quick System Check"
# Verify all checks pass
```

### **Step 2: User Authentication Tests**
```bash
# Test 1: Create test user with encrypted password
# Navigate to: http://your-server/QuickTestKolejLogin.aspx
# Click "Create Test User"
# Verify user created successfully

# Test 2: Test login flow
# Navigate to: http://your-server/Login_J.aspx
# Login with test credentials
# Verify OTP email sent
# Complete OTP verification
# Verify redirect to blank.aspx
```

### **Step 3: Password Recovery Tests**
```bash
# Test password recovery
# Navigate to: http://your-server/Login_J.aspx
# Click "Lupa Kata Laluan?"
# Enter test user ID
# Verify temporary password email sent
# Test login with temporary password
```

### **Step 4: Security Tests**
```bash
# Test account lockout
# Attempt login with wrong password 5 times
# Verify account gets locked
# Verify appropriate error message shown

# Test OTP expiration
# Generate OTP but don't use immediately
# Wait for expiration period
# Verify expired OTP is rejected
```

---

## 🚨 **ROLLBACK PROCEDURES**

### **If Database Rollback Needed**
```sql
-- Option 1: Execute rollback script
-- 1. Edit PRODUCTION_ROLLBACK_SCRIPT_kj_pengguna.sql
-- 2. Comment out the safety check line
-- 3. Execute the script

-- Option 2: Restore from backup
RESTORE DATABASE [SPMJ_PDSA] 
FROM DISK = 'C:\Backup\SPMJ_PDSA_PreEnhancement_20250627.bak'
WITH REPLACE;
```

### **If Application Rollback Needed**
```bash
# Restore application files from backup
xcopy "C:\Backup\SPMJ_App_Backup_20250627\*" "C:\inetpub\wwwroot\SPMJ\" /E /Y

# Restart IIS
iisreset /restart
```

---

## 📊 **MONITORING & MAINTENANCE**

### **Daily Monitoring**
- [ ] Check OTP microservice health: `http://localhost:5000/health`
- [ ] Monitor failed login attempts in database
- [ ] Check email service logs for delivery issues
- [ ] Verify no account lockouts without cause

### **Weekly Maintenance**
- [ ] Clean up expired OTP tokens (automatic via stored procedure)
- [ ] Review security audit logs
- [ ] Check system performance metrics
- [ ] Verify backup procedures working

### **Monthly Review**
- [ ] Analyze login patterns and security metrics
- [ ] Review and update email templates if needed
- [ ] Check for system updates and security patches
- [ ] Test disaster recovery procedures

---

## 📞 **SUPPORT CONTACTS**

### **Technical Issues**
- **Database Issues**: DBA Team
- **Application Issues**: Development Team  
- **Email Service Issues**: Infrastructure Team
- **Security Concerns**: Security Team

### **Emergency Procedures**
1. **Immediate Issues**: Execute rollback procedures
2. **Service Outage**: Check OTP microservice status
3. **Security Breach**: Disable affected accounts, review logs
4. **Data Issues**: Restore from backup, investigate cause

---

## ✅ **DEPLOYMENT SUCCESS CRITERIA**

### **Database Enhancement**
- [x] All required columns added to kj_pengguna table
- [x] Indexes created for performance
- [x] Default values initialized for existing records
- [x] No data loss or corruption

### **Application Enhancement**
- [x] Enhanced login functionality working
- [x] OTP verification flow operational
- [x] Password recovery system functional
- [x] All existing functionality preserved

### **Security Enhancement**
- [x] Encrypted password authentication working
- [x] Account lockout protection active
- [x] Audit trail logging operational
- [x] Email service integration functional

### **User Experience**
- [x] Login flow smooth and intuitive
- [x] Error messages clear and helpful
- [x] Password recovery easy to use
- [x] OTP verification user-friendly

---

## 🎉 **DEPLOYMENT COMPLETE**

Once all checklist items are verified and tests pass:

**✅ SPMJ KOLEJ-PDSA Enhanced Login System is LIVE!**

The system now provides:
- 🔐 **Enterprise-grade security** with encrypted passwords
- 📧 **OTP verification** for enhanced authentication
- 🔑 **Password recovery** with email integration
- 📊 **Comprehensive security monitoring** and audit trail

**Production deployment successful! 🚀**
