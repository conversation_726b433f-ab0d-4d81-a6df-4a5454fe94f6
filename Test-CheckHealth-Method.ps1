# Test the EmailServiceClient.CheckHealth method directly
# This simulates what the Pwd.aspx.vb CheckEmailServiceHealth web method does

Write-Host "Testing EmailServiceClient.CheckHealth method" -ForegroundColor Cyan

# Create a simple VB.NET test script to simulate the CheckHealth call
$vbTestCode = @'
Imports System.Net
Imports System.IO

Module TestHealthCheck
    Sub Main()
        Try
            Dim baseUrl As String = "http://localhost:5000"
            
            ' Simulate the CheckHealth method
            Dim url As String = baseUrl & "/health"
            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
            request.Method = "GET"
            request.Timeout = 5000
            
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                Using reader As New StreamReader(response.GetResponseStream())
                    Dim responseText As String = reader.ReadToEnd()
                    Dim isHealthy As Boolean = responseText.Contains("healthy") AndAlso response.StatusCode = 200
                    
                    Console.WriteLine("Health Check Response: " & responseText)
                    Console.WriteLine("Contains 'healthy': " & responseText.Contains("healthy"))
                    Console.WriteLine("Status Code: " & response.StatusCode)
                    Console.WriteLine("Is Healthy: " & isHealthy)
                    
                    If isHealthy Then
                        Console.WriteLine("JSON Result: {""status"":""online"",""message"":""Email service is operational""}")
                    Else
                        Console.WriteLine("JSON Result: {""status"":""offline"",""message"":""Email service is not responding""}")
                    End If
                End Using
            End Using
            
        Catch ex As Exception
            Console.WriteLine("Error: " & ex.Message)
            Console.WriteLine("JSON Result: {""status"":""offline"",""message"":""Email service connection failed: " & ex.Message & """}")
        End Try
    End Sub
End Module
'@

# Save the VB.NET code to a file
$vbTestCode | Out-File -FilePath "TestHealthCheck.vb" -Encoding UTF8

Write-Host "VB.NET test code saved to TestHealthCheck.vb" -ForegroundColor Green
Write-Host "This simulates the exact same logic as the CheckEmailServiceHealth web method" -ForegroundColor Yellow

# Now test the simulation logic using PowerShell equivalent
Write-Host "`nRunning PowerShell simulation of the health check logic..." -ForegroundColor Yellow

try {
    $baseUrl = "http://localhost:5000"
    $url = $baseUrl + "/health"
    
    $request = [System.Net.WebRequest]::Create($url)
    $request.Method = "GET"
    $request.Timeout = 5000
    
    $response = $request.GetResponse()
    $reader = New-Object System.IO.StreamReader($response.GetResponseStream())
    $responseText = $reader.ReadToEnd()
    
    $containsHealthy = $responseText.Contains("healthy")
    $statusCode = [int]$response.StatusCode
    $isHealthy = $containsHealthy -and ($statusCode -eq 200)
    
    Write-Host "Health Check Response: $responseText" -ForegroundColor White
    Write-Host "Contains 'healthy': $containsHealthy" -ForegroundColor White
    Write-Host "Status Code: $statusCode" -ForegroundColor White
    Write-Host "Is Healthy: $isHealthy" -ForegroundColor White
    
    if ($isHealthy) {
        $jsonResult = "{""status"":""online"",""message"":""Email service is operational""}"
        Write-Host "JSON Result: $jsonResult" -ForegroundColor Green
    } else {
        $jsonResult = "{""status"":""offline"",""message"":""Email service is not responding""}"
        Write-Host "JSON Result: $jsonResult" -ForegroundColor Red
    }
    
    $reader.Close()
    $response.Close()
    
} catch {
    $errorMsg = $_.Exception.Message
    $jsonResult = "{""status"":""offline"",""message"":""Email service connection failed: $errorMsg""}"
    Write-Host "Error: $errorMsg" -ForegroundColor Red
    Write-Host "JSON Result: $jsonResult" -ForegroundColor Red
}

Write-Host "`nTest complete - The CheckHealth method should return 'online' status" -ForegroundColor Cyan
