<%@ Page Title="Admin - Pengurusan <PERSON>" Language="vb" MasterPageFile="~/Main.Master" AutoEventWireup="false" CodeBehind="AdminPasswordManager.aspx.vb" Inherits="SPMJ.AdminPasswordManager" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        .admin-panel {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }
        .admin-header {
            color: #2c5282;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .form-section h4 {
            color: #2c5282;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-weight: 600;
            cursor: pointer;
            margin-right: 10px;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a67d8;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-danger {
            background: #e53e3e;
            color: white;
        }
        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .user-info {
            background: #e6fffa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #81e6d9;
            margin-bottom: 20px;
        }
        .temp-password {
            font-family: monospace;
            font-size: 18px;
            color: #e53e3e;
            font-weight: bold;
            background: #fff5f5;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #fed7d7;
            margin: 10px 0;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="admin-panel">
        <h2 class="admin-header">Pengurusan Kata Laluan Pengguna</h2>
        
        <asp:Panel ID="PanelMessage" runat="server" Visible="false">
            <div class="alert" id="alertDiv" runat="server">
                <asp:Label ID="LabelMessage" runat="server"></asp:Label>
            </div>
        </asp:Panel>

        <!-- User Search Section -->
        <div class="form-section">
            <h4>Cari Pengguna</h4>            <div class="form-group">
                <label for="TextBoxSearchUser">ID Pengguna</label>
                <asp:TextBox ID="TextBoxSearchUser" runat="server" CssClass="form-control" MaxLength="50" ToolTip="Masukkan ID pengguna"></asp:TextBox>
            </div>
            <asp:Button ID="ButtonSearchUser" runat="server" Text="Cari Pengguna" CssClass="btn btn-secondary" />
        </div>

        <!-- User Info Display -->
        <asp:Panel ID="PanelUserInfo" runat="server" Visible="false">
            <div class="user-info">
                <h4>Maklumat Pengguna</h4>
                <p><strong>ID:</strong> <asp:Label ID="LabelUserId" runat="server"></asp:Label></p>
                <p><strong>Nama:</strong> <asp:Label ID="LabelUserName" runat="server"></asp:Label></p>
                <p><strong>Email:</strong> <asp:Label ID="LabelUserEmail" runat="server"></asp:Label></p>
                <p><strong>Status:</strong> <asp:Label ID="LabelUserStatus" runat="server"></asp:Label></p>
            </div>
        </asp:Panel>

        <!-- Password Management Actions -->
        <asp:Panel ID="PanelActions" runat="server" Visible="false">
            
            <!-- Create New Password -->
            <div class="form-section">
                <h4>Tetapkan Kata Laluan Baru</h4>                <div class="form-group">
                    <label for="TextBoxNewPassword">Kata Laluan Sementara</label>
                    <asp:TextBox ID="TextBoxNewPassword" runat="server" CssClass="form-control" MaxLength="100" ToolTip="Masukkan kata laluan sementara"></asp:TextBox>
                    <small style="color: #666;">Sekurang-kurangnya 6 aksara. Biarkan kosong untuk jana automatik.</small>
                </div>                <div class="form-group">
                    <label for="TextBoxUserEmail">Email Pengguna</label>
                    <asp:TextBox ID="TextBoxUserEmail" runat="server" CssClass="form-control" MaxLength="255" TextMode="SingleLine" ToolTip="<EMAIL>"></asp:TextBox>
                    <small style="color: #666;">Email untuk menghantar kata laluan sementara</small>
                </div>
                <asp:Button ID="ButtonCreatePassword" runat="server" Text="Tetapkan Kata Laluan & Hantar Email" CssClass="btn btn-primary" />
            </div>

            <!-- Reset Password -->
            <div class="form-section">
                <h4>Reset Kata Laluan</h4>
                <p>Reset kata laluan pengguna dan hantar kata laluan sementara melalui email.</p>
                <asp:Button ID="ButtonResetPassword" runat="server" Text="Reset Kata Laluan & Hantar Email" CssClass="btn btn-danger" 
                           OnClientClick="return confirm('Adakah anda pasti ingin reset kata laluan pengguna ini?');" />
            </div>

        </asp:Panel>

        <!-- Generated Password Display -->
        <asp:Panel ID="PanelGeneratedPassword" runat="server" Visible="false">
            <div class="form-section">
                <h4>Kata Laluan Sementara Dijana</h4>
                <p>Kata laluan sementara berikut telah ditetapkan untuk pengguna:</p>
                <div class="temp-password">
                    <asp:Label ID="LabelGeneratedPassword" runat="server"></asp:Label>
                </div>
                <p><strong>Nota:</strong> Kata laluan ini telah dihantar ke email pengguna. Pengguna akan diminta menukar kata laluan pada log masuk pertama.</p>
            </div>
        </asp:Panel>

    </div>
</asp:Content>
