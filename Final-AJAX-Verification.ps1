# Final AJAX Controls Verification Script
# Verifies that all AJAX controls have been completely removed from the project

Write-Host "=== FINAL AJAX CONTROLS VERIFICATION ===" -ForegroundColor Green
Write-Host "Checking for any remaining AJAX controls..." -ForegroundColor Yellow

$projectPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ"
$issuesFound = 0

# Check for any remaining AJAX controls
Write-Host "`n1. Checking for ScriptManager controls..." -ForegroundColor Cyan
$scriptManagers = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse | Select-String "asp:ScriptManager"
if ($scriptManagers) {
    Write-Host "❌ Found ScriptManager controls:" -ForegroundColor Red
    $scriptManagers | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" -ForegroundColor Red }
    $issuesFound++
} else {
    Write-Host "✅ No ScriptManager controls found" -ForegroundColor Green
}

Write-Host "`n2. Checking for ScriptManagerProxy controls..." -ForegroundColor Cyan
$scriptManagerProxies = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse | Select-String "asp:ScriptManagerProxy"
if ($scriptManagerProxies) {
    Write-Host "❌ Found ScriptManagerProxy controls:" -ForegroundColor Red
    $scriptManagerProxies | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" -ForegroundColor Red }
    $issuesFound++
} else {
    Write-Host "✅ No ScriptManagerProxy controls found" -ForegroundColor Green
}

Write-Host "`n3. Checking for UpdatePanel controls..." -ForegroundColor Cyan
$updatePanels = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse | Select-String "asp:UpdatePanel"
if ($updatePanels) {
    Write-Host "❌ Found UpdatePanel controls:" -ForegroundColor Red
    $updatePanels | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" -ForegroundColor Red }
    $issuesFound++
} else {
    Write-Host "✅ No UpdatePanel controls found" -ForegroundColor Green
}

Write-Host "`n4. Checking for ContentTemplate tags..." -ForegroundColor Cyan
$contentTemplates = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse | Select-String "ContentTemplate"
if ($contentTemplates) {
    Write-Host "❌ Found ContentTemplate tags:" -ForegroundColor Red
    $contentTemplates | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" -ForegroundColor Red }
    $issuesFound++
} else {
    Write-Host "✅ No ContentTemplate tags found" -ForegroundColor Green
}

Write-Host "`n5. Checking for AsyncPostBackTrigger controls..." -ForegroundColor Cyan
$triggers = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse | Select-String "AsyncPostBackTrigger"
if ($triggers) {
    Write-Host "❌ Found AsyncPostBackTrigger controls:" -ForegroundColor Red
    $triggers | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" -ForegroundColor Red }
    $issuesFound++
} else {
    Write-Host "✅ No AsyncPostBackTrigger controls found" -ForegroundColor Green
}

Write-Host "`n6. Checking master pages..." -ForegroundColor Cyan
$masterAjax = Get-ChildItem $projectPath -Filter "*.master" -Recurse | Select-String "asp:(ScriptManager|UpdatePanel)"
if ($masterAjax) {
    Write-Host "❌ Found AJAX controls in master pages:" -ForegroundColor Red
    $masterAjax | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber)" -ForegroundColor Red }
    $issuesFound++
} else {
    Write-Host "✅ No AJAX controls found in master pages" -ForegroundColor Green
}

# Check specific critical files mentioned in error
Write-Host "`n7. Verifying critical files..." -ForegroundColor Cyan

$criticalFiles = @(
    "Login_J.aspx",
    "Login.aspx", 
    "Pwd.aspx",
    "Kolej.Master"
)

foreach ($file in $criticalFiles) {
    $filePath = Join-Path $projectPath $file
    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        if ($content -match "asp:(ScriptManager|UpdatePanel|AsyncPostBackTrigger)") {
            Write-Host "❌ AJAX controls still found in: $file" -ForegroundColor Red
            $issuesFound++
        } else {
            Write-Host "✅ $file is clean" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Yellow
    }
}

# Summary
Write-Host "`n=== VERIFICATION SUMMARY ===" -ForegroundColor Green
if ($issuesFound -eq 0) {
    Write-Host "✅ SUCCESS: All AJAX controls have been successfully removed!" -ForegroundColor Green
    Write-Host "✅ The parser error should now be resolved." -ForegroundColor Green
    Write-Host "`n📋 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. Test the application in a browser" -ForegroundColor White
    Write-Host "   2. Verify that Login_J.aspx loads without parser errors" -ForegroundColor White
    Write-Host "   3. Test login and password change functionality" -ForegroundColor White
} else {
    Write-Host "❌ ISSUES FOUND: $issuesFound remaining AJAX control references" -ForegroundColor Red
    Write-Host "⚠️  Manual cleanup may be required for some files." -ForegroundColor Yellow
}

Write-Host "`n🔧 Project is now .NET Framework 3.5 compatible!" -ForegroundColor Green
