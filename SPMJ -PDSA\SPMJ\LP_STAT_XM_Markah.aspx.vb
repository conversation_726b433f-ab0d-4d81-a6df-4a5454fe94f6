﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_XM_Markah
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KATEGORI
        Cb_Kategori.Items.Clear()
        Cb_Kategori.Items.Add("(SEMUA SEKTOR)")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = ""
        Cb_Kategori.Items.Add("KERAJAAN")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "1"
        Cb_Kategori.Items.Add("IPTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "3"
        Cb_Kategori.Items.Add("SWASTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "2"

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where (jenis < 3) order by dc_kolej"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA KOLEJ)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'KURSUS
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("(SEMUA JENIS PENDAFTARAN)")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
        Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
        Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
        Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
        Cb_Kursus.Items.Add("KEBIDANAN I")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"
    End Sub
    
    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim SQL, SQL_pilih, X, Tahun, Siri, Id_XM As String
        Dim Jwtn, Jwtn2, Jwtn3 As Integer
        Tahun = "0" : Siri = "0"
        'ID XM
        X = "" : Id_XM = ""
        If Cb_Kolej.SelectedIndex > 0 Then X += " where p.id_kolej=" & Cb_Kolej.SelectedValue
        If Cb_Kursus.SelectedIndex > 0 Then
            If X.Length = 0 Then X += " where " Else X += " and "
            X += " j_xm=" & Cb_Kursus.SelectedIndex
        End If
        If Tx_Tahun.Text <> "" And Tx_Siri.Text <> "" Then
            If X.Length = 0 Then
                X += " where tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            Else
                X += " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            End If
        End If

        If X = "" Then
        Else
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

            'Comment Ori 15092015 -OSH
            'SQL = "select top 1 px.id_xm, tahun, siri from xm_calon xc  inner join pn_xm px on xc.id_xm=px.id_xm inner join pelatih p on p.nokp=xc.nokp " & _
            '      " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
            '       " left join pn_kolej pk on pk.id_kolej=xc.id_pusat " & X & " order by tahun desc, siri desc, j_xm desc"

            'Fixing multiple records display per canidate 15092015 - OSH
            SQL = "select top 1 px.id_xm, tahun, siri from xm_calon xc  inner join pn_xm px on xc.id_xm=px.id_xm inner join pelatih p on p.nokp=xc.nokp " & _
                 " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                  " left join pn_kolej pk on pk.id_kolej=xc.id_pusat and p.status is null" & X & " order by tahun desc, siri desc, j_xm desc"

            Cmd.CommandText = SQL : Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then Id_XM = Rdr(0) : X = " and xc.id_xm=" & Id_XM : Tahun = Rdr(1) : Siri = Rdr(2) Else X = ""
            Rdr.Close() : Cn.Close()
        End If
        'Comment Ori 15092015 -OSH
        'SQL = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join pn_xm px on px.id_xm=xc.id_xm " & _
        '      "left outer join pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and  not(xc.keputusan='T') " & X

        SQL = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join pn_xm px on px.id_xm=xc.id_xm " & _
             "left outer join pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and  not(xc.keputusan='T') and p.status is null " & X
        SQL_pilih = ""
        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL += "" Else SQL_pilih += " and pnk.jenis=" & Cb_Kategori.SelectedIndex 'Kategori
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL += ""
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            'Comment bug report 11092012 by OSH
            'SQL += " and ( pn.id_KOLEJ>=0 and  pn.id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue

            'Fixing 1oss query prefix pn to pnk 11092012 by OSH
            SQL += " and ( pnk.id_KOLEJ>=0 and  pnk.id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue Gorverment MOH

        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            'Comment bug report 11092012 by OSH
            'SQL += "  and ( pn.id_KOLEJ>=101 and  pn.id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue

            'Fixing 1oss query prefix pn to pnk 11092012 by OSH
            SQL += "  and ( pnk.id_KOLEJ>=101 and  pnk.id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue Public University /Institution
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            'Comment bug report 11092012 by OSH
            'SQL += "  and ( pn.id_KOLEJ>=200  and  pn.JENIS<3) " ' Cb_Kategori.SelectedValue

            'Comment bug report 11092012 by OSH
            SQL += "  and ( pnk.id_KOLEJ>=200  and  pnk.JENIS<3) " ' Cb_Kategori.SelectedValue Private University /Institution
        End If

        If Cb_Kolej.SelectedIndex < 1 Then SQL += "" Else SQL_pilih += " and pnk.id_kolej=" & Cb_Kolej.SelectedValue 'Kolej
        If Cb_Kursus.SelectedIndex < 1 Then
            SQL += ""
        Else
            If Cb_Kursus.SelectedIndex = 1 Then Jwtn = 1 : Jwtn2 = 5 : Jwtn3 = 8
            If Cb_Kursus.SelectedIndex = 2 Then Jwtn = 2 : Jwtn2 = 2 : Jwtn3 = 2
            If Cb_Kursus.SelectedIndex = 3 Then Jwtn = 3 : Jwtn2 = 3 : Jwtn3 = 3
            If Cb_Kursus.SelectedIndex = 4 Then Jwtn = 4 : Jwtn2 = 4 : Jwtn3 = 4
            SQL += " and (p.j_kursus = " & Jwtn & " or p.j_kursus = " & Jwtn2 & " or p.j_kursus = " & Jwtn3 & ")" 'kursus
        End If

        SQL = SQL + SQL_pilih
        Jana(SQL, Tahun, Siri)
    End Sub

    Public Sub Jana(ByVal SQL As String, ByVal tahun As String, ByVal siri As String)
        Dim J, J1, J2, J3, J4, J5, J6, J7, J8, J9, J0 As Double
        Dim Tajuk, Tajuk2 As String

        Tajuk = "STATISTIK MARKAH PEPERIKSAAN BAGI " & Cb_Kategori.SelectedItem.Text & ", " & Cb_Kolej.SelectedItem.Text
        Tajuk2 = "KURSUS " & Cb_Kursus.SelectedItem.Text & _
        IIf(tahun > "", " TAHUN " & tahun, "") & _
        IIf(siri > "", " SIRI " & siri, "")

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='3' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='3' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>MARKAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>PERATUS (%)</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'body
        Header = ""
        J1 = 0 : J2 = 0 : J3 = 0 : J4 = 0 : J5 = 0 : J6 = 0 : J7 = 0 : J8 = 0 : J9 = 0 : J0 = 0
        Cmd.CommandText = SQL + " and markah_jum between 90 and 100 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J1 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 80 and 89.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J2 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 70 and 79.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J3 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 60 and 69.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J4 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 50 and 59.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J5 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 40 and 49.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J6 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 30 and 39.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J7 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 20 and 29.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J8 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 10 and 19.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J9 = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = SQL + " and markah_jum between 0 and 9.9 "
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then J0 = Rdr(0)
        Rdr.Close()

        J = J1 + J2 + J3 + J4 + J5 + J6 + J7 + J8 + J9 + J0
        Header += "<tr>"
        Header += "    <td>90 - 100</td> "
        Header += "    <td>" & J1 & "</td>" : If J1 = 0 Then  Else J1 = (J1 / J) * 100
        Header += "    <td>" & J1 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>80 - 89</td> "
        Header += "    <td>" & J2 & "</td>" : If J2 = 0 Then  Else J2 = (J2 / J) * 100
        Header += "    <td>" & J2 & " %</td>"
        Header += "<tr>"
        Header += "    <td>70 - 79</td> "
        Header += "    <td>" & J3 & "</td>" : If J3 = 0 Then  Else J3 = (J3 / J) * 100
        Header += "    <td>" & J3 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>60 - 69</td> "
        Header += "    <td>" & J4 & "</td>" : If J4 = 0 Then  Else J4 = (J4 / J) * 100
        Header += "    <td>" & J4 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>50 - 59</td> "
        Header += "    <td>" & J5 & "</td>" : If J5 = 0 Then  Else J5 = (J5 / J) * 100
        Header += "    <td>" & J5 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>40 - 49</td> "
        Header += "    <td>" & J6 & "</td>" : If J6 = 0 Then  Else J6 = (J6 / J) * 100
        Header += "    <td>" & J6 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>30 - 39</td> "
        Header += "    <td>" & J7 & "</td>" : If J7 = 0 Then  Else J7 = (J7 / J) * 100
        Header += "    <td>" & J7 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>20 - 29</td> "
        Header += "    <td>" & J8 & "</td>" : If J8 = 0 Then  Else J8 = (J8 / J) * 100
        Header += "    <td>" & J8 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>=""10 - 19""</td> "
        Header += "    <td>" & J9 & "</td>" : If J9 = 0 Then  Else J9 = (J9 / J) * 100
        Header += "    <td>" & J9 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td>0 - 9</td> "
        Header += "    <td>" & J0 & "</td>" : If J0 = 0 Then  Else J0 = (J0 / J) * 100
        Header += "    <td>" & J0 & " %</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: right;'>" & J & "</td>"
        Header += "    <td style='vertical-align: middle; text-align: right;'>" & J1 + J2 + J3 + J4 + J5 + J6 + J7 + J8 + J9 + J0 & " %</td>"
        Header += "</tr>"
        Response.Write(Header)

        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub

    Protected Sub Cb_Kategori_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kategori.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String = ""

        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL = "< 3" Else SQL = "=" & Cb_Kategori.SelectedValue
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL = "jenis< 3"
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " ( id_KOLEJ>=0 and  id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += " ( id_KOLEJ>=101 and  id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += " ( id_KOLEJ>=200  and  JENIS<3) " ' Cb_Kategori.SelectedValue
        End If

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where " & SQL & " order by dc_kolej" 'jenis
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub
End Class