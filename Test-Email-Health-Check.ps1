# Test Email Service Health Check from .NET 3.5 Application
# This script tests the exact same calls that the Pwd.aspx page makes

Write-Host "🔍 Testing Email Service Health Check Integration" -ForegroundColor Cyan
Write-Host "=" * 50

# Test 1: Direct service health check
Write-Host "`n1. Testing direct service health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Direct health check: SUCCESS" -ForegroundColor Green
    Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   Content: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "❌ Direct health check: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Test web method call (simulated)
Write-Host "`n2. Testing AJAX web method call..." -ForegroundColor Yellow
try {
    # Create the JSON payload for the web method
    $jsonPayload = "{}"
    $bytes = [System.Text.Encoding]::UTF8.GetBytes($jsonPayload)
    
    $request = [System.Net.WebRequest]::Create("http://localhost:8080/SPMJ/Pwd.aspx/CheckEmailServiceHealth")
    $request.Method = "POST"
    $request.ContentType = "application/json; charset=utf-8"
    $request.ContentLength = $bytes.Length
    
    $requestStream = $request.GetRequestStream()
    $requestStream.Write($bytes, 0, $bytes.Length)
    $requestStream.Close()
    
    $response = $request.GetResponse()
    $responseStream = $response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($responseStream)
    $responseText = $reader.ReadToEnd()
    
    Write-Host "✅ AJAX web method: SUCCESS" -ForegroundColor Green
    Write-Host "   Response: $responseText" -ForegroundColor White
    
    $reader.Close()
    $responseStream.Close()
    $response.Close()
    
} catch {
    Write-Host "❌ AJAX web method: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Note: This may fail if SPMJ application is not running" -ForegroundColor Yellow
}

# Test 3: Check if email service is accessible without auth
Write-Host "`n3. Testing email service without authentication..." -ForegroundColor Yellow
try {
    $request = [System.Net.WebRequest]::Create("http://localhost:5000/health")
    $request.Method = "GET"
    $request.Timeout = 5000
    
    $response = $request.GetResponse()
    $responseStream = $response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($responseStream)
    $responseText = $reader.ReadToEnd()
    
    Write-Host "✅ No-auth health check: SUCCESS" -ForegroundColor Green
    Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   Content: $responseText" -ForegroundColor White
    Write-Host "   Contains 'healthy': $($responseText.Contains('healthy'))" -ForegroundColor White
    
    $reader.Close()
    $responseStream.Close()
    $response.Close()
    
} catch {
    Write-Host "❌ No-auth health check: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n" + "=" * 50
Write-Host "Health Check Diagnostics Complete" -ForegroundColor Cyan
