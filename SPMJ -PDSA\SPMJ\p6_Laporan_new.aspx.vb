Imports System.Data.OleDb
'Imports CrystalDecisions.Shared
'Imports CrystalDecisions.CrystalReports.Engine
'Imports System.Data.DataSet

Partial Public Class p6_Laporan
    Inherits System.Web.UI.Page

    'Sub setTblCon(ByRef rep As CrystalDecisions.Web.Report)
    '    Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
    '    Dim Tloginfo As CrystalDecisions.Shared.TableLogOnInfo
    '    For Each tbCurrent In rep.Database.Tables
    '        Tloginfo = tbCurrent.LogOnInfo
    '        With Tloginfo.ConnectionInfo

    '            .DatabaseName = ConfigurationManager.AppSettings("dB")
    '            .UserID = "sa"
    '            .Password = "aljaroom5621"
    '            .ServerName = ConfigurationManager.AppSettings("IP_App")

    '        End With
    '        tbCurrent.ApplyLogOnInfo(Tloginfo)
    '    Next
    'End Sub
    
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Crystal Reports functionality has been removed
        ' This page now displays a message indicating reports are unavailable
        Response.Write("<script>alert('Crystal Reports functionality has been removed. Please use alternative reporting methods.'); window.history.back();</script>")
    End Sub
End Class
