﻿<%@ Page Title="SPMJ - LANTIK KPSL JUTURAWAT BERDAFTAR SEKTOR AWAM" Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P3_APC_Lantik_KPSL.aspx.vb" Inherits="SPMJ.P3_APC_LANTIK_KPSL" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        .style9
        {
            height: 23px;
        }
        .style12
        {
            height: 21px;
        }
        .style33
    {
            height: 20px;
        }
        p.<PERSON><PERSON>
	{margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	        margin-left: 0in;
            margin-right: 0in;
            margin-top: 0in;
        }
        .style62
        {
            height: 24px;
            width: 158px;
        }
        .style79
        {
            height: 17px;
            width: 158px;
        }
        .style80
        {
            height: 17px;
        }
        .style81
        {
            width: 650px;
            height: 436px;
            left: 0px;
            top: 70px;
            position: static;
        }
        .style82
        {
            height: 23px;
            color: #CC0000;
            padding-right;
        }
        .style83
        {
            height: 20px;
            width: 158px;
        }
        .style87
        {
            width: 158px;
            height: 21px;
        }
        .style90
        {
            height: 19px;
            margin-left: 40px;
            }
        .style92
        {
            height: 22px;
            width: 158px;
        }
        .style96
        {
            width: 158px;
        }
        .style102
        {
            height: 20px;
            width: 14px;
        }
        .style103
        {
            height: 17px;
            width: 36px;
        }
        .style104
        {
            height: 20px;
            width: 36px;
        }
        .style106
    {
        height: 19px;
        width: 14px;
    }
    .style107
    {
        height: 19px;
        width: 36px;
    }
    .style108
    {
        height: 19px;
        width: 158px;
    }
    .style109
    {
        height: 24px;
        width: 36px;
    }
    .style110
    {
        height: 24px;
        width: 14px;
    }
        .style111
    {
        height: 24px;
        }
    .style112
    {
        height: 10px;
        margin-left: 40px;
        }
    .style113
    {
        height: 10px;
        width: 14px;
    }
    .style114
    {
        height: 10px;
        width: 36px;
    }
    .style115
    {
        height: 10px;
        width: 158px;
    }
        .style116
        {
            height: 23px;
            width: 14px;
        }
        .style117
        {
            height: 21px;
            width: 14px;
        }
        .style118
        {
            width: 14px;
        }
        .style119
        {
            height: 17px;
            width: 14px;
        }
        .style120
        {
            height: 23px;
            width: 36px;
        }
        .style121
        {
            width: 158px;
            height: 23px;
        }
        .style126
        {
            height: 103px;
            width: 36px;
        }
        .style127
        {
            width: 158px;
            height: 103px;
        }
        .style128
        {
            height: 103px;
        }
        .style129
        {
            width: 14px;
            height: 103px;
        }
        .style130
        {
            height: 29px;
            width: 36px;
        }
        .style131
        {
            width: 158px;
            height: 29px;
        }
        .style132
        {
            height: 29px;
        }
        .style133
        {
            width: 14px;
            height: 29px;
        }
        .style134
        {
            width: 36px;
        }
        .style135
        {
            height: 22px;
            width: 36px;
        }
        .style136
        {
            height: 22px;
        }
        .style137
        {
            height: 22px;
            width: 14px;
        }
        .style138
        {
            height: 7px;
            width: 36px;
        }
        .style139
        {
            height: 7px;
            width: 158px;
        }
        .style140
        {
            height: 7px;
        }
        .style141
        {
            height: 7px;
            width: 14px;
        }
        .auto-style2 {
            height: 2px;
            width: 36px;
        }
        .auto-style3 {
            height: 2px;
        }
        .auto-style4 {
            height: 2px;
            width: 14px;
        }
        .auto-style5 {
            height: 20px;
            width: 186px;
        }
        .auto-style6 {
            height: 10px;
            width: 186px;
        }
        .auto-style7 {
            height: 19px;
            width: 186px;
        }
        .auto-style8 {
            height: 22px;
            width: 186px;
        }
        .auto-style9 {
            width: 186px;
        }
        .auto-style10 {
            width: 186px;
            height: 23px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Localize ID="Localize1" runat="server"></asp:Localize>
    <div style="width:100%;">
    <table id="Table1" width="100%" style="font-variant: small-caps">
    <br>
    <tr><td>
    
    <table id="Table2" align="center" border="0" cellpadding="-1" cellspacing="0" 
        
            style="border: 1px solid black; margin-left: 0px; font-family: Arial; font-size: 8pt; text-transform: none; line-height: 21px; width: 74%;" 
            bgcolor="White" class="style81">
        <tr>
            <td align="center" bgcolor="#8BB900" 
                valign="top" colspan="4" 
                
                
                
                
                style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #000000; vertical-align: middle; text-align: center; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bolder;" class="style132" >
                PEMBAHARUAN APC - PERLANTIKAN JURURAWAT BERDAFTAR KPSL </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="auto-style5">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style33">
                            <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                            </asp:ScriptManagerProxy>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" 
                class="style102">
                &nbsp;</td>
        </tr>
        <tr style="line-height: 10px">
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style114">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="auto-style6">
                </td>
                                     <td bgcolor="White" class="style112" 
                style="line-height: 5px">
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style113">
            </td>
        </tr>
        <tr style="line-height: 21px">
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style107"><span class="style82">*&nbsp;</span>
            </td>
                <td valign="top" align="left" bgcolor="white" 
                class="auto-style7">
                    <asp:UpdatePanel ID="UpdatePanel21" runat="server">
                    <ContentTemplate>
                    <asp:TextBox ID="lb_NoKP" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="132px" 
                            AutoPostBack="True" Enabled="False" 
                            ReadOnly="True">NO. KAD PENGENALAN</asp:TextBox>
                    </ContentTemplate>
                    </asp:UpdatePanel></td>
                <td bgcolor="White" class="style90">
                                                 <asp:UpdatePanel ID="UpdatePanel7" runat="server">
                                                     <ContentTemplate>
                                                         <asp:TextBox ID="Tx_NoKP" runat="server" AutoPostBack="True" CssClass="std" 
                                                             MaxLength="12" Width="200px" Wrap="False"></asp:TextBox>
                                                     </ContentTemplate>
                                                 </asp:UpdatePanel>
                                     </td><td align="left" bgcolor="#ffffff" valign="top" class="style106">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style135"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="auto-style8">NAMA</td>
            <td bgcolor="White" class="style136">
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style137">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style135">
                </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="auto-style8">TARIKH LANTIKAN JB </TD>
            <td align="left" bgcolor="white" valign="top" class="style136">
                            <asp:TextBox ID="Tx_Tkh_Lantik_JB" runat="server" CssClass="std" 
                    Width="95px" AutoPostBack="True" Wrap="False"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Tkh_Lantik_JB_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Lantik_JB" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Tkh_Lantik_JB_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Tkh_Lantik_JB" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style137">
                                                    </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="auto-style5">no. rujuk surat pelantikan jb </TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:TextBox ID="Tx_No_Rujukan" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
         <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style103"> <span class="style82">*</span>
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="auto-style8">markah cpd </TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        <asp:TextBox ID="Tx_CPD" runat="server" CssClass="std" Width="95px" 
                            AutoPostBack="True"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="auto-style9" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
       
        <tr>
                        <td bgcolor="#ffffff" class="style120">
                            </td>
                        <td bgcolor="#ffffff" class="auto-style10" valign="top">
                            </td>
                        <td bgcolor="#ffffff" class="style9" valign="top">
                <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="96px" />
            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style116">
                            </td>
                    </tr>
        <tr>
            <td bgcolor="#ffffff" class="auto-style2">
                </td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle" class="auto-style3">
                </td>
            <td bgcolor="#ffffff" class="auto-style4">
                </td>
        </tr>
    </table></td></tr></table>
    
    </br></br>
    </div>

</asp:Content>

