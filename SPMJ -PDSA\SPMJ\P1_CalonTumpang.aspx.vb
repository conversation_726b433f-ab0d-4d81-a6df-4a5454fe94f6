﻿'Imports CrystalDecisions.Shared
'Imports CrystalDecisions.CrystalReports.Engine
'Imports System.Data.DataSet
Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
'Imports CrystalDecisions.Shared

Partial Public Class P1_CalonTumpang
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        If Cb_Jenis.Text.Trim = "" Then Cb_Jenis.Focus() : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim tkh_xm As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim Rdr As OleDbDataReader

        tkh_xm = ""
        Cmd.CommandText = "select top 1 convert(char(12), t1_tkh, 103) as 'tkh',tahun,siri from pn_xm where j_xm= " & Cb_Jenis.SelectedValue & " order by tahun desc, siri desc, j_xm desc"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr(2)) Then tkh_xm = " SIRI " & Rdr(2) & " " 'Else tkh_xm = "( TARIKH PEPERIKSAAN : - )"
            If Not IsDBNull(Rdr(1)) Then tkh_xm &= "/" & Rdr(1) ' Else tkh_xm &= "( TARIKH PEPERIKSAAN : - )"
            If Not IsDBNull(Rdr(0)) Then tkh_xm &= " ( TARIKH PEPERIKSAAN : " & Rdr(0) & " )" Else tkh_xm &= "( TARIKH PEPERIKSAAN : - )"
        End If
        'If Rdr.Read Then tkh_xm = "( TARIKH PEPERIKSAAN : " & Rdr(0) & " )" Else tkh_xm = "( TARIKH PEPERIKSAAN : - )"
        Rdr.Close()
        Cn.Close()

        Session("Var_0") = CInt(Cb_Jenis.SelectedValue)
        Session("Var_1") = tkh_xm
        Session("Lpr_Nama") = "Calon_Tumpang"

        Response.Write("<script language='javascript'>win=window.open('P6_Laporan.aspx',null,'width=1000,height=700,top='+ (screen.height-600)/3 +',left='+ (screen.width-800)/2 +'','true');</script>")
    End Sub
End Class