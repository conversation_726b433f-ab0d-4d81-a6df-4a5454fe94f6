<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" />
  <xs:element name="UserInfo">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" minOccurs="0" name="userdet" nillable="true" type="q1:UserDetails" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UserInfoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UserInfoResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:element ref="xs:schema" />
              <xs:any />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PointByCategory">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" minOccurs="0" name="userdet" nillable="true" type="q2:UserDetails" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PointByCategoryResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PointByCategoryResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:element ref="xs:schema" />
              <xs:any />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CumulativePoint">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" minOccurs="0" name="userdet" nillable="true" type="q3:UserDetails" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CumulativePointResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CumulativePointResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ActualPoint">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" minOccurs="0" name="userdet" nillable="true" type="q4:UserDetails" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ActualPointResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ActualPointResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="HcpList">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="HcpListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="HcpListResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:element ref="xs:schema" />
              <xs:any />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PointByYear">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" minOccurs="0" name="userdet" nillable="true" type="q5:UserDetails" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PointByYearResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PointByYearResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:element ref="xs:schema" />
              <xs:any />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>