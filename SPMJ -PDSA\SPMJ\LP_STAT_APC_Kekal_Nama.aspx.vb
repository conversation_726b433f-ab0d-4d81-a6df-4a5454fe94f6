﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_APC_Kekal_Nama
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 5 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'JAWATAN
        With Cb_Jawatan
            .Items.Clear()
            .Items.Add("Jururawat Berdaftar")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("Jururawat Masyarakat")
            .Items.Item(.Items.Count - 1).Value = "2"
            .Items.Add("Penolong Jururawat")
            .Items.Item(.Items.Count - 1).Value = "3"
        End With
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim j_daftar As Integer = Cb_Jawatan.SelectedValue
        Dim L, L1, P, P1, X, X1, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12 As Long
        Dim Tajuk, Tajuk2 As String
        'Comment Original 29052020 - OSH 
        'Tajuk = "Laporan Statistik Pengekalan Nama pada tahun  " & Cb_Jawatan.SelectedItem.Text & ", " & tahun

        'Fix labael title 29052020 - OSH 
        Tajuk = "Laporan Statistik Pengekalan Nama " & Cb_Jawatan.SelectedItem.Text & " Bagi Tahun Operasi " & tahun
        Tajuk2 = "mengikut Jawatan, Bulan, dan Jantina"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        'Response.Write("<table  border=""1"" >")

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='40' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='40' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='3' style='vertical-align: middle; text-align: left;'>PENGEKALAN NAMA</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JANUARI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>FEBRUARI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>MAC</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>APRIL</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>MEI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JUN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JULAI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>OGOS</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SEPTEMBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>OKTOBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>NOVEMBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>DISEMBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Header += "<tr style='vertical-align: middle; text-align: center;'>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        T1 = 0
        Header = ""
        Header += "<tr>"

        'JAN
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count( distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=1 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=1 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count( distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=1 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=1 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count( distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=1 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=1 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T1 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'FEB
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=2 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=2 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=2 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=2 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=2 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=2 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T2 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'MAC
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=3 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=3 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=3 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=3 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=3 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=3 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T3 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'APR
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=4 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=4 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=4 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=4 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=4 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=4 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T4 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'MAY
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=5 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=5 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=5 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=5 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=5 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=5 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T5 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'JUN
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=6 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=6 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=6 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=6 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=6 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=6 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T6 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'JUL
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=7 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=7 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=7 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=7 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=7 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=7 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T7 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'AUG
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=8 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=8 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=8 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=8 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=8 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=8 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T8 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'SEP
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=9 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=9 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=9 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=9 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=9 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=9 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T9 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'OCT
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=10 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=10 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=10 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=10 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=10 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=10 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T10 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'NOV
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=11 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=11 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=11 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=11 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=11 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=11 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T11 = L + P + X : L1 += L : P1 += P : X1 += X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        'DIS
        L = 0 : P = 0 : X = 0
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=12 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=12 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=12 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=12 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()
        'Fixing report based on year operation and months 29052020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and month(log_tkh)=12 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"

        'Comment Origina1 29052020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and month(apc_tkh)=12 and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T12 = L + P + X : L1 += L : P1 += P : X1 += X

        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"
        Header += "    <td>" & L1 & "</td><td>" & P1 & "</td><td>" & X1 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T1 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T2 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T3 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T4 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T5 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T6 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T7 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T8 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T9 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T10 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T11 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T12 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & L1 + P1 + X1 & "</td>"
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")

        'Add Footnote 29052020 - OSH 
        Header = ""
        Header = "<table>"
        Header += "<tr></tr>"
        Header += "<tr></tr>"
        Header += "<tr>"
        Header += "    <td colspan='15'>X dan (space): Rekod yang didaftarkan tidak lengkap</td> "
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")
        Response.End()
        Response.Flush()
    End Sub

End Class