Imports System.Data.OleDb
Imports System.Configuration
Imports System.Collections.Generic

Partial Public Class OtpVerification
    Inherits System.Web.UI.Page
    
    Private ReadOnly emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
    Private emailClient As EmailServiceClient
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Check if user has temporary login session
        If Session("TEMP_USER_ID") Is Nothing Then
            ' Redirect to correct login page based on system type
            If Session("TEMP_SYSTEM_TYPE") = "KOLEJ-PDSA" Then
                Response.Redirect("Login_J.aspx")
            Else
                Response.Redirect("Login.aspx")
            End If
            Return
        End If

        If Not IsPostBack Then
            ' Initialize email service client
            Try
                If Not String.IsNullOrEmpty(emailServiceUrl) Then
                    emailClient = New EmailServiceClient(emailServiceUrl)
                End If
            Catch ex As Exception
                ShowMessage("Ralat sistem email: " & ex.Message, "error")
            End Try
        End If
    End Sub

    Protected Sub btnVerifyOTP_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnVerifyOTP.Click
        Dim userId As String = Session("TEMP_USER_ID")?.ToString()
        Dim otpCode As String = txtOtpCode.Text.Trim()
        
        ' Validate input
        If String.IsNullOrEmpty(otpCode) Then
            ShowMessage("Sila masukkan kod pengesahan.", "error")
            txtOtpCode.Focus()
            Return
        End If
        
        If otpCode.Length <> 6 Then
            ShowMessage("Kod pengesahan mestilah 6 digit.", "error")
            txtOtpCode.Focus()
            Return
        End If
        
        Try
            If emailClient Is Nothing Then
                ShowMessage("Perkhidmatan email tidak tersedia.", "error")
                Return
            End If            ' Validate OTP with proper purpose for KOLEJ system
            Dim otpPurpose As String = "LOGIN_KOLEJ"
            If Session("TEMP_SYSTEM_TYPE") = "KOLEJ-PDSA" Then
                ' Check if this is for encrypted password (secure login)
                otpPurpose = "LOGIN_KOLEJ_SECURE"
            End If
            
            Dim response = emailClient.ValidateOTP(userId, otpCode, otpPurpose)
            
            If response.Success Then
                ' OTP valid - complete login process
                CompleteLoginProcess()
            Else
                ShowMessage(response.Message, "error")
                txtOtpCode.Text = ""
                txtOtpCode.Focus()
            End If
            
        Catch ex As Exception
            ShowMessage("Ralat pengesahan OTP: " & ex.Message, "error")
        End Try
    End Sub

    Protected Sub btnResendOTP_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnResendOTP.Click
        Dim userId As String = Session("TEMP_USER_ID")?.ToString()

        Try
            If emailClient Is Nothing Then
                ShowMessage("Perkhidmatan email tidak tersedia.", "error")
                Return
            End If

            ' Get user email from database
            Dim userEmail As String = GetUserEmail(userId)
            If String.IsNullOrEmpty(userEmail) Then
                ShowMessage("Alamat email pengguna tidak dijumpai.", "error")
                Return
            End If            ' Generate new OTP with proper purpose for KOLEJ system
            Dim otpPurpose As String = "LOGIN_KOLEJ"
            If Session("TEMP_SYSTEM_TYPE") = "KOLEJ-PDSA" Then
                ' Check if this is for encrypted password (secure login)
                otpPurpose = "LOGIN_KOLEJ_SECURE"
            End If
            
            Dim response = emailClient.GenerateOTP(userId, userEmail, otpPurpose)

            If response.Success Then
                ShowMessage("Kod pengesahan baru telah dihantar ke email anda.", "success")
                ' Reset countdown timer
                pnlCountdown.Visible = True
                pnlResend.Visible = False
                ClientScript.RegisterStartupScript(Me.GetType(), "resetTimer", "countdown = 60; timer = setInterval(function() { countdown--; document.getElementById('countdown').innerText = countdown; if (countdown <= 0) { clearInterval(timer); document.getElementById('" & pnlCountdown.ClientID & "').style.display = 'none'; document.getElementById('" & pnlResend.ClientID & "').style.display = 'block'; } }, 1000);", True)
            Else
                ShowMessage(response.Message, "error")
            End If

        Catch ex As Exception
            ShowMessage("Ralat menghantar semula kod: " & ex.Message, "error")
        End Try
    End Sub
    Protected Sub btnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        ' Clear temporary session and redirect to correct login page
        Dim systemType As String = Session("TEMP_SYSTEM_TYPE")?.ToString()
        ClearTemporarySession()

        If systemType = "KOLEJ-PDSA" Then
            Response.Redirect("Login_J.aspx")
        Else
            Response.Redirect("Login.aspx")
        End If
    End Sub

    ''' <summary>
    ''' Complete the login process after successful OTP verification
    ''' </summary>
    Private Sub CompleteLoginProcess()
        Try            ' Move temporary session data to permanent session
            Session("Id_PG") = Session("TEMP_USER_ID")
            Session("PWD") = "" ' Don't store password
            Session("Id_KOLEJ") = Session("TEMP_ID_KOLEJ")
            Session("Dc_KOLEJ") = Session("TEMP_DC_KOLEJ")
            Session("ORIGIN") = Session("TEMP_ORIGIN")
            Session("SYSTEM_TYPE") = Session("TEMP_SYSTEM_TYPE")
            Session("OTP_VERIFIED") = True
            Session("LOGIN_METHOD") = "OTP_VERIFIED"
            
            ' Clear temporary session data
            ClearTemporarySession()
            
            ' Log successful OTP verification for KOLEJ-PDSA
            System.Diagnostics.Debug.WriteLine($"KOLEJ-PDSA OTP Verification successful for user: {Session("Id_PG")} at {DateTime.Now}")
            
            ' Redirect to main system (blank.aspx)
            Response.Redirect("blank.aspx")
            
        Catch ex As Exception
            ShowMessage("Ralat melengkapkan log masuk: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Clear temporary session variables
    ''' </summary>      

    Private Sub ClearTemporarySession()
        Session.Remove("TEMP_USER_ID")
        Session.Remove("TEMP_ID_KOLEJ")
        Session.Remove("TEMP_DC_KOLEJ")
        Session.Remove("TEMP_ORIGIN")
        Session.Remove("TEMP_SYSTEM_TYPE")
        Session.Remove("TEMP_EMAIL")
    End Sub

    ''' <summary>
    ''' Get user email from database
    ''' </summary>
    Private Function GetUserEmail(userId As String) As String
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT email FROM kj_pengguna WHERE id_pg = ? AND status = 1"
            command.Parameters.AddWithValue("@id_pg", userId)
            
            reader = command.ExecuteReader()
            
            If reader.Read() AndAlso Not reader.IsDBNull(0) Then
                Return reader("email").ToString()
            End If
            
            Return ""
            
        Catch ex As Exception
            Return ""
        Finally
            If reader IsNot Nothing Then reader.Close()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
        End Try
    End Function

    ''' <summary>
    ''' Show message to user with appropriate styling
    ''' </summary>
    Private Sub ShowMessage(message As String, messageType As String)
        lblMessage.Text = message
        pnlMessage.Visible = True
        
        Select Case messageType.ToLower()
            Case "success"
                divMessage.Attributes("class") = "message-panel message-success"
            Case "error"
                divMessage.Attributes("class") = "message-panel message-error"
            Case "info"
                divMessage.Attributes("class") = "message-panel message-info"
            Case Else
                divMessage.Attributes("class") = "message-panel message-info"
        End Select
    End Sub

End Class
