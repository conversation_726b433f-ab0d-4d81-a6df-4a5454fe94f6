﻿<%@ Page Language="vb"  MaintainScrollPositionOnPostback="true" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P2_Daftar.aspx.vb" Inherits="SPMJ.WebForm14" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        .style9
        {
            height: 23px;
        }
        .style12
        {
            height: 21px;
        }
        .style33
    {
            height: 20px;
        }
        p.<PERSON><PERSON><PERSON><PERSON>
	{margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	        margin-left: 0in;
            margin-right: 0in;
            margin-top: 0in;
        }
        .style62
        {
            height: 24px;
            width: 158px;
        }
        .style79
        {
            height: 17px;
            width: 158px;
        }
        .style80
        {
            height: 17px;
        }
        .style81
        {
            width: 650px;
            height: 436px;
            left: 0px;
            top: 70px;
            position: static;
        }
        .style82
        {
            height: 23px;
            color: #CC0000;
            padding-right;
        }
        .style83
        {
            height: 20px;
            width: 158px;
        }
        .style87
        {
            width: 158px;
            height: 21px;
        }
        .style90
        {
            height: 19px;
            margin-left: 40px;
            }
        .style92
        {
            height: 22px;
            width: 158px;
        }
        .style96
        {
            width: 158px;
        }
        .style102
        {
            height: 20px;
            width: 14px;
        }
        .style103
        {
            height: 17px;
            width: 36px;
        }
        .style104
        {
            height: 20px;
            width: 36px;
        }
        .style106
    {
        height: 19px;
        width: 14px;
    }
    .style107
    {
        height: 19px;
        width: 36px;
    }
    .style108
    {
        height: 19px;
        width: 158px;
    }
    .style109
    {
        height: 24px;
        width: 36px;
    }
    .style110
    {
        height: 24px;
        width: 14px;
    }
        .style111
    {
        height: 24px;
        }
    .style112
    {
        height: 10px;
        margin-left: 40px;
        }
    .style113
    {
        height: 10px;
        width: 14px;
    }
    .style114
    {
        height: 10px;
        width: 36px;
    }
    .style115
    {
        height: 10px;
        width: 158px;
    }
        .style116
        {
            height: 23px;
            width: 14px;
        }
        .style117
        {
            height: 21px;
            width: 14px;
        }
        .style118
        {
            width: 14px;
        }
        .style119
        {
            height: 17px;
            width: 14px;
        }
        .style120
        {
            height: 23px;
            width: 36px;
        }
        .style121
        {
            width: 158px;
            height: 23px;
        }
        .style126
        {
            height: 103px;
            width: 36px;
        }
        .style127
        {
            width: 158px;
            height: 103px;
        }
        .style128
        {
            height: 103px;
        }
        .style129
        {
            width: 14px;
            height: 103px;
        }
        .style130
        {
            height: 29px;
            width: 36px;
        }
        .style131
        {
            width: 158px;
            height: 29px;
        }
        .style132
        {
            height: 29px;
        }
        .style133
        {
            width: 14px;
            height: 29px;
        }
        .style134
        {
            width: 36px;
        }
        .style135
        {
            height: 22px;
            width: 36px;
        }
        .style136
        {
            height: 22px;
        }
        .style137
        {
            height: 22px;
            width: 14px;
        }
        .style138
        {
            height: 7px;
            width: 36px;
        }
        .style139
        {
            height: 7px;
            width: 158px;
        }
        .style140
        {
            height: 7px;
        }
        .style141
        {
            height: 7px;
            width: 14px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Localize ID="Localize1" runat="server"></asp:Localize>
    <div style="width:100%;">
    <table id="Table1" width="100%" style="font-variant: small-caps">
    <br>
    <tr><td>
    
    <table id="Table2" align="center" border="0" cellpadding="-1" cellspacing="0" 
        
            style="border: 1px solid black; margin-left: 0px; font-family: Arial; font-size: 8pt; text-transform: none; line-height: 21px; width: 74%;" 
            bgcolor="White" class="style81">
        <tr>
            <td align="center" bgcolor="#8BB900" 
                valign="top" colspan="4" 
                
                
                
                
                style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #000000; vertical-align: middle; text-align: center; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bolder;" >
                pendaftaran penuh - pendaftaran baru</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style83">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style33">
                            <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                            </asp:ScriptManagerProxy>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" 
                class="style102">
                &nbsp;</td>
        </tr>
        <tr style="line-height: 21px; background-color: #999966; vertical-align: middle;" 
            bgcolor="#999966">
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style130"><span class="style82">*&nbsp;</span>
                </td>
				<TD vAlign="middle" align="left" bgColor="#8BB900" class="style131" 
                style="font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;">&nbsp; Jenis Pendaftaran</TD>
            <td align="left" valign="middle" bgcolor="#8BB900" valign="middle" class="style132">
                <asp:DropDownList ID="Cb_Jenis" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" Width="200px" CssClass="std">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT BERDAFTAR</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT MASYARAKAT</asp:ListItem>
                    <asp:ListItem Value="3">PENOLONG JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">BIDAN</asp:ListItem>
                </asp:DropDownList>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style133">
                </td>
        </tr>
        <tr style="line-height: 10px">
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style114">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style115">
                </td>
                                     <td bgcolor="White" class="style112" 
                style="line-height: 5px">
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style113">
            </td>
        </tr>
        <tr style="line-height: 21px">
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style107"><span class="style82">*&nbsp;</span>
            </td>
                <td valign="top" align="left" bgcolor="white" 
                class="style108">
                    <asp:UpdatePanel ID="UpdatePanel21" runat="server">
                    <ContentTemplate>
                    <asp:TextBox ID="lb_NoKP" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="132px" 
                            AutoPostBack="True" Enabled="False" 
                            ReadOnly="True">NO. KAD PENGENALAN</asp:TextBox>
                    </ContentTemplate>
                    </asp:UpdatePanel></td>
                <td bgcolor="White" class="style90">
                                                 <asp:UpdatePanel ID="UpdatePanel7" runat="server">
                                                     <ContentTemplate>
                                                         <asp:DropDownList ID="Cb_NoKP" runat="server" 
                                                             CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="11" 
                                                             Width="80px" AutoPostBack="True">
                                                             <asp:ListItem>BARU</asp:ListItem>
                                                             <asp:ListItem>TENTERA</asp:ListItem>
                                                             <asp:ListItem>PASPORT</asp:ListItem>
                                                         </asp:DropDownList>
                                                         <asp:TextBox ID="Tx_NoKP" runat="server" AutoPostBack="True" CssClass="std" 
                                                             MaxLength="12" Width="200px" Wrap="False"></asp:TextBox>
                                                     </ContentTemplate>
                                                 </asp:UpdatePanel>
                                                 <asp:Button ID="cmd_Semak" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                     Height="20px" tabIndex="3" Text="SEMAK" Width="60px" />
                                     </td><td align="left" bgcolor="#ffffff" valign="top" class="style106">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style135"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">NAMA</td>
            <td bgcolor="White" class="style136">
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style137">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style138"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style139">WARGANEGARA</TD>
            <td align="left" bgcolor="white" valign="top" class="style140">
                <asp:DropDownList ID="Cb_Warga" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="286px" CssClass="std" 
                    AutoPostBack="True">
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style141">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style135">
                </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">TARIKH LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style136">
                            <asp:TextBox ID="Tx_Tkh_Lahir" runat="server" CssClass="std" 
                    Width="95px" AutoPostBack="True" Wrap="False"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Tkh_Lahir_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Lahir" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Tkh_Lahir_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Tkh_Lahir" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style137">
                                                    </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">TEMPAT LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:TextBox ID="Tx_Tpt_Lahir" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style103"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">JANTINA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Jantina" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" CssClass="std">
                    <asp:ListItem Value=""></asp:ListItem>
                    <asp:ListItem Value="1">LELAKI</asp:ListItem>
                    <asp:ListItem Value="2">PEREMPUAN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style103"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">BANGSA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel4" runat="server">
                    <ContentTemplate>
                        <asp:DropDownList ID="Cb_Bangsa" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="12" Width="190px" AutoPostBack="True">
                            <asp:ListItem></asp:ListItem>
                            <asp:ListItem Value="1">MELAYU</asp:ListItem>
                            <asp:ListItem Value="2">CINA</asp:ListItem>
                            <asp:ListItem Value="3">INDIA</asp:ListItem>
                            <asp:ListItem Value="4">LAIN-LAIN</asp:ListItem>
                            <asp:ListItem Value="5">BUMIPUTERA SABAH</asp:ListItem>
                            <asp:ListItem Value="6">BUMIPUTERA SARAWAK</asp:ListItem>
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">ETNIK&nbsp;</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                    <ContentTemplate>
                        <asp:DropDownList ID="Cb_Etnik" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="12" Width="190px" Enabled="False">
                            <asp:ListItem></asp:ListItem>
                            <asp:ListItem Value="L">MELAYU</asp:ListItem>
                            <asp:ListItem Value="P">CINA</asp:ListItem>
                            <asp:ListItem Value="India">INDIA</asp:ListItem>
                            <asp:ListItem Value="Lain-Lain">LAIN-LAIN</asp:ListItem>
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">AGAMA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Agama" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="13" Width="190px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">ISLAM</asp:ListItem>
                    <asp:ListItem Value="2">BUDDHA</asp:ListItem>
                    <asp:ListItem Value="3">HINDU</asp:ListItem>
                    <asp:ListItem Value="4">KRISTIAN</asp:ListItem>
                    <asp:ListItem Value="5">LAIN-LAIN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">UMUR&nbsp;</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel8" runat="server">
                    <ContentTemplate>
                        <asp:TextBox ID="Tx_Umur" runat="server" CssClass="std" Width="95px" 
                            AutoPostBack="True" Enabled="False"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">TARAF PERKAHWINAN</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Kahwin" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="22" Width="190px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">BUJANG</asp:ListItem>
                    <asp:ListItem Value="2">BERKAHWIN</asp:ListItem>
                    <asp:ListItem Value="3">DUDA</asp:ListItem>
                    <asp:ListItem Value="4">JANDA</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style103"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">ALAMAT TETAP</TD>
            <td align="left" bgcolor="white" valign="top" 
                id="Tx_TP_Alamat" class="style33">
                <asp:TextBox ID="Tx_TP_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" valign="top" class="style103"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">POSKOD</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_TP_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style103"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">BANDAR</TD>
            <td align="left" bgcolor="white" valign="top" class="style9">
                <asp:TextBox ID="Tx_TP_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style116">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style103"><span class="style82">*&nbsp; </span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style87"><asp:UpdatePanel ID="UpdatePanel5" runat="server">
                    <ContentTemplate>
                    <asp:TextBox ID="tx_Negeri" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="132px" 
                            AutoPostBack="True">NEGERI</asp:TextBox>
                    </ContentTemplate>
                    </asp:UpdatePanel></TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel9" runat="server">
                <ContentTemplate>
                <asp:DropDownList ID="Cb_TP_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="220px" AutoPostBack="True">
                <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
                </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">ALAMAT SURAT-MENYURAT</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">POSKOD</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">BANDAR</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">NEGERI</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:DropDownList ID="Cb_SM_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="220px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">NO. TELEFON RUMAH</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Tel_R" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">NO. TELEFON BIMBIT</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Tel_HP" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">E-MEL</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Emel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD vAlign="top" bgColor="#ffffff" class="style96">&nbsp;</TD>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td align="right" valign="top" bgcolor="#ffffff" class="style103"><span class="style82">*&nbsp; </span>
                        </td>
								<TD vAlign="top" bgColor="#ffffff" class="style96">KOLEJ/INSTITUSI</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:UpdatePanel ID="UpdatePanel20" runat="server">
                                <ContentTemplate>
                                    <asp:RadioButtonList ID="RadioButtonList1" runat="server" AutoPostBack="True" 
                                        CellPadding="0" CellSpacing="0" Height="17px" RepeatDirection="Horizontal" 
                                        Width="329px">
                                        <asp:ListItem Value="1" Selected="True">KERAJAAN</asp:ListItem>
                                        <asp:ListItem Value="2">SWASTA</asp:ListItem>
                                        <asp:ListItem Value="3">LUAR NEGARA</asp:ListItem>
                                    </asp:RadioButtonList>
                                    masukkan kata kunci dan klik butang &#39;cari&#39; 
                                    <asp:TextBox ID="tx_Cari" runat="server" CssClass="std" Width="150px"></asp:TextBox>
                                    <asp:Button ID="bt_Cari" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                        Height="20px" tabIndex="3" Text="CARI" Width="50px" />
                                    <br />
                                    <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                                        Font-Size="8pt" Height="19px" tabIndex="33" Width="529px" AutoPostBack="True">
                                    </asp:DropDownList>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style103"><span class="style82">*&nbsp; </span>
                        </td>
								<TD vAlign="top" bgColor="#ffffff" class="style96">TAJAAN</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:DropDownList ID="Cb_Tajaan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="193px">
                            </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr style="line-height: 21px">
                        <td align="right" bgcolor="#ffffff" class="style104"><span class="style82">*&nbsp; </span>
                        </td>
								<td valign="top" bgcolor="#ffffff" class="style83">SESI PENGAMBILAN</td><td bgcolor="#ffffff" 
                            style="FONT-SIZE: 8pt; FONT-FAMILY: arial; FONT-VARIANT: small-caps; white-space: normal;" 
                            valign="top">
                            <asp:DropDownList ID="Cb_Sesi_Bulan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="100px">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_Sesi_Tahun" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="93px">
                            </asp:DropDownList>
                     </td><td bgcolor="#ffffff" class="style102"></td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style103"><span class="style82">*&nbsp; </span>
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style79">TARIKH MULA LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style80">
                            <asp:TextBox ID="Tx_M_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_M_Latihan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_M_Latihan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_M_Latihan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_M_Latihan" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style119">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style134"><span class="style82">*&nbsp; </span>
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style96">TARIKH TAMAT 
                                    LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top">
                            <asp:TextBox ID="Tx_T_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_T_Latihan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_T_Latihan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_T_Latihan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_T_Latihan" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style103"><span class="style82">*&nbsp; </span>
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style79">TARIKH PEPERIKSAAN 
                                    AKHIR</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style80">
                            <asp:TextBox ID="Tx_Periksa" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Periksa_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Periksa" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Periksa_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Periksa" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style119">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;
                        </td>
                        <td bgcolor="#ffffff" class="style96">
                        </td>
                        <td bgcolor="#ffffff">
                            <asp:DropDownList ID="Cb_K" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_I" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <br />
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96" valign="top">
                        KELAYAKAN IKHTISAS</td>
                        <td bgcolor="#ffffff" align="left">
                            <table align="left"><tr>
                                <td align="left" width="100%"><asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                    <ContentTemplate>
                                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#5D7B9D" 
    AutoGenerateColumns="False" Height="106px" BorderStyle="Solid" BorderWidth="1px">
                                            <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                            <RowStyle BackColor="White" VerticalAlign="Middle" />
                                            <Columns>
                                                <asp:TemplateField HeaderText="#">
                                                    <HeaderStyle HorizontalAlign="Center" />
                                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                </asp:TemplateField>
                                                <asp:TemplateField HeaderText="KELAYAKAN">
                                                    <ItemTemplate>
                                                        <asp:DropDownList ID="Cb_Kelayakan" runat="server" 
                                                                Font-Names="Arial" Font-Size="8pt" Height="19px" tabIndex="33" 
                                                            Width="193px">
                                                        </asp:DropDownList>
                                                    </ItemTemplate>
                                                </asp:TemplateField>
                                                <asp:TemplateField HeaderText="TARIKH KELAYAKAN">
                                                    <ItemTemplate>
                                                        <asp:TextBox ID="Tx_Tkh_Layak" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                        <cc1:MaskedEditExtender ID="Tx_Tkh_Layak_MaskedEditExtender" runat="server" 
                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                    CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                    CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                    Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Layak" 
                                    UserDateFormat="DayMonthYear">
                                                        </cc1:MaskedEditExtender>
                                                        <cc1:CalendarExtender ID="Tx_Tkh_Layak_CalendarExtender" runat="server" 
                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                    TargetControlID="Tx_Tkh_Layak">
                                                        </cc1:CalendarExtender>
                                                    </ItemTemplate>
                                                </asp:TemplateField>
                                                <asp:TemplateField ShowHeader="False">
                                                    <ItemTemplate>
                                                        <asp:Button ID="Button1" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="+" Width="21px" Font-Bold="True" onclick="Button1_Click" />
                                                    </ItemTemplate>
                                                    <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                    <HeaderStyle Width="65px" />
                                                </asp:TemplateField>
                                            </Columns>
                                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                            <SelectedRowStyle Font-Bold="False" />
                                            <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                            <AlternatingRowStyle BackColor="White" />
                                        </asp:GridView>
                                    </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96" valign="top">
                            KELAYAKAN AKADEMIK</td>
                        <td bgcolor="#ffffff">
                            <table align="left"><tr>
                                <td align="left">
                                    <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                                        <ContentTemplate>
                                            <asp:GridView ID="GdA" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#5D7B9D" 
    AutoGenerateColumns="False" Height="106px" BorderStyle="Solid" BorderWidth="1px">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:DropDownList ID="Cb_Akademik" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="193px">
                                                            </asp:DropDownList>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="TARIKH KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Tkh_Layak" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_Tkh_Layak_MaskedEditExtender" runat="server" 
                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                    CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                    CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                    Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Layak" 
                                    UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Tkh_Layak_CalendarExtender" runat="server" 
                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                    TargetControlID="Tx_Tkh_Layak">
                                                            </cc1:CalendarExtender>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button2" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="+" Width="21px" Font-Bold="True" onclick="Button1_Click" />
                                                        </ItemTemplate>
                                                        <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                        <HeaderStyle Width="65px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr id="tr1">
                        <td bgcolor="#ffffff" class="style103">
                            </td>
                        <td bgcolor="#999966" colspan="2" 
                            style="border: 1px solid #999966; color: #FFFFFF; font-weight: bold;">
                            &nbsp; Senarai Semak</td><td class="style118"></td>
                    </tr ><tr>
                        <td  id="tr2" bgcolor="#ffffff" class="style103">
                        </td>
                        <td bgcolor="#F5F5F1" colspan="2" valign="top" 
                            style="border: 1px solid #999966; line-height: normal" align="left"><table align="center"><tr>
                                <td align="left">
                                    <asp:CheckBoxList ID="chk_Semak" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" RepeatColumns="2" RepeatDirection="Horizontal" 
                                Width="408px" Height="31px">
                                <asp:ListItem VALUE="Salinan Kad Pengenalan">Salinan Kad Pengenalan</asp:ListItem>
                                <asp:ListItem VALUE="Salinan Sijil Lahir">Salinan Sijil Lahir</asp:ListItem>
                                <asp:ListItem VALUE="Salinan Sijil Kesihatan">Salinan Sijil Kesihatan</asp:ListItem>
                                <asp:ListItem VALUE="Salinan Sijil Akademik">Salinan Sijil Akademik</asp:ListItem>
                                <asp:ListItem>Foto</asp:ListItem>
                                        <asp:ListItem>Bayaran Pendaftaran</asp:ListItem>
                                    </asp:CheckBoxList></td></tr></table>
                            
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
        <tr valign="bottom">
                        <td align="right" bgcolor="#ffffff" class="style109"><span class="style82">*&nbsp; </span>
                            </td>
                        <td bgcolor="#999966" class="style62" 
                                         
                            style="font-weight: bold; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-variant: small-caps;" 
                            valign="bottom">
                            &nbsp; Status&nbsp;</td>
                        <td bgcolor="#999966" class="style111">
                            <asp:UpdatePanel ID="UpdatePanel6" runat="server">
                                <ContentTemplate>
                                    <asp:DropDownList ID="Cb_Status" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="150px" AutoPostBack="True">
                                        <asp:ListItem></asp:ListItem>
                                        <asp:ListItem Value="1">LENGKAP</asp:ListItem>
                                        <asp:ListItem Value="2">TIDAK LENGKAP</asp:ListItem>
                                    </asp:DropDownList>
                                </ContentTemplate>
                            </asp:UpdatePanel>
            </td>
                        <td bgcolor="#ffffff" class="style110">
                            </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style126">
                            </td>
                        <td bgcolor="#999966" class="style127" valign="top" 
                            
                            style="font-weight: bold; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-variant: small-caps;">
                            &nbsp; Catatan</td>
                        <td bgcolor="#999966" valign="top" class="style128">
                <asp:TextBox ID="Tx_Catatan" runat="server" CssClass="std" Height="89px" 
                    TextMode="MultiLine" Width="325px"></asp:TextBox>
            </td>
                        <td bgcolor="#ffffff" class="style129">
                            </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style120">
                            </td>
                        <td bgcolor="#ffffff" class="style121" valign="top">
                            </td>
                        <td bgcolor="#ffffff" class="style9" valign="top">
                <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="96px" />
            &nbsp;<asp:Button ID="cmd_Padam" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="PADAM" Width="96px" Visible="False" />
                            <cc1:ConfirmButtonExtender ID="cmd_Padam_ConfirmButtonExtender" runat="server" 
                                ConfirmText="Padam Rekod Ini?" Enabled="True" TargetControlID="cmd_Padam">
                            </cc1:ConfirmButtonExtender>
            </td>
                        <td bgcolor="#ffffff" class="style116">
                            </td>
                    </tr>
        <tr>
            <td bgcolor="#ffffff" class="style134">
                </td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle">
                </td>
            <td bgcolor="#ffffff" class="style118">
                </td>
        </tr>
    </table></td></tr></table>
    
    </br></br>
    </div>

</asp:Content>
