# SPMJ KOLEJ Email Microservice Integration Test
# Complete verification script

Write-Host "🔍 SPMJ KOLEJ EMAIL MICROSERVICE INTEGRATION TEST" -ForegroundColor Cyan
Write-Host "=" * 60

# Test 1: Check if EmailServiceClient.vb has the correct endpoint
Write-Host "`n1. Testing EmailServiceClient Configuration..." -ForegroundColor Yellow
$emailClientPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\EmailServiceClient.vb"

if (Test-Path $emailClientPath) {
    $clientContent = Get-Content $emailClientPath -Raw
    
    # Check for correct API endpoint
    if ($clientContent -match "/api/admin/password/send-change-notification") {
        Write-Host "✅ EmailServiceClient uses correct endpoint" -ForegroundColor Green
    } else {
        Write-Host "❌ EmailServiceClient endpoint needs fixing" -ForegroundColor Red
    }
    
    # Check for API key support
    if ($clientContent -match "X-API-Key") {
        Write-Host "✅ API Key authentication configured" -ForegroundColor Green
    } else {
        Write-Host "❌ API Key authentication missing" -ForegroundColor Red
    }
    
    # Check for proper request structure
    if ($clientContent -match "templateType.*password_change_notification") {
        Write-Host "✅ Password change notification template configured" -ForegroundColor Green
    } else {
        Write-Host "❌ Password change notification template missing" -ForegroundColor Red
    }
} else {
    Write-Host "❌ EmailServiceClient.vb not found" -ForegroundColor Red
}

# Test 2: Check ForcePasswordChange.aspx.vb integration
Write-Host "`n2. Testing ForcePasswordChange Integration..." -ForegroundColor Yellow
$forceChangePath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx.vb"

if (Test-Path $forceChangePath) {
    $forceChangeContent = Get-Content $forceChangePath -Raw
    
    # Check for microservice initialization
    if ($forceChangeContent -match "InitializeEmailService") {
        Write-Host "✅ Email service initialization present" -ForegroundColor Green
    } else {
        Write-Host "❌ Email service initialization missing" -ForegroundColor Red
    }
    
    # Check for health check implementation
    if ($forceChangeContent -match "TestEmailServiceConnection") {
        Write-Host "✅ Health check implementation present" -ForegroundColor Green
    } else {
        Write-Host "❌ Health check implementation missing" -ForegroundColor Red
    }
    
    # Check for notification sending
    if ($forceChangeContent -match "SendPasswordChangeNotification") {
        Write-Host "✅ Password change notification sending present" -ForegroundColor Green
    } else {
        Write-Host "❌ Password change notification sending missing" -ForegroundColor Red
    }
} else {
    Write-Host "❌ ForcePasswordChange.aspx.vb not found" -ForegroundColor Red
}

# Test 3: Check Web.config configuration
Write-Host "`n3. Testing Web.config Configuration..." -ForegroundColor Yellow
$webConfigPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Web.config"

if (Test-Path $webConfigPath) {
    $webConfigContent = Get-Content $webConfigPath -Raw
    
    # Check for email service URL
    if ($webConfigContent -match 'EmailServiceUrl.*http://localhost:5000') {
        Write-Host "✅ EmailServiceUrl configured correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ EmailServiceUrl needs configuration" -ForegroundColor Red
    }
    
    # Check for API key
    if ($webConfigContent -match 'EmailServiceApiKey.*SPMJ-EmailService-2024-SecureKey') {
        Write-Host "✅ EmailServiceApiKey configured correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ EmailServiceApiKey needs configuration" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Web.config not found" -ForegroundColor Red
}

# Test 4: Check if Email Microservice is running
Write-Host "`n4. Testing Email Microservice Status..." -ForegroundColor Yellow

try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method Get -TimeoutSec 5
    if ($healthResponse.status -eq "healthy") {
        Write-Host "✅ Email microservice is healthy and running" -ForegroundColor Green
        Write-Host "   Timestamp: $($healthResponse.timestamp)" -ForegroundColor Gray
    } else {
        Write-Host "⚠️ Email microservice responded but not healthy" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Email microservice not reachable at http://localhost:5000" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
}

# Test 5: Check AdminPasswordController endpoints
Write-Host "`n5. Testing AdminPassword Controller Endpoints..." -ForegroundColor Yellow

try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/admin/password/health" -Method Get -TimeoutSec 5
    if ($healthResponse.Status -eq "Healthy") {
        Write-Host "✅ AdminPassword controller is healthy" -ForegroundColor Green
        Write-Host "   Available endpoints:" -ForegroundColor Gray
        foreach ($endpoint in $healthResponse.Endpoints) {
            Write-Host "   - $endpoint" -ForegroundColor Gray
        }
        
        # Check for the specific endpoint we need
        if ($healthResponse.Endpoints -contains "/api/admin/password/send-change-notification") {
            Write-Host "✅ Password change notification endpoint available" -ForegroundColor Green
        } else {
            Write-Host "❌ Password change notification endpoint missing" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ AdminPassword controller not reachable" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
}

# Test 6: Integration Compatibility Check
Write-Host "`n6. Testing .NET 3.5 Compatibility..." -ForegroundColor Yellow

$compatibilityIssues = @()

# Check for .NET 3.5 incompatible syntax
$filesToCheck = @(
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\EmailServiceClient.vb",
    "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx.vb"
)

foreach ($file in $filesToCheck) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
          # Check for .NET 3.5 incompatible patterns
        if ($content -match '\?\?') {
            $compatibilityIssues += "$file contains null-coalescing operator (??)"
        }
        
        if ($content -match 'String\.IsNullOrWhiteSpace') {
            $compatibilityIssues += "$file uses IsNullOrWhiteSpace (not available in .NET 3.5)"
        }
        
        if ($content -match 'var\s+\w+\s*=') {
            $compatibilityIssues += "$file uses 'var' keyword extensively"
        }
    }
}

if ($compatibilityIssues.Count -eq 0) {
    Write-Host "✅ No .NET 3.5 compatibility issues found" -ForegroundColor Green
} else {
    Write-Host "⚠️ Potential .NET 3.5 compatibility issues:" -ForegroundColor Yellow
    foreach ($issue in $compatibilityIssues) {
        Write-Host "   - $issue" -ForegroundColor Gray
    }
}

# Summary
Write-Host "`n" + "=" * 60
Write-Host "🎯 INTEGRATION TEST SUMMARY" -ForegroundColor Cyan

$allTests = @(
    "EmailServiceClient Configuration",
    "ForcePasswordChange Integration", 
    "Web.config Configuration",
    "Email Microservice Status",
    "AdminPassword Controller",
    ".NET 3.5 Compatibility"
)

Write-Host "`nTest Results:" -ForegroundColor White
for ($i = 1; $i -le 6; $i++) {
    Write-Host "  $i. $($allTests[$i-1]): " -NoNewline
    # This would normally show the actual test results
    Write-Host "See details above" -ForegroundColor Gray
}

Write-Host "`n🔧 RECOMMENDED ACTIONS:" -ForegroundColor Yellow
Write-Host "1. Ensure email microservice is running on http://localhost:5000"
Write-Host "2. Verify API key matches between .NET 3.5 app and microservice"
Write-Host "3. Test password change flow end-to-end"
Write-Host "4. Monitor debug output for integration errors"
Write-Host "5. Validate email templates are working correctly"

Write-Host "`n✅ Integration test completed!" -ForegroundColor Green
