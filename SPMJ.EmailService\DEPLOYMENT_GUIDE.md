# SPMJ Email Service Integration - Deployment Guide

## Overview
This integration provides modern email functionality for the SPMJ .NET 3.5 application through a .NET 9 microservice, solving TLS 1.2 compatibility issues and adding advanced features.

## Architecture

```
┌─────────────────┐    HTTP/HTTPS     ┌─────────────────┐    SMTP/TLS 1.2    ┌─────────────────┐
│                 │   (Port 5000)     │                 │    (Port 587)      │                 │
│ SPMJ .NET 3.5   │◄─────────────────►│ .NET 9 Email    │◄──────────────────►│ Modern Email    │
│ Application     │                   │ Microservice    │                    │ Provider        │
│                 │                   │                 │                    │ (Gmail/O365)    │
└─────────────────┘                   └─────────────────┘                    └─────────────────┘
```

## Features Implemented

### ✅ End User Password Recovery
- **Email-based password reset links**
- **Token-based security (24-hour expiration)**
- **Modern responsive UI**
- **Integration with existing authentication system**

### ✅ Admin Password Management
- **Create/reset user passwords via email**
- **Temporary password generation**
- **Admin audit trail**
- **Email notifications to users**

### ✅ OTP Email Authentication
- **6-digit OTP codes (5-minute expiration)**
- **Email-based delivery**
- **Fallback to normal login if email service unavailable**
- **Rate limiting and security measures**

### ✅ Database Integration
- **Uses existing `pn_pengguna` table**
- **Additional tables for tokens and audit**
- **Backward compatibility maintained**

## Deployment Steps

### 1. Database Setup

Execute the database migration script:

```sql
-- Run on your SPMJ database
-- File: SPMJ.EmailService/Database_EmailService_Migration.sql
```

This will create:
- `password_reset_tokens` table
- `otp_tokens` table  
- `email_audit_log` table
- Add `email` column to `pn_pengguna` (if not exists)

### 2. Email Service Configuration

Update `SPMJ.EmailService/appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-server;Database=SPMJ_PDSA;User ID=sa;Password=your-password;TrustServerCertificate=True"
  },
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "UseSsl": true,
    "Username": "<EMAIL>",
    "Password": "your-app-password",
    "FromEmail": "<EMAIL>",
    "FromName": "SPMJ System"
  }
}
```

### 3. Deploy .NET 9 Microservice

#### Option A: Local Development
```powershell
cd "d:\source_code\.NET 3.5. - O\SPMJ -PDSA\SPMJ.EmailService"
dotnet run
```
Service will be available at: `http://localhost:5000`

#### Option B: IIS Deployment
1. Publish the .NET 9 application
2. Create new IIS site for the microservice
3. Configure application pool for .NET 9
4. Update connection strings for production

#### Option C: Docker Deployment
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0
WORKDIR /app
COPY . .
EXPOSE 80
ENTRYPOINT ["dotnet", "SPMJ.EmailService.dll"]
```

### 4. Update .NET 3.5 Application

Add to `Web.config`:

```xml
<appSettings>
  <!-- Existing settings -->
  <add key="EmailServiceUrl" value="http://localhost:5000" />
</appSettings>
```

### 5. Add Files to .NET 3.5 Project

Ensure these files are included in your SPMJ project:
- `EmailServiceClient.vb`
- `PasswordResetModern.aspx` & `.aspx.vb`
- `OtpVerification.aspx` & `.aspx.vb`
- `AdminPasswordManager.aspx` & `.aspx.vb`

### 6. Update User Email Addresses

```sql
-- Update email addresses for users who need email functionality
UPDATE pn_pengguna SET email = '<EMAIL>' WHERE id_pg = 'user_id';
```

## API Endpoints

### Password Reset
- `POST /api/password/reset/request` - Request password reset
- `GET /api/password/reset/validate/{token}` - Validate reset token
- `POST /api/password/reset/complete` - Complete password reset

### Admin Password Management
- `POST /api/password/admin/create` - Admin create password
- `POST /api/password/admin/reset` - Admin reset password

### OTP Management
- `POST /api/otp/generate` - Generate OTP
- `POST /api/otp/validate` - Validate OTP
- `POST /api/otp/cleanup` - Cleanup expired OTPs

### Health Check
- `GET /health` - Service health status

## Configuration Options

### Email Provider Setup

#### Gmail Setup
1. Enable 2-factor authentication
2. Generate App Password
3. Use App Password in configuration

#### Microsoft 365 Setup
```json
{
  "SmtpServer": "smtp.office365.com",
  "SmtpPort": 587,
  "Username": "<EMAIL>",
  "Password": "your-password"
}
```

#### SendGrid Setup
```json
{
  "SmtpServer": "smtp.sendgrid.net",
  "SmtpPort": 587,
  "Username": "apikey",
  "Password": "your-sendgrid-api-key"
}
```

## Security Features

### ✅ Password Security
- **SHA256 + Salt encryption** (compatible with existing system)
- **Secure token generation** (32-byte random tokens)
- **Token expiration** (24 hours for reset, 5 minutes for OTP)

### ✅ Email Security
- **TLS 1.2 encryption** for SMTP communication
- **Rate limiting** for OTP requests
- **Email masking** in logs and UI

### ✅ API Security
- **CORS configuration** for .NET 3.5 integration
- **Input validation** and sanitization
- **Error handling** with secure error messages

## Monitoring and Maintenance

### Health Monitoring
```vb
Dim emailClient As New EmailServiceClient("http://localhost:5000")
If emailClient.CheckHealth() Then
    ' Service is healthy
End If
```

### Cleanup Tasks
- **Expired tokens cleanup**: Automatic via cleanup endpoint
- **Email audit logs**: Monitor successful/failed email sending
- **Performance monitoring**: Check response times

### Troubleshooting

#### Email Service Not Available
- System falls back to normal login without OTP
- Admin functions show appropriate error messages
- Users can still reset passwords manually

#### Database Connection Issues
- Check connection strings in both applications
- Verify SQL Server permissions
- Check firewall settings

#### SMTP Issues
- Verify email provider settings
- Check firewall/network connectivity
- Validate credentials and app passwords

## Testing Checklist

### ✅ Password Reset Flow
1. Request reset via email
2. Receive email with reset link
3. Click link and set new password
4. Login with new password

### ✅ Admin Functions
1. Search for user
2. Set temporary password
3. User receives email
4. User logs in and changes password

### ✅ OTP Flow
1. User logs in with credentials
2. OTP sent to registered email
3. User enters OTP code
4. Access granted to application

### ✅ Fallback Scenarios
1. Email service unavailable
2. Invalid email addresses
3. Network connectivity issues
4. SMTP authentication failures

## Production Considerations

### Performance
- **Async operations** for email sending
- **Memory caching** for OTP validation
- **Connection pooling** for database access

### Scalability
- **Stateless design** allows horizontal scaling
- **Database connection pooling**
- **Separate email service** can handle high loads

### Backup and Recovery
- **Database backups** include new email tables
- **Configuration backups** for email settings
- **Disaster recovery** procedures updated

## Support and Maintenance

### Log Locations
- **Email Service**: Application logs in service directory
- **SPMJ Application**: Existing log mechanisms
- **Database**: Email audit logs in `email_audit_log` table

### Common Issues
1. **"Email service not available"** - Check microservice status
2. **"SMTP authentication failed"** - Verify email credentials
3. **"Token expired"** - User needs to request new reset link
4. **"User not found"** - Check user exists and is active

This integration provides a robust, modern email system while maintaining full backward compatibility with your existing SPMJ application.
