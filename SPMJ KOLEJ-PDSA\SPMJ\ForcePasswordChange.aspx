<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="ForcePasswordChange.aspx.vb" Inherits="SPMJ.ForcePasswordChange" title="SPMJ KOLEJ - <PERSON><PERSON><PERSON>" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .force-password-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .force-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .force-icon {
            font-size: 80px;
            color: #ffeb3b;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .force-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .force-subtitle {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .security-notice {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            border-left: 5px solid #ffeb3b;
        }
        
        .security-notice h4 {
            margin: 0 0 10px 0;
            color: #ffeb3b;
            font-size: 18px;
        }
        
        .security-notice ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .security-notice li {
            margin: 8px 0;
            font-size: 14px;
        }
        
        .password-form {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            font-size: 16px;
        }
        
        .form-input {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            border-color: #ffeb3b;
            outline: none;
            box-shadow: 0 0 10px rgba(255, 235, 59, 0.3);
        }
        
        .password-strength {
            margin-top: 10px;
            font-size: 14px;
        }
        
        .strength-weak { color: #ff5722; }
        .strength-medium { color: #ff9800; }
        .strength-strong { color: #4caf50; }
        
        .btn-force-change {
            width: 100%;
            padding: 18px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 15px 0;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4caf50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .btn-primary:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .message-panel {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .message-success {
            background-color: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 2px solid #4caf50;
        }
        
        .message-error {
            background-color: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 2px solid #f44336;
        }
        
        .message-warning {
            background-color: rgba(255, 152, 0, 0.2);
            color: #ff9800;
            border: 2px solid #ff9800;
        }
        
        .requirements-list {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .requirements-list h4 {
            margin: 0 0 15px 0;
            color: #ffeb3b;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .requirement-icon {
            margin-right: 10px;
            font-weight: bold;
        }
        
        .requirement-met { color: #4caf50; }
        .requirement-unmet { color: #ff5722; }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="force-password-container">
        <div class="force-header">
            <div class="force-icon">🔐</div>
            <div class="force-title">Wajib Tukar Kata Laluan</div>
            <div class="force-subtitle">
                Untuk keselamatan akaun anda, kata laluan lama perlu dikemaskini kepada kata laluan selamat yang baharu.
            </div>
        </div>
        
        <div class="security-notice">
            <h4>🛡️ Pemberitahuan Keselamatan</h4>
            <p>Sistem telah mengesan bahawa akaun anda menggunakan kata laluan lama yang tidak selamat.</p>
            <ul>
                <li>Kata laluan baharu akan disulitkan dengan teknologi SHA256</li>
                <li>Ini meningkatkan keselamatan akaun anda</li>
                <li>Anda perlu menukar kata laluan sekarang untuk meneruskan</li>
                <li>Kata laluan baharu mestilah berbeza daripada kata laluan lama</li>
            </ul>
        </div>
        
        <!-- Message Panel -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false">
            <div class="message-panel" id="divMessage" runat="server">
                <asp:Label ID="lblMessage" runat="server"></asp:Label>
            </div>
        </asp:Panel>
        
        <div class="password-form">
            <div class="form-group">
                <label class="form-label">Kata Laluan Baharu:</label>
                <asp:TextBox ID="txtNewPassword" runat="server" CssClass="form-input" TextMode="Password" placeholder="Masukkan kata laluan baharu yang selamat"></asp:TextBox>
                <div class="password-strength" id="strengthIndicator"></div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Sahkan Kata Laluan Baharu:</label>
                <asp:TextBox ID="txtConfirmPassword" runat="server" CssClass="form-input" TextMode="Password" placeholder="Masukkan semula kata laluan baharu"></asp:TextBox>
            </div>
            
            <div class="requirements-list">
                <h4>📋 Keperluan Kata Laluan Selamat:</h4>
                <div class="requirement-item">
                    <span class="requirement-icon requirement-unmet" id="req-length">❌</span>
                    <span>Sekurang-kurangnya 8 aksara</span>
                </div>
                <div class="requirement-item">
                    <span class="requirement-icon requirement-unmet" id="req-upper">❌</span>
                    <span>Mengandungi huruf besar (A-Z)</span>
                </div>
                <div class="requirement-item">
                    <span class="requirement-icon requirement-unmet" id="req-lower">❌</span>
                    <span>Mengandungi huruf kecil (a-z)</span>
                </div>                <div class="requirement-item">
                    <span class="requirement-icon requirement-unmet" id="req-number">❌</span>
                    <span>Mengandungi nombor (0-9)</span>
                </div>
                <div class="requirement-item">
                    <span class="requirement-icon requirement-unmet" id="req-special">❌</span>
                    <span>Mengandungi aksara khas (!@#$%^&*)</span>
                </div>
                <div class="requirement-item">
                    <span class="requirement-icon requirement-unmet" id="req-different">❌</span>
                    <span>Berbeza daripada kata laluan lama</span>
                </div>
            </div>
            
            <div class="form-group">
                <asp:Button ID="btnChangePassword" runat="server" Text="Tukar Kata Laluan Sekarang" CssClass="btn-force-change btn-primary" />
            </div>
        </div>
    </div>
    
    <script type="text/javascript">        // Password strength validation
        function checkPasswordStrength() {
            var password = document.getElementById('<%= txtNewPassword.ClientID %>').value;
            var strengthIndicator = document.getElementById('strengthIndicator');
            
            // Check requirements
            var hasLength = password.length >= 8;
            var hasUpper = /[A-Z]/.test(password);
            var hasLower = /[a-z]/.test(password);
            var hasNumber = /[0-9]/.test(password);
            var hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
            var isDifferent = password !== '<%= Session("TEMP_OLD_PASSWORD") %>';
            
            // Update requirement indicators
            updateRequirement('req-length', hasLength);
            updateRequirement('req-upper', hasUpper);
            updateRequirement('req-lower', hasLower);
            updateRequirement('req-number', hasNumber);
            updateRequirement('req-special', hasSpecial);
            updateRequirement('req-different', isDifferent);
            
            // Calculate strength
            var score = 0;
            if (hasLength) score++;
            if (hasUpper) score++;
            if (hasLower) score++;
            if (hasNumber) score++;
            if (hasSpecial) score++;
            if (isDifferent) score++;
            
            // Update strength indicator
            if (score < 4) {
                strengthIndicator.innerHTML = 'Lemah';
                strengthIndicator.className = 'password-strength strength-weak';
            } else if (score < 6) {
                strengthIndicator.innerHTML = 'Sederhana';
                strengthIndicator.className = 'password-strength strength-medium';
            } else {
                strengthIndicator.innerHTML = 'Kuat';
                strengthIndicator.className = 'password-strength strength-strong';
            }
            
            // Enable/disable submit button (now requires all 6 criteria)
            var submitBtn = document.getElementById('<%= btnChangePassword.ClientID %>');
            submitBtn.disabled = score < 6;
        }
        
        function updateRequirement(id, met) {
            var element = document.getElementById(id);
            if (met) {
                element.innerHTML = '✅';
                element.className = 'requirement-icon requirement-met';
            } else {
                element.innerHTML = '❌';
                element.className = 'requirement-icon requirement-unmet';
            }
        }
        
        // Check password match
        function checkPasswordMatch() {
            var password = document.getElementById('<%= txtNewPassword.ClientID %>').value;
            var confirmPassword = document.getElementById('<%= txtConfirmPassword.ClientID %>').value;
            
            if (confirmPassword.length > 0) {
                if (password === confirmPassword) {
                    document.getElementById('<%= txtConfirmPassword.ClientID %>').style.borderColor = '#4caf50';
                } else {
                    document.getElementById('<%= txtConfirmPassword.ClientID %>').style.borderColor = '#f44336';
                }
            }
        }
        
        // Event listeners
        document.getElementById('<%= txtNewPassword.ClientID %>').addEventListener('input', function() {
            checkPasswordStrength();
            checkPasswordMatch();
        });
        
        document.getElementById('<%= txtConfirmPassword.ClientID %>').addEventListener('input', checkPasswordMatch);
        
        // Initial check
        window.onload = function() {
            checkPasswordStrength();
            document.getElementById('<%= txtNewPassword.ClientID %>').focus();
        };
    </script>
</asp:Content>
