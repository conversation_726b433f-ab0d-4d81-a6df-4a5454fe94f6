<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="Test_RequestSize.aspx.vb" Inherits="SPMJ.Test_RequestSize" title="Request Size Test Page" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .test-container {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-result {
            background-color: #f0f8ff;
            border: 1px solid #0066cc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #f0fff0;
            border-color: #00cc00;
        }
        .warning {
            background-color: #fff8f0;
            border-color: #ff9900;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="test-container">
        <h2>Request Size Test Page</h2>
        <p>This page tests if the "request entity is too large" fix is working correctly.</p>
        
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <div class="test-result">
                    <h3>Test Results:</h3>
                    <asp:Label ID="lblResults" runat="server" Text="Click the test button to verify the fix."></asp:Label>
                </div>
                
                <asp:Button ID="btnTestLargeRequest" runat="server" Text="Test Large Request" 
                    CssClass="btn btn-primary" OnClick="btnTestLargeRequest_Click" />
                
                <!-- Hidden fields to simulate large ViewState -->
                <asp:HiddenField ID="hiddenData1" runat="server" />
                <asp:HiddenField ID="hiddenData2" runat="server" />
                <asp:HiddenField ID="hiddenData3" runat="server" />
                
                <!-- GridView to simulate typical PILIH button scenario -->
                <asp:GridView ID="gvTestData" runat="server" AutoGenerateColumns="false" 
                    CssClass="table table-striped" OnSelectedIndexChanged="gvTestData_SelectedIndexChanged">
                    <Columns>
                        <asp:TemplateField>
                            <ItemTemplate>
                                <asp:Button ID="btnPilih" runat="server" Text="PILIH" 
                                    CommandName="Select" CssClass="btn btn-sm btn-success" />
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="ID" HeaderText="ID" />
                        <asp:BoundField DataField="Name" HeaderText="Name" />
                        <asp:BoundField DataField="Description" HeaderText="Description" />
                    </Columns>
                </asp:GridView>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
