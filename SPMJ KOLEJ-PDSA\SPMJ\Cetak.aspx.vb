﻿Imports CrystalDecisions.Shared

Partial Public Class Cetak
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim Tloginfo As CrystalDecisions.Shared.TableLogOnInfo

        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        Select Case Session("Lpr_Nama")
            Case "Senarai_Calon" 'full list
                Dim report As SN_Calon = New SN_Calon
                report.SetParameterValue(0, Session("Var_1"))
                report.SetParameterValue(1, Session("Var_2"))
                report.SetParameterValue(2, Session("Tajuk"))
                report.SetParameterValue(3, Session("Var_3"))
                report.SetParameterValue(4, Session("Var_4"))

                'ADD ON 04 DEC 2012 FOR EASY ACCESS TO SPMJ DATABASE 
                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
                'report.SetDatabaseLogon("sa", "aljaroom5621", "************", ConfigurationManager.AppSettings("dB"), True)
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                'report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Senarai_Calon0" 'saring list
                Dim report As SN_Calon0 = New SN_Calon0
                report.SetParameterValue(0, Session("Var_1"))
                report.SetParameterValue(1, Session("Var_2"))
                report.SetParameterValue(2, Session("Tajuk"))
                report.SetParameterValue(3, Session("Var_3"))
                report.SetParameterValue(4, Session("Var_4"))

                'ADD ON 04 DEC 2012 FOE EASY ACCESS TO SPMJ DATABASE 
                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next

                'report.SetDatabaseLogon("sa", "aljaroom5621", "************", ConfigurationManager.AppSettings("dB"), True)
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Senarai_Calon1" 'jana list
                Dim report As SN_Calon1 = New SN_Calon1
                report.SetParameterValue(0, Session("Var_1"))
                report.SetParameterValue(1, Session("Var_2"))
                report.SetParameterValue(2, Session("Tajuk"))
                report.SetParameterValue(3, Session("Var_3"))
                report.SetParameterValue(4, Session("Var_4"))

                'ADD ON 04 DEC 2012 FOE EASY ACCESS TO SPMJ DATABASE 
                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", "************", ConfigurationManager.AppSettings("dB"), True)
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
        End Select

    End Sub

End Class