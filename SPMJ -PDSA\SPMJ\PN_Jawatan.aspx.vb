﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm44
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Gred", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
    End Sub

    Public Sub Reset()
        Tx_NoKP.Text = ""
        Tx_Nama.Text = ""
        Tx_NoPd.Text = ""
        Cb_Jawatan.Items.Clear()
        Tx_NoPd_Baru.Text = ""
        Tx_NoPd_Baru.Enabled = True
        Tx_Tkh_Daftar.Text = ""
        Tx_Tkh_Daftar.Enabled = True
        Tx_Tkh.Text = ""
        chkUndo.Visible = False
        chkUndo.Checked = False
    End Sub

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        If Tx_NoKP.Text = "" Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String

        SQL = "select nama, case j_daftar when 1 then 'JB-'+cast(nopd as varchar(6)) when 2 then 'JM-'+cast(nopd as varchar(6)) "
        SQL += "when 3 then 'PJ-'+cast(nopd as varchar(6)) end, tkh_daftar from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
        Cmd.CommandText = SQL
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Tx_Nama.Text = Rdr(0)
            Tx_NoPd.Text = Rdr(1)
            If Not IsDBNull(Rdr(2)) Then Tx_Tkh.Text = Rdr(2)
        End If
        Rdr.Close()
        Cn.Close()

        If InStr(Tx_NoPd.Text, "JM") > 0 Then
            Cb_Jawatan.Items.Clear() : Cb_Jawatan.Items.Add("JURURAWAT BERDAFTAR")
        ElseIf InStr(Tx_NoPd.Text, "PJ") > 0 Then
            Cb_Jawatan.Items.Clear() : Cb_Jawatan.Items.Add("JURURAWAT BERDAFTAR") : Cb_Jawatan.Items.Add("JURURAWAT MASYARAKAT")
        Else
            Cb_Jawatan.Items.Clear()
        End If

        If InStr(Tx_NoPd.Text, "JB") > 0 Or InStr(Tx_NoPd.Text, "JM") > 0 Then chkUndo.Visible = True Else chkUndo.Visible = False
    End Sub

    Protected Sub cmd_Simpan_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Simpan.Click
        If Tx_NoPd_Baru.Text = "" Then Exit Sub
        If Tx_Tkh_Daftar.Text = "" Then Exit Sub
        If Cb_Jawatan.Items.Count = 0 Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String = ""
        Dim nopd_lama As String = Tx_NoPd.Text, nopd_baru As String = Tx_NoPd_Baru.Text
        If IsNumeric(nopd_baru) Then nopd_baru = nopd_baru Else nopd_baru = Mid(Tx_NoPd_Baru.Text, 4, Tx_NoPd_Baru.Text.Length - 3)
        If IsNumeric(nopd_lama) Then nopd_lama = nopd_lama Else nopd_lama = Mid(Tx_NoPd.Text, 4, Tx_NoPd.Text.Length - 3)

        If chkUndo.Checked = True Then
            If Cb_Jawatan.SelectedIndex = 0 Then
                Cmd.CommandText = "select * from jt_penuh where nopd = '" & nopd_baru & "' and j_daftar=3"
            Else
                Cmd.CommandText = "select * from jt_penuh where nopd = '" & nopd_baru & "' and j_daftar=2"
            End If
        Else
            If Cb_Jawatan.SelectedIndex = 0 Then
                Cmd.CommandText = "select * from jt_penuh where nopd = '" & nopd_baru & "' and j_daftar=1"
            Else
                Cmd.CommandText = "select * from jt_penuh where nopd = '" & nopd_baru & "' and j_daftar=2"
            End If
        End If
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Telah Wujud!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        If chkUndo.Checked = True Then
            If Cb_Jawatan.SelectedIndex = 0 Then
                If InStr(Tx_NoPd_Baru.Text, "PJ", 0) > 0 Then SQL = "update jt_penuh set j_daftar=3, tkh_daftar = " & Chk_Tkh(Tx_Tkh_Daftar.Text) & ", nopd = '" & nopd_baru & "' "
                If InStr(Tx_NoPd.Text, "JB", 0) > 0 Then SQL += ", nopd_jb = '" & nopd_lama & "', tkh_jb= " & Chk_Tkh(Tx_Tkh.Text) & " "
                If InStr(Tx_NoPd.Text, "JM", 0) > 0 Then SQL += ", nopd_jm = '" & nopd_lama & "', tkh_jm= " & Chk_Tkh(Tx_Tkh.Text) & " "
                SQL += "where nokp = '" & Tx_NoKP.Text & "' "
            End If
            If Cb_Jawatan.SelectedIndex = 1 Then
                If InStr(Tx_NoPd_Baru.Text, "JM", 0) > 0 Then SQL = "update jt_penuh set j_daftar=2, tkh_daftar = " & Chk_Tkh(Tx_Tkh_Daftar.Text) & ", nopd = '" & nopd_baru & "' where nokp = '" & Tx_NoKP.Text & "'"
            End If
        Else
            If Cb_Jawatan.SelectedIndex = 0 Then
                If InStr(Tx_NoPd.Text, "JM", 0) > 0 Then SQL = "update jt_penuh set j_daftar=1, tkh_daftar = " & Chk_Tkh(Tx_Tkh_Daftar.Text) & ", nopd = '" & nopd_baru & "', nopd_jb='" & nopd_baru & "', tkh_jb=" & Chk_Tkh(Tx_Tkh_Daftar.Text) & ", nopd_jm='" & nopd_lama & "', tkh_jm=" & Chk_Tkh(Tx_Tkh.Text) & " where nokp = '" & Tx_NoKP.Text & "'"
                If InStr(Tx_NoPd.Text, "PJ", 0) > 0 Then SQL = "update jt_penuh set j_daftar=1, tkh_daftar = " & Chk_Tkh(Tx_Tkh_Daftar.Text) & ", nopd = '" & nopd_baru & "', nopd_jb='" & nopd_baru & "', tkh_jb=" & Chk_Tkh(Tx_Tkh_Daftar.Text) & ", nopd_pj='" & nopd_lama & "', tkh_pj=" & Chk_Tkh(Tx_Tkh.Text) & " where nokp = '" & Tx_NoKP.Text & "'"
            End If
            If Cb_Jawatan.SelectedIndex = 1 Then
                If InStr(Tx_NoPd.Text, "PJ", 0) > 0 Then SQL = "update jt_penuh set j_daftar=2, tkh_daftar = " & Chk_Tkh(Tx_Tkh_Daftar.Text) & ", nopd = '" & nopd_baru & "', nopd_pj='" & nopd_lama & "', tkh_pj=" & Chk_Tkh(Tx_Tkh.Text) & " where nokp = '" & Tx_NoKP.Text & "'"
            End If
        End If

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()
        Reset()
        Msg(Me, "Rekod telah dikemaskini...")
    End Sub

    Protected Sub chkUndo_CheckedChanged(ByVal sender As Object, ByVal e As EventArgs) Handles chkUndo.CheckedChanged
        If chkUndo.Checked = True Then
            If InStr(Tx_NoPd.Text, "JB") > 0 Then
                Cb_Jawatan.Items.Clear() : Cb_Jawatan.Items.Add("PENOLONG JURURAWAT") : Cb_Jawatan.Items.Add("JURURAWAT MASYARAKAT")
            ElseIf InStr(Tx_NoPd.Text, "JM") > 0 Then
                Cb_Jawatan.Items.Clear() : Cb_Jawatan.Items.Add("PENOLONG JURURAWAT")
            Else
                Cb_Jawatan.Items.Clear()
            End If            
        Else
            Cb_Jawatan.Items.Clear()
            Tx_NoPd_Baru.Text = ""
            Tx_Tkh_Daftar.Text = ""
        End If
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        If Cb_Jawatan.Items.Count = 0 Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim nopd As String

        Tx_NoPd_Baru.Text = ""
        Tx_Tkh_Daftar.Text = ""
        If chkUndo.Checked = True Then
            If Cb_Jawatan.SelectedIndex = 0 Then
                Cmd.CommandText = "select nopd_pj, tkh_pj from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
                nopd = "PJ-"
            Else
                Cmd.CommandText = "select nopd_jm, tkh_jm from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
                nopd = "JM-"
            End If
        Else
            If Cb_Jawatan.SelectedIndex = 0 Then
                Cmd.CommandText = "select nopd_jb, tkh_jb from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
                nopd = "JB-"
            Else
                Cmd.CommandText = "select nopd_jm, tkh_jm from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
                nopd = "JM-"
            End If
        End If

        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr(0)) Then Tx_NoPd_Baru.Text = nopd & Rdr(0)
            If Not IsDBNull(Rdr(1)) Then Tx_Tkh_Daftar.Text = Rdr(1)
        End If
        Rdr.Close()
        Cn.Close()
    End Sub
End Class