﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm50
    Inherits System.Web.UI.Page

    Public x As String

    Public Sub Surat_Majikan_Ulangan()
        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Tuan :</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Kami : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> KKM 87/A3/9/35(   )</td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Tarikh &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> " + Tarikh(1) + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br/>Pengurus Sumber Manusia,"
        x += "<br/>" + StrConv(Session("mj_nama"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("mj_alamat"), VbStrConv.ProperCase)
        x += "<br/>" + Session("mj_poskod") + " " + StrConv(Session("mj_bandar"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("mj_negeri"), VbStrConv.ProperCase) + "."
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt;'>Tuan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>PERMOHONAN PERAKUAN PENGAMALAN SEMENTARA."
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'>Dengan segala hormatnya merujuk perkara di atas."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Merujuk kepada garis panduan Penggajian Jururawat Terlatih Warganegara Asing (JTWA) Bil. (89) dlm. KKM-87 (572), bagi membolehkan <b>permohonan perlanjutan Perakuan Pengamalan Sementara (TPC) untuk tahun ketiga dan seterusnya diproses, pihak tuan perlu mengemukakan perkara-perkara berikut:-</b>"
        x += "</div>"
        x += "<br/>"
        '----
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'>"
        x += "<tr><td style='width:9.8%;'></td><td>i.</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>Borang permohonan;</td></tr>"
        x += "<tr><td style='width:9.8%;'></td><td>ii.</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>Salinan Kontrak Kerja yang <b>disahkan</b> (untuk JTA yang terlibat);</td></tr>"
        x += "<tr><td style='width:9.8%;'></td><td style='vertical-align:top;' >iii.</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>Laporan penilaian prestasi <b>terkini</b> dan alasan untuk dilanjutkan perkhidmatan;</td></tr>"
        x += "<tr><td style='width:9.8%;'></td><td>iv.</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>Salinan pasport dan visa yang <b>disahkan</b>;</td></tr>"
        x += "<tr><td style='width:9.8%;'></td><td>v.</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>Gambar pasport dan fee RM70.00</td></tr>"
        x += "</table>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "3.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sehubungan dengan itu, sila ambil maklum Mesyuarat Lembaga Jururawat Malaysia kali ke-98 pada <b> 18 Mac 2010 </b>telah memutuskan <b>JTWA yang mempunyai pengalaman klinikal tidak kurang dari TIGA TAHUN dan KEPAKARAN<i>(specialization)</i> dalam bidang pengkhususan kejururawatan sahaja layak dipertimbangkan berkhidmat di Malaysia.</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "4.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Bersama-sama ini disertakan ................. Perakuan Pengamalan Sementara, Surat Akuan Penerimaan untuk dipenuhi oleh jururawat seperti di lampiran 1, resit bayaran bernombor .................  berjumlah .................."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "5.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sila kembalikan Surat Akuan Penerimaan Perakuan Pengamalan Sementara setelah dipenuhi oleh jururawat yang berkenaan dengan segera ke LJM."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "6.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>SAHLAKU TPC HANYA UNTUK 12 BULAN SAHAJA."
        x += " SILA PERBAHARUI TPC INI 2 BULAN SEBELUM TAMAT TEMPOH. KELEWATAN / KEGAGALAN BERBUAT DEMIKIAN MENYEBABKAN TPC TERBATALKAN.</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"

        x += "<br>"
        x += "<br>"
        x += "</br>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ. ABD. WAHAB) </b>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."

        x += "<br/>" '<div style='font-family: Arial; font-size: 12pt;'
        x += "<br> s.k.:- Fail Program"
        x += "</div>"

        x += Footer_Surat()

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select dc_amalan 'MAJIKAN', alamat, poskod, bandar, dc_negeri 'NEGERI' from pn_tpt_amalan pta inner join pn_negeri pn on pta.negeri=pn.id_negeri where dc_amalan like '%" & Tx_Nama.Text & "%'  order by dc_amalan"
        Tb = "pn_tpt_amalan"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Nama.Text.Trim = "" Then Exit Sub
        Cari("")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(3).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("mj_nama") = Gd.SelectedRow.Cells(2).Text
        Session("mj_alamat") = Gd.SelectedRow.Cells(3).Text
        Session("mj_poskod") = Gd.SelectedRow.Cells(4).Text
        Session("mj_bandar") = Gd.SelectedRow.Cells(5).Text
        Session("mj_negeri") = Gd.SelectedRow.Cells(6).Text
        Surat_Majikan_Ulangan()
    End Sub
End Class