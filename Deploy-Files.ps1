# SPMJ KOLEJ File Copy Deployment (No Admin Required)
param(
    [string]$TargetPath = "C:\temp\SPMJ_Deployment",
    [string]$MicroserviceUrl = "http://localhost:5000"
)

Write-Host "=== SPMJ KOLEJ FILE DEPLOYMENT ===" -ForegroundColor Green

# Check email microservice
Write-Host "`nChecking email microservice..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$MicroserviceUrl/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "Email microservice is running and healthy" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Gray
} catch {
    Write-Host "Email microservice not accessible: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Continuing with deployment..." -ForegroundColor Gray
}

# Deploy files to temp location
Write-Host "`nDeploying files to: $TargetPath" -ForegroundColor Cyan

$sourceFiles = @{
    "SPMJ KOLEJ-PDSA\SPMJ\SPMJ_KOLEJ_PWD.dll" = "bin\SPMJ_KOLEJ_PWD.dll"
    "SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx" = "Pwd.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx" = "ForcePasswordChange.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\Web.config" = "Web.config"
    "SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx.vb" = "App_Code\Pwd.aspx.vb"
    "SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx.vb" = "App_Code\ForcePasswordChange.aspx.vb"
    "SPMJ KOLEJ-PDSA\SPMJ\EmailServiceClient.vb" = "App_Code\EmailServiceClient.vb"
    "SPMJ KOLEJ-PDSA\SPMJ\PasswordHelper.vb" = "App_Code\PasswordHelper.vb"
}

# Create target directories
if (-not (Test-Path $TargetPath)) {
    New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
    Write-Host "Created deployment directory: $TargetPath" -ForegroundColor Green
}

$dirs = @("bin", "App_Code")
foreach ($dir in $dirs) {
    $dirPath = "$TargetPath\$dir"
    if (-not (Test-Path $dirPath)) {
        New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    }
}

# Copy files
Write-Host "`nCopying files..." -ForegroundColor Cyan
$deployedCount = 0
$totalCount = $sourceFiles.Count

foreach ($source in $sourceFiles.Keys) {
    $target = "$TargetPath\$($sourceFiles[$source])"
    if (Test-Path $source) {
        try {
            Copy-Item -Path $source -Destination $target -Force
            $fileInfo = Get-ChildItem $target
            Write-Host "✓ $($sourceFiles[$source]) ($($fileInfo.Length) bytes)" -ForegroundColor Green
            $deployedCount++
        } catch {
            Write-Host "✗ Failed to deploy $source : $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ Source file not found: $source" -ForegroundColor Red
    }
}

Write-Host "`nDeployment Summary:" -ForegroundColor White
Write-Host "Files deployed: $deployedCount / $totalCount" -ForegroundColor $(if ($deployedCount -eq $totalCount) { "Green" } else { "Yellow" })

# Verification
Write-Host "`nVerifying deployment..." -ForegroundColor Cyan
$verifyFiles = @(
    "$TargetPath\bin\SPMJ_KOLEJ_PWD.dll",
    "$TargetPath\Pwd.aspx",
    "$TargetPath\ForcePasswordChange.aspx",
    "$TargetPath\Web.config"
)

foreach ($file in $verifyFiles) {
    if (Test-Path $file) {
        $info = Get-ChildItem $file
        Write-Host "✓ $(Split-Path $file -Leaf) - $($info.Length) bytes - $($info.LastWriteTime)" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing: $(Split-Path $file -Leaf)" -ForegroundColor Red
    }
}

Write-Host "`n=== DEPLOYMENT COMPLETE ===" -ForegroundColor Green
Write-Host "Files deployed to: $TargetPath" -ForegroundColor White
Write-Host "Next steps:" -ForegroundColor White
Write-Host "1. Copy files from $TargetPath to your web server" -ForegroundColor Gray
Write-Host "2. Configure IIS application pointing to the deployment folder" -ForegroundColor Gray
Write-Host "3. Test the application in browser" -ForegroundColor Gray
