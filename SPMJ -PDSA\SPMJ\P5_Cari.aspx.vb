﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm28
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', CONVERT(varchar(12), jpn.nn_tkh, 103) + '-31/12/' + CAST(year(getdate()) as varchar(4))  as 'TEMPOH NOTIS NIAT' from jt_penuh jp left outer join jt_penuh_nniat jpn on jp.nokp=jpn.nokp and jpn.nn_tahun=year(getdate()) left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.apc_tahun = year(getdate()) where jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Public Sub Memori()
        '***********************************
        '  Kena Tambah Variable untuk Id_PG
        '***********************************
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD'  from tmp_nniat ta inner join jt_penuh jp on ta.nokp = jp.nokp order by nama"
        Tb = "tmp_apc"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd_Pilih.DataSource = List_Data.Tables(Tb)
        Gd_Pilih.DataBind()
        If Gd_Pilih.Rows.Count > 0 Then cmd_proses.visible = True Else cmd_proses.visible = False
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Fix security access 23072018 - OSH 
        If Session("ORIGIN") = "yes" Then
            If IsPostBack Then Exit Sub
            Memori()
        Else
            Response.Redirect("p0_Login.aspx")
        End If

        'Comment Ori 23072018 - OSH
        'If IsPostBack Then Exit Sub
        'Memori()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(30)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(4).Width = Unit.Pixel(50)
        e.Row.Cells(5).Width = Unit.Pixel(50)
        e.Row.Cells(6).Width = Unit.Pixel(70)
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Cb_Jenis.SelectedIndex < 1 Then Exit Sub
        If Tx_Nama.Text = "" And Tx_NoKP.Text = "" And Tx_NoPd.Text = "" Then Exit Sub
        Cari("nama like '" & Tx_Nama.Text & "%' and jp.nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%'")
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Try
            Cmd.CommandText = "insert tmp_nniat (nokp) values ('" & Gd.SelectedRow.Cells(3).Text & "')"
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Memori()
        Catch ex As Exception
            Cn.Close()
            Msg(Me, "Rekod Sudah Dipilih!")
        End Try
    End Sub

    Private Sub Gd_Pilih_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_Pilih.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(1)
        e.Row.Cells(1).Width = Unit.Pixel(20)
        e.Row.Cells(3).Width = Unit.Pixel(60)
        e.Row.Cells(4).Width = Unit.Pixel(60)
        e.Row.VerticalAlign = VerticalAlign.Top
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
        e.Row.Cells(3).ForeColor = Drawing.Color.Maroon
        e.Row.Cells(3).Font.Bold = True
        e.Row.Font.Size = FontUnit.Point(7)

    End Sub

    Protected Sub Gd_Pilih_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd_Pilih.SelectedIndexChanged
        'If Msg(Me, "Padam Rekod Ini - " & Gd_Pilih.SelectedRow.Cells(3).Text & "?", MsgBoxStyle.YesNo + MsgBoxStyle.SystemModal, "Pembaharuan APC") = MsgBoxResult.Yes Then
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "delete tmp_nniat where nokp = '" & Gd_Pilih.SelectedRow.Cells(3).Text & "'"
        Cmd.ExecuteNonQuery()
        Cn.Close()
        Memori()
        If Gd_Pilih.Rows.Count < 1 Then cmd_Proses.Visible = False
        'End If
    End Sub

    Protected Sub cmd_Proses_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Proses.Click
        Response.Redirect("p5_proses.aspx")
    End Sub
End Class