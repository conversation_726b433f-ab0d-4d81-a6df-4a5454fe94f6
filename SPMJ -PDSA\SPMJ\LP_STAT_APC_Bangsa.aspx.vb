﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_APC_Bangsa
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 20 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'JAWATAN
        With Cb_Jawatan
            .Items.Clear()
            .Items.Add("Jururawat Berdaftar")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("Jururawat Masyarakat")
            .Items.Item(.Items.Count - 1).Value = "2"
            .Items.Add("Penolong Jururawat")
            .Items.Item(.Items.Count - 1).Value = "3"
        End With
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim j_daftar As Integer = Cb_Jawatan.SelectedValue
        Dim M, C, I, L, BSB, BSW, X, T1, T2, T3 As Long
        Dim Tajuk, Tajuk2 As String
        Tajuk = "Laporan Statistik Pengeluaran APC Bagi " & Cb_Jawatan.SelectedItem.Text & ", " & tahun
        Tajuk2 = "mengikut Negeri, Sektor, dan Bangsa"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        'Response.Write("<table  border=""1"" >")
        T1 = 0 : T2 = 0 : T3 = 0
        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='22' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='22' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: left;'>NEGERI</td>"
        Header += "    <td colspan='7' style='vertical-align: middle; text-align: center;'>KERAJAAN</td>"
        Header += "    <td colspan='7' style='vertical-align: middle; text-align: center;'>SWASTA</td>"
        Header += "    <td colspan='7' style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Header += "<tr style='vertical-align: middle; text-align: center;'>"
        Header += "    <td>M</td><td>C</td><td>I</td><td>L</td><td>BSB</td><td>BSW</td><td>X</td> "
        Header += "    <td>M</td><td>C</td><td>I</td><td>L</td><td>BSB</td><td>BSW</td><td>X</td> "
        Header += "    <td>M</td><td>C</td><td>I</td><td>L</td><td>BSB</td><td>BSW</td><td>X</td> "
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'NEGERI
        Cmd.CommandText = "SELECT Id_NEGERI, Dc_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "Negeri")
        Rdr.Close()
        'Cn.Close()


        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td>" + dr.Item(1) + "</td> "

            M = 0 : C = 0 : I = 0 : L = 0 : BSB = 0 : BSW = 0 : X = 0
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=1)"
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=1) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then M = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=2)"
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=2)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then C = Rdr(0)
            Rdr.Close()
            'Comment Original 25062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=3)"
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=3) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then I = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 - OSH
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=4)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=4) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=5)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=5)and apc_batal= 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then BSB = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=6)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=6)and apc_batal =0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then BSW = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh )"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=1 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh ) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - M - C - I - L - BSB - BSW
            Rdr.Close()
            T1 = T1 + M + C + I + L + BSB + BSW + X
            Header += "    <td>" & M & "</td><td>" & C & "</td><td>" & I & "</td><td>" & L & "</td><td>" & BSB & "</td><td>" & BSW & "</td><td>" & X & "</td>"

            M = 0 : C = 0 : I = 0 : L = 0 : BSB = 0 : BSW = 0 : X = 0
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=1)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=1) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then M = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=2)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=2)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then C = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=3)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=3)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then I = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=4)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=4)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=5)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=5)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then BSB = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=6)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=6) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then BSW = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - M - C - I - L - BSB - BSW
            Rdr.Close()
            T2 = T2 + M + C + I + L + BSB + BSW + X
            Header += "    <td>" & M & "</td><td>" & C & "</td><td>" & I & "</td><td>" & L & "</td><td>" & BSB & "</td><td>" & BSW & "</td><td>" & X & "</td>"

            M = 0 : C = 0 : I = 0 : L = 0 : BSB = 0 : BSW = 0 : X = 0
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=1)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=1) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then M = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=2)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=2)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then C = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=3)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=3)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then I = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=4)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=4) and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=5)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=5)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then BSB = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=6)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where bangsa=6)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then BSW = Rdr(0)
            Rdr.Close()
            'Comment Original 26062020 -OSH 
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh)"
            'Improve query active APC only 26062020 - OSH 
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh)and apc_batal = 0"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - M - C - I - L - BSB - BSW
            Rdr.Close()
            T3 = T3 + M + C + I + L + BSB + BSW + X
            Header += "    <td>" & M & "</td><td>" & C & "</td><td>" & I & "</td><td>" & L & "</td><td>" & BSB & "</td><td>" & BSW & "</td><td>" & X & "</td>"

            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td colspan='7' style='vertical-align: middle; text-align: center;'>" & T1 & "</td>"
        Header += "    <td colspan='7' style='vertical-align: middle; text-align: center;'>" & T2 & "</td>"
        Header += "    <td colspan='7' style='vertical-align: middle; text-align: center;'>" & T3 & "</td>"
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub
End Class