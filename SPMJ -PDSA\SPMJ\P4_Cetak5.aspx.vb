﻿Public Partial Class WebForm51
    Inherits System.Web.UI.Page
    Public x As String

    
    Public Sub Surat_Imigresen_Baru()
        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt color:#151B8D;'> Ruj. Tuan : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt color:#151B8D;'> Ruj. Kami : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt color:#000000;'> KKM 87/A3/1/158( ... ) </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt color:#151B8D;'> Tarikh &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt color:#000000;'> " + Tarikh(1) + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<div style='font-family: Arial; font-size: 12pt'>"
        x += "<br/>Pengarah"
        x += "<br/>Jabatan Imigresen Malaysia"
        x += "<br/>Tingkat 3, (Podium Blok 2G4), Presint 2"
        x += "<br/>Pusat Pentadbiran Kerajaan Persekutuan"
        x += "<br/>62550 Putrajaya."
        x += "<br/>(u/p: Pengarah Bahagian Pas Penggajian)"
        x += "</div>"

        x += "<br/>"
        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 12pt'>Tuan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>KEPUTUSAN MESYUARAT PENGAMBILAN PERKHIDMATAN JURURAWAT TERLATIH"
        If Cb_Jenis.SelectedValue = 1 Then x += ", INSTRUKTOR KLINIKAL DAN PENGAJAR JURURAWAT WARGANEGARA ASING."
        If Cb_Jenis.SelectedValue = 3 Then x += " WARGANEGARA ASING SEBAGAI INSTRUKTOR KLINIKAL DAN PENGAJAR JURURAWAT."
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt'>Dengan segala hormatnya merujuk perkara di atas."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Dengan ini dimaklumkan bahawa Mesyuarat Jawatankuasa Kelulusan Pengambilan Perkhidmatan Jururawat Terlatih "
        If Cb_Jenis.SelectedValue = 1 Then x += ", Instruktor Klinikal dan Pengajar Jururawat "
        x += "Warganegara Asing "
        If Cb_Jenis.SelectedValue = 3 Then x += "sebagai Instruktor Klinikal dan Pengajar Jururawat "
        x += "telah diadakan pada ------------- dan mesyuarat tersebut telah menetapkan keputusan seperti di lampiran 1 untuk bertugas di "
        If Cb_Jenis.SelectedValue = 1 Then x += "Fasiliti Kesihatan, Kolej dan Universiti "
        If Cb_Jenis.SelectedValue = 2 Then x += "Fasiliti Kesihatan "
        If Cb_Jenis.SelectedValue = 3 Then x += "Kolej / Universiti "
        x += "yang telah memohon perkhidmatan mereka."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "3.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sehubungan dengan itu, "
        If Cb_Jenis.SelectedValue = 1 Then x += "Jururawat Terlatih, Instruktor Klinikal dan Pengajar Jururawat "
        If Cb_Jenis.SelectedValue = 2 Then x += "Jururawat Terlatih "
        If Cb_Jenis.SelectedValue = 3 Then x += "Instruktor Klinikal dan Pengajar Jururawat "
        x += "Warganegara Asing yang telah diluluskan perlu melengkapkan perkara berikut :-"
        x += "</div><br/>"
        
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'>"        
        x += "<tr><td style='width:9.5%;'></td><td style='vertical-align:top;' >3.1</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "Majikan perlu <b>memperolehi Perakuan Pengamalan Sementara (TPC)</b> dari Lembaga Jururawat Malaysia (LJM)  terlebih dahulu <b>sebelum</b> memohon "
        If Cb_Jenis.SelectedValue = 1 Then x += "<b>kelulusan pas pengajian</b> dari Kementerian Pengajian Tinggi Malaysia <b>(Instruktor Klinikal dan Pengajar Jururawat sahaja)</b>. Selepas itu, majikan perlu memohon "
        If Cb_Jenis.SelectedValue = 3 Then x += "<b>kelulusan pas pengajian</b> dari Kementerian Pengajian Tinggi Malaysia. Selepas itu, majikan perlu memohon "
        x += "<b>kelulusan <i>Multiple Entry Visa</i></b> dari Jabatan Imigresen.</td></tr>"
        x += "<tr></tr>"
        x += "<tr><td style='width:9.5%;'></td><td style='vertical-align:top;' >3.2</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "Setelah majikan <b>mendapat TPC</b> "
        If Cb_Jenis.SelectedValue = 1 Then x += "<b>, kelulusan pas pengajian</b> dari Kementerian Pengajian Tinggi Malaysia <b>(Instruktor Klinikal dan Pengajar Jururawat sahaja)</b> "
        If Cb_Jenis.SelectedValue = 3 Then x += "<b>, kelulusan pas pengajian</b> dari Kementerian Pengajian Tinggi Malaysia "
        x += "dan <b><i>Multiple Entry Visa</i></b> dari Jabatan Imigresen Malaysia, Jururawat Terlatih Warganegara Asing tersebut <b>dibenarkan berkhidmat</b> di "
        If Cb_Jenis.SelectedValue = 1 Then x += "Fasiliti Kesihatan dan Kolej / Universiti "
        If Cb_Jenis.SelectedValue = 2 Then x += "Fasiliti Kesihatan "
        If Cb_Jenis.SelectedValue = 3 Then x += "Kolej / Universiti "
        x += "yang mengambil perkhidmatan mereka.</td></tr>"
        
        x += "</table>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "4.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tawaran kelulusan ini hanya sah laku dalam tempoh <b>enam (6) bulan</b> dari tarikh kelulusan dan <b>tamat pada -------------.</b> Permohonan Perakuan Pengamalan Sementara (TPC) perlu dibuat sebelum "
        x += "tamat tempoh tawaran kelulusan. <b>Kelewatan/kegagalan majikan berbuat demikian menyebabkan kelulusan TPC terbatal dan permohonan pendaftaran perakuan pengamalan sementara (TPC) perlu dibuat semula.</b>"
        x += "</div>"


        x += "<br/><div style='font-family: Arial; font-size: 12pt'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"


        x += "<br>"
        x += "<br>"
        x += "</br>"


        x += "<br/><div style='font-family: Arial; font-size: 12pt'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ. ABD. WAHAB) </b>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."

        x += "<br/><div style='font-family: Arial; font-size: 12pt'>"
        x += "<br>s.k -"
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  Timbalan Pengarah "
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  Cawangan Kawalan Amalan Perubatan Swasta"
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  Bahagian Amalan Perubatan"
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  Kementerian Kesihatan Malaysia"
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  62590 Putrajaya"
        x += "<br>"
        x += "<br>"
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  Pengurus Sumber Manusia"
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  Hospital / Pusat Perubatan / Institusi"
        x += "<br>"
        x += "<br>&nbsp; &nbsp; &nbsp; &nbsp;  Fail Program"
        x += "</div>"

        x += Footer_Surat()

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Cb_Jenis.SelectedIndex < 0 Then Cb_Jenis.Focus() : Exit Sub
        Surat_Imigresen_Baru()
    End Sub
End Class