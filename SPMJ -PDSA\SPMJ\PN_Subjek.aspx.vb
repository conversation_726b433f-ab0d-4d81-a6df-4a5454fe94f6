﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm62
    Inherits System.Web.UI.Page

    Public Sub Reset()
        Session("PN_Pinda") = False
        Tx_Id.ReadOnly = False
        Tx_Id.Text = ""
        Tx_subjek.Text = ""
        Cb_Jenis.SelectedIndex = -1
        cmd_baru.Visible = False
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select dc_subjek 'SUBJEK', id_subjek, id_jenis 'KELAYAKAN' from pn_subjek order by id_jenis, dc_subjek"
        Tb = "pn_subjek"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Exam", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Cari("")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Dim SQL As String

        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If Session("PN_Pinda") Then
            SQL = "update pn_subjek set dc_subjek = '" & Tx_Subjek.Text.Trim.ToUpper & "', id_jenis='" & Cb_Jenis.SelectedItem.Text & "'  where id_subjek = '" & Tx_Id.Text & "'"
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Tx_Id.Text = ""
            Tx_Subjek.Text = ""
            Cari("")
            Reset()
            Msg(Me, "Rekod Telah Dikemaskini...")
            Exit Sub
        End If

        'If Tx_Id.Text = "" Then Msg(Me, "Sila isi Kod Negara!") : Exit Sub
        If Tx_Subjek.Text = "" Then Msg(Me, "Sila isi Subjek!") : Exit Sub
        If Cb_Jenis.SelectedIndex < 1 Then Msg(Me, "Sila pilih Kelayakan!") : Exit Sub

        If Tx_Id.Text = "" Then        
            Cmd.CommandText = "select * from pn_subjek where id_jenis = '" & Cb_Jenis.SelectedItem.Text & "' and dc_subjek='" & Tx_Subjek.Text.Trim.ToUpper & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Msg(Me, "Rekod Telah Ada!")
                Rdr.Close()
                Exit Sub
            End If
            Rdr.Close()
        Else
            Cmd.CommandText = "select * from pn_subjek where id_subjek = " & CInt(Tx_Id.Text.Trim)
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Msg(Me, "Rekod Telah Ada!")
                Rdr.Close()
                Exit Sub
            End If
            Rdr.Close()
        End If

        SQL = "insert pn_subjek (dc_subjek, id_jenis) select '" & Tx_Subjek.Text.Trim.ToUpper & "', '" & Cb_Jenis.SelectedItem.Text & "'"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Tx_Id.Text = ""
            Tx_Subjek.Text = ""
            Msg(Me, "Rekod Telah Disimpan...")
            Cari("")
        Catch ex As Exception
            Msg(Me, "Ralat!")
        End Try
        Reset()

    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(3).Visible = False
        e.Row.Cells(0).Wrap = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("PN_Padam") Then
            Session("PN_Padam") = False
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Dim SQL As String
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "delete from pn_subjek where id_subjek='" & Gd.SelectedRow.Cells(3).Text & "' and dc_subjek='" & Gd.SelectedRow.Cells(2).Text & "'"
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Tx_Id.Text = ""
                Msg(Me, "Rekod Telah Dipadam...")
                Cari("")
                Tx_Id.Text = ""
                Tx_Subjek.Text = ""
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
            Cn.Close()
            Exit Sub
        End If
        Reset()
        Session("PN_Pinda") = True
        cmd_baru.Visible = True
        Tx_Id.Text = Gd.SelectedRow.Cells(3).Text
        Tx_Subjek.Text = Gd.SelectedRow.Cells(2).Text
        Select Case Gd.SelectedRow.Cells(4).Text
            Case "PMR" : Cb_Jenis.SelectedIndex = 1
            Case "SPM" : Cb_Jenis.SelectedIndex = 2
            Case "STPM" : Cb_Jenis.SelectedIndex = 3
        End Select
    End Sub

    Protected Sub Cb_Padam_Click(ByVal sender As Object, ByVal e As EventArgs)
        Session("PN_Padam") = True
    End Sub

    Protected Sub cmd_baru_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_baru.Click
        Reset()
        Tx_Id.Focus()
    End Sub
End Class