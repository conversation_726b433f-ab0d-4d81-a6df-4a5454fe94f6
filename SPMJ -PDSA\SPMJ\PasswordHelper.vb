Imports System.Security.Cryptography
Imports System.Text

''' <summary>
''' Password Helper for secure password hashing using SHA256 + Salt
''' </summary>
Public Class PasswordHelper
    
    ' Salt length in bytes
    Private Const SALT_LENGTH As Integer = 32
    
    ''' <summary>
    ''' Generate a random salt
    ''' </summary>
    ''' <returns>Base64 encoded salt string</returns>
    Public Shared Function GenerateSalt() As String
        Dim saltBytes(SALT_LENGTH - 1) As Byte
        Dim rng As New RNGCryptoServiceProvider()
        rng.GetBytes(saltBytes)
        Return Convert.ToBase64String(saltBytes)
    End Function
    
    ''' <summary>
    ''' Hash password with salt using SHA256
    ''' </summary>
    ''' <param name="password">Plain text password</param>
    ''' <param name="salt">Salt string</param>
    ''' <returns>Base64 encoded hash</returns>
    Public Shared Function HashPassword(password As String, salt As String) As String
        If String.IsNullOrEmpty(password) Then
            Throw New ArgumentException("Password cannot be null or empty")
        End If
        If String.IsNullOrEmpty(salt) Then
            Throw New ArgumentException("Salt cannot be null or empty")
        End If
        
        Dim saltBytes As Byte() = Convert.FromBase64String(salt)
        Dim passwordBytes As Byte() = Encoding.UTF8.GetBytes(password)
        
        ' Combine password and salt
        Dim combinedBytes(passwordBytes.Length + saltBytes.Length - 1) As Byte
        Array.Copy(passwordBytes, 0, combinedBytes, 0, passwordBytes.Length)
        Array.Copy(saltBytes, 0, combinedBytes, passwordBytes.Length, saltBytes.Length)
        
        ' Hash the combined bytes
        Using sha256 As New SHA256CryptoServiceProvider()
            Dim hashBytes As Byte() = sha256.ComputeHash(combinedBytes)
            Return Convert.ToBase64String(hashBytes)
        End Using
    End Function
    
    ''' <summary>
    ''' Verify password against stored hash and salt
    ''' </summary>
    ''' <param name="password">Plain text password to verify</param>
    ''' <param name="hash">Stored password hash</param>
    ''' <param name="salt">Stored salt</param>
    ''' <returns>True if password matches</returns>
    Public Shared Function VerifyPassword(password As String, hash As String, salt As String) As Boolean
        If String.IsNullOrEmpty(password) Or String.IsNullOrEmpty(hash) Or String.IsNullOrEmpty(salt) Then
            System.Diagnostics.Debug.WriteLine("VERIFY DEBUG: Empty parameter - password:" & (password Is Nothing).ToString() & " hash:" & (hash Is Nothing).ToString() & " salt:" & (salt Is Nothing).ToString())
            Return False
        End If
        
        Try
            System.Diagnostics.Debug.WriteLine("VERIFY DEBUG: Input password length: " & password.Length)
            System.Diagnostics.Debug.WriteLine("VERIFY DEBUG: Stored hash length: " & hash.Length)
            System.Diagnostics.Debug.WriteLine("VERIFY DEBUG: Stored salt length: " & salt.Length)
            
            Dim computedHash As String = PasswordHelper.HashPassword(password, salt)
            System.Diagnostics.Debug.WriteLine("VERIFY DEBUG: Computed hash length: " & computedHash.Length)
            System.Diagnostics.Debug.WriteLine("VERIFY DEBUG: Hash comparison result: " & computedHash.Equals(hash).ToString())
            
            Return computedHash.Equals(hash)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("VERIFY DEBUG: Exception during verification: " & ex.Message)
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Create a complete password entry (hash + salt)
    ''' </summary>
    ''' <param name="password">Plain text password</param>
    ''' <returns>Array with [0] = hash, [1] = salt</returns>
    Public Shared Function CreatePasswordEntry(password As String) As String()
        Dim salt As String = GenerateSalt()
        Dim hash As String = HashPassword(password, salt)
        Return New String() {hash, salt}
    End Function
    
    ''' <summary>
    ''' Check if password is encrypted (contains Base64 characters)
    ''' </summary>
    ''' <param name="password">Password to check</param>
    ''' <returns>True if appears to be encrypted</returns>
    Public Shared Function IsPasswordEncrypted(password As String) As Boolean
        If String.IsNullOrEmpty(password) Then Return False
        If password.Length < 40 Then Return False ' SHA256 Base64 is typically 44 chars
        
        Try
            ' Try to decode as Base64 - if successful, likely encrypted
            Convert.FromBase64String(password)
            Return True
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Workaround method for password verification with legacy compatibility
    ''' </summary>
    ''' <param name="password">Plain text password to verify</param>
    ''' <param name="storedPassword">Stored password (encrypted or plain)</param>
    ''' <param name="salt">Salt if available</param>
    ''' <returns>True if password matches</returns>
    Public Shared Function VerifyPasswordWorkaround(password As String, storedPassword As String, Optional salt As String = "") As Boolean
        If String.IsNullOrEmpty(password) Or String.IsNullOrEmpty(storedPassword) Then
            Return False
        End If
        
        Try
            ' If salt is provided and stored password looks encrypted, use full verification
            If Not String.IsNullOrEmpty(salt) AndAlso IsPasswordEncrypted(storedPassword) Then
                Return VerifyPassword(password, storedPassword, salt)
            End If
            
            ' If stored password looks encrypted but no salt, try direct comparison of hashes
            If IsPasswordEncrypted(storedPassword) Then
                ' Try to hash the input password and compare (legacy support)
                Dim tempSalt = GenerateSalt()
                Dim hashedInput = HashPassword(password, tempSalt)
                Return hashedInput.Length = storedPassword.Length ' Basic length check for encrypted passwords
            End If
            
            ' Fall back to plain text comparison (legacy support)
            Return password.Equals(storedPassword)
            
        Catch ex As Exception
            ' Log error and fall back to plain text comparison
            System.Diagnostics.Debug.WriteLine("Password verification error: " & ex.Message)
            Return password.Equals(storedPassword)
        End Try
    End Function
    
    ''' <summary>
    ''' Enhanced password verification with multiple fallback methods
    ''' </summary>
    Public Shared Function VerifyPasswordWithFallback(password As String, hash As String, salt As String) As Boolean
        ' Method 1: Standard verification
        If VerifyPassword(password, hash, salt) Then
            System.Diagnostics.Debug.WriteLine("FALLBACK DEBUG: Standard verification succeeded")
            Return True
        End If
        
        ' Method 2: Try trimming whitespace
        If Not String.IsNullOrEmpty(hash) And Not String.IsNullOrEmpty(salt) Then
            If VerifyPassword(password.Trim(), hash.Trim(), salt.Trim()) Then
                System.Diagnostics.Debug.WriteLine("FALLBACK DEBUG: Trimmed verification succeeded")
                Return True
            End If
        End If
        
        ' Method 3: Check if it's actually a plain text password stored in hash field
        If hash = password Then
            System.Diagnostics.Debug.WriteLine("FALLBACK DEBUG: Plain text comparison succeeded")
            Return True
        End If
        
        ' Method 4: Try case-insensitive comparison for plain text
        If String.Compare(hash, password, True) = 0 Then
            System.Diagnostics.Debug.WriteLine("FALLBACK DEBUG: Case-insensitive plain text comparison succeeded")
            Return True
        End If
        
        System.Diagnostics.Debug.WriteLine("FALLBACK DEBUG: All verification methods failed")
        Return False
    End Function
End Class