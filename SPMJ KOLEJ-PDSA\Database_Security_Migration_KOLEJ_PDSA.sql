-- =============================================================================
-- SPMJ KOLEJ-PDSA Database Schema Enhancement for Secure Authentication
-- Migration Script for Enhanced Security Features
-- Compatible with SPMJ Email Service Integration
-- Date: June 15, 2025
-- =============================================================================

--USE [SPMJ_PDSA]
--GO

PRINT 'Starting SPMJ KOLEJ-PDSA Security Enhancement Migration...'

-- =============================================================================
-- STEP 1: ADD ENHANCED SECURITY COLUMNS TO kj_pengguna TABLE
-- =============================================================================

PRINT 'Step 1: Adding enhanced security columns to kj_pengguna table...'

-- Check if columns exist before adding them
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'salt')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [salt] NVARCHAR(255) NULL
    PRINT '✓ Added salt column'
END
ELSE
    PRINT '⚠ salt column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'email')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [email] NVARCHAR(255) NULL
    PRINT '✓ Added email column'
END
ELSE
    PRINT '⚠ email column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_encrypted')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [pwd_encrypted] BIT DEFAULT 0
    PRINT '✓ Added pwd_encrypted column'
END
ELSE
    PRINT '⚠ pwd_encrypted column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_migrated')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [pwd_migrated] BIT DEFAULT 0
    PRINT '✓ Added pwd_migrated column'
END
ELSE
    PRINT '⚠ pwd_migrated column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'failed_login_attempts')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [failed_login_attempts] INT DEFAULT 0
    PRINT '✓ Added failed_login_attempts column'
END
ELSE
    PRINT '⚠ failed_login_attempts column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'account_locked')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [account_locked] BIT DEFAULT 0
    PRINT '✓ Added account_locked column'
END
ELSE
    PRINT '⚠ account_locked column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'last_successful_login')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [last_successful_login] DATETIME NULL
    PRINT '✓ Added last_successful_login column'
END
ELSE
    PRINT '⚠ last_successful_login column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'last_failed_login')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [last_failed_login] DATETIME NULL
    PRINT '✓ Added last_failed_login column'
END
ELSE
    PRINT '⚠ last_failed_login column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'locked_date')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [locked_date] DATETIME NULL
    PRINT '✓ Added locked_date column'
END
ELSE
    PRINT '⚠ locked_date column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'migration_date')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [migration_date] DATETIME NULL
    PRINT '✓ Added migration_date column'
END
ELSE
    PRINT '⚠ migration_date column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'force_change_date')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [force_change_date] DATETIME NULL
    PRINT '✓ Added force_change_date column'
END
ELSE
    PRINT '⚠ force_change_date column already exists'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'force_change_required')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [force_change_required] BIT DEFAULT 0
    PRINT '✓ Added force_change_required column'
END
ELSE
    PRINT '⚠ force_change_required column already exists'

-- =============================================================================
-- STEP 2: CREATE EMAIL SERVICE INTEGRATION TABLES
-- =============================================================================

PRINT 'Step 2: Creating email service integration tables...'

-- OTP Tokens Table
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'otp_tokens')
BEGIN
    CREATE TABLE [dbo].[otp_tokens] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [user_id] NVARCHAR(50) NOT NULL,
        [otp_code] NVARCHAR(10) NOT NULL,
        [purpose] NVARCHAR(50) NOT NULL,
        [expires_at] DATETIME NOT NULL,
        [used] BIT DEFAULT 0,
        [created_at] DATETIME DEFAULT GETDATE(),
        [used_at] DATETIME NULL,
        [ip_address] NVARCHAR(45) NULL
    )
    
    -- Create index for performance
    CREATE INDEX IX_otp_tokens_user_id ON [dbo].[otp_tokens] ([user_id])
    CREATE INDEX IX_otp_tokens_expires_at ON [dbo].[otp_tokens] ([expires_at])
    
    PRINT '✓ Created otp_tokens table'
END
ELSE
    PRINT '⚠ otp_tokens table already exists'

-- Password Reset Tokens Table
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'password_reset_tokens')
BEGIN
    CREATE TABLE [dbo].[password_reset_tokens] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [user_id] NVARCHAR(50) NOT NULL,
        [token] NVARCHAR(255) NOT NULL,
        [expires_at] DATETIME NOT NULL,
        [used] BIT DEFAULT 0,
        [created_at] DATETIME DEFAULT GETDATE(),
        [used_at] DATETIME NULL,
        [ip_address] NVARCHAR(45) NULL
    )
    
    -- Create index for performance
    CREATE INDEX IX_password_reset_tokens_user_id ON [dbo].[password_reset_tokens] ([user_id])
    CREATE INDEX IX_password_reset_tokens_token ON [dbo].[password_reset_tokens] ([token])
    CREATE INDEX IX_password_reset_tokens_expires_at ON [dbo].[password_reset_tokens] ([expires_at])
    
    PRINT '✓ Created password_reset_tokens table'
END
ELSE
    PRINT '⚠ password_reset_tokens table already exists'

-- Email Audit Log Table
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'email_audit_log')
BEGIN
    CREATE TABLE [dbo].[email_audit_log] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [user_id] NVARCHAR(50) NOT NULL,
        [email_address] NVARCHAR(255) NOT NULL,
        [email_type] NVARCHAR(50) NOT NULL,
        [status] NVARCHAR(20) NOT NULL,
        [error_message] NVARCHAR(MAX) NULL,
        [sent_at] DATETIME DEFAULT GETDATE(),
        [ip_address] NVARCHAR(45) NULL
    )
    
    -- Create index for performance
    CREATE INDEX IX_email_audit_log_user_id ON [dbo].[email_audit_log] ([user_id])
    CREATE INDEX IX_email_audit_log_sent_at ON [dbo].[email_audit_log] ([sent_at])
    
    PRINT '✓ Created email_audit_log table'
END
ELSE
    PRINT '⚠ email_audit_log table already exists'

-- =============================================================================
-- STEP 3: UPDATE EXISTING DATA
-- =============================================================================

PRINT 'Step 3: Updating existing data with default values...'

-- Initialize default values for existing users
UPDATE [dbo].[kj_pengguna] 
SET 
    [pwd_encrypted] = 0,
    [pwd_migrated] = 0,
    [failed_login_attempts] = 0,
    [account_locked] = 0
WHERE 
    [pwd_encrypted] IS NULL 
    OR [pwd_migrated] IS NULL 
    OR [failed_login_attempts] IS NULL 
    OR [account_locked] IS NULL

PRINT '✓ Updated existing user records with default security values'

-- =============================================================================
-- STEP 4: CREATE CLEANUP PROCEDURES
-- =============================================================================

PRINT 'Step 4: Creating cleanup procedures...'

-- Procedure to cleanup expired OTP tokens
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_cleanup_expired_otp]') AND type = 'P')
    DROP PROCEDURE [dbo].[sp_cleanup_expired_otp]
GO

CREATE PROCEDURE [dbo].[sp_cleanup_expired_otp]
AS
BEGIN
    DELETE FROM [dbo].[otp_tokens] 
    WHERE [expires_at] < GETDATE() 
       OR ([used] = 1 AND [used_at] < DATEADD(hour, -24, GETDATE()))
    
    PRINT 'Cleaned up expired OTP tokens: ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + ' records deleted'
END
GO

-- Procedure to cleanup expired password reset tokens
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_cleanup_expired_password_tokens]') AND type = 'P')
    DROP PROCEDURE [dbo].[sp_cleanup_expired_password_tokens]
GO

CREATE PROCEDURE [dbo].[sp_cleanup_expired_password_tokens]
AS
BEGIN
    DELETE FROM [dbo].[password_reset_tokens] 
    WHERE [expires_at] < GETDATE() 
       OR ([used] = 1 AND [used_at] < DATEADD(hour, -24, GETDATE()))
    
    PRINT 'Cleaned up expired password reset tokens: ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + ' records deleted'
END
GO

PRINT '✓ Created cleanup procedures'

-- =============================================================================
-- STEP 5: CREATE SAMPLE DATA FOR TESTING
-- =============================================================================

PRINT 'Step 5: Creating sample test data...'

-- Insert sample test user (if not exists)
IF NOT EXISTS (SELECT 1 FROM [dbo].[kj_pengguna] WHERE [id_pg] = 'test_kolej')
BEGIN
    INSERT INTO [dbo].[kj_pengguna] ([id_pg], [pwd], [nama], [email], [status], [id_kolej], [pwd_encrypted], [pwd_migrated])
    VALUES ('test_kolej', 'test123', 'Test User Kolej', '<EMAIL>', 1, 1, 0, 0)
    
    PRINT '✓ Created test user: test_kolej / test123'
END
ELSE
    PRINT '⚠ Test user already exists'

-- =============================================================================
-- COMPLETION
-- =============================================================================

PRINT ''
PRINT '======================================================================='
PRINT 'SPMJ KOLEJ-PDSA Security Enhancement Migration COMPLETED SUCCESSFULLY!'
PRINT '======================================================================='
PRINT ''
PRINT 'SUMMARY OF CHANGES:'
PRINT '• Enhanced kj_pengguna table with security columns'
PRINT '• Created email service integration tables (OTP, password reset)'
PRINT '• Added audit logging capabilities'
PRINT '• Created cleanup procedures for token management'
PRINT '• Initialized default values for existing users'
PRINT ''
PRINT 'NEXT STEPS:'
PRINT '1. Deploy enhanced KOLEJ-PDSA application'
PRINT '2. Ensure email service microservice is running'
PRINT '3. Test secure authentication with existing users'
PRINT '4. Set up scheduled cleanup jobs for token tables'
PRINT ''
PRINT 'SECURITY FEATURES ENABLED:'
PRINT '• SHA256 + Salt password hashing'
PRINT '• Failed login attempt tracking'
PRINT '• Account lockout protection'
PRINT '• OTP email verification'
PRINT '• Password reset via email'
PRINT '• Comprehensive audit logging'
PRINT ''
PRINT 'Migration completed on: ' + CONVERT(NVARCHAR(30), GETDATE(), 120)
PRINT '======================================================================='
