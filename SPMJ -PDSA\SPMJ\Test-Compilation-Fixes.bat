@echo off
echo ===============================================================================
echo Testing PN_AdminPasswordManager Compilation Fixes
echo ===============================================================================
echo.

echo [1/3] Checking for duplicate class definitions...
findstr /C:"Public Class UserInfo" "PN_AdminPasswordManager.aspx.vb" > temp_userinfo_check.txt
set /a count=0
for /f %%i in (temp_userinfo_check.txt) do set /a count+=1
del temp_userinfo_check.txt
if %count% EQU 1 (
    echo ✓ UserInfo class definition is unique
) else (
    echo ✗ Found %count% UserInfo class definitions - should be only 1
)

echo.
echo [2/3] Checking for syntax issues...
findstr /C:"For i As Integer" "PN_AdminPasswordManager.aspx.vb" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ For loop syntax found
    findstr /C:"Next" "PN_AdminPasswordManager.aspx.vb" >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Matching Next statement found
    ) else (
        echo ✗ Missing Next statement
    )
) else (
    echo ✗ For loop syntax not found
)

echo.
echo [3/3] Attempting compilation...
"%WINDIR%\Microsoft.NET\Framework\v3.5\MSBuild.exe" SPMJ.vbproj /p:Configuration=Debug /verbosity:minimal

echo.
echo Checking compilation result...
if exist "bin\SPMJ.dll" (
    echo ===============================================================================
    echo ✓ SUCCESS: All compilation errors have been fixed!
    echo ===============================================================================
    echo.
    echo The PN_AdminPasswordManager page should now work without errors.
    echo You can access it at: /PN_AdminPasswordManager.aspx
    echo.
) else (
    echo ===============================================================================
    echo ✗ Compilation still has issues
    echo ===============================================================================
    echo.
    echo Please check the MSBuild output above for any remaining errors.
    echo.
)

pause
