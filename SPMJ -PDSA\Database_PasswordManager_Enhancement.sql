-- ===================================================================
-- DATABASE SCHEMA UPDATE FOR <PERSON><PERSON><PERSON><PERSON><PERSON> PASSWORD MANAGEMENT
-- PN_AdminPasswordManager - SHA256+Salt Support
-- ===================================================================

-- Add new columns to pn_pengguna table for enhanced security
BEGIN TRY
    -- Add salt column for SHA256 encryption
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'salt')
    BEGIN
        ALTER TABLE pn_pengguna ADD salt NVARCHAR(50) NULL;
        PRINT 'Added salt column to pn_pengguna table';
    END

    -- Add temporary password flag
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'is_temporary')
    BEGIN
        ALTER TABLE pn_pengguna ADD is_temporary BIT DEFAULT 0;
        PRINT 'Added is_temporary column to pn_pengguna table';
    END

    -- Add force change password flag
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'force_change')
    BEGIN
        ALTER TABLE pn_pengguna ADD force_change BIT DEFAULT 0;
        PRINT 'Added force_change column to pn_pengguna table';
    END

    -- Add password change date
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'tarikh_tukar_katalaluan')
    BEGIN
        ALTER TABLE pn_pengguna ADD tarikh_tukar_katalaluan DATETIME NULL;
        PRINT 'Added tarikh_tukar_katalaluan column to pn_pengguna table';
    END

    -- Add login attempt tracking
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'failed_login_attempts')
    BEGIN
        ALTER TABLE pn_pengguna ADD failed_login_attempts INT DEFAULT 0;
        PRINT 'Added failed_login_attempts column to pn_pengguna table';
    END

    -- Add account locked flag
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'is_locked')
    BEGIN
        ALTER TABLE pn_pengguna ADD is_locked BIT DEFAULT 0;
        PRINT 'Added is_locked column to pn_pengguna table';
    END

    -- Add last password change by admin
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'last_changed_by')
    BEGIN
        ALTER TABLE pn_pengguna ADD last_changed_by NVARCHAR(50) NULL;
        PRINT 'Added last_changed_by column to pn_pengguna table';
    END

END TRY
BEGIN CATCH
    PRINT 'Error updating pn_pengguna table: ' + ERROR_MESSAGE();
END CATCH;

-- ===================================================================
-- CREATE PASSWORD AUDIT LOG TABLE
-- ===================================================================
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'password_log')
    BEGIN
        CREATE TABLE password_log (
            log_id INT IDENTITY(1,1) PRIMARY KEY,
            user_id NVARCHAR(50) NOT NULL,
            admin_id NVARCHAR(50) NOT NULL,
            action NVARCHAR(100) NOT NULL,
            is_temporary BIT DEFAULT 0,
            timestamp DATETIME DEFAULT GETDATE(),
            ip_address NVARCHAR(50) NULL,
            user_agent NVARCHAR(255) NULL,
            success BIT DEFAULT 1,
            notes NVARCHAR(500) NULL
        );
        
        PRINT 'Created password_log table successfully';
        
        -- Create indexes for better performance
        CREATE INDEX IX_password_log_user_id ON password_log(user_id);
        CREATE INDEX IX_password_log_admin_id ON password_log(admin_id);
        CREATE INDEX IX_password_log_timestamp ON password_log(timestamp);
        
        PRINT 'Created indexes on password_log table';
    END
END TRY
BEGIN CATCH
    PRINT 'Error creating password_log table: ' + ERROR_MESSAGE();
END CATCH;

-- ===================================================================
-- CREATE EMAIL QUEUE TABLE FOR MICROSERVICE INTEGRATION
-- ===================================================================
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'email_queue')
    BEGIN
        CREATE TABLE email_queue (
            queue_id INT IDENTITY(1,1) PRIMARY KEY,
            user_id NVARCHAR(50) NOT NULL,
            email_address NVARCHAR(255) NOT NULL,
            email_type NVARCHAR(50) NOT NULL, -- 'password_reset', 'welcome', 'force_reset'
            email_data NTEXT NULL,
            status NVARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'failed'
            attempts INT DEFAULT 0,
            created_date DATETIME DEFAULT GETDATE(),
            sent_date DATETIME NULL,
            error_message NVARCHAR(500) NULL
        );
        
        PRINT 'Created email_queue table successfully';
        
        -- Create indexes
        CREATE INDEX IX_email_queue_status ON email_queue(status);
        CREATE INDEX IX_email_queue_user_id ON email_queue(user_id);
        CREATE INDEX IX_email_queue_created_date ON email_queue(created_date);
        
        PRINT 'Created indexes on email_queue table';
    END
END TRY
BEGIN CATCH
    PRINT 'Error creating email_queue table: ' + ERROR_MESSAGE();
END CATCH;

-- ===================================================================
-- CREATE STORED PROCEDURES FOR PASSWORD MANAGEMENT
-- ===================================================================

-- Procedure to update password with security enhancements
BEGIN TRY
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_UpdatePasswordSecure')
        DROP PROCEDURE sp_UpdatePasswordSecure;
    
    EXEC('
    CREATE PROCEDURE sp_UpdatePasswordSecure
        @user_id NVARCHAR(50),
        @password_hash NVARCHAR(255),
        @salt NVARCHAR(50),
        @is_temporary BIT = 0,
        @force_change BIT = 0,
        @admin_id NVARCHAR(50)
    AS
    BEGIN
        SET NOCOUNT ON;
        
        BEGIN TRY
            UPDATE pn_pengguna 
            SET katalaluan = @password_hash,
                salt = @salt,
                is_temporary = @is_temporary,
                force_change = @force_change,
                tarikh_tukar_katalaluan = GETDATE(),
                last_changed_by = @admin_id,
                failed_login_attempts = 0,
                is_locked = 0
            WHERE id_pg = @user_id;
            
            -- Log the password change
            INSERT INTO password_log (user_id, admin_id, action, is_temporary)
            VALUES (@user_id, @admin_id, ''Password Updated'', @is_temporary);
            
            SELECT 1 AS Success;
        END TRY
        BEGIN CATCH
            SELECT 0 AS Success, ERROR_MESSAGE() AS ErrorMessage;
        END CATCH
    END
    ');
    
    PRINT 'Created sp_UpdatePasswordSecure procedure successfully';
END TRY
BEGIN CATCH
    PRINT 'Error creating sp_UpdatePasswordSecure procedure: ' + ERROR_MESSAGE();
END CATCH;

-- Procedure to validate admin privileges
BEGIN TRY
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_ValidateAdminAccess')
        DROP PROCEDURE sp_ValidateAdminAccess;
    
    EXEC('
    CREATE PROCEDURE sp_ValidateAdminAccess
        @admin_id NVARCHAR(50)
    AS
    BEGIN
        SET NOCOUNT ON;
        
        SELECT 
            id_pg,
            nama,
            akses,
            modul,
            CASE 
                WHEN akses LIKE ''%admin%'' OR akses LIKE ''%1%'' OR akses LIKE ''%pengurusan%'' OR modul LIKE ''%admin%''
                THEN 1 
                ELSE 0 
            END AS IsAdmin
        FROM pn_pengguna 
        WHERE id_pg = @admin_id AND status = ''1'';
    END
    ');
    
    PRINT 'Created sp_ValidateAdminAccess procedure successfully';
END TRY
BEGIN CATCH
    PRINT 'Error creating sp_ValidateAdminAccess procedure: ' + ERROR_MESSAGE();
END CATCH;

-- ===================================================================
-- INSERT DEFAULT CONFIGURATION DATA
-- ===================================================================
BEGIN TRY
    -- Create system configuration table if not exists
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'system_config')
    BEGIN
        CREATE TABLE system_config (
            config_key NVARCHAR(100) PRIMARY KEY,
            config_value NVARCHAR(500),
            description NVARCHAR(255),
            created_date DATETIME DEFAULT GETDATE(),
            updated_date DATETIME DEFAULT GETDATE()
        );
        
        PRINT 'Created system_config table successfully';
    END
    
    -- Insert password policy configurations
    IF NOT EXISTS (SELECT * FROM system_config WHERE config_key = 'PASSWORD_MIN_LENGTH')
    BEGIN
        INSERT INTO system_config (config_key, config_value, description)
        VALUES ('PASSWORD_MIN_LENGTH', '8', 'Minimum password length requirement');
    END
    
    IF NOT EXISTS (SELECT * FROM system_config WHERE config_key = 'PASSWORD_TEMP_LENGTH')
    BEGIN
        INSERT INTO system_config (config_key, config_value, description)
        VALUES ('PASSWORD_TEMP_LENGTH', '12', 'Temporary password length');
    END
    
    IF NOT EXISTS (SELECT * FROM system_config WHERE config_key = 'PASSWORD_EXPIRY_DAYS')
    BEGIN
        INSERT INTO system_config (config_key, config_value, description)
        VALUES ('PASSWORD_EXPIRY_DAYS', '90', 'Password expiry in days');
    END
    
    IF NOT EXISTS (SELECT * FROM system_config WHERE config_key = 'MAX_LOGIN_ATTEMPTS')
    BEGIN
        INSERT INTO system_config (config_key, config_value, description)
        VALUES ('MAX_LOGIN_ATTEMPTS', '5', 'Maximum failed login attempts before account lock');
    END
    
    IF NOT EXISTS (SELECT * FROM system_config WHERE config_key = 'EMAIL_SERVICE_URL')
    BEGIN
        INSERT INTO system_config (config_key, config_value, description)
        VALUES ('EMAIL_SERVICE_URL', 'http://localhost:5000/api/email', 'Email microservice URL');
    END
    
    PRINT 'Inserted default system configuration values';
    
END TRY
BEGIN CATCH
    PRINT 'Error inserting system configuration: ' + ERROR_MESSAGE();
END CATCH;

-- ===================================================================
-- CREATE VIEWS FOR REPORTING
-- ===================================================================
BEGIN TRY
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = 'vw_PasswordAuditReport')
        DROP VIEW vw_PasswordAuditReport;
    
    EXEC('
    CREATE VIEW vw_PasswordAuditReport AS
    SELECT 
        pl.log_id,
        pl.user_id,
        u.nama AS user_name,
        pl.admin_id,
        a.nama AS admin_name,
        pl.action,
        CASE WHEN pl.is_temporary = 1 THEN ''Temporary'' ELSE ''Permanent'' END AS password_type,
        pl.timestamp,
        pl.ip_address,
        u.status AS user_status
    FROM password_log pl
    LEFT JOIN pn_pengguna u ON pl.user_id = u.id_pg
    LEFT JOIN pn_pengguna a ON pl.admin_id = a.id_pg
    ');
    
    PRINT 'Created vw_PasswordAuditReport view successfully';
END TRY
BEGIN CATCH
    PRINT 'Error creating vw_PasswordAuditReport view: ' + ERROR_MESSAGE();
END CATCH;

-- ===================================================================
-- FINAL STATUS REPORT
-- ===================================================================
PRINT '';
PRINT '========================================';
PRINT 'DATABASE SCHEMA UPDATE COMPLETED';
PRINT '========================================';
PRINT 'Enhanced Password Management Features:';
PRINT '✓ SHA256 + Salt encryption support';
PRINT '✓ Temporary password functionality';
PRINT '✓ Force password change capability';
PRINT '✓ Password audit logging';
PRINT '✓ Email queue for microservice integration';
PRINT '✓ Admin access validation';
PRINT '✓ Security configuration management';
PRINT '✓ Reporting views for audit trails';
PRINT '';
PRINT 'Ready for PN_AdminPasswordManager deployment!';
PRINT '========================================';

-- Show table structures for verification
SELECT 'pn_pengguna columns' AS Info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pn_pengguna' 
ORDER BY ORDINAL_POSITION;

SELECT 'password_log structure' AS Info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'password_log' 
ORDER BY ORDINAL_POSITION;

SELECT 'email_queue structure' AS Info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'email_queue' 
ORDER BY ORDINAL_POSITION;
