﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm69
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login_J.aspx")
        LBLKOLEJ.Text = Session("Dc_KOLEJ")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'senarai
        'Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and (j_kursus=1 or j_kursus=5 or j_kursus=8)"
        'Rdr = Cmd.ExecuteReader()
        'If Rdr.Read Then Label2.Text = Rdr(0)
        'Rdr.Close()

        'ADD DEGREE RN TRAINNER TOTAL RECORDS LIST 24082013 -OSH   
        Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=5"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblDgrRN.Text = Rdr(0)
        Rdr.Close()
        'COMMENT ORI 09032014 - OSH
        'ADD DIPLOMA RN TRAINNER TOTAL RECORDS LIST 24082013 -OSH   
        'Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and (j_kursus=1 or j_kursus=8)"

        'ADD DIPLOMA RN TRAINNER TOTAL RECORDS LIST ONLY 09032014 -OSH
        Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=1"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblDipRN.Text = Rdr(0)
        Rdr.Close()

        'ADD DIPLOMA KPSL TRAINNER TOTAL RECORDS LIST 09032014 -OSH   
        Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=8"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblCoRN.Text = Rdr(0)
        Rdr.Close()

        'CM TRAINNER TOTAL RECORDS LIST
        Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=2"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblCN.Text = Rdr(0)
        Rdr.Close()

        'AN TRAINNER TOTAL RECORDS LIST
        Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=3"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblAN.Text = Rdr(0)
        Rdr.Close()

        'MIDWIFE TRAINNER TOTAL RECORDS LIST
        Cmd.CommandText = "select count(nokp) from pelatih where id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=4"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblMW.Text = Rdr(0)
        Rdr.Close()

        'calon
        'ADD DEGREE RN EXAM CANIDATE TOTAL RECORDS LIST 24082013 -OSH   
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp where p.id_kolej = " & Session("Id_KOLEJ") & " and  j_kursus=5 and id_xm=(select top 1 id_xm from pn_xm where j_xm=1 order by tahun desc, siri desc, j_xm desc)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblDgrRNeL.Text = Rdr(0)
        Rdr.Close()

        'COMMENT ORI 09032014 - OSH
        'ADD DIPLOMA RN EXAM CANIDATE TOTAL RECORDS LIST 24082013 -OSH   
        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp where p.id_kolej = " & Session("Id_KOLEJ") & " and (j_kursus=1 or j_kursus=8) and id_xm=(select top 1 id_xm from pn_xm where j_xm=1 order by tahun desc, siri desc, j_xm desc)"
        'IMPROVEMENT DIPLOMA RN EXAM CANIDATE TOTAL RECORDS LIST ONLY 09032014 -OSH   
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp where p.id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=1 and id_xm=(select top 1 id_xm from pn_xm where j_xm=1 order by tahun desc, siri desc, j_xm desc)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblDipRNeL.Text = Rdr(0)
        Rdr.Close()

        'ADD KPSL RN EXAM CANIDATE TOTAL RECORDS LIST 09032014 -OSH   
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp where p.id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=8 and id_xm=(select top 1 id_xm from pn_xm where j_xm=1 order by tahun desc, siri desc, j_xm desc)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblCoRNeL.Text = Rdr(0)
        Rdr.Close()

        'CERTIFICATE CN EXAM CANIDATE TOTAL RECORDS LIST
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp where p.id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=2 and id_xm=(select top 1 id_xm from pn_xm where j_xm=2 order by tahun desc, siri desc, j_xm desc)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblCNeL.Text = Rdr(0)
        Rdr.Close()

        'CERTIFICATE AN EXAM CANIDATE TOTAL RECORDS LIST
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp where p.id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=3 and id_xm=(select top 1 id_xm from pn_xm where j_xm=3 order by tahun desc, siri desc, j_xm desc)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblANeL.Text = Rdr(0)
        Rdr.Close()

        'ADV MIDWIFE EXAM CANIDATE TOTAL RECORDS LIST
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp where p.id_kolej = " & Session("Id_KOLEJ") & " and j_kursus=4 and id_xm=(select top 1 id_xm from pn_xm where j_xm=4 order by tahun desc, siri desc, j_xm desc)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then LblMWeL.Text = Rdr(0)
        Rdr.Close()

        Cn.Close()
    End Sub

End Class