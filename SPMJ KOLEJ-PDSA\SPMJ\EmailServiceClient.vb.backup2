Imports System.Net
Imports System.IO
Imports System.Text
Imports System.Web.Script.Serialization
Imports System.Collections.Generic

''' <summary>
''' Client for communicating with the .NET 9 Email Microservice
''' Provides email functionality for .NET 3.5 KOLEJ-PDSA application
''' </summary>
Public Class EmailServiceClient
    Private ReadOnly _baseUrl As String
    Private ReadOnly _timeout As Integer = 30000 ' 30 seconds
    Private _apiKey As String = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"

    Public Sub New(baseUrl As String)
        _baseUrl = baseUrl.TrimEnd("/"c)
        
        ' Enable SSL for email service communication (.NET 3.5 compatible)
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 Or SecurityProtocolType.Tls
    End Sub
    
    ''' <summary>
    ''' Set API key for authentication
    ''' </summary>
    Public Sub SetApiKey(apiKey As String)
        If Not String.IsNullOrEmpty(apiKey) Then
            _apiKey = apiKey
        End If
    End Sub

#Region "Health Check"

    ''' <summary>
    ''' Check if email service is available and healthy
    ''' </summary>
    Public Function CheckHealth() As Boolean
        Try
            System.Diagnostics.Debug.WriteLine("Checking email service health at: " & _baseUrl)
            
            Dim healthUrl As String = _baseUrl & "/health"
            Dim request As HttpWebRequest = CType(WebRequest.Create(healthUrl), HttpWebRequest)
            request.Method = "GET"
            request.Timeout = 10000 ' 10 seconds for health check
            
            ' Add API key header
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
            End If
            
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                If response.StatusCode = HttpStatusCode.OK Then
                    System.Diagnostics.Debug.WriteLine("✅ Email service is healthy")
                    Return True
                Else
                    System.Diagnostics.Debug.WriteLine("⚠️ Email service health check failed: HTTP " & CInt(response.StatusCode).ToString())
                    Return False
                End If
            End Using
            
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("❌ Email service health check web exception: " & webEx.Message)
            Return False
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Email service health check exception: " & ex.Message)
            Return False
        End Try
    End Function

#End Region

#Region "Password Change Notification"

    ''' <summary>
    ''' Send password change notification via microservice
    ''' </summary>
    Public Function SendPasswordChangeNotification(userId As String, userEmail As String) As Boolean
        Try
            If String.IsNullOrEmpty(userEmail) Then
                System.Diagnostics.Debug.WriteLine("Cannot send notification - email address is empty")
                Return False
            End If
              
            Dim notificationUrl As String = _baseUrl & "/api/admin/password/send-notification"
            System.Diagnostics.Debug.WriteLine("Sending notification to: " & notificationUrl)
            
            ' Create request data as Dictionary for .NET 3.5 compatibility
            Dim data As New Dictionary(Of String, Object)
            data.Add("userId", userId)
            data.Add("userName", userId)
            data.Add("password", "***")
            data.Add("isTemporary", False)
            data.Add("systemName", "KOLEJ-PDSA")
            data.Add("systemUrl", "http://localhost:8080")
            data.Add("timestamp", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            data.Add("supportEmail", "<EMAIL>")
            data.Add("adminId", "SYSTEM")
            data.Add("adminName", "System Auto-Notification")
            
            Dim requestData As New Dictionary(Of String, Object)
            requestData.Add("to", userEmail)
            requestData.Add("subject", "Password Changed - SPMJ KOLEJ System")
            requestData.Add("templateType", "password_change_notification")
            requestData.Add("data", data)
            
            Dim serializer As New JavaScriptSerializer()
            Dim jsonData As String = serializer.Serialize(requestData)
            
            System.Diagnostics.Debug.WriteLine("Request JSON: " & jsonData)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(notificationUrl), HttpWebRequest)
            request.Method = "POST"
            request.ContentType = "application/json"
            request.Timeout = _timeout
            
            ' Add API key header
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
                System.Diagnostics.Debug.WriteLine("Added API key to notification request")
            Else
                System.Diagnostics.Debug.WriteLine("⚠️ No API key provided for notification request")
            End If
            
            ' Send request data
            Using writer As New StreamWriter(request.GetRequestStream())
                writer.Write(jsonData)
            End Using
            
            ' Get response
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                If response.StatusCode = HttpStatusCode.OK Then
                    System.Diagnostics.Debug.WriteLine("✅ Password change notification sent successfully")
                    Return True
                Else
                    System.Diagnostics.Debug.WriteLine("⚠️ Notification response: HTTP " & CInt(response.StatusCode).ToString())
                    Return False
                End If
            End Using
            
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("❌ Notification web exception: " & webEx.Message)
            If webEx.Response IsNot Nothing Then
                Using errorResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                    System.Diagnostics.Debug.WriteLine("Error status: HTTP " & CInt(errorResponse.StatusCode).ToString())
                End Using
            End If
            Return False
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Notification exception: " & ex.Message)
            Return False
        End Try
    End Function

#End Region

End Class

''' <summary>
''' Response structure for email service communication (.NET 3.5 compatible)
''' </summary>
Public Class EmailServiceResponse
    Private _success As Boolean
    Private _message As String
    Private _data As Object
    
    Public Property Success() As Boolean
        Get
            Return _success
        End Get
        Set(ByVal value As Boolean)
            _success = value
        End Set
    End Property
    
    Public Property Message() As String
        Get
            Return _message
        End Get
        Set(ByVal value As String)
            _message = value
        End Set
    End Property
    
    Public Property Data() As Object
        Get
            Return _data
        End Get
        Set(ByVal value As Object)
            _data = value
        End Set
    End Property
End Class
        End Try
    End Function

    ''' <summary>
    ''' Validate OTP code
    ''' </summary>
    Public Function ValidateOTP(userId As String, otpCode As String, purpose As String) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .OtpCode = otpCode,
                .Purpose = purpose
            }

            Dim response = PostRequest("/api/otp/validate", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

#End Region

#Region "Password Reset Methods"

    ''' <summary>
    ''' Request password reset for end user
    ''' </summary>
    Public Function RequestPasswordReset(userId As String, email As String, Optional baseUrl As String = Nothing) As EmailServiceResponse
        Try
            Dim requestData = New With {
                .UserId = userId,
                .Email = email,
                .BaseUrl = baseUrl
            }

            Dim response = PostRequest("/api/password/reset/request", requestData)
            Return ParseResponse(response)
        Catch ex As Exception
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat komunikasi dengan sistem email: " & ex.Message
            }
        End Try
    End Function

#End Region

#Region "Health Check"    ''' <summary>
    ''' Check if email service is available with enhanced response
    ''' </summary>
    Public Function CheckHealth() As String
        Try
            ' Health check with proper URL and timeout
            Dim url As String = _baseUrl & "/api/health"
            System.Diagnostics.Debug.WriteLine("Health check URL: " & url)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
            request.Method = "GET"
            request.Timeout = 5000 ' 5 second timeout for health check
            request.ContentType = "application/json"
            
            ' Add API key header if available
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
            End If
            
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                If response.StatusCode = HttpStatusCode.OK Then
                    Using reader As New StreamReader(response.GetResponseStream())
                        Dim result As String = reader.ReadToEnd()
                        System.Diagnostics.Debug.WriteLine("Health check response: " & result)
                        Return result
                    End Using
                Else
                    Return "unhealthy - HTTP " & CInt(response.StatusCode).ToString()
                End If
            End Using
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("Health check web exception: " & webEx.Message)
            If webEx.Response IsNot Nothing Then
                Dim httpResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                Return "unhealthy - HTTP " & CInt(httpResponse.StatusCode).ToString() & ": " & webEx.Message
            Else
                Return "unhealthy - Connection failed: " & webEx.Message
            End If
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Health check exception: " & ex.Message)
            Return "unhealthy - Error: " & ex.Message
        End Try
    End Function
      ''' <summary>
    ''' Send password change notification via microservice
    ''' </summary>
    Public Function SendPasswordChangeNotification(userId As String, userEmail As String) As Boolean
        Try
            If String.IsNullOrEmpty(userEmail) Then
                System.Diagnostics.Debug.WriteLine("Cannot send notification - email address is empty")
                Return False
            End If
              Dim notificationUrl As String = _baseUrl & "/api/admin/password/send-notification"
            System.Diagnostics.Debug.WriteLine("Sending notification to: " & notificationUrl)
            
            ' Create request data matching AdminPasswordNotificationRequest format
            Dim requestData = New With {
                .to = userEmail,
                .subject = "Password Changed - SPMJ KOLEJ System",
                .templateType = "password_change_notification",
                .data = New With {
                    .userId = userId,
                    .userName = userId,
                    .password = "***",
                    .isTemporary = False,
                    .systemName = "KOLEJ-PDSA",
                    .systemUrl = "http://localhost:8080",
                    .timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    .supportEmail = "<EMAIL>",
                    .adminId = "SYSTEM",
                    .adminName = "System Auto-Notification"
                }
            }
            Dim serializer As New JavaScriptSerializer()
            Dim jsonData As String = serializer.Serialize(requestData)
            
            System.Diagnostics.Debug.WriteLine("Request JSON: " & jsonData)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(notificationUrl), HttpWebRequest)
            request.Method = "POST"
            request.ContentType = "application/json"
            request.Timeout = _timeout
            
            ' Add API key header
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
                System.Diagnostics.Debug.WriteLine("Added API key to notification request")
            Else
                System.Diagnostics.Debug.WriteLine("⚠️ No API key provided for notification request")
            End If
            
            ' Send request data
            Using writer As New StreamWriter(request.GetRequestStream())
                writer.Write(jsonData)
            End Using
            
            ' Get response
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                If response.StatusCode = HttpStatusCode.OK Then
                    System.Diagnostics.Debug.WriteLine("✅ Password change notification sent successfully")
                    Return True
                Else
                    System.Diagnostics.Debug.WriteLine("⚠️ Notification response: HTTP " & CInt(response.StatusCode).ToString())
                    Return False
                End If
            End Using
            
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("❌ Notification web exception: " & webEx.Message)
            If webEx.Response IsNot Nothing Then
                Using errorResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                    System.Diagnostics.Debug.WriteLine("Error status: HTTP " & CInt(errorResponse.StatusCode).ToString())
                End Using
            End If
            Return False
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Notification exception: " & ex.Message)
            Return False
        End Try
    End Function

#End Region

#Region "Private Helper Methods"
    Private Function PostRequest(endpoint As String, data As Object) As String

        Dim url As String = _baseUrl & endpoint
        Dim serializer As New JavaScriptSerializer()
        Dim jsonData As String = serializer.Serialize(data)
        
        Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
        request.Method = "POST"
        request.ContentType = "application/json"
        
        ' Use X-API-Key header instead of Bearer token
        If Not String.IsNullOrEmpty(_apiKey) Then
            request.Headers.Add("X-API-Key", _apiKey)
        End If
        
        request.Timeout = _timeout
        
        Using writer As New StreamWriter(request.GetRequestStream())
            writer.Write(jsonData)
        End Using
        
        Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
            Using reader As New StreamReader(response.GetResponseStream())
                Return reader.ReadToEnd()
            End Using
        End Using
    End Function

    Private Function GetRequest(endpoint As String) As String
        Dim url As String = _baseUrl & endpoint
        
        Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
        request.Method = "GET"
        
        ' Use X-API-Key header instead of Bearer token
        If Not String.IsNullOrEmpty(_apiKey) Then
            request.Headers.Add("X-API-Key", _apiKey)
        End If
        
        request.Timeout = _timeout
        
        Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
            Using reader As New StreamReader(response.GetResponseStream())
                Return reader.ReadToEnd()
            End Using
        End Using
    End Function

    Private Function ParseResponse(jsonResponse As String) As EmailServiceResponse
        Try
            Dim serializer As New JavaScriptSerializer()
            Dim responseObj = serializer.DeserializeObject(jsonResponse)
            
            Dim dict As Dictionary(Of String, Object) = CType(responseObj, Dictionary(Of String, Object))
            
            Return New EmailServiceResponse With {
                .Success = CBool(dict("success")),
                .Message = If(dict.ContainsKey("message"), dict("message").ToString(), ""),
                .Data = If(dict.ContainsKey("data"), dict("data"), Nothing)
            }
        Catch
            Return New EmailServiceResponse With {
                .Success = False,
                .Message = "Ralat memproses respons sistem email"
            }
        End Try
    End Function

#End Region

#Region "Notification Methods"

#End Region

End Class

''' <summary>
''' Response structure for email service communication
''' </summary>
Public Class EmailServiceResponse
    Public Property Success As Boolean
    Public Property Message As String
    Public Property Data As Object
End Class
