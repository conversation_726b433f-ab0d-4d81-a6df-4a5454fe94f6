-- =====================================================================================
-- SPMJ KOLEJ-PDSA PRODUCTION ALTER SCRIPT FOR kj_pengguna TABLE
-- =====================================================================================
-- Purpose: Add required columns for enhanced login system with encrypted passwords,
--          OTP verification, and password recovery functionality
-- Target Table: kj_pengguna
-- Date: 2025-06-27
-- Version: 1.0 PRODUCTION
-- =====================================================================================

-- Enable error handling
SET NOCOUNT ON;
SET XACT_ABORT ON;

PRINT '====================================================================================='
PRINT 'SPMJ KOLEJ-PDSA PRODUCTION ALTER SCRIPT - kj_pengguna TABLE ENHANCEMENT'
PRINT '====================================================================================='
PRINT 'Starting execution at: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================================================
-- STEP 1: BACKUP VERIFICATION
-- =====================================================================================
PRINT 'Step 1: Verifying backup requirements...'

-- Check if backup table exists (recommended before production changes)
IF OBJECT_ID('kj_pengguna_backup_' + FORMAT(GETDATE(), 'yyyyMMdd'), 'U') IS NULL
BEGIN
    PRINT '⚠️  WARNING: No backup table found for today.'
    PRINT '   Recommended: CREATE BACKUP TABLE before running this script:'
    PRINT '   SELECT * INTO kj_pengguna_backup_' + FORMAT(GETDATE(), 'yyyyMMdd') + ' FROM kj_pengguna;'
    PRINT ''
END
ELSE
BEGIN
    PRINT '✅ Backup table found: kj_pengguna_backup_' + FORMAT(GETDATE(), 'yyyyMMdd')
END

-- =====================================================================================
-- STEP 2: ADD EMAIL COLUMN
-- =====================================================================================
PRINT 'Step 2: Adding email column for OTP and password recovery...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'email')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [email] NVARCHAR(255) NULL;
    PRINT '✅ Added email column (NVARCHAR(255) NULL)'
END
ELSE
BEGIN
    PRINT '⚠️  email column already exists - skipping'
END

-- =====================================================================================
-- STEP 3: ADD PASSWORD SECURITY COLUMNS
-- =====================================================================================
PRINT 'Step 3: Adding password security columns...'

-- Add salt column for password hashing
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'salt')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [salt] NVARCHAR(255) NULL;
    PRINT '✅ Added salt column (NVARCHAR(255) NULL)'
END
ELSE
BEGIN
    PRINT '⚠️  salt column already exists - skipping'
END

-- Add password encryption flag
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_encrypted')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [pwd_encrypted] BIT NOT NULL DEFAULT 0;
    PRINT '✅ Added pwd_encrypted column (BIT NOT NULL DEFAULT 0)'
END
ELSE
BEGIN
    PRINT '⚠️  pwd_encrypted column already exists - skipping'
END

-- Add password migration tracking
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_migrated')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [pwd_migrated] BIT NOT NULL DEFAULT 0;
    PRINT '✅ Added pwd_migrated column (BIT NOT NULL DEFAULT 0)'
END
ELSE
BEGIN
    PRINT '⚠️  pwd_migrated column already exists - skipping'
END

-- =====================================================================================
-- STEP 4: ADD ACCOUNT SECURITY COLUMNS
-- =====================================================================================
PRINT 'Step 4: Adding account security columns...'

-- Add failed login attempts counter
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'failed_login_attempts')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [failed_login_attempts] INT NOT NULL DEFAULT 0;
    PRINT '✅ Added failed_login_attempts column (INT NOT NULL DEFAULT 0)'
END
ELSE
BEGIN
    PRINT '⚠️  failed_login_attempts column already exists - skipping'
END

-- Add account locked flag
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'account_locked')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [account_locked] BIT NOT NULL DEFAULT 0;
    PRINT '✅ Added account_locked column (BIT NOT NULL DEFAULT 0)'
END
ELSE
BEGIN
    PRINT '⚠️  account_locked column already exists - skipping'
END

-- =====================================================================================
-- STEP 5: ADD AUDIT TRAIL COLUMNS
-- =====================================================================================
PRINT 'Step 5: Adding audit trail columns...'

-- Add last successful login timestamp
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'last_successful_login')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [last_successful_login] DATETIME NULL;
    PRINT '✅ Added last_successful_login column (DATETIME NULL)'
END
ELSE
BEGIN
    PRINT '⚠️  last_successful_login column already exists - skipping'
END

-- Add last failed login timestamp
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'last_failed_login')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [last_failed_login] DATETIME NULL;
    PRINT '✅ Added last_failed_login column (DATETIME NULL)'
END
ELSE
BEGIN
    PRINT '⚠️  last_failed_login column already exists - skipping'
END

-- Add account locked date
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'locked_date')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [locked_date] DATETIME NULL;
    PRINT '✅ Added locked_date column (DATETIME NULL)'
END
ELSE
BEGIN
    PRINT '⚠️  locked_date column already exists - skipping'
END

-- =====================================================================================
-- STEP 6: ADD PASSWORD MANAGEMENT COLUMNS
-- =====================================================================================
PRINT 'Step 6: Adding password management columns...'

-- Add password last changed date
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'pwd_last_changed')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [pwd_last_changed] DATETIME NULL;
    PRINT '✅ Added pwd_last_changed column (DATETIME NULL)'
END
ELSE
BEGIN
    PRINT '⚠️  pwd_last_changed column already exists - skipping'
END

-- Add force password change flag
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'force_change_required')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [force_change_required] BIT NOT NULL DEFAULT 0;
    PRINT '✅ Added force_change_required column (BIT NOT NULL DEFAULT 0)'
END
ELSE
BEGIN
    PRINT '⚠️  force_change_required column already exists - skipping'
END

-- Add migration tracking date
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'kj_pengguna' AND COLUMN_NAME = 'migration_date')
BEGIN
    ALTER TABLE [dbo].[kj_pengguna] ADD [migration_date] DATETIME NULL;
    PRINT '✅ Added migration_date column (DATETIME NULL)'
END
ELSE
BEGIN
    PRINT '⚠️  migration_date column already exists - skipping'
END

-- =====================================================================================
-- STEP 7: INITIALIZE DEFAULT VALUES FOR EXISTING RECORDS
-- =====================================================================================
PRINT 'Step 7: Initializing default values for existing records...'

-- Update NULL values to defaults for security columns
UPDATE [dbo].[kj_pengguna] 
SET 
    [pwd_encrypted] = 0,
    [pwd_migrated] = 0,
    [failed_login_attempts] = 0,
    [account_locked] = 0,
    [force_change_required] = 0
WHERE 
    [pwd_encrypted] IS NULL 
    OR [pwd_migrated] IS NULL 
    OR [failed_login_attempts] IS NULL 
    OR [account_locked] IS NULL
    OR [force_change_required] IS NULL;

DECLARE @updated_records INT = @@ROWCOUNT;
PRINT '✅ Updated ' + CAST(@updated_records AS VARCHAR(10)) + ' existing records with default security values'

-- =====================================================================================
-- STEP 8: CREATE INDEXES FOR PERFORMANCE
-- =====================================================================================
PRINT 'Step 8: Creating indexes for performance...'

-- Index on email for OTP and password recovery lookups
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_kj_pengguna_email')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_kj_pengguna_email] 
    ON [dbo].[kj_pengguna] ([email]) 
    WHERE [email] IS NOT NULL;
    PRINT '✅ Created index IX_kj_pengguna_email'
END
ELSE
BEGIN
    PRINT '⚠️  Index IX_kj_pengguna_email already exists - skipping'
END

-- Index on account_locked for security queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_kj_pengguna_account_locked')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_kj_pengguna_account_locked] 
    ON [dbo].[kj_pengguna] ([account_locked], [failed_login_attempts]);
    PRINT '✅ Created index IX_kj_pengguna_account_locked'
END
ELSE
BEGIN
    PRINT '⚠️  Index IX_kj_pengguna_account_locked already exists - skipping'
END

-- =====================================================================================
-- STEP 9: ADD COLUMN DESCRIPTIONS
-- =====================================================================================
PRINT 'Step 9: Adding column descriptions...'

-- Add extended properties for documentation
EXEC sp_addextendedproperty 
    @name = 'MS_Description',
    @value = 'Email address for OTP verification and password recovery',
    @level0type = 'SCHEMA', @level0name = 'dbo',
    @level1type = 'TABLE', @level1name = 'kj_pengguna',
    @level2type = 'COLUMN', @level2name = 'email';

EXEC sp_addextendedproperty 
    @name = 'MS_Description',
    @value = 'Salt value for password hashing (SHA256)',
    @level0type = 'SCHEMA', @level0name = 'dbo',
    @level1type = 'TABLE', @level1name = 'kj_pengguna',
    @level2type = 'COLUMN', @level2name = 'salt';

EXEC sp_addextendedproperty 
    @name = 'MS_Description',
    @value = 'Flag indicating if password is encrypted (1) or plain text (0)',
    @level0type = 'SCHEMA', @level0name = 'dbo',
    @level1type = 'TABLE', @level1name = 'kj_pengguna',
    @level2type = 'COLUMN', @level2name = 'pwd_encrypted';

PRINT '✅ Added column descriptions'

-- =====================================================================================
-- STEP 10: FINAL VERIFICATION
-- =====================================================================================
PRINT 'Step 10: Final verification...'

-- Verify all required columns exist
DECLARE @missing_columns TABLE (column_name VARCHAR(50));

INSERT INTO @missing_columns (column_name)
SELECT column_name FROM (
    VALUES 
    ('email'), ('salt'), ('pwd_encrypted'), ('pwd_migrated'),
    ('failed_login_attempts'), ('account_locked'), ('last_successful_login'),
    ('last_failed_login'), ('locked_date'), ('pwd_last_changed'),
    ('force_change_required'), ('migration_date')
) AS required_columns(column_name)
WHERE column_name NOT IN (
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'kj_pengguna'
);

DECLARE @missing_count INT = (SELECT COUNT(*) FROM @missing_columns);

IF @missing_count = 0
BEGIN
    PRINT '✅ All required columns successfully added to kj_pengguna table'
    
    -- Display final column count
    DECLARE @total_columns INT = (
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'kj_pengguna'
    );
    
    DECLARE @total_records INT = (SELECT COUNT(*) FROM kj_pengguna);
    
    PRINT '📊 Final Statistics:'
    PRINT '   • Total columns in kj_pengguna: ' + CAST(@total_columns AS VARCHAR(10))
    PRINT '   • Total records in kj_pengguna: ' + CAST(@total_records AS VARCHAR(10))
    PRINT '   • Records with default security values: ' + CAST(@updated_records AS VARCHAR(10))
END
ELSE
BEGIN
    PRINT '❌ Missing columns detected:'
    SELECT column_name FROM @missing_columns;
    RAISERROR('Some required columns are missing. Please review and re-run the script.', 16, 1);
END

-- =====================================================================================
-- COMPLETION MESSAGE
-- =====================================================================================
PRINT ''
PRINT '====================================================================================='
PRINT '✅ SPMJ KOLEJ-PDSA PRODUCTION ALTER SCRIPT COMPLETED SUCCESSFULLY!'
PRINT '====================================================================================='
PRINT 'Completion time: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT 'SUMMARY OF CHANGES:'
PRINT '• Added email column for OTP verification and password recovery'
PRINT '• Added password security columns (salt, encryption flags)'
PRINT '• Added account security columns (failed attempts, locking)'
PRINT '• Added audit trail columns (login timestamps)'
PRINT '• Added password management columns (change tracking)'
PRINT '• Created performance indexes'
PRINT '• Initialized default values for existing records'
PRINT '• Added column documentation'
PRINT ''
PRINT 'NEXT STEPS:'
PRINT '1. Deploy enhanced KOLEJ-PDSA application code'
PRINT '2. Ensure OTP microservice is running on port 5000'
PRINT '3. Test enhanced login functionality with existing users'
PRINT '4. Monitor system performance and security logs'
PRINT ''
PRINT 'FEATURES NOW AVAILABLE:'
PRINT '• Encrypted password authentication with OTP verification'
PRINT '• Password recovery via email'
PRINT '• Account lockout protection (5 failed attempts)'
PRINT '• Comprehensive audit trail and security monitoring'
PRINT ''
PRINT '🎉 KOLEJ-PDSA Enhanced Login System is now ready for production use!'
PRINT '====================================================================================='

SET NOCOUNT OFF;
