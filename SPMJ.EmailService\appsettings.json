{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=SPMJ_PDSA;User ID=sa;Password=*********;TrustServerCertificate=True"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "UseSsl": true, "Username": "<EMAIL>", "Password": "ejbe mhrr elwf ynwx", "FromEmail": "<EMAIL>", "FromName": "NOTIFIKASI SPMJ"}, "ApiSettings": {"ApiKey": "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia", "AllowedOrigins": ["http://localhost:8080", "http://************"], "RateLimitPerMinute": 60}, "OtpSettings": {"ExpirationMinutes": 5, "Length": 6}, "PasswordResetSettings": {"ExpirationHours": 24, "BaseUrl": "http://localhost:8080"}}