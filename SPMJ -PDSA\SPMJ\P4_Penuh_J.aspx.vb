﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Public Class P4_Penuh_J
    Inherits System.Web.UI.Page

    Public Sub Isi_TPC()
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'Dim List_Adp As New SqlDataAdapter("select tpc_tahun as 'TAHUN', stuff('0000',5-len(tpc_no),len(tpc_no),tpc_no) as 'NO. TPC', CONVERT(varchar(12), tpc_tkh, 103) as 'TARIKH TPC', dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR', pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax  from jt_tpc_tpc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & Session("NOKP") & "' order by tpc_tahun desc", Cn)
        Dim List_Adp As New SqlDataAdapter("select tpc_tahun as 'TAHUN', CONVERT(varchar(12), tpc_tkh, 103) as 'TARIKH TPC', convert(varchar(12), tpc_tmt, 103) as 'TARIKH TAMAT',dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR', pta.alamat, pta.poskod, pta.bandar, png.dc_negeri as 'NEGERI', pta.tel, pta.fax  from jt_tpc_tpc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & Session("NOKP") & "' order by tpc_tkh desc", Cn)

        List_Adp.Fill(List_Data, "jt_tpc_tpc")
        Gd_TPC.DataSource = List_Data.Tables("jt_tpc_tpc")
        Gd_TPC.DataBind()
    End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Public Sub Kira_Latihan()
        Dim i, j_teori, j, j_ns As Int16, tx As TextBox
        With GdL
            For i = 0 To 38
                tx = .Rows(i).FindControl("tx_masa")
                If tx.Text = "" Then Else j_teori += CInt(tx.Text)
            Next
        End With
        With GdL
            For i = 39 To .Rows.Count - 1
                tx = .Rows(i).FindControl("tx_masa")
                If tx.Text = "" Then Else j += CInt(tx.Text)
            Next
        End With
        j_ns = 0
        With GdL
            For i = 13 To 38
                tx = .Rows(i).FindControl("tx_masa")
                If tx.Text = "" Then Else j_ns += CInt(tx.Text)
            Next
        End With
        Try
            Tx_J_NS.Text = CInt(j_ns / j_teori * 100) & "%"
            Tx_J_Amali.Text = j & " M"
            Tx_J_Teori.Text = j_teori & " J"
        Catch ex As Exception

        End Try
    End Sub

    Public Sub Isi_Pinda()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select * from jt_tpc where nokp = '" & Session("NOKP") & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr("j_daftar")) Then Cb_Jenis.Items.FindByValue(Rdr("j_daftar")).Selected = True
            If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama")
            If Not IsDBNull(Rdr("nokp")) Then Tx_NoKP.Text = Rdr("nokp") : Tx_NoKP0.Text = Rdr("nokp")
            If Not IsDBNull(Rdr("nopd")) Then Tx_NoPd.Text = "Tpc-" & Rdr("nopd")
            If Not IsDBNull(Rdr("umur")) Then Tx_Umur.Text = Rdr("umur")
            If Not IsDBNull(Rdr("tkh_daftar")) Then Tx_Tkh_Daftar.Text = Format(Rdr("tkh_daftar"), "dd/MM/yyyy")
            If Not IsDBNull(Rdr("tpt_lahir")) Then Tx_Tpt_Lahir.Text = Rdr("tpt_lahir")
            If Not IsDBNull(Rdr("tkh_lahir")) Then Tx_Tkh_Lahir.Text = Rdr("tkh_lahir")
            If Not IsDBNull(Rdr("warganegara")) Then Cb_Warga.Items.FindByValue(Rdr("warganegara")).Selected = True
            If Not IsDBNull(Rdr("taraf_warganegara")) Then If Rdr("taraf_warganegara") = 2 Then chk_PR.Checked = True
            If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") > 0 Then Cb_Jantina.Items.FindByValue(Rdr("jantina")).Selected = True
            If Not IsDBNull(Rdr("t_kahwin")) Then If Rdr("t_kahwin") > 0 Then Cb_Kahwin.Items.FindByValue(Rdr("t_kahwin")).Selected = True
            If Not IsDBNull(Rdr("tp_alamat")) Then Tx_TP_Alamat.Text = Rdr("tp_alamat")
            If Not IsDBNull(Rdr("tp_poskod")) Then Tx_TP_Poskod.Text = Rdr("tp_poskod")
            If Not IsDBNull(Rdr("tp_bandar")) Then Tx_TP_Bandar.Text = Rdr("tp_bandar")
            If Not IsDBNull(Rdr("tp_negeri")) Then If Rdr("tp_negeri") > 0 Then Cb_TP_Negeri.Items.FindByValue(Rdr("tp_negeri")).Selected = True
            If Not IsDBNull(Rdr("sm_alamat")) Then Tx_SM_Alamat.Text = Rdr("sm_alamat")
            If Not IsDBNull(Rdr("sm_poskod")) Then Tx_SM_Poskod.Text = Rdr("sm_poskod")
            If Not IsDBNull(Rdr("sm_bandar")) Then Tx_SM_Bandar.Text = Rdr("sm_bandar")
            If Not IsDBNull(Rdr("sm_negeri")) Then If Rdr("sm_negeri") > 0 Then Cb_SM_Negeri.Items.FindByValue(Rdr("sm_negeri")).Selected = True
            If Not IsDBNull(Rdr("emel")) Then Tx_Emel.Text = Rdr("emel")
            If Not IsDBNull(Rdr("id_kolej")) Then If Rdr("id_kolej") > 0 Then Cb_Kolej.Items.FindByValue(Rdr("id_kolej")).Selected = True
            If Not IsDBNull(Rdr("tkh_latihan_tamat")) Then Tx_T_Latihan.Text = Rdr("tkh_latihan_tamat")
            If Not IsDBNull(Rdr("lp_nama")) Then Tx_LP.Text = Rdr("lp_nama")
            If Not IsDBNull(Rdr("lp_nopd")) Then Tx_LP_NoPd.Text = Rdr("lp_nopd")
            If Not IsDBNull(Rdr("lp_tkhsah")) Then Tx_LP_TkhSah.Text = Rdr("lp_tkhsah")

            If Not IsDBNull(Rdr("mohon_no")) Then Tx_NoSiri.Text = Rdr("mohon_no")
            If Not IsDBNull(Rdr("mohon_tkh")) Then Tx_TkhMohon.Text = Rdr("mohon_tkh")
            If Not IsDBNull(Rdr("mohon_tkhresit")) Then Tx_TkhResit.Text = Rdr("mohon_tkhresit")
            If Not IsDBNull(Rdr("mohon_noresit")) Then Tx_NoResit.Text = Rdr("mohon_noresit")
            If Not IsDBNull(Rdr("keputusan_msy")) Then Tx_Mesyuarat.Text = Rdr("keputusan_msy")
            If Not IsDBNull(Rdr("keputusan_tkh")) Then Tx_Tkh_Lulus.Text = Rdr("keputusan_tkh")
            If Not IsDBNull(Rdr("keputusan_tkh_tamat")) Then Tx_Tkh_Lulus0.Text = Rdr("keputusan_tkh_tamat")

            If Not IsDBNull(Rdr("ss1")) Then If Rdr("ss1") = 1 Then chk_Semak.Items(0).Selected = True
            If Not IsDBNull(Rdr("ss2")) Then If Rdr("ss2") = 1 Then chk_Semak.Items(1).Selected = True
            If Not IsDBNull(Rdr("ss3")) Then If Rdr("ss3") = 1 Then chk_Semak.Items(2).Selected = True
            If Not IsDBNull(Rdr("ss4")) Then If Rdr("ss4") = 1 Then chk_Semak.Items(3).Selected = True
            If Not IsDBNull(Rdr("ss5")) Then If Rdr("ss5") = 1 Then chk_Semak.Items(4).Selected = True
            If Not IsDBNull(Rdr("ss6")) Then If Rdr("ss6") = 1 Then chk_Semak.Items(5).Selected = True
            If Not IsDBNull(Rdr("ss7")) Then If Rdr("ss7") = 1 Then chk_Semak.Items(6).Selected = True
            If Not IsDBNull(Rdr("ss8")) Then If Rdr("ss8") = 1 Then chk_Semak.Items(7).Selected = True
            If Not IsDBNull(Rdr("ss9")) Then If Rdr("ss9") = 1 Then chk_Semak.Items(8).Selected = True
            If Not IsDBNull(Rdr("ss10")) Then If Rdr("ss10") = 1 Then chk_Semak.Items(9).Selected = True
            If Not IsDBNull(Rdr("ss11")) Then If Rdr("ss11") = 1 Then chk_Semak.Items(10).Selected = True
            If Not IsDBNull(Rdr("ss12")) Then If Rdr("ss12") = 1 Then chk_Semak.Items(11).Selected = True
            If Not IsDBNull(Rdr("ss13")) Then If Rdr("ss13") = 1 Then chk_Semak.Items(12).Selected = True
            If Not IsDBNull(Rdr("ss14")) Then If Rdr("ss14") = 1 Then chk_Semak.Items(13).Selected = True
            If Not IsDBNull(Rdr("ss15")) Then If Rdr("ss15") = 1 Then chk_Semak.Items(14).Selected = True
            If Not IsDBNull(Rdr("ss16")) Then If Rdr("ss16") = 1 Then chk_Semak.Items(15).Selected = True

            If Not IsDBNull(Rdr("log_status")) And Rdr("log_status") > 0 Then Cb_Status.Items.FindByValue(Rdr("log_status")).Selected = True
            If Not IsDBNull(Rdr("log_catatan")) Then Tx_Catatan.Text = Rdr("log_catatan")
        End If
        Rdr.Close()

        Dim i As Int16, tb, tt As TextBox, tk As DropDownList
        Cmd.CommandText = "select * from jt_tpc_kelayakan where nokp = '" & Session("NOKP") & "' and jenis = 1"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Gd.Rows.Item(i).Visible = True
            tk = Gd.Rows.Item(i).FindControl("Cb_Kelayakan")
            tb = Gd.Rows.Item(i).FindControl("Tx_Bidang")
            tt = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")
            If Not IsDBNull(Rdr("kelayakan")) Then tk.SelectedItem.Text = Rdr("kelayakan")
            If Not IsDBNull(Rdr("bidang")) Then tb.Text = Rdr("bidang")
            If Not IsDBNull(Rdr("tkh_kelayakan")) Then tt.Text = Rdr("tkh_kelayakan")
            i = i + 1
        End While
        Rdr.Close()

        i = 0
        Cmd.CommandText = "select * from jt_tpc_kelayakan where nokp = '" & Session("NOKP") & "' and jenis = 2"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            GdA.Rows.Item(i).Visible = True
            tk = GdA.Rows.Item(i).FindControl("Cb_Kelayakan")
            tt = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")
            If Not IsDBNull(Rdr("kelayakan")) Then tk.SelectedItem.Text = Rdr("kelayakan")
            If Not IsDBNull(Rdr("tkh_kelayakan")) Then tt.Text = Rdr("tkh_kelayakan")
            i = i + 1
        End While
        Rdr.Close()

        i = 0
        Dim pd, ph As TextBox, pb, ps As DropDownList
        Cmd.CommandText = "select * from jt_tpc_pengalaman where nokp = '" & Session("NOKP") & "'"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            GdP.Rows.Item(i).Visible = True
            pd = GdP.Rows.Item(i).FindControl("Tx_Dari")
            ph = GdP.Rows.Item(i).FindControl("Tx_Hingga")
            pb = GdP.Rows.Item(i).FindControl("Cb_Bidang")
            ps = GdP.Rows.Item(i).FindControl("Cb_Sebagai")
            If Not IsDBNull(Rdr("dari")) Then pd.Text = Rdr("dari")
            If Not IsDBNull(Rdr("hingga")) Then ph.Text = Rdr("hingga")
            If Not IsDBNull(Rdr("bidang")) Then pb.SelectedItem.Text = Rdr("bidang")
            If Not IsDBNull(Rdr("sebagai")) Then ps.SelectedItem.Text = Rdr("sebagai")
            i += 1
        End While
        Rdr.Close()

        i = 0
        Dim mta, mt As TextBox, mtb, mm As DropDownList, cb As Button
        Cmd.CommandText = "select * from jt_tpc_majikan where nokp = '" & Session("NOKP") & "' order by id"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            GdM.Rows.Item(i).Visible = True
            mm = GdM.Rows.Item(i).FindControl("Cb_Majikan")
            mta = GdM.Rows.Item(i).FindControl("Tx_Tempoh")
            mtb = GdM.Rows.Item(i).FindControl("Cb_Tempoh")
            mt = GdM.Rows.Item(i).FindControl("Tx_TkhTamat")
            cb = GdM.Rows.Item(i).FindControl("button4")
            If Not IsDBNull(Rdr("tpt_amalan")) Then mm.Items.FindByValue(Rdr("tpt_amalan")).Selected = True
            If Not IsDBNull(Rdr("tempoh_a")) Then mta.Text = Rdr("tempoh_a")
            If Not IsDBNull(Rdr("tempoh_b")) Then mtb.SelectedItem.Text = Rdr("tempoh_b")
            If Not IsDBNull(Rdr("tkh_tamat")) Then mt.Text = Rdr("tkh_tamat")
            If Not IsDBNull(Rdr("status")) Then If Rdr("status") = 1 Then cb.Visible = True Else cb.Visible = False
            i += 1
        End While
        Rdr.Close()
        Cn.Close()
        Isi_TPC()
        If Tx_Tkh_Lahir.Text = "" Then Else Kira_Umur2(Tx_Tkh_Lahir.Text, Tx_Umur)
        Cb_Jenis.Enabled = False : Tx_NoKP.Enabled = False
    End Sub

    Public Sub Simpan_Pinda()
        'check for majikan , kalau blank jgn save

        Dim SQL As String
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Cmd2 As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Cmd2.Connection = Cn

        If chk_Tukar.Checked Then
            Session("NOKP_TUKAR") = Tx_NoKP.Text
        Else
            Session("NOKP_TUKAR") = Tx_NoKP0.Text 'Session("NOKP")
        End If

        ''Tarikh lahir
        'Dim a As DateTime
        'Dim b As String
        ''Dim c As String
        'If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
        '    b = Tx_Tkh_Lahir.Text.Trim
        '    a = DateTime.ParseExact(b, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        '    b = a.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
        '    'c = "'" & b & "'"
        'Else
        '    b = "NULL"
        '    'c = "NULL"
        'End If

        '' Tarikh Tamat Latihan
        'Dim a1 As DateTime
        'Dim b1 As String
        ''Dim c1 As String
        'If Tx_T_Latihan.Text.Trim <> String.Empty Then
        '    b1 = Tx_T_Latihan.Text.Trim
        '    a1 = DateTime.ParseExact(b1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        '    b1 = a1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
        '    'c1 = "'" & b1 & "'"
        'Else
        '    b1 = "NULL"
        '    'c1 = "NULL"
        'End If


        ''Tarikh Sah Lembaga
        'Dim a2 As DateTime
        'Dim b2 As String
        ''Dim c2 As String
        'If Tx_LP_TkhSah.Text.Trim <> String.Empty Then
        '    b2 = Tx_LP_TkhSah.Text.Trim
        '    a2 = DateTime.ParseExact(b2, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        '    b2 = a2.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
        '    'c2 = "'" & b2 & "'"
        'Else
        '    b2 = "NULL"
        '    'c2 = "NULL"
        'End If

        ''Tarikh Pemohonan
        'Dim a3 As DateTime
        'Dim b3 As String
        ''Dim c3 As String
        'If Tx_TkhMohon.Text.Trim <> String.Empty Then
        '    b3 = Tx_TkhMohon.Text.Trim
        '    a3 = DateTime.ParseExact(b3, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        '    b3 = a3.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
        '    'c3 = "'" & b3 & "'"
        'Else
        '    b3 = "NULL"
        '    'c3 = "NULL"
        'End If


        ''Tarikh Resit
        'Dim a4 As DateTime
        'Dim b4 As String
        ''Dim c4 As String
        'If Tx_TkhResit.Text.Trim <> String.Empty Then
        '    b4 = Tx_TkhResit.Text.Trim
        '    a4 = DateTime.ParseExact(b4, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        '    b4 = a4.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
        '    'c4 = "'" & b4 & "'"
        'Else
        '    b4 = "NULL"
        '    'c4 = "NULL"
        'End If


        ''Tarikh Lulus 
        'Dim a5 As DateTime
        'Dim b5 As String
        ''Dim c5 As String
        'If Tx_Tkh_Lulus.Text.Trim <> String.Empty Then
        '    b5 = Tx_Tkh_Lulus.Text.Trim
        '    a5 = DateTime.ParseExact(b5, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        '    b5 = a5.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
        '    'c5 = "'" & b5 & "'"
        'Else
        '    b5 = "NULL"
        '    'c5 = "NULL"
        'End If

        ''Tarikh Lulus Tamat
        'Dim a6 As DateTime
        'Dim b6 As String
        ''Dim c6 As String
        'If Tx_Tkh_Lulus.Text.Trim <> String.Empty Then
        '    b6 = Tx_Tkh_Lulus.Text.Trim
        '    a6 = DateTime.ParseExact(b6, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        '    b6 = a6.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
        '    'c6 = "'" & b6 & "'"
        'Else
        '    b6 = "NULL"
        '    'c6 = "NULL"
        'End If

        'Fixing Date Format Covertion 29062018 -OSH
        'SQL = "update jt_tpc set " & _
        '      "nama = '" & Tx_Nama.Text & "'," & _
        '      "umur = '" & Tx_Umur.Text & "'," & _
        '      "tpt_lahir = '" & Tx_Tpt_Lahir.Text & "'," & _
        '      "tkh_lahir = '" & b & "'," & _
        '      "warganegara = " & Cb_Warga.SelectedValue & "," & _
        '      "taraf_warganegara = " & Tukar_Warga(chk_PR, Cb_Kahwin, Cb_Warga) & "," & _
        '      "jantina = '" & Cb_Jantina.SelectedValue & "'," & _
        '      "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," & _
        '      "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim) & "'," & _
        '      "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," & _
        '      "tp_bandar = '" & Tx_TP_Bandar.Text.Trim & "'," & _
        '      "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
        '      "emel = '" & Tx_Emel.Text.Trim & "'," & _
        '      "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim) & "'," & _
        '      "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," & _
        '      "sm_bandar = '" & Tx_SM_Bandar.Text.Trim & "'," & _
        '      "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
        '      "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," & _
        '      "tkh_latihan_tamat = '" & b1 & "'," & _
        '      "lp_nama = '" & Tx_LP.Text & "'," & _
        '      "lp_nopd = '" & Tx_LP_NoPd.Text & "'," & _
        '      "lp_tkhsah = '" & b2 & "'," & _
        '      "mohon_no = '" & Tx_NoSiri.Text.Trim & "'," & _
        '      "mohon_tkh = '" & b3 & "'," & _
        '      "mohon_tkhresit= '" & b4 & "'," & _
        '      "mohon_noresit= '" & Tx_NoResit.Text.Trim & "'," & _
        '      "log_status = '" & Cb_Status.SelectedItem.Value & "'," & _
        '      "log_catatan = '" & Tx_Catatan.Text.Trim & "'," & _
        '      "keputusan_msy = '" & Tx_Mesyuarat.Text & "'," & _
        '      "keputusan_tkh = '" & b5 & "'," & _
        '      "keputusan_tkh_tamat = '" & b6 & "'," & _
        '      "ss1 = " & SSemak(0) & "," & _
        '      "ss2 = " & SSemak(1) & "," & _
        '      "ss3 = " & SSemak(2) & "," & _
        '      "ss4 = " & SSemak(3) & "," & _
        '      "ss5 = " & SSemak(4) & "," & _
        '      "ss6 = " & SSemak(5) & "," & _
        '      "ss7 = " & SSemak(6) & "," & _
        '      "ss8 = " & SSemak(7) & "," & _
        '      "ss9 = " & SSemak(8) & "," & _
        '      "ss10 = " & SSemak(9) & "," & _
        '      "ss11 = " & SSemak(10) & "," & _
        '      "ss12 = " & SSemak(11) & "," & _
        '      "ss13 = " & SSemak(12) & "," & _
        '      "ss14 = " & SSemak(13) & "," & _
        '      "ss15 = " & SSemak(14) & "," & _
        '      "ss16 = " & SSemak(15) & " " & _
        '      "where nokp = '" & Tx_NoKP0.Text & "'" 'Session("NOKP") 
        'SQL += "; "

        'Comment Original 29062018 - OSH
        SQL = "update jt_tpc set " &
                "nama = '" & Tx_Nama.Text & "'," &
                "umur = '" & Tx_Umur.Text & "'," &
                "tpt_lahir = '" & Apo(Tx_Tpt_Lahir.Text.Trim) & "'," &
                "tkh_lahir = " & Chk_Tkh(Tx_Tkh_Lahir.Text) & "," &
                "warganegara = " & Cb_Warga.SelectedValue & "," &
                "taraf_warganegara = " & Tukar_Warga(chk_PR, Cb_Kahwin, Cb_Warga) & "," &
                "jantina = '" & Cb_Jantina.SelectedValue & "'," &
                "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," &
                "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim) & "'," &
                "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," &
                "tp_bandar = '" & Apo(Tx_TP_Bandar.Text.Trim) & "'," &
                "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," &
                "emel = '" & Tx_Emel.Text.Trim & "'," &
                "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim) & "'," &
                "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," &
                "sm_bandar = '" & Apo(Tx_SM_Bandar.Text.Trim) & "'," &
                "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," &
                "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," &
                "tkh_latihan_tamat = " & Chk_Tkh(Tx_T_Latihan.Text) & "," &
                "lp_nama = '" & Apo(Tx_LP.Text.Trim) & "'," &
                "lp_nopd = '" & Tx_LP_NoPd.Text & "'," &
                "lp_tkhsah = " & Chk_Tkh(Tx_LP_TkhSah.Text) & "," &
                "mohon_no = '" & Tx_NoSiri.Text.Trim & "'," &
                "mohon_tkh = " & Chk_Tkh(Tx_TkhMohon.Text) & "," &
                "mohon_tkhresit= " & Chk_Tkh(Tx_TkhResit.Text) & "," &
                "mohon_noresit= '" & Tx_NoResit.Text.Trim & "'," &
                "log_status = '" & Cb_Status.SelectedItem.Value & "'," &
                "log_catatan = '" & Apo(Tx_Catatan.Text.Trim) & "'," &
                "keputusan_msy = '" & Apo(Tx_Mesyuarat.Text.Trim) & "'," &
                "keputusan_tkh = " & Chk_Tkh(Tx_Tkh_Lulus.Text) & "," &
                "keputusan_tkh_tamat = " & Chk_Tkh(Tx_Tkh_Lulus0.Text) & "," &
                "ss1 = " & SSemak(0) & "," &
                "ss2 = " & SSemak(1) & "," &
                "ss3 = " & SSemak(2) & "," &
                "ss4 = " & SSemak(3) & "," &
                "ss5 = " & SSemak(4) & "," &
                "ss6 = " & SSemak(5) & "," &
                "ss7 = " & SSemak(6) & "," &
                "ss8 = " & SSemak(7) & "," &
                "ss9 = " & SSemak(8) & "," &
                "ss10 = " & SSemak(9) & "," &
                "ss11 = " & SSemak(10) & "," &
                "ss12 = " & SSemak(11) & "," &
                "ss13 = " & SSemak(12) & "," &
                "ss14 = " & SSemak(13) & "," &
                "ss15 = " & SSemak(14) & "," &
                "ss16 = " & SSemak(15) & " " &
                "where nokp = '" & Tx_NoKP0.Text & "'" 'Session("NOKP") 
        SQL += "; "

        If chk_Tukar.Checked And Tx_NoKP.Enabled Then
            SQL += "update jt_tpc set nokp = '" & Session("NOKP_TUKAR") & "' where nokp = '" & Tx_NoKP0.Text & "'; " 'Session("NOKP") 
            SQL += "update jt_tpc_tpc set nokp = '" & Session("NOKP_TUKAR") & "' where nokp = '" & Tx_NoKP0.Text & "'; " 'Session("NOKP") & "'; "
        End If

        If chk_Pinda.Checked And Cb_Jenis.Enabled Then
            SQL += "update jt_tpc set j_daftar = '" & Cb_Jenis.SelectedItem.Value & "' where nokp = '" & Tx_NoKP0.Text & "'; " 'Session("NOKP") & "'; "
            SQL += "update jt_tpc_tpc set j_daftar = '" & Cb_Jenis.SelectedItem.Value & "' where nokp = '" & Tx_NoKP0.Text & "'; " 'Session("NOKP") & "'; "
        End If

        SQL += "delete from jt_tpc_kelayakan where nokp = '" & Tx_NoKP0.Text & "'; " 'Session("NOKP") & "'; "
        'ikhtisas
        Dim i As Int16, tb, tt As TextBox, tk As DropDownList
        For i = 0 To Gd.Rows.Count - 1
            If Not Gd.Rows.Item(i).Visible Then Exit For
            tk = Gd.Rows.Item(i).FindControl("Cb_Kelayakan")
            tb = Gd.Rows.Item(i).FindControl("Tx_Bidang")
            tt = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")

            'Fix Date Format Covertion 29062018 -OSH
            'SQL += "insert jt_tpc_kelayakan (nokp, jenis, kelayakan, bidang, tkh_kelayakan) values (" & _
            '"'" & Session("NOKP_TUKAR") & "'," & _
            '"1," & _
            '"'" & tk.SelectedItem.Text & "'," & _
            '"'" & tb.Text.Trim & "'," & _
            '"'" & tt.Text & "'" & _
            '")"

            'Comment Original 29062018 - OSH 
            SQL += "insert jt_tpc_kelayakan (nokp, jenis, kelayakan, bidang, tkh_kelayakan) values (" &
            "'" & Session("NOKP_TUKAR") & "'," &
            "1," &
            "'" & tk.SelectedItem.Text & "'," &
            "'" & tb.Text.Trim & "'," &
            "" & Chk_Tkh(tt.Text) & "" &
            ")"
            SQL += ";"
        Next

        'akademik
        For i = 0 To GdA.Rows.Count - 1
            If Not GdA.Rows.Item(i).Visible Then Exit For
            tk = GdA.Rows.Item(i).FindControl("Cb_Kelayakan")
            tt = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")

            'Fix Date Format Covertion 29062018 -OSH
            ' SQL += "insert jt_tpc_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
            '"'" & Session("NOKP_TUKAR") & "'," & _
            '"2," & _
            '"'" & tk.SelectedItem.Text & "'," & _
            '"'" & tt.Text & "'" & _
            '")"
            ' SQL += ";"

            'Comment Original 29062018 - OSH
            SQL += "insert jt_tpc_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" &
            "'" & Session("NOKP_TUKAR") & "'," &
            "2," &
            "'" & tk.SelectedItem.Text & "'," &
            "" & Chk_Tkh(tt.Text) & "" &
            ")"
            SQL += ";"
        Next

        SQL += "delete from jt_tpc_pengalaman where nokp = '" & Tx_NoKP0.Text & "'; " 'Session("NOKP") & "'; "
        'pengalaman
        Dim pd, ph As TextBox, pb, ps As DropDownList
        For i = 0 To GdP.Rows.Count - 1
            If Not GdP.Rows.Item(i).Visible Then Exit For
            pd = GdP.Rows.Item(i).FindControl("Tx_Dari")
            ph = GdP.Rows.Item(i).FindControl("Tx_Hingga")
            pb = GdP.Rows.Item(i).FindControl("Cb_Bidang")
            ps = GdP.Rows.Item(i).FindControl("Cb_Sebagai")


            'Fix Date Format Covertion 29062018 -OSH
            'SQL += "insert jt_tpc_pengalaman (nokp, dari, hingga, bidang, sebagai) values (" & _
            '"'" & Session("NOKP_TUKAR") & "'," & _
            '"'" & pd.Text & "'," & _
            '"'" & ph.Text & "'," & _
            '"'" & pb.SelectedItem.Text.Trim & "'," & _
            '"'" & ps.SelectedItem.Text.Trim & "'" & _
            '")"

            'Comment Original 29062018 - OSH 
            SQL += "insert jt_tpc_pengalaman (nokp, dari, hingga, bidang, sebagai) values (" &
            "'" & Session("NOKP_TUKAR") & "'," &
            "" & Chk_Tkh(pd.Text) & "," &
            "" & Chk_Tkh(ph.Text) & "," &
            "'" & pb.SelectedItem.Text.Trim & "'," &
            "'" & ps.SelectedItem.Text.Trim & "'" &
            ")"
            SQL += ";"
        Next

        For i = GdM.Rows.Count - 1 To -1 Step -1
            If i = -1 Then Exit For
            Dim cb As Button = GdM.Rows.Item(i).FindControl("Button4")
            If GdM.Rows.Item(i).Visible Then
                Dim mta, mt As TextBox, mtb, mm As DropDownList
                mm = GdM.Rows.Item(i).FindControl("Cb_Majikan")
                mta = GdM.Rows.Item(i).FindControl("Tx_Tempoh")
                mtb = GdM.Rows.Item(i).FindControl("Cb_Tempoh")
                mt = GdM.Rows.Item(i).FindControl("Tx_TkhTamat")
                If mta.Text.Trim = "" Then mta.Text = "0"

                SQL += "delete from jt_tpc_majikan where nokp='" & Tx_NoKP0.Text & "' and  tpt_amalan = '" & mm.SelectedItem.Value & "' and status=1;"   ' Session("NOKP")

                'Fix Date Format Covertion 29062018 -OSH
                ' SQL += "insert jt_tpc_majikan (nokp, tpt_amalan, tempoh_a, tempoh_b, tkh_tamat, status) values (" & _
                '"'" & Session("NOKP_TUKAR") & "'," & _
                '"'" & mm.SelectedItem.Value & "'," & _
                '"" & CInt(mta.Text.Trim) & "," & _
                '"'" & mtb.SelectedItem.Text & "'," & _
                '"" & Fix_Date(mt.Text) & ",1" & _
                '")"

                'Comment Original 29062018 -OSH
                SQL += "insert jt_tpc_majikan (nokp, tpt_amalan, tempoh_a, tempoh_b, tkh_tamat, status) values (" &
                "'" & Session("NOKP_TUKAR") & "'," &
                "'" & mm.SelectedItem.Value & "'," &
                "" & CInt(mta.Text.Trim) & "," &
                "'" & mtb.SelectedItem.Text & "'," &
                "" & Chk_Tkh(mt.Text) & ",1" &
                ")"
                SQL += ";"
                Exit For
            End If
        Next

        'latihan
        SQL += "delete from tmp_tpc_latihan where nokp = '" & Tx_NoKP0.Text & "'; " 'Session("NOKP") & "'; "
        Dim lm As TextBox
        For i = 0 To GdL.Rows.Count - 1
            If Not GdL.Rows.Item(i).Visible Then Exit For
            lm = GdL.Rows.Item(i).FindControl("tx_masa")
            If lm.Text.Trim = "" Or Not IsNumeric(lm.Text) Then
            Else
                SQL += "insert tmp_tpc_latihan (nokp, id_latihan, masa_catit) values (" &
                "'" & Session("NOKP_TUKAR") & "'," &
                "'" & GdL.Rows.Item(i).Cells(4).Text & "'," &
                "" & CInt(lm.Text) & ")"
                SQL += "; "
            End If
        Next

        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            'Update_TPC(Session("NOKP_TUKAR"))
            'Session("Msg_Tajuk") = "Pinda/Semak Rekod TPC"
            'Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            'Response.Redirect("p0_Mesej.aspx")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try

        'UPDATE TPC RECORDS 06272018 -OSH
        Try
            Cmd2.CommandText = "Update_TPCs"
            Cmd2.CommandType = CommandType.StoredProcedure

            Cmd2.Parameters.AddWithValue("@NoKP", Session("NOKP_TUKAR"))
            Cmd2.ExecuteNonQuery()
            Session("Msg_Tajuk") = "Pinda/Semak Rekod TPC"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
        Cn.Close()
    End Sub

    Public Sub Grid_Row(ByVal X As Int16, ByVal Y As Int16)
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter("select top " & X & " * from tmp_grid", Cn)

        List_Adp.Fill(List_Data, "tmp_grid")
        If Y = 1 Then Gd.DataSource = List_Data.Tables("tmp_grid") : Gd.DataBind()
        If Y = 2 Then GdA.DataSource = List_Data.Tables("tmp_grid") : GdA.DataBind()
        If Y = 3 Then GdP.DataSource = List_Data.Tables("tmp_grid") : GdP.DataBind()
        If Y = 4 Then GdM.DataSource = List_Data.Tables("tmp_grid") : GdM.DataBind()
    End Sub

    Public Sub Isi_Latihan()
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter("select distinct ptl.*, ttl.masa_catit, ttl.status from pn_tpc_latihan ptl left outer join tmp_tpc_latihan  ttl on ptl.id_latihan = ttl.id_latihan and ttl.nokp = '" & Session("nokp") & "'", Cn)

        List_Adp.Fill(List_Data, "pn_tpc_latihan")
        GdL.DataSource = List_Data.Tables("pn_tpc_latihan") : GdL.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'IKHTISAS
        Cmd.CommandText = "select dc_ikhtisas, id_ikhtisas from pn_ikhtisas order by id_ikhtisas"
        Rdr = Cmd.ExecuteReader()
        Cb_I.Items.Clear()
        Cb_I.Items.Add("")
        While Rdr.Read
            Cb_I.Items.Add(Rdr(0))
            Cb_I.Items.Item(Cb_I.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AKADEMIK
        Cmd.CommandText = "select dc_akademik, id_akademik from pn_akademik order by id_akademik"
        Rdr = Cmd.ExecuteReader()
        Cb_K.Items.Clear()
        Cb_K.Items.Add("")
        While Rdr.Read
            Cb_K.Items.Add(Rdr(0))
            Cb_K.Items.Item(Cb_K.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'TPT_AMALAN
        Cmd.CommandText = "select dc_amalan, id_amalan from pn_tpt_amalan order by dc_amalan"
        Rdr = Cmd.ExecuteReader()
        Cb_M.Items.Clear()
        Cb_M.Items.Add("")
        While Rdr.Read
            Cb_M.Items.Add(Rdr(0))
            Cb_M.Items.Item(Cb_M.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'DISIPLIN
        Cmd.CommandText = "select dc_disiplin, id_disiplin from pn_disiplin order by dc_disiplin"
        Rdr = Cmd.ExecuteReader()
        Cb_D.Items.Clear()
        Cb_D.Items.Add("")
        While Rdr.Read
            Cb_D.Items.Add(Rdr(0))
            Cb_D.Items.Item(Cb_D.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Grid_Row(10, 1)
        Grid_Row(10, 2)
        Grid_Row(10, 3)
        Grid_Row(10, 4)
        Isi_Latihan()
        Kira_Latihan()

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_SM_Negeri.Items.Clear()
        Cb_SM_Negeri.Items.Add("")
        While Rdr.Read
            Cb_SM_Negeri.Items.Add(Rdr(0))
            Cb_SM_Negeri.Items.Item(Cb_SM_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'NEGERA
        Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM PN_NEGARA ORDER BY Dc_NEGARA"
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'WARGA
        Cmd.CommandText = "SELECT dc_negara, id_negara FROM pn_negara ORDER BY dc_negara"
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KOLEJ
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cn.Close()

        If Session("PINDA") = True Then Isi_Pinda()
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer, G As String ', x As Button
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$" & sender.id) - InStr(sender.uniqueid, "$ctl") - 4))
        G = Mid(sender.uniqueid, InStr(sender.uniqueid, "$Gd") + 1, InStr(sender.uniqueid, "$ctl") - InStr(sender.uniqueid, "$Gd") - 1)
        Try
            sender.visible = False
            If G = "Gd" Then Gd.Rows.Item(i - 1).Visible = True
            If G = "GdA" Then GdA.Rows.Item(i - 1).Visible = True
            If G = "GdP" Then GdP.Rows.Item(i - 1).Visible = True
            'If G = "GdR" Then GdR.Rows.Item(i - 1).Visible = True
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        Gd.Height = Unit.Pixel(16)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(4).Width = Unit.Pixel(22)
        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Private Sub GdA_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdA.RowCreated
        GdA.Height = Unit.Pixel(16)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(3).Width = Unit.Pixel(22)
        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Private Sub GdA_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdA.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Dim cb As DropDownList
        cb = e.Row.FindControl("cb_kelayakan")

        Dim i As Int16
        For i = 0 To Cb_K.Items.Count - 1
            cb.Items.Add(Cb_K.Items(i).Text)
            cb.Items.Item(cb.Items.Count - 1).Value = Cb_K.Items(i).Value
        Next
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        'Mandatori
        Dim X As String = ""
        If Cb_Jenis.Text.Trim = "" Then X += "  - Jenis Pendaftaran."
        If Tx_Nama.Text.Trim = "" Then X += "  - Nama."
        If Tx_NoKP.Text.Trim = "" Then X += "  - No. Kad Pengenalan."
        If Cb_Warga.Text.Trim = "" Then X += "  - Warganegara."
        If X.Trim = "" Then Else Msg(Me, "Sila isikan maklumat berikut:" & X) : Exit Sub

        If Session("PINDA") = True Then Simpan_Pinda() : Exit Sub

    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Dim cb As DropDownList
        cb = e.Row.FindControl("cb_kelayakan")

        Dim i As Int16
        For i = 0 To Cb_I.Items.Count - 1
            cb.Items.Add(Cb_I.Items(i).Text)
            cb.Items.Item(cb.Items.Count - 1).Value = Cb_I.Items(i).Value
        Next
    End Sub

    Private Sub GdP_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdP.RowCreated
        GdA.Height = Unit.Pixel(16)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(5).Width = Unit.Pixel(22)
        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Private Sub GdP_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdP.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Dim cb As DropDownList
        cb = e.Row.FindControl("cb_bidang")
        cb.Items.Clear()

        Dim i As Int16
        For i = 0 To Cb_D.Items.Count - 1
            cb.Items.Add(Cb_D.Items(i).Text)
            cb.Items.Item(cb.Items.Count - 1).Value = Cb_D.Items(i).Value
        Next
    End Sub

    Private Sub GdM_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdM.RowCreated
        GdM.Height = Unit.Pixel(16)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(4).Width = Unit.Pixel(22)
        'e.Row.Cells(4).Visible = False
        e.Row.VerticalAlign = VerticalAlign.Top

        If e.Row.RowIndex > -1 Then If e.Row.Cells(0).Text = "+" Then Else e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Private Sub GdM_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdM.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Dim cb As DropDownList
        cb = e.Row.FindControl("cb_majikan")

        Dim i As Int16
        For i = 0 To Cb_M.Items.Count - 1
            cb.Items.Add(Cb_M.Items(i).Text)
            cb.Items.Item(cb.Items.Count - 1).Value = Cb_M.Items(i).Value
        Next
    End Sub

    Private Sub Gd_TPC_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_TPC.RowCreated
        e.Row.Height = Unit.Pixel(1)
        e.Row.Cells(0).Width = Unit.Pixel(30)
        e.Row.Cells(1).Width = Unit.Pixel(40)
        e.Row.Cells(2).Width = Unit.Pixel(60)
        e.Row.Cells(3).Width = Unit.Pixel(70)
        e.Row.Cells(4).Width = Unit.Pixel(300)
        e.Row.Cells(5).Width = Unit.Pixel(60)
        e.Row.Cells(6).Visible = False
        e.Row.Cells(7).Visible = False
        e.Row.Cells(8).Visible = False
        'e.Row.Cells(9).Visible = False
        e.Row.Cells(10).Visible = False
        e.Row.Cells(11).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub GdM_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles GdM.SelectedIndexChanged
        ' If GdM.Rows.Count = 1 Then Exit Sub
        'If MsgBoxResult.Yes = Msg(Me, "Tamatkan Tempoh TPC Bagi Tempat Amalan Ini?", MsgBoxStyle.YesNo + MsgBoxStyle.SystemModal, "TPC") Then
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "update jt_tpc_majikan set status = 0 where status = 1 and nokp = '" & Tx_NoKP.Text & "'"
        Cmd.ExecuteNonQuery()

        Dim i As Int16
        i = 0
        Dim mta, mt As TextBox, mtb, mm As DropDownList, cb As Button
        Cmd.CommandText = "select * from jt_tpc_majikan where nokp = '" & Session("NOKP") & "'"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            GdM.Rows.Item(i).Visible = True
            mm = GdM.Rows.Item(i).FindControl("Cb_Majikan")
            mta = GdM.Rows.Item(i).FindControl("Tx_Tempoh")
            mtb = GdM.Rows.Item(i).FindControl("Cb_Tempoh")
            mt = GdM.Rows.Item(i).FindControl("Tx_TkhTamat")
            cb = GdM.Rows.Item(i).FindControl("Button4")
            If Not IsDBNull(Rdr("tpt_amalan")) Then mm.Items.FindByValue(Rdr("tpt_amalan")).Selected = True
            If Not IsDBNull(Rdr("tempoh_a")) Then mta.Text = Rdr("tempoh_a")
            If Not IsDBNull(Rdr("tempoh_b")) Then mtb.SelectedItem.Text = Rdr("tempoh_b")
            If Not IsDBNull(Rdr("tkh_tamat")) Then mt.Text = Rdr("tkh_tamat")
            If Not IsDBNull(Rdr("status")) Then If Rdr("status") = 1 Then cb.Visible = True Else cb.Visible = False
            i = i + 1
        End While
        cb = GdM.Rows.Item(i).FindControl("Button4") : cb.Visible = False
        GdM.Rows.Item(i).Visible = True
        Rdr.Close()
        Cn.Close()
        Msg(Me, "Rekod Telah Dikemaskini...")
        'End If
    End Sub

    Protected Sub chk_Pinda_CheckedChanged(ByVal sender As Object, ByVal e As EventArgs) Handles chk_Pinda.CheckedChanged
        If Session("AKSES") < 1 Then Msg(Me, "Akses Terhad") : Exit Sub
        If chk_Pinda.Checked = True Then Cb_Jenis.Enabled = True Else Cb_Jenis.Enabled = False
    End Sub

    Protected Sub chk_Tukar_CheckedChanged(ByVal sender As Object, ByVal e As EventArgs) Handles chk_Tukar.CheckedChanged
        If Session("AKSES") < 1 Then Msg(Me, "Akses Terhad") : Exit Sub
        If chk_Tukar.Checked = True Then Tx_NoKP.Enabled = True Else Tx_NoKP.Enabled = False
    End Sub

    Private Sub GdL_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdL.RowCreated
        GdL.Height = Unit.Pixel(1)
        e.Row.Height = Unit.Pixel(1)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(1).Width = Unit.Pixel(240)
        e.Row.Cells(2).Width = Unit.Pixel(80)
        e.Row.Cells(3).Width = Unit.Pixel(80)
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(6).Visible = False
        e.Row.Cells(7).Visible = False
        e.Row.Cells(8).Visible = False
        e.Row.Cells(9).Visible = False
        e.Row.VerticalAlign = VerticalAlign.Top
        e.Row.Cells(2).HorizontalAlign = HorizontalAlign.Right
        e.Row.Cells(3).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(4).HorizontalAlign = HorizontalAlign.Center

        If e.Row.RowIndex > 38 Then
            e.Row.BackColor = Drawing.Color.LightYellow
        End If

        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
    End Sub

    Private Sub GdL_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdL.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.Cells(5).Text : e.Row.Cells(5).Text = ""
        e.Row.Cells(2).Text = e.Row.Cells(6).Text & " " & e.Row.Cells(7).Text : e.Row.Cells(6).Text = ""

        If IsNumeric(e.Row.Cells(8).Text) Then
            Dim lm As TextBox
            lm = e.Row.FindControl("tx_masa")
            lm.Text = e.Row.Cells(8).Text : e.Row.Cells(8).Text = ""
        End If
    End Sub

    Protected Sub cmdHantar2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar2.Click
        Kira_Latihan()
    End Sub

End Class