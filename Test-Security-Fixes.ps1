#!/usr/bin/env powershell

# Test the SPMJ Email Service after security fixes

Write-Host "🔧 Testing SPMJ Email Service Security Fixes" -ForegroundColor Cyan

$EmailServiceUrl = "http://localhost:5000"
$ApiKey = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"

function Test-ServiceHealth {
    Write-Host "`n🔍 Testing Service Health..." -ForegroundColor Yellow
    
    try {
        $response = Invoke-WebRequest -Uri "$EmailServiceUrl/health" -UseBasicParsing -TimeoutSec 10
        Write-Host "✅ Health Check: SUCCESS" -ForegroundColor Green
        Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
        Write-Host "   Response: $($response.Content)" -ForegroundColor White
        return $true
    }
    catch {
        Write-Host "❌ Health Check: FAILED" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor White
        return $false
    }
}
}

function Test-ApiWithoutKey {
    Write-Host "`n🔍 Testing API Without Key (Should Fail)..." -ForegroundColor Yellow
    
    try {
        $body = @{
            UserId = "test-user"
            Email = "<EMAIL>"
        } | ConvertTo-Json
        
        $response = Invoke-WebRequest -Uri "$EmailServiceUrl/api/password/reset/request" `
            -Method POST `
            -Body $body `
            -ContentType "application/json" `
            -UseBasicParsing -TimeoutSec 10
            
        Write-Host "❌ Security Test: FAILED - Request should have been blocked!" -ForegroundColor Red
        return $false
    }
    catch {
        if ($_.Exception.Message -like "*401*" -or $_.Exception.Message -like "*Unauthorized*") {
            Write-Host "✅ Security Test: SUCCESS - Unauthorized request properly blocked" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Security Test: UNEXPECTED ERROR" -ForegroundColor Red
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor White
            return $false
        }
    }
}

function Test-ApiWithKey {
    Write-Host "`n🔍 Testing API With Valid Key (Should Succeed)..." -ForegroundColor Yellow
    
    try {
        $headers = @{
            "X-API-Key" = $ApiKey
            "Content-Type" = "application/json"
        }
        
        $body = @{
            UserId = "test-user"
            Email = "<EMAIL>"
        } | ConvertTo-Json
        
        $response = Invoke-WebRequest -Uri "$EmailServiceUrl/api/password/reset/request" `
            -Method POST `
            -Headers $headers `
            -Body $body `
            -UseBasicParsing -TimeoutSec 10
            
        Write-Host "✅ API Key Test: SUCCESS - Request processed" -ForegroundColor Green
        Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
        return $true
    }
    catch {
        Write-Host "⚠️ API Key Test: Service may not be running or user doesn't exist" -ForegroundColor Yellow
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor White
        return $false
    }
}

function Test-CorsRestriction {
    Write-Host "`n🔍 Testing CORS Restrictions..." -ForegroundColor Yellow
    
    try {
        $headers = @{
            "Origin" = "https://malicious-site.com"
            "X-API-Key" = $ApiKey
        }
        
        $response = Invoke-WebRequest -Uri "$EmailServiceUrl/api/password/reset/request" `
            -Method POST `
            -Headers $headers `
            -UseBasicParsing -TimeoutSec 10
            
        Write-Host "❌ CORS Test: FAILED - Malicious origin should be blocked!" -ForegroundColor Red
        return $false
    }
    catch {
        Write-Host "✅ CORS Test: SUCCESS - Malicious origin properly blocked" -ForegroundColor Green
        return $true
    }
}

# Main Test Execution
Write-Host "Starting security tests..." -ForegroundColor White

$healthResult = Test-ServiceHealth
$securityResult = Test-ApiWithoutKey
$apiKeyResult = Test-ApiWithKey
$corsResult = Test-CorsRestriction

Write-Host "`n📊 TEST RESULTS SUMMARY" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host "Health Check:      $(if($healthResult) {'✅ PASS'} else {'❌ FAIL'})" -ForegroundColor $(if($healthResult) {'Green'} else {'Red'})
Write-Host "Security (No Key): $(if($securityResult) {'✅ PASS'} else {'❌ FAIL'})" -ForegroundColor $(if($securityResult) {'Green'} else {'Red'})
Write-Host "API Key Auth:      $(if($apiKeyResult) {'✅ PASS'} else {'⚠️ PARTIAL'})" -ForegroundColor $(if($apiKeyResult) {'Green'} else {'Yellow'})
Write-Host "CORS Protection:   $(if($corsResult) {'✅ PASS'} else {'❌ FAIL'})" -ForegroundColor $(if($corsResult) {'Green'} else {'Red'})

if ($healthResult -and $securityResult) {
    Write-Host "`n🎉 SECURITY FIXES VERIFIED!" -ForegroundColor Green
    Write-Host "   The microservice is now secure against third-party hijacking." -ForegroundColor Green
} else {
    Write-Host "`n⚠️ ISSUES DETECTED" -ForegroundColor Yellow
    Write-Host "   Please check the service configuration and logs." -ForegroundColor Yellow
}

Write-Host "`nTo start the service manually:" -ForegroundColor Cyan
Write-Host "cd 'd:\2024\.NET 3.5. - Q\SPMJ.EmailService'" -ForegroundColor White
Write-Host "dotnet run" -ForegroundColor White
