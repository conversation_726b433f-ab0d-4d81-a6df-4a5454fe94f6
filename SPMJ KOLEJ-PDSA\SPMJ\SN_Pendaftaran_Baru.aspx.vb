﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class SN_Pendaftaran_Baru1
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
    End Sub


    Protected Sub cmd_Jana_Daftar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana_Daftar.Click

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select top 1 * from pn_xm where j_xm=" & Cb_Kursus.SelectedIndex & " and status ='0' order by tahun desc, siri desc, j_xm desc"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr("tkh_papar")) Then
                Fn_Daftar()
            Else
                MsgBox("Tiada Senarai Calon Lulus Untuk  Kategori Ini.", MsgBoxStyle.Information, Me)
            End If
        End If
        Rdr.Close()
        Cn.Close()
    End Sub

    Public Sub Fn_Daftar()
        'Senarai Pendafaran Jururawat Baru melalui BLESS 12032014 -OSH  
        Session("Lpr_Nama") = "SNPendJururawat"
        Session("Var_1") = Session("Id_KOLEJ")
        Session("Var_2") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_3") = 5 Else Session("Var_3") = Cb_Kursus.SelectedValue
        If Cb_Kursus.SelectedValue = 1 Then Session("Var_4") = 8 Else Session("Var_4") = Cb_Kursus.SelectedValue
        Response.Write("<script language='javascript'>win=window.open('Cetak_Daftar.aspx',null,'width=1000,height=750,top='+ (screen.height-750)/3 +',left='+ (screen.width-1000)/2 +'','true');</script>")
    End Sub
End Class