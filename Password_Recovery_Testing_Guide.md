# PASSWORD RECOVERY TESTING GUIDE

## 🧪 STEP-BY-STEP TESTING PROCEDURE

### **Pre-Testing Setup**

1. **Database Schema Verification:**
   ```sql
   -- Run this to check required columns exist:
   SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_NAME = 'pn_pengguna' 
   AND COLUMN_NAME IN ('is_temporary', 'force_change', 'salt', 'password_migrated');
   ```

2. **Compile Fixed Code:**
   ```powershell
   cd "SPMJ -PDSA"
   C:\Windows\Microsoft.NET\Framework\v3.5\vbc.exe /target:library /reference:System.Web.dll,System.dll,System.Data.dll,System.Configuration.dll /out:SPMJ_Recovery_Fixed.dll "SPMJ\p0_Login.aspx.vb" "SPMJ\p0_PasswordChangeForced.aspx.vb" "SPMJ\SPMJ_Mod.vb" "SPMJ\PasswordHelper.vb"
   ```

3. **Deploy Fixed DLL to Web Application**

---

### **🔄 COMPLETE PASSWORD RECOVERY WORKFLOW TEST**

#### **Test Case 1: Normal Password Recovery Flow**

**Step 1: Request Password Recovery**
1. Navigate to login page
2. Click "Lupa Kata Laluan?" link
3. Enter valid User ID that has email address
4. Click "Hantar" button
5. **Expected:** Success message "Kata laluan sementara telah dihantar ke email..."

**Step 2: Check Email & Database**
1. Check user's email for temporary password
2. Check database record:
   ```sql
   SELECT id_pg, is_temporary, force_change, password_migrated 
   FROM pn_pengguna WHERE id_pg = 'your_test_user';
   ```
3. **Expected:** `is_temporary=1`, `force_change=1`, `password_migrated=1`

**Step 3: Login with Temporary Password**
1. Enter User ID and temporary password from email
2. Click "Log Masuk" button
3. **Expected:** Automatically redirects to `p0_PasswordChangeForced.aspx`
4. **Debug logs should show:**
   ```
   === Password Recovery Debug ===
   Is Temporary: True
   Force Change: True
   ```

**Step 4: Change Password**
1. Enter new password and confirmation
2. Click "Kemaskini" button
3. **Expected:** Success message and redirect to main system
4. **Debug logs should show:**
   ```
   === Password Change Debug ===
   Rows affected: 1
   ```

**Step 5: Login with New Password**
1. Logout and return to login page
2. Enter User ID and new permanent password
3. **Expected:** Normal login flow, access to main system
4. **Database check:**
   ```sql
   SELECT is_temporary, force_change FROM pn_pengguna WHERE id_pg = 'your_test_user';
   ```
   **Expected:** `is_temporary=0`, `force_change=0`

---

### **🧪 EDGE CASE TESTING**

#### **Test Case 2: User Without Email**
1. Request recovery for user without email address
2. **Expected:** Generic message (security - don't reveal if user exists)

#### **Test Case 3: Invalid User ID**
1. Request recovery for non-existent user
2. **Expected:** Generic message (security)

#### **Test Case 4: Multiple Recovery Requests**
1. Request recovery multiple times for same user
2. **Expected:** Each request generates new temporary password

#### **Test Case 5: SQL Injection Attempts**
1. Try recovery with: `'; DROP TABLE pn_pengguna; --`
2. **Expected:** "ID Pengguna tidak sah" error message

---

### **🐛 DEBUGGING ISSUES**

#### **Problem: Cannot Login After Recovery**
**Check Debug Logs:**
```
=== Password Recovery Debug ===
User: [user_id]
Password Migrated: True/False
Is Temporary: True/False
Force Change: True/False
Salt Length: [number]
```

**Troubleshooting:**
1. If `Is Temporary: False` - Database columns missing or not updated
2. If `Salt Length: 0` - Password recovery didn't create salt properly
3. If login fails entirely - Password hash verification issue

#### **Problem: Stuck in Password Change Loop**
**Check After Password Change:**
```sql
SELECT is_temporary, force_change, password_migrated 
FROM pn_pengguna WHERE id_pg = 'user_id';
```

**Should be:** `is_temporary=0`, `force_change=0`, `password_migrated=1`

**If not updated:** Check p0_PasswordChangeForced.aspx.vb SQL query

#### **Problem: Email Not Sent**
1. Check EmailServiceClient.vb integration
2. Verify SMTP settings in configuration
3. Check email address format in database

---

### **📊 TEST RESULTS CHECKLIST**

#### **Password Recovery Request:**
- [ ] ✅ Valid user with email gets success message
- [ ] ✅ User without email gets generic message  
- [ ] ✅ Invalid user gets generic message
- [ ] ✅ SQL injection attempts blocked
- [ ] ✅ Email sent with temporary password
- [ ] ✅ Database updated with temporary flags

#### **Login with Temporary Password:**
- [ ] ✅ Temporary password validates correctly
- [ ] ✅ System detects temporary password flags
- [ ] ✅ Redirects to password change page
- [ ] ✅ Session variables set correctly
- [ ] ✅ Debug logs show temporary password detection

#### **Password Change Process:**
- [ ] ✅ New password validated and encrypted
- [ ] ✅ Database updated with new password
- [ ] ✅ Temporary flags cleared (is_temporary=0, force_change=0)
- [ ] ✅ Success message displayed
- [ ] ✅ Debug logs show successful update

#### **Normal Login After Change:**
- [ ] ✅ New password authenticates correctly
- [ ] ✅ No temporary password flags detected
- [ ] ✅ Normal login flow (no forced redirect)
- [ ] ✅ Full system access granted

---

### **🔧 TROUBLESHOOTING COMMANDS**

#### **Check Database Schema:**
```sql
DESCRIBE pn_pengguna;
-- or
SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna';
```

#### **Reset User for Testing:**
```sql
UPDATE pn_pengguna 
SET is_temporary = 0, force_change = 0, password_migrated = 0, pwd = 'plain_password', salt = NULL
WHERE id_pg = 'test_user';
```

#### **Check Debug Logs:**
- Monitor Visual Studio Output window
- Check IIS logs
- Use browser developer tools for JavaScript errors

---

### **✅ SUCCESS CRITERIA**

**Test is SUCCESSFUL if:**
1. User can request password recovery
2. Temporary password received via email
3. Login with temporary password redirects to change page
4. Password change completes successfully  
5. Normal login works with new password
6. No infinite redirect loops
7. Debug logs show proper flag handling

**Test FAILS if:**
- User cannot login after recovery
- Stuck in password change loop
- Database flags not updated correctly
- SQL injection vulnerabilities exist
- Email service integration broken

---

**🎯 FINAL VERIFICATION:**
Complete workflow from password recovery request to normal system access should work seamlessly without manual database intervention.
