@echo off
echo ======================================================
echo SPMJ PN_AdminPasswordManager Fix Application
echo ======================================================
echo.

echo [1/4] Cleaning temporary files...
if exist "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\bin\SPMJ.dll" (
    del "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\bin\SPMJ.dll"
)
if exist "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\bin\SPMJ.pdb" (
    del "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\bin\SPMJ.pdb"
)

echo [2/4] Clearing ASP.NET temporary files...
if exist "%WINDOWS%\Microsoft.NET\Framework\v2.0.50727\Temporary ASP.NET Files\*" (
    for /d %%i in ("%WINDOWS%\Microsoft.NET\Framework\v2.0.50727\Temporary ASP.NET Files\*") do rmdir /s /q "%%i" 2>nul
)

echo [3/4] Building the project...
cd /d "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ"

REM Try to build using MSBuild if available
"%WINDIR%\Microsoft.NET\Framework\v3.5\MSBuild.exe" SPMJ.vbproj /p:Configuration=Debug /p:Platform=AnyCPU

echo [4/4] Checking build result...
if exist "bin\SPMJ.dll" (
    echo.
    echo ========================================
    echo SUCCESS: Build completed successfully!
    echo ========================================
    echo.
    echo The PN_AdminPasswordManager page should now work correctly.
    echo You can access it at: /PN_AdminPasswordManager.aspx
    echo.
) else (
    echo.
    echo ========================================
    echo WARNING: Build may have failed
    echo ========================================
    echo.
    echo Please check the build output above for errors.
    echo You may need to open the project in Visual Studio 2017 and rebuild manually.
    echo.
)

echo Press any key to continue...
pause > nul
