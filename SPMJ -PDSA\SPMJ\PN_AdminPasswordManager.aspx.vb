Option Strict On
Option Explicit On

Imports System
Imports System.Data
Imports System.Data.OleDb
Imports System.Configuration
Imports System.Web
Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports System.Security.Cryptography
Imports System.Text
Imports System.Net

''' <summary>
''' PN_AdminPasswordManager - Advanced Password Management System
''' .NET Framework 3.5.1 Compatible Implementation + .NET 9 Microservice Integration
''' Features: EXCLUSIVE SHA256+Salt encryption, .NET 9 Email microservice integration, Temporary passwords
''' Security: SHA256 + Salt ONLY - No fallback encryption methods
''' Email: Migrated to dedicated .NET 9 microservice for enhanced reliability and modern features
''' </summary>
Partial Public Class PN_AdminPasswordManager
    Inherits System.Web.UI.Page

#Region "Constants and Configuration"
    
    ' Security constants for .NET 3.5.1 - SHA256 + Salt ONLY Implementation
    Private Const SALT_LENGTH As Integer = 16
    Private Const TEMP_PASSWORD_LENGTH As Integer = 12
    Private Const REGULAR_PASSWORD_LENGTH As Integer = 8
    Private Const MIN_PASSWORD_LENGTH As Integer = 6
    Private Const MAX_LOGIN_ATTEMPTS As Integer = 5
    
    ' Encryption configuration - SHA256 + Salt EXCLUSIVE
    Private Const ENCRYPTION_METHOD As String = "SHA256+Salt"
    Private Const HASH_ALGORITHM As String = "SHA256"
    Private Const SALT_ENCODING As String = "Base64"
    Private Const HASH_OUTPUT_FORMAT As String = "HexLowercase"    ' Email microservice configuration - Updated for .NET 9 microservice with API Key authentication
    Private Const EMAIL_SERVICE_URL As String = "http://localhost:5000/api/admin/password"
    Private Const EMAIL_API_KEY As String = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
    
    ' Password character sets for .NET 3.5.1 compatibility
    Private Const TEMP_CHARS As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%"
    Private Const REGULAR_CHARS As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    
#End Region

#Region "Page-Level Variables"
    
    ' Current user being managed
    ''' <summary>
    ''' Current user being managed - stored in ViewState for postback persistence
    ''' </summary>
    Private Property m_currentUser As UserData
        Get
            If ViewState("CurrentUser") IsNot Nothing Then
                Return DirectCast(ViewState("CurrentUser"), UserData)
            End If
            Return Nothing
        End Get
        Set(ByVal value As UserData)
            ViewState("CurrentUser") = value
        End Set
    End Property
    
    ' .NET 3.5.1 compatible random number generator
    Private Shared m_random As New Random(DateTime.Now.Millisecond)
    
#End Region

#Region "Page Events"

    ''' <summary>
    ''' Page Load Event - .NET 3.5.1 Standard Implementation
    ''' </summary>
    ''' <param name="sender">Event sender</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try            ' Security validation - .NET 3.5.1 standard approach
            If Not ValidateUserSession() Then
                Session.Abandon()
                Response.Redirect("p0_Login.aspx", True)
                Return
            End If

            If Not ValidateAdminPrivileges() Then
                ShowMessage("Access Denied: Insufficient privileges for password management.", "error")
                Response.Redirect("default.aspx", True)
                Return
            End If

            ' Validate SHA256 encryption system on startup
            If Not Page.IsPostBack Then
                If Not ValidateSHA256Encryption() Then
                    ShowMessage("SECURITY ALERT: SHA256 encryption validation failed. System not secure.", "error")
                    LogError("Page_Load", "SHA256 encryption system validation failed - stopping execution")
                    Return
                End If
            End If

            ' Initialize page controls on first load
            If Not Page.IsPostBack Then
                InitializePageControls()
            End If

        Catch ex As Exception
            ' .NET 3.5.1 compatible error handling
            LogError("Page_Load", ex.Message)
            ShowMessage("System error during page initialization: " + ex.Message, "error")
        End Try
    End Sub

#End Region

#Region "Button Event Handlers"

    ''' <summary>
    ''' Search User Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub btnSearchUser_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSearchUser.Click
        Try
            ClearAllPanels()

            ' Input validation - .NET 3.5.1 standard
            Dim searchId As String = TrimSafeString(txtSearchUser.Text)
            If String.IsNullOrEmpty(searchId) Then
                ShowMessage("Please enter a User ID to search.", "error")
                txtSearchUser.Focus()
                Return
            End If

            ' Search for user in database
            Dim userData As UserData = SearchUserInDatabase(searchId)
            If userData IsNot Nothing Then
                m_currentUser = userData
                DisplayUserInformation(userData)
                ShowPasswordActions()

                ' Pre-populate email if available
                If Not String.IsNullOrEmpty(userData.Email) Then
                    txtUserEmail.Text = userData.Email
                End If

                ShowMessage("User found successfully. Choose password management action below.", "success")
            Else
                ShowMessage("User with ID '" + searchId + "' not found in the system.", "error")
            End If

        Catch ex As Exception
            LogError("btnSearchUser_Click", ex.Message)
            ShowMessage("Error searching for user: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Set Password Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>       

    Protected Sub btnSetPassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSetPassword.Click
        Try
            LogError("btnSetPassword_Click", "Password set button clicked - starting process")

            If Not EnsureCurrentUserAvailable("btnSetPassword_Click") Then Return

            ' Validate email if email sending is enabled
            If chkSendEmail.Checked Then
                Dim emailText As String = TrimSafeString(txtUserEmail.Text)
                If Not IsValidEmailFormatWithMicroservice(emailText) Then
                    ShowMessage("Please enter a valid email address to send password notification.", "error")
                    txtUserEmail.Focus()
                    Return
                End If
            End If

            ' Determine password type and generate
            Dim newPassword As String = ""
            Dim isTemporary As Boolean = rbTempPassword.Checked
            Dim customPassword As String = TrimSafeString(txtCustomPassword.Text)

            If Not String.IsNullOrEmpty(customPassword) Then
                If customPassword.Length < MIN_PASSWORD_LENGTH Then
                    ShowMessage("Custom password must be at least " + MIN_PASSWORD_LENGTH.ToString() + " characters long.", "error")
                    txtCustomPassword.Focus()
                    Return
                End If
                newPassword = customPassword
            Else
                If isTemporary Then
                    newPassword = GenerateTemporaryPassword()
                Else
                    newPassword = GenerateSecurePassword()
                End If
            End If            ' Update password in database with SHA256+Salt
            LogError("btnSetPassword_Click", "Attempting to update password for user: " + m_currentUser.UserId + " with password length: " + newPassword.Length.ToString())

            If UpdatePasswordWithCorrectStructure(m_currentUser.UserId, newPassword, isTemporary, False) Then
                LogError("btnSetPassword_Click", "Password update successful with correct structure")
                DisplayPasswordResult(newPassword, isTemporary)

                ' Send email if requested
                If chkSendEmail.Checked Then
                    Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                    If Not String.IsNullOrEmpty(emailAddress) Then
                        SendPasswordNotificationEmail(emailAddress, newPassword, isTemporary, False)
                    End If
                End If

                ClearPasswordInputs()
                LogPasswordActivity(m_currentUser.UserId, "Password Set", isTemporary)
                ShowMessage("Password has been set successfully.", "success")
            Else
                LogError("btnSetPassword_Click", "Password update failed")
                ShowMessage("Failed to set password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnSetPassword_Click", ex.Message)
            ShowMessage("Error setting password: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Force Reset Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>    

    Protected Sub btnForceReset_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnForceReset.Click
        Try
            LogError("btnForceReset_Click", "Force Reset button clicked - checking current user")

            If Not EnsureCurrentUserAvailable("btnForceReset_Click") Then Return            ' Generate temporary password for forced reset
            Dim tempPassword As String = GenerateTemporaryPassword()
            LogError("btnForceReset_Click", "Generated temporary password length: " + tempPassword.Length.ToString())
            
            If String.IsNullOrEmpty(tempPassword) Then
                LogError("btnForceReset_Click", "Generated password is null or empty!")
                ShowMessage("Failed to generate temporary password. Please try again.", "error")
                Return
            End If            ' Update password and force change on next login
            LogError("btnForceReset_Click", "Calling UpdatePasswordWithCorrectStructure for user: " + m_currentUser.UserId)

            If UpdatePasswordWithCorrectStructure(m_currentUser.UserId, tempPassword, True, True) Then
                LogError("btnForceReset_Click", "Password update successful with correct table structure")
                DisplayPasswordResult(tempPassword, True)

                ' Send email notification
                If chkSendEmail.Checked Then
                    Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                    If IsValidEmailFormat(emailAddress) Then
                        SendPasswordNotificationEmail(emailAddress, tempPassword, True, True)
                    End If
                End If

                ClearPasswordInputs()
                LogPasswordActivity(m_currentUser.UserId, "Force Reset", True)
                ShowMessage("Password forcibly reset. User must change password on next login.", "warning")
            Else
                LogError("btnForceReset_Click", "UpdatePasswordWithCorrectStructure returned False - trying legacy method")
                
                ' Try the old UpdateUserPasswordSecure as fallback
                If UpdateUserPasswordSecure(m_currentUser.UserId, tempPassword, True, True) Then
                    LogError("btnForceReset_Click", "Legacy password update successful")
                    DisplayPasswordResult(tempPassword, True)
                    
                    ' Send email notification
                    If chkSendEmail.Checked Then
                        Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                        If IsValidEmailFormat(emailAddress) Then
                            SendPasswordNotificationEmail(emailAddress, tempPassword, True, True)
                        End If
                    End If
                    
                    ClearPasswordInputs()
                    LogPasswordActivity(m_currentUser.UserId, "Force Reset (Legacy)", True)
                    ShowMessage("Password forcibly reset. User must change password on next login.", "warning")
                    
                ElseIf EmergencyPasswordReset(m_currentUser.UserId, tempPassword) Then
                    LogError("btnForceReset_Click", "Emergency password reset successful")
                    DisplayPasswordResult(tempPassword, True)
                    
                    ' Send email notification
                    If chkSendEmail.Checked Then
                        Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                        If IsValidEmailFormat(emailAddress) Then
                            SendPasswordNotificationEmail(emailAddress, tempPassword, True, True)
                        End If
                    End If
                    
                    ClearPasswordInputs()
                    LogPasswordActivity(m_currentUser.UserId, "Emergency Reset", True)
                    ShowMessage("Password forcibly reset using emergency method. User must change password on next login.", "warning")
                Else
                    LogError("btnForceReset_Click", "All password reset methods failed")
                    
                    ' Enhanced diagnostic before final error
                    LogError("btnForceReset_Click", "COMPREHENSIVE FAILURE ANALYSIS:")
                    LogError("btnForceReset_Click", "- UpdatePasswordWithCorrectStructure: FAILED")
                    LogError("btnForceReset_Click", "- UpdateUserPasswordSecure (legacy): FAILED")  
                    LogError("btnForceReset_Click", "- EmergencyPasswordReset: FAILED")
                    LogError("btnForceReset_Click", "- User ID: '" + m_currentUser.UserId + "'")
                    LogError("btnForceReset_Click", "- User Name: '" + m_currentUser.Name + "'")
                    
                    ' Run connectivity test to help diagnose
                    If TestDatabaseConnectivity(m_currentUser.UserId) Then
                        LogError("btnForceReset_Click", "Database connectivity test PASSED - User exists but updates fail")
                        ShowMessage("Database connectivity OK but password update failed. Check application logs for details.", "error")
                    Else
                        LogError("btnForceReset_Click", "Database connectivity test FAILED - User not found or DB unavailable")
                        ShowMessage("Database connectivity issue detected. User may not exist or database unavailable.", "error")
                    End If
                End If
            End If

        Catch ex As Exception
            LogError("btnForceReset_Click", ex.Message)
            ShowMessage("Error forcing password reset: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Generate Temporary Password Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>    
    Protected Sub btnGenerateTemp_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGenerateTemp.Click
        Try
            If Not EnsureCurrentUserAvailable("btnGenerateTemp_Click") Then Return

            ' Generate temporary password for first-time users
            Dim tempPassword As String = GenerateTemporaryPassword()            ' Update password as temporary
            If UpdatePasswordWithCorrectStructure(m_currentUser.UserId, tempPassword, True, False) Then
                DisplayPasswordResult(tempPassword, True)

                ' Send welcome email for first-time users
                If chkSendEmail.Checked Then
                    Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                    If IsValidEmailFormat(emailAddress) Then
                        SendWelcomeEmail(emailAddress, tempPassword)
                    End If
                End If

                ClearPasswordInputs()
                LogPasswordActivity(m_currentUser.UserId, "Temporary Generated", True)
                ShowMessage("Temporary password generated for first-time user.", "info")
            Else
                ShowMessage("Failed to generate temporary password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnGenerateTemp_Click", ex.Message)
            ShowMessage("Error generating temporary password: " + ex.Message, "error")
        End Try
    End Sub

#End Region

#Region "Database Operations"

    ''' <summary>
    ''' Search user in database - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="userId">User ID to search for</param>
    ''' <returns>UserData object if found, Nothing if not found</returns>
    Private Function SearchUserInDatabase(ByVal userId As String) As UserData
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()
            command = New OleDbCommand()
            command.Connection = connection
            ' Enhanced search: search by ID (exact match) or by name (partial match)
            ' Removed non-existent date fields: tarikh_daftar, tarikh_login_akhir
            command.CommandText = "SELECT Id_PG, NAMA, email, STATUS, MODUL, AKSES FROM pn_pengguna WHERE Id_PG = ? OR NAMA LIKE ?"
            command.Parameters.AddWithValue("@id_pg", userId)
            command.Parameters.AddWithValue("@nama", "%" + userId + "%")

            reader = command.ExecuteReader()

            If reader.Read() Then
                Dim userData As New UserData()
                userData.UserId = GetSafeStringValue(reader, "id_pg")
                userData.Name = GetSafeStringValue(reader, "nama")
                userData.Email = GetSafeStringValue(reader, "email")
                userData.Status = GetSafeStringValue(reader, "status")
                userData.UserModule = GetSafeStringValue(reader, "modul")
                userData.Access = GetSafeStringValue(reader, "akses")
                ' Set default values for date fields since they don't exist in the table
                userData.RegisterDate = "Not available"
                userData.LastLogin = "Not available"
                Return userData
            End If

            Return Nothing

        Catch ex As Exception
            LogError("SearchUserInDatabase", ex.Message)
            Return Nothing
        Finally
            If reader IsNot Nothing Then reader.Close()
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then 
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Update user password with SHA256+Salt encryption - .NET 3.5.1 Compatible
    ''' </summary>    ''' <summary>
    ''' Update user password with SHA256+Salt encryption - EXCLUSIVE SHA256 Implementation
    ''' .NET 3.5.1 Compatible - SHA256 + Salt ONLY, No Fallbacks
    ''' </summary>
    ''' <param name="userId">User ID to update</param>
    ''' <param name="newPassword">New password in plain text</param>
    ''' <param name="isTemporary">Whether this is a temporary password</param>
    ''' <param name="forceChange">Whether to force change on next login</param>
    ''' <returns>True if successful, False if failed</returns>
    ''' <remarks>
    ''' This method uses ONLY SHA256 + Salt encryption.
    ''' Each password gets a unique cryptographically secure salt.
    ''' No fallback encryption methods are available.
    ''' </remarks>
    Private Function UpdateUserPasswordSecure(ByVal userId As String, ByVal newPassword As String, ByVal isTemporary As Boolean, ByVal forceChange As Boolean) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing

        Try
            ' Validate inputs
            If String.IsNullOrEmpty(userId) Then
                LogError("UpdateUserPasswordSecure", "User ID cannot be null or empty")
                Return False
            End If

            If String.IsNullOrEmpty(newPassword) Then
                LogError("UpdateUserPasswordSecure", "Password cannot be null or empty")
                Return False
            End If

            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            LogError("UpdateUserPasswordSecure", "Attempting database connection...")
            connection.Open()
            LogError("UpdateUserPasswordSecure", "Database connection successful")

            ' Generate unique cryptographically secure salt and hash password
            Dim salt As String = ""
            Dim hashedPassword As String = GeneratePasswordHashWithNewSalt(newPassword, salt)
            
            LogError("UpdateUserPasswordSecure", "Generated hash length: " + hashedPassword.Length.ToString() + ", Salt length: " + salt.Length.ToString())
            
            command = New OleDbCommand()
            command.Connection = connection            ' Check if enhanced columns exist, if not use basic update
            ' Try enhanced update first, fallback to basic update if columns don't exist
            LogError("UpdateUserPasswordSecure", "Attempting database update for user: " + userId)
              ' First verify the user exists
            Dim verifyCommand As New OleDbCommand("SELECT COUNT(*) FROM pn_pengguna WHERE Id_PG = ?", connection)
            verifyCommand.Parameters.AddWithValue("@id_pg", userId.Trim())
            Dim userCount As Integer = Convert.ToInt32(verifyCommand.ExecuteScalar())
            LogError("UpdateUserPasswordSecure", "User verification - Found " + userCount.ToString() + " records for user: '" + userId + "'")
            
            ' Also check with different parameter approaches
            If userCount = 0 Then
                LogError("UpdateUserPasswordSecure", "Trying alternative parameter binding for user verification")
                verifyCommand.Parameters.Clear()
                verifyCommand.CommandText = "SELECT COUNT(*) FROM pn_pengguna WHERE Id_PG = '" + userId.Replace("'", "''") + "'"
                userCount = Convert.ToInt32(verifyCommand.ExecuteScalar())
                LogError("UpdateUserPasswordSecure", "Alternative verification - Found " + userCount.ToString() + " records for user: '" + userId + "'")
            End If

            If userCount = 0 Then
                LogError("UpdateUserPasswordSecure", "User not found in database: '" + userId + "' - checking for exact matches")

                ' Debug: Show all users in database for comparison
                verifyCommand.CommandText = "SELECT TOP 5 Id_PG FROM pn_pengguna"
                verifyCommand.Parameters.Clear()
                Dim debugReader As OleDbDataReader = verifyCommand.ExecuteReader()
                Dim existingUsers As String = ""
                While debugReader.Read()
                    existingUsers += "'" + debugReader("id_pg").ToString() + "', "
                End While
                debugReader.Close()
                LogError("UpdateUserPasswordSecure", "Sample users in database: " + existingUsers)

                Return False
            End If

            Try
                ' Enhanced update with all security fields
                Dim sql As String = "UPDATE pn_pengguna SET PWD = ?, salt = ?, is_temporary = ?, force_change = ?, tarikh_tukar_katalaluan = ?, last_changed_by = ? WHERE Id_PG = ?"
                command.CommandText = sql

                LogError("UpdateUserPasswordSecure", "Attempting enhanced update with SQL: " + sql)
                LogError("UpdateUserPasswordSecure", "Parameters - User: " + userId + ", Hash length: " + hashedPassword.Length.ToString() + ", Salt: " + salt.Substring(0, Math.Min(4, salt.Length)) + "...")

                command.Parameters.AddWithValue("@password", hashedPassword)
                command.Parameters.AddWithValue("@salt", salt)

                ' Safe boolean to integer conversion - .NET 3.5.1 compatible
                Dim temporaryFlag As Integer = 0
                If isTemporary Then temporaryFlag = 1
                command.Parameters.AddWithValue("@is_temporary", temporaryFlag)

                Dim forceChangeFlag As Integer = 0
                If forceChange Then forceChangeFlag = 1
                command.Parameters.AddWithValue("@force_change", forceChangeFlag)
                command.Parameters.AddWithValue("@date_changed", DateTime.Now)

                ' Safe session handling for admin ID
                Dim adminId As String = "SYSTEM"
                If Session("Id_PG") IsNot Nothing Then
                    adminId = Session("Id_PG").ToString()
                End If

                command.Parameters.AddWithValue("@changed_by", adminId)
                command.Parameters.AddWithValue("@id_pg", userId.Trim())

                LogError("UpdateUserPasswordSecure", "Executing enhanced update query with " + command.Parameters.Count.ToString() + " parameters...")
                LogError("UpdateUserPasswordSecure", "Target user ID: '" + userId.Trim() + "'")

                Dim rowsAffected As Integer = command.ExecuteNonQuery()
                LogError("UpdateUserPasswordSecure", "Enhanced update completed. Rows affected: " + rowsAffected.ToString())

                ' Log successful password update with SHA256 encryption
                If rowsAffected > 0 Then
                    LogError("UpdateUserPasswordSecure", "Enhanced password update successful for user: " + userId)
                    Return True
                Else
                    LogError("UpdateUserPasswordSecure", "Enhanced password update returned 0 rows affected for user: " + userId)
                    Return False
                End If
            Catch columnEx As Exception
                ' If enhanced columns don't exist, fallback to basic update
                LogError("UpdateUserPasswordSecure", "Enhanced update failed: " + columnEx.Message)
                LogError("UpdateUserPasswordSecure", "Attempting fallback to basic update...")
                
                ' Clear previous parameters and use basic update
                command.Parameters.Clear()
                
                ' Try with salt column first
                Try
                    command.CommandText = "UPDATE pn_pengguna SET PWD = ?, salt = ? WHERE Id_PG = ?"
                    LogError("UpdateUserPasswordSecure", "Trying basic update with salt...")
                    command.Parameters.AddWithValue("@password", hashedPassword)
                    command.Parameters.AddWithValue("@salt", salt)
                    command.Parameters.AddWithValue("@id_pg", userId.Trim())
                    
                    Dim rowsAffected As Integer = command.ExecuteNonQuery()
                    LogError("UpdateUserPasswordSecure", "Basic salt update completed. Rows affected: " + rowsAffected.ToString())
                    
                    If rowsAffected > 0 Then
                        LogError("UpdateUserPasswordSecure", "Basic password+salt update successful for user: " + userId)
                        Return True
                    Else
                        LogError("UpdateUserPasswordSecure", "Basic password+salt update returned 0 rows affected for user: " + userId)
                        Return False
                    End If
                      Catch saltEx As Exception
                    ' If salt column doesn't exist, use only password
                    LogError("UpdateUserPasswordSecure", "Salt column update failed: " + saltEx.Message)
                    LogError("UpdateUserPasswordSecure", "Attempting password-only update...")
                      command.Parameters.Clear()
                    command.CommandText = "UPDATE pn_pengguna SET PWD = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@password", hashedPassword)
                    command.Parameters.AddWithValue("@id_pg", userId.Trim())
                    
                    Dim rowsAffected As Integer = command.ExecuteNonQuery()
                    LogError("UpdateUserPasswordSecure", "Password-only update completed. Rows affected: " + rowsAffected.ToString())
                    
                    If rowsAffected > 0 Then
                        LogError("UpdateUserPasswordSecure", "Password-only update successful for user: " + userId)
                        Return True
                    Else
                        LogError("UpdateUserPasswordSecure", "Password-only update returned 0 rows affected for user: " + userId)
                        Return False
                    End If
                End Try
            End Try

        Catch ex As Exception
            LogError("UpdateUserPasswordSecure", "SHA256 password update failed: " + ex.Message)
            Return False
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then 
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

#End Region

#Region "Security and Encryption - SHA256 + Salt Only"    ''' <summary>
    ''' Generate cryptographically secure salt using RNGCryptoServiceProvider
    ''' .NET 3.5.1 Compatible - SHA256 + Salt ONLY implementation
    ''' </summary>
    ''' <returns>Base64 encoded secure salt string</returns>
    Private Function GenerateSecureSalt() As String
        ' Use RNGCryptoServiceProvider for cryptographically secure random bytes
        ' Note: In .NET 3.5.1, RNGCryptoServiceProvider doesn't implement IDisposable
        Dim saltBytes(SALT_LENGTH - 1) As Byte
        Dim rng As New RNGCryptoServiceProvider()
        Try
            rng.GetBytes(saltBytes)
        Finally
            ' Manually dispose of the RNG provider in .NET 3.5.1
            If rng IsNot Nothing Then
                rng = Nothing
            End If
        End Try
        Return Convert.ToBase64String(saltBytes)
    End Function

    ''' <summary>
    ''' Hash password using SHA256 with salt - EXCLUSIVE SHA256 Implementation
    ''' .NET 3.5.1 Compatible - No fallback methods, SHA256 + Salt ONLY
    ''' </summary>
    ''' <param name="password">Plain text password to hash</param>
    ''' <param name="salt">Cryptographically secure salt</param>
    ''' <returns>SHA256 hash as lowercase hex string</returns>
    ''' <remarks>
    ''' This method implements ONLY SHA256 + Salt encryption.
    ''' No fallback methods are provided - SHA256 is mandatory.
    ''' Salt is appended to password before hashing for maximum security.
    ''' </remarks>
    Private Function HashPasswordWithSalt(ByVal password As String, ByVal salt As String) As String
        ' Validate inputs
        If String.IsNullOrEmpty(password) Then
            Throw New ArgumentException("Password cannot be null or empty", "password")
        End If
        If String.IsNullOrEmpty(salt) Then
            Throw New ArgumentException("Salt cannot be null or empty", "salt")
        End If

        ' Combine password and salt (salt appended for security)
        Dim saltedPassword As String = password + salt
        
        ' Create SHA256 hash - .NET 3.5.1 compatible, no fallbacks
        Using sha256 As SHA256 = SHA256.Create()
            Dim inputBytes As Byte() = Encoding.UTF8.GetBytes(saltedPassword)
            Dim hashBytes As Byte() = sha256.ComputeHash(inputBytes)
            
            ' Convert to lowercase hex string for consistency
            Dim hashString As New StringBuilder(hashBytes.Length * 2)
            For Each b As Byte In hashBytes
                hashString.Append(b.ToString("x2"))
            Next
            
            Return hashString.ToString()
        End Using
    End Function

    ''' <summary>
    ''' Verify password against stored hash and salt - SHA256 + Salt ONLY
    ''' </summary>
    ''' <param name="plainTextPassword">Password to verify</param>
    ''' <param name="storedHash">Stored SHA256 hash from database</param>
    ''' <param name="storedSalt">Stored salt from database</param>
    ''' <returns>True if password matches, False otherwise</returns>
    Private Function VerifyPasswordHash(ByVal plainTextPassword As String, ByVal storedHash As String, ByVal storedSalt As String) As Boolean
        Try
            ' Generate hash from provided password and stored salt
            Dim computedHash As String = HashPasswordWithSalt(plainTextPassword, storedSalt)
            
            ' Perform secure string comparison (case-insensitive for hash)
            Return String.Equals(computedHash, storedHash, StringComparison.OrdinalIgnoreCase)
        Catch ex As Exception
            LogError("VerifyPasswordHash", ex.Message)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Generate complete password hash with new salt - SHA256 + Salt ONLY
    ''' </summary>
    ''' <param name="password">Plain text password</param>
    ''' <param name="generatedSalt">Output parameter for the generated salt</param>
    ''' <returns>SHA256 hash using new salt</returns>
    Private Function GeneratePasswordHashWithNewSalt(ByVal password As String, ByRef generatedSalt As String) As String
        ' Generate new cryptographically secure salt
        generatedSalt = GenerateSecureSalt()
        
        ' Hash password with new salt
        Return HashPasswordWithSalt(password, generatedSalt)
    End Function

    ''' <summary>
    ''' Generate secure temporary password - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>Temporary password string</returns>
    Private Function GenerateTemporaryPassword() As String
        Dim password As New StringBuilder(TEMP_PASSWORD_LENGTH)

        ' Ensure complexity: uppercase, number, special character
        password.Append(TEMP_CHARS.Substring(m_random.Next(26), 1)) ' Uppercase
        password.Append(TEMP_CHARS.Substring(m_random.Next(26, 36), 1)) ' Number
        password.Append(TEMP_CHARS.Substring(m_random.Next(36, 40), 1)) ' Special

        ' Fill remaining with random characters
        For i As Integer = 3 To TEMP_PASSWORD_LENGTH - 1
            password.Append(TEMP_CHARS.Substring(m_random.Next(TEMP_CHARS.Length), 1))
        Next

        ' Shuffle the password for .NET 3.5.1 compatibility
        Return ShuffleString(password.ToString())
    End Function

    ''' <summary>
    ''' Generate regular secure password - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>Regular password string</returns>
    Private Function GenerateSecurePassword() As String
        Dim password As New StringBuilder(REGULAR_PASSWORD_LENGTH)

        For i As Integer = 0 To REGULAR_PASSWORD_LENGTH - 1
            password.Append(REGULAR_CHARS.Substring(m_random.Next(REGULAR_CHARS.Length), 1))
        Next

        Return password.ToString()
    End Function

    ''' <summary>
    ''' Shuffle string characters - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="input">Input string to shuffle</param>
    ''' <returns>Shuffled string</returns>
    Private Function ShuffleString(ByVal input As String) As String
        Dim chars As Char() = input.ToCharArray()
        
        For i As Integer = chars.Length - 1 To 1 Step -1
            Dim j As Integer = m_random.Next(i + 1)
            Dim temp As Char = chars(i)
            chars(i) = chars(j)
            chars(j) = temp
        Next
        
        Return New String(chars)
    End Function

#End Region

#Region "Authentication and Authorization"

    ''' <summary>
    ''' Validate user session - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>True if valid session, False otherwise</returns>
    Private Function ValidateUserSession() As Boolean
        Try
            Dim sessionId As Object = Session("Id_PG")
            If sessionId Is Nothing Then Return False
            
            Dim userId As String = sessionId.ToString().Trim()
            Return Not String.IsNullOrEmpty(userId)
        Catch
            Return False
        End Try
    End Function    ''' <summary>
    ''' Validate admin privileges - .NET 3.5.1 Compatible
    ''' Enhanced with session-based validation for improved reliability

    ''' <returns>True if user has admin privileges, False otherwise</returns>
    Private Function ValidateAdminPrivileges() As Boolean
        Try
            ' First check session variables for quick validation
            If Session("AKSES") IsNot Nothing AndAlso Session("MODUL") IsNot Nothing Then
                Dim userAccess As String = Session("AKSES").ToString().ToLower()
                Dim userModule As String = Session("MODUL").ToString()
                
                ' Check for admin privileges using session data
                If userAccess.Contains("4") OrElse userAccess.Contains("admin") OrElse userModule = "111111" Then
                    Return True
                End If
            End If
            
            ' Fallback to database validation if session check fails
            Dim connection As OleDbConnection = Nothing
            Dim command As OleDbCommand = Nothing
            Dim reader As OleDbDataReader = Nothing

            Try
                connection = New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()

                command = New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT AKSES, MODUL FROM pn_pengguna WHERE Id_PG = ? AND STATUS = '1'"
                command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())

                reader = command.ExecuteReader()
                If reader.Read() Then
                    Dim access As String = GetSafeStringValue(reader, "akses").ToLower()
                    Dim modul As String = GetSafeStringValue(reader, "modul").ToLower()
                    
                    ' Check for admin privileges - enhanced logic
                    Return access.Contains("4") OrElse access.Contains("admin") OrElse _
                           modul.Contains("111111") OrElse modul.Contains("admin")
                End If

                Return False

            Finally
                If reader IsNot Nothing Then reader.Close()
                If command IsNot Nothing Then command.Dispose()
                If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                    connection.Close()
                End If
            End Try

        Catch ex As Exception
            LogError("ValidateAdminPrivileges", ex.Message)
            Return False
        End Try
    End Function

#End Region

#Region "Email Integration"

    ''' <summary>
    ''' Send password notification email - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="emailAddress">Recipient email address</param>
    ''' <param name="password">Password to send</param>
    ''' <param name="isTemporary">Whether password is temporary</param>
    ''' <param name="isForceReset">Whether this is a force reset</param>    
    Private Sub SendPasswordNotificationEmail(ByVal emailAddress As String, ByVal password As String, ByVal isTemporary As Boolean, ByVal isForceReset As Boolean)
        Try
            ' Determine template type and subject
            Dim templateType As String
            Dim subject As String

            If isForceReset Then
                templateType = "force_reset"
                subject = "Password Reset - SPMJ System (Action Required)"
            ElseIf isTemporary Then
                templateType = "password_temporary"
                subject = "Password Reset - SPMJ System"
            Else
                templateType = "password_permanent"
                subject = "Password Update - SPMJ System"
            End If

            ' Get admin information
            Dim adminId As String = If(Session("Id_PG") IsNot Nothing, Session("Id_PG").ToString(), "SYSTEM")
            Dim adminName As String = If(Session("NAMA") IsNot Nothing, Session("NAMA").ToString(), "System Administrator")

            ' Prepare email data for .NET 9 microservice - .NET 3.5.1 compatible JSON
            Dim emailData As String = "{"
            emailData += """to"":""" + emailAddress + ""","
            emailData += """subject"":""" + subject + ""","
            emailData += """templateType"":""" + templateType + ""","
            emailData += """data"":{"
            emailData += """userName"":""" + m_currentUser.Name.Replace("""", "\""") + ""","
            emailData += """userId"":""" + m_currentUser.UserId + ""","
            emailData += """password"":""" + password + ""","

            Dim isTemporaryString As String
            If isTemporary Then
                isTemporaryString = "true"
            Else
                isTemporaryString = "false"
            End If
            emailData += """isTemporary"":" + isTemporaryString + ","
            emailData += """systemUrl"":""" + Request.Url.GetLeftPart(UriPartial.Authority) + ""","
            emailData += """timestamp"":""" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + ""","
            emailData += """adminId"":""" + adminId + ""","
            emailData += """adminName"":""" + adminName.Replace("""", "\""") + """"
            emailData += "}"
            emailData += "}"            ' Call .NET 9 email microservice with API Key authentication - .NET 3.5.1 compatible
            Using client As New WebClient()
                client.Headers("Content-Type") = "application/json"
                client.Headers("X-API-Key") = EMAIL_API_KEY

                Dim endpoint As String
                If isForceReset Then
                    endpoint = "/send-force-reset"
                Else
                    endpoint = "/send-notification"
                End If

                Dim response As String = client.UploadString(EMAIL_SERVICE_URL + endpoint, emailData)

                ' Show email status
                ShowEmailStatus(True, "Password email sent successfully to " + emailAddress)
                LogError("SendPasswordNotificationEmail", "Email sent successfully via .NET 9 microservice to " + emailAddress)
            End Using

        Catch ex As Exception
            LogError("SendPasswordNotificationEmail", ".NET 9 microservice error: " + ex.Message)
            ' Show email failure but don't stop the process
            ShowEmailStatus(False, "Failed to send email via microservice: " + ex.Message)
        End Try
    End Sub
    ''' <summary>
    ''' Send welcome email for first-time users via .NET 9 microservice - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="emailAddress">Recipient email address</param>
    ''' <param name="tempPassword">Temporary password</param>
    Private Sub SendWelcomeEmail(ByVal emailAddress As String, ByVal tempPassword As String)
        Try
            ' Prepare welcome email data for .NET 9 microservice - .NET 3.5.1 compatible JSON
            Dim emailData As String = "{"
            emailData += """to"":""" + emailAddress + ""","
            emailData += """subject"":""Welcome to SPMJ System - Account Created"","
            emailData += """templateType"":""welcome_new_user"","
            emailData += """data"":{"
            emailData += """userName"":""" + m_currentUser.Name.Replace("""", "\""") + ""","
            emailData += """userId"":""" + m_currentUser.UserId + ""","
            emailData += """tempPassword"":""" + tempPassword + ""","
            emailData += """systemUrl"":""" + Request.Url.GetLeftPart(UriPartial.Authority) + ""","
            emailData += """supportEmail"":""<EMAIL>"""
            emailData += "}"
            emailData += "}"            ' Call .NET 9 email microservice with API Key authentication
            Using client As New WebClient()
                client.Headers("Content-Type") = "application/json"
                client.Headers("X-API-Key") = EMAIL_API_KEY
                
                Dim response As String = client.UploadString(EMAIL_SERVICE_URL + "/send-welcome", emailData)
                
                ' Show email status
                ShowEmailStatus(True, "Welcome email sent successfully to " + emailAddress)
                LogError("SendWelcomeEmail", "Welcome email sent successfully via .NET 9 microservice to " + emailAddress)
            End Using

        Catch ex As Exception
            LogError("SendWelcomeEmail", ".NET 9 microservice error: " + ex.Message)
            ShowEmailStatus(False, "Failed to send welcome email via microservice: " + ex.Message)
        End Try
    End Sub

#End Region

#Region "Validation and Utility Methods"

    ''' <summary>
    ''' Validate email format - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="email">Email address to validate</param>
    ''' <returns>True if valid format, False otherwise</returns>
    Private Function IsValidEmailFormat(ByVal email As String) As Boolean
        Try
            If String.IsNullOrEmpty(email) Then Return False
            email = email.Trim()
            
            ' Basic email validation for .NET 3.5.1
            Dim atIndex As Integer = email.IndexOf("@")
            Dim dotIndex As Integer = email.LastIndexOf(".")
            
            Return email.Length >= 5 AndAlso _
                   atIndex > 0 AndAlso _
                   dotIndex > atIndex AndAlso _
                   dotIndex < email.Length - 1
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Safely trim string - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="input">Input string</param>
    ''' <returns>Trimmed string or empty string if null</returns>
    Private Function TrimSafeString(ByVal input As String) As String
        If input Is Nothing Then Return ""
        Return input.Trim()
    End Function

    ''' <summary>
    ''' Get safe string value from database reader - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="reader">Database reader</param>
    ''' <param name="columnName">Column name to read</param>
    ''' <returns>String value or empty string if null/DBNull</returns>
    Private Function GetSafeStringValue(ByVal reader As OleDbDataReader, ByVal columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return ""
            Else
                Return reader(columnName).ToString()
            End If
        Catch
            Return ""
        End Try
    End Function

    ''' <summary>
    ''' Get safe date value from database reader - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="reader">Database reader</param>
    ''' <param name="columnName">Column name to read</param>
    ''' <returns>Formatted date string or "Not available" if null/DBNull</returns>
    Private Function GetSafeDateValue(ByVal reader As OleDbDataReader, ByVal columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return "Not available"
            Else
                Dim dateValue As DateTime = CDate(reader(columnName))
                Return dateValue.ToString("dd/MM/yyyy")
            End If
        Catch
            Return "Not available"
        End Try
    End Function

#End Region

#Region "UI Management Methods"

    ''' <summary>
    ''' Initialize page controls - .NET 3.5.1 Compatible
    ''' </summary>
    Private Sub InitializePageControls()
        Try
            ClearAllPanels()
            ShowMessage("Admin Password Manager initialized. Search for a user to begin password management.", "info")
            txtSearchUser.Focus()
        Catch ex As Exception
            LogError("InitializePageControls", ex.Message)
            ShowMessage("Error initializing page controls: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Display user information - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="userData">User data to display</param>    
    Private Sub DisplayUserInformation(ByVal userData As UserData)
        Try
            lblUserId.Text = userData.UserId

            ' Safe string assignment with null checking - .NET 3.5.1 compatible
            If String.IsNullOrEmpty(userData.Name) Then
                lblUserName.Text = "No name available"
            Else
                lblUserName.Text = userData.Name
            End If

            If String.IsNullOrEmpty(userData.Email) Then
                lblUserEmail.Text = "No email available"
            Else
                lblUserEmail.Text = userData.Email
            End If

            If String.IsNullOrEmpty(userData.UserModule) Then
                lblUserModule.Text = "Not assigned"
            Else
                lblUserModule.Text = userData.UserModule
            End If

            If String.IsNullOrEmpty(userData.Access) Then
                lblUserAccess.Text = "Standard"
            Else
                lblUserAccess.Text = userData.Access
            End If

            lblLastLogin.Text = userData.LastLogin
            lblPasswordDate.Text = userData.RegisterDate

            ' Format status with color coding
            Select Case userData.Status
                Case "1"
                    lblUserStatus.Text = "✅ Active"
                Case "0"
                    lblUserStatus.Text = "❌ Inactive"
                Case Else
                    lblUserStatus.Text = "❓ Unknown"
            End Select

            pnlUserInfo.Visible = True
        Catch ex As Exception
            LogError("DisplayUserInformation", ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Show password management actions panel
    ''' </summary>
    Private Sub ShowPasswordActions()
        pnlPasswordActions.Visible = True
    End Sub

    ''' <summary>
    ''' Display password generation result - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="password">Generated password</param>
    ''' <param name="isTemporary">Whether password is temporary</param>    
    Private Sub DisplayPasswordResult(ByVal password As String, ByVal isTemporary As Boolean)
        lblGeneratedPassword.Text = password

        ' Safe string assignment for password type - .NET 3.5.1 compatible
        If isTemporary Then
            lblPasswordType.Text = "Temporary (Must change on first login)"
        Else
            lblPasswordType.Text = "Permanent"
        End If

        pnlPasswordResult.Visible = True
    End Sub

    ''' <summary>
    ''' Clear all panels for new search
    ''' </summary>
    Private Sub ClearAllPanels()
        pnlUserInfo.Visible = False
        pnlPasswordActions.Visible = False
        pnlPasswordResult.Visible = False
        pnlEmailStatus.Visible = False
    End Sub

    ''' <summary>
    ''' Clear password input fields
    ''' </summary>
    Private Sub ClearPasswordInputs()
        txtCustomPassword.Text = ""
    End Sub

    ''' <summary>
    ''' Show message with styling - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="message">Message to display</param>
    ''' <param name="messageType">Type of message (success, error, info, warning)</param>
    Private Sub ShowMessage(ByVal message As String, ByVal messageType As String)
        lblMessage.Text = message
        pnlMessage.Visible = True

        ' Set CSS class based on message type
        Select Case messageType.ToLower()
            Case "success"
                divMessage.Attributes("class") = "message-panel msg-success"
            Case "error"
                divMessage.Attributes("class") = "message-panel msg-error"
            Case "info"
                divMessage.Attributes("class") = "message-panel msg-info"
            Case "warning"
                divMessage.Attributes("class") = "message-panel msg-warning"
            Case Else
                divMessage.Attributes("class") = "message-panel msg-info"
        End Select
    End Sub

    ''' <summary>
    ''' Show email status - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="success">Whether email was successful</param>
    ''' <param name="message">Status message</param>    

    Private Sub ShowEmailStatus(ByVal success As Boolean, ByVal message As String)
        pnlEmailStatus.Visible = True

        ' Safe string assignment for email status - .NET 3.5.1 compatible
        Dim statusIcon As String
        Dim cssClass As String

        If success Then
            statusIcon = "✅ "
            cssClass = "email-sent"
        Else
            statusIcon = "❌ "
            cssClass = "email-failed"
        End If

        lblEmailStatus.Text = statusIcon + message
        divEmailStatus.Attributes("class") = cssClass
    End Sub

#End Region

#Region "Logging and Audit"

    ''' <summary>
    ''' Log password activity for audit trail - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="userId">User ID</param>
    ''' <param name="action">Action performed</param>
    ''' <param name="isTemporary">Whether password is temporary</param>
    Private Sub LogPasswordActivity(ByVal userId As String, ByVal action As String, ByVal isTemporary As Boolean)
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        
        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()
            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "INSERT INTO password_log (user_id, admin_id, action, is_temporary, timestamp, ip_address) VALUES (?, ?, ?, ?, ?, ?)"
            command.Parameters.AddWithValue("@user_id", userId)
            command.Parameters.AddWithValue("@admin_id", Session("Id_PG").ToString())
            command.Parameters.AddWithValue("@action", action)
            
            ' Safe boolean to integer conversion for logging - .NET 3.5.1 compatible
            Dim temporaryLogFlag As Integer = 0
            If isTemporary Then temporaryLogFlag = 1
            command.Parameters.AddWithValue("@is_temporary", temporaryLogFlag)
            
            command.Parameters.AddWithValue("@timestamp", DateTime.Now)
            command.Parameters.AddWithValue("@ip_address", Request.UserHostAddress)

            command.ExecuteNonQuery()
        Catch ex As Exception
            ' Logging failure should not stop the main process
            LogError("LogPasswordActivity", ex.Message)
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then 
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Sub

    ''' <summary>
    ''' Log errors for debugging - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="methodName">Method where error occurred</param>
    ''' <param name="errorMessage">Error message</param>
    Private Sub LogError(ByVal methodName As String, ByVal errorMessage As String)
        Try
            ' Simple error logging for .NET 3.5.1 - could be enhanced with proper logging framework
            Dim errorLog As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " - " + methodName + " - " + errorMessage
            ' In production, write to log file or database
            ' For now, we'll use application state for debugging
            Application("LastError") = errorLog
        Catch
            ' Ignore logging errors to prevent cascading failures
        End Try
    End Sub

#End Region

#Region "Encryption Validation and Status"

    ''' <summary>
    ''' Validate SHA256 encryption is working properly - System Self-Test
    ''' </summary>
    ''' <returns>True if SHA256 encryption is functioning correctly</returns>
    Private Function ValidateSHA256Encryption() As Boolean
        Try
            ' Test SHA256 encryption with known values
            Dim testPassword As String = "TestPassword123"
            Dim testSalt As String = "TestSalt456"
            
            ' Generate hash twice to ensure consistency
            Dim hash1 As String = HashPasswordWithSalt(testPassword, testSalt)
            Dim hash2 As String = HashPasswordWithSalt(testPassword, testSalt)
            
            ' Validate hash properties
            Dim isValid As Boolean = True
            isValid = isValid AndAlso Not String.IsNullOrEmpty(hash1)
            isValid = isValid AndAlso hash1.Length = 64 ' SHA256 produces 64-character hex string
            isValid = isValid AndAlso hash1.Equals(hash2) ' Same input should produce same output
            isValid = isValid AndAlso hash1.Equals(hash1.ToLower()) ' Should be lowercase
            
            ' Log validation result
            If isValid Then
                LogError("ValidateSHA256Encryption", "SHA256 encryption validation PASSED - System ready")
            Else
                LogError("ValidateSHA256Encryption", "SHA256 encryption validation FAILED - Security compromised")
            End If
            
            Return isValid
        Catch ex As Exception
            LogError("ValidateSHA256Encryption", "SHA256 validation error: " + ex.Message)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Get encryption status report for system monitoring
    ''' </summary>
    ''' <returns>Encryption status information string</returns>
    Private Function GetEncryptionStatusReport() As String
        Try
            Dim report As New StringBuilder()

            report.AppendLine("=== ENCRYPTION STATUS REPORT ===")
            report.AppendLine("Encryption Method: " + ENCRYPTION_METHOD)
            report.AppendLine("Hash Algorithm: " + HASH_ALGORITHM)
            report.AppendLine("Salt Length: " + SALT_LENGTH.ToString() + " bytes")
            report.AppendLine("Salt Encoding: " + SALT_ENCODING)
            report.AppendLine("Hash Output: " + HASH_OUTPUT_FORMAT)
            
            ' Safe validation result assignment - .NET 3.5.1 compatible
            Dim validationResult As String
            If ValidateSHA256Encryption() Then
                validationResult = "PASSED"
            Else
                validationResult = "FAILED"
            End If
            report.AppendLine("SHA256 Validation: " + validationResult)
            
            report.AppendLine("Fallback Methods: NONE (SHA256+Salt ONLY)")
            report.AppendLine("Security Level: MAXIMUM")
            report.AppendLine("Framework: .NET 3.5.1 Compatible")
            report.AppendLine("=== END REPORT ===")
            
            Return report.ToString()
        Catch ex As Exception
            Return "Encryption status report failed: " + ex.Message
        End Try
    End Function

#End Region

    ''' <summary>
    ''' Ensure current user is available, recover from ViewState or displayed data if needed
    ''' </summary>
    ''' <param name="operationName">Name of the operation for logging</param>
    ''' <returns>True if current user is available, False otherwise</returns>
    Private Function EnsureCurrentUserAvailable(ByVal operationName As String) As Boolean
        If m_currentUser IsNot Nothing Then
            LogError(operationName, "Current user available: " + m_currentUser.UserId)
            Return True
        End If
        
        LogError(operationName, "m_currentUser is Nothing - attempting recovery")
        
        ' Try to recover user information from displayed data
        Dim recoveredUserId As String = TrimSafeString(lblUserId.Text)
        If Not String.IsNullOrEmpty(recoveredUserId) Then
            LogError(operationName, "Attempting to recover user data for ID: " + recoveredUserId)
            
            ' Search for user again to restore m_currentUser
            Dim userData As UserData = SearchUserInDatabase(recoveredUserId)
            If userData IsNot Nothing Then
                m_currentUser = userData
                LogError(operationName, "Successfully recovered user data for: " + userData.UserId)
                Return True
            Else
                LogError(operationName, "Failed to recover user data for ID: " + recoveredUserId)
            End If
        Else
            LogError(operationName, "No user ID found in displayed data")
        End If
        
        ShowMessage("Please search for a user first.", "error")
        Return False
    End Function

    ''' <summary>
    ''' Diagnostic test function to identify the exact cause of password update failure
    ''' </summary>
    ''' <param name="userId">User ID to test</param>
    ''' <returns>Detailed diagnostic information</returns>
    Private Function DiagnosePasswordUpdateIssue(ByVal userId As String) As String
        Dim diagnostics As New StringBuilder()
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        
        Try
            diagnostics.AppendLine("=== PASSWORD UPDATE DIAGNOSTIC TEST ===")
            diagnostics.AppendLine("User ID: " + userId)
            diagnostics.AppendLine("Test Time: " + DateTime.Now.ToString())
            
            ' Test 1: Database Connection
            diagnostics.AppendLine()
            diagnostics.AppendLine("1. Testing Database Connection:")
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()
            diagnostics.AppendLine("   ✓ Database connection successful")
            
            ' Test 2: User Existence
            diagnostics.AppendLine()
            diagnostics.AppendLine("2. Testing User Existence:")
            command = New OleDbCommand("SELECT COUNT(*) FROM pn_pengguna WHERE Id_PG = ?", connection)
            command.Parameters.AddWithValue("@id_pg", userId)
            Dim userCount As Integer = Convert.ToInt32(command.ExecuteScalar())
            diagnostics.AppendLine("   User count in database: " + userCount.ToString())
            
            If userCount = 0 Then
                diagnostics.AppendLine("   ❌ USER NOT FOUND - This is the primary issue!")
                Return diagnostics.ToString()
            Else
                diagnostics.AppendLine("   ✓ User exists in database")
            End If
            
            ' Test 3: Check User Data
            diagnostics.AppendLine()
            diagnostics.AppendLine("3. Testing User Data Retrieval:")
            command.Parameters.Clear()
            command.CommandText = "SELECT Id_PG, NAMA, email, STATUS FROM pn_pengguna WHERE Id_PG = ?"
            command.Parameters.AddWithValue("@id_pg", userId)
            
            Dim reader As OleDbDataReader = command.ExecuteReader()
            If reader.Read() Then
                diagnostics.AppendLine("   User ID: " + GetSafeStringValue(reader, "id_pg"))
                diagnostics.AppendLine("   Name: " + GetSafeStringValue(reader, "nama"))
                diagnostics.AppendLine("   Email: " + GetSafeStringValue(reader, "email"))
                diagnostics.AppendLine("   Status: " + GetSafeStringValue(reader, "status"))
                diagnostics.AppendLine("   ✓ User data retrieved successfully")
            Else
                diagnostics.AppendLine("   ❌ User data could not be retrieved")
            End If
            reader.Close()
            
            ' Test 4: Database Schema Check
            diagnostics.AppendLine()
            diagnostics.AppendLine("4. Testing Database Schema:")

            ' Check for enhanced columns
            Try
                command.CommandText = "SELECT TOP 1 PWD, salt, is_temporary, force_change FROM pn_pengguna"
                reader = command.ExecuteReader()
                reader.Close()
                diagnostics.AppendLine("   ✓ Enhanced columns available (PWD, salt, is_temporary, force_change)")
            Catch schemaEx As Exception
                diagnostics.AppendLine("   ❌ Enhanced columns not available: " + schemaEx.Message)
                
                ' Check for basic columns
                Try
                    command.CommandText = "SELECT TOP 1 PWD, salt FROM pn_pengguna"
                    reader = command.ExecuteReader()
                    reader.Close()
                    diagnostics.AppendLine("   ✓ Basic columns available (PWD, salt)")
                Catch basicEx As Exception
                    diagnostics.AppendLine("   ❌ Basic columns not available: " + basicEx.Message)
                    
                    ' Check for minimal columns
                    Try
                        command.CommandText = "SELECT TOP 1 PWD FROM pn_pengguna"
                        reader = command.ExecuteReader()
                        reader.Close()
                        diagnostics.AppendLine("   ✓ Minimal columns available (PWD only)")
                    Catch minimalEx As Exception
                        diagnostics.AppendLine("   ❌ Even PWD column not available: " + minimalEx.Message)
                    End Try
                End Try
            End Try
            
            ' Test 5: Password Generation and Hashing
            diagnostics.AppendLine()
            diagnostics.AppendLine("5. Testing Password Generation and Hashing:")
            Dim testPassword As String = GenerateTemporaryPassword()
            diagnostics.AppendLine("   Generated password length: " + testPassword.Length.ToString())
            
            Dim salt As String = ""
            Dim hashedPassword As String = GeneratePasswordHashWithNewSalt(testPassword, salt)
            diagnostics.AppendLine("   Hash length: " + hashedPassword.Length.ToString())
            diagnostics.AppendLine("   Salt length: " + salt.Length.ToString())
            diagnostics.AppendLine("   ✓ Password generation and hashing successful")
            
            ' Test 6: Actual Update Test (Dry Run)
            diagnostics.AppendLine()
            diagnostics.AppendLine("6. Testing Update Query Construction:")
              ' Try enhanced update query construction
            Try
                Dim sql As String = "UPDATE pn_pengguna SET PWD = ?, salt = ?, is_temporary = ?, force_change = ?, tarikh_tukar_katalaluan = ?, last_changed_by = ? WHERE Id_PG = ?"
                command.CommandText = sql
                command.Parameters.Clear()
                command.Parameters.AddWithValue("@password", hashedPassword)
                command.Parameters.AddWithValue("@salt", salt)
                command.Parameters.AddWithValue("@is_temporary", 1)
                command.Parameters.AddWithValue("@force_change", 1)
                command.Parameters.AddWithValue("@date_changed", DateTime.Now)
                command.Parameters.AddWithValue("@changed_by", "DIAGNOSTIC_TEST")
                command.Parameters.AddWithValue("@id_pg", userId)
                
                diagnostics.AppendLine("   Enhanced query: " + sql)
                diagnostics.AppendLine("   Parameters: 7")
                diagnostics.AppendLine("   ✓ Enhanced update query constructed successfully")
                
            Catch queryEx As Exception
                diagnostics.AppendLine("   ❌ Enhanced query construction failed: " + queryEx.Message)
            End Try
            
            diagnostics.AppendLine()
            diagnostics.AppendLine("=== DIAGNOSTIC COMPLETE ===")
            
            Return diagnostics.ToString()
            
        Catch ex As Exception
            diagnostics.AppendLine()
            diagnostics.AppendLine("❌ DIAGNOSTIC ERROR: " + ex.Message)
            diagnostics.AppendLine("Stack Trace: " + ex.StackTrace)
            Return diagnostics.ToString()
            
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Run comprehensive diagnostic test on the current user
    ''' </summary>
    Protected Sub btnRunDiagnostic_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnRunDiagnostic.Click
        Try
            If m_currentUser Is Nothing Then
                ShowMessage("Please search for a user first to run diagnostics.", "error")
                Return
            End If
            
            Dim diagnosticResult As String = DiagnosePasswordUpdateIssue(m_currentUser.UserId)
            LogError("DIAGNOSTIC_TEST", diagnosticResult)
            
            ' Show summary in message
            If diagnosticResult.Contains("USER NOT FOUND") Then
                ShowMessage("DIAGNOSTIC: User not found in database - Check user ID exactly!", "error")
            ElseIf diagnosticResult.Contains("Enhanced columns available") Then
                ShowMessage("DIAGNOSTIC: Database schema is complete - Issue may be in update logic", "info")
            ElseIf diagnosticResult.Contains("Basic columns available") Then
                ShowMessage("DIAGNOSTIC: Basic schema available - Should use fallback update", "warning")
            ElseIf diagnosticResult.Contains("Minimal columns available") Then
                ShowMessage("DIAGNOSTIC: Only basic password column available", "warning")
            Else
                ShowMessage("DIAGNOSTIC: Check application logs for detailed analysis", "info")
            End If
            
        Catch ex As Exception
            LogError("btnRunDiagnostic_Click", ex.Message)
            ShowMessage("Diagnostic test failed: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Emergency password reset function with minimal complexity
    ''' Used when the main UpdateUserPasswordSecure function fails
    ''' </summary>
    ''' <param name="userId">User ID</param>
    ''' <param name="newPassword">New password</param>
    ''' <returns>True if successful</returns>
    Private Function EmergencyPasswordReset(ByVal userId As String, ByVal newPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        
        Try
            LogError("EmergencyPasswordReset", "Starting emergency password reset for user: " + userId)
            
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()
            
            ' Generate simple hash without salt for emergency cases
            Dim simpleHash As String = ""
            Using sha256 As SHA256 = SHA256.Create()
                Dim inputBytes As Byte() = Encoding.UTF8.GetBytes(newPassword)
                Dim hashBytes As Byte() = sha256.ComputeHash(inputBytes)
                
                Dim hashString As New StringBuilder(hashBytes.Length * 2)
                For Each b As Byte In hashBytes
                    hashString.Append(b.ToString("x2"))
                Next
                simpleHash = hashString.ToString()
            End Using
            
            LogError("EmergencyPasswordReset", "Generated simple hash length: " + simpleHash.Length.ToString())
            
            ' Try the simplest possible update
            command = New OleDbCommand("UPDATE pn_pengguna SET PWD = ? WHERE Id_PG = ?", connection)
            command.Parameters.Add("@password", OleDbType.VarChar, 255).Value = simpleHash
            command.Parameters.Add("@id_pg", OleDbType.VarChar, 50).Value = userId.Trim()
            
            Dim rowsAffected As Integer = command.ExecuteNonQuery()
            LogError("EmergencyPasswordReset", "Emergency update completed. Rows affected: " + rowsAffected.ToString())
            
            Return rowsAffected > 0
            
        Catch ex As Exception
            LogError("EmergencyPasswordReset", "Emergency reset failed: " + ex.Message)
            Return False
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Test basic database connectivity and user existence
    ''' </summary>
    ''' <param name="userId">User ID to test</param>
    ''' <returns>True if user exists and database is accessible</returns>
    Private Function TestDatabaseConnectivity(ByVal userId As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        
        Try
            LogError("TestDatabaseConnectivity", "Testing connectivity for user: " + userId)
            
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()
            LogError("TestDatabaseConnectivity", "Database connection successful")
            
            ' Test 1: Check if table exists
            command = New OleDbCommand("SELECT COUNT(*) FROM pn_pengguna", connection)
            Dim totalUsers As Integer = Convert.ToInt32(command.ExecuteScalar())
            LogError("TestDatabaseConnectivity", "Total users in database: " + totalUsers.ToString())
            
            ' Test 2: Check specific user with different approaches
            command.CommandText = "SELECT COUNT(*) FROM pn_pengguna WHERE Id_PG = ?"
            command.Parameters.Clear()
            command.Parameters.Add("@id_pg", OleDbType.VarChar, 50).Value = userId.Trim()
            Dim userCount1 As Integer = Convert.ToInt32(command.ExecuteScalar())
            LogError("TestDatabaseConnectivity", "User count (parameterized): " + userCount1.ToString())
            
            ' Test 3: Direct SQL approach
            command.CommandText = "SELECT COUNT(*) FROM pn_pengguna WHERE Id_PG = '" + userId.Replace("'", "''").Trim() + "'"
            command.Parameters.Clear()
            Dim userCount2 As Integer = Convert.ToInt32(command.ExecuteScalar())
            LogError("TestDatabaseConnectivity", "User count (direct SQL): " + userCount2.ToString())
            
            ' Test 4: Case insensitive search
            command.CommandText = "SELECT COUNT(*) FROM pn_pengguna WHERE UPPER(Id_PG) = UPPER(?)"
            command.Parameters.Clear()
            command.Parameters.Add("@id_pg", OleDbType.VarChar, 50).Value = userId.Trim()
            Dim userCount3 As Integer = Convert.ToInt32(command.ExecuteScalar())
            LogError("TestDatabaseConnectivity", "User count (case insensitive): " + userCount3.ToString())
            
            Return userCount1 > 0 Or userCount2 > 0 Or userCount3 > 0
            
        Catch ex As Exception
            LogError("TestDatabaseConnectivity", "Connectivity test failed: " + ex.Message)
            Return False
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Get recent log entries for debugging purposes
    ''' </summary>
    ''' <returns>Recent log entries as formatted string</returns>
    Private Function GetRecentLogEntries() As String
        Try
            ' This is a simplified log viewer - in a real application you'd read from log files
            ' For now, we'll create a summary of recent operations
            Dim logSummary As New StringBuilder()
            logSummary.AppendLine("=== RECENT OPERATION SUMMARY ===")
            logSummary.AppendLine("Current Time: " + DateTime.Now.ToString())
            
            If m_currentUser IsNot Nothing Then
                logSummary.AppendLine("Current User: " + m_currentUser.UserId)
                logSummary.AppendLine("User Name: " + m_currentUser.Name)
                logSummary.AppendLine("User Email: " + m_currentUser.Email)
                logSummary.AppendLine("User Status: " + m_currentUser.Status)
            Else
                logSummary.AppendLine("No current user selected")
            End If
            
            logSummary.AppendLine("Database Connection String Available: " + (Not String.IsNullOrEmpty(SPMJ_Mod.ServerId)).ToString())
            logSummary.AppendLine("Session ID Available: " + (Session("Id_PG") IsNot Nothing).ToString())
            
            Return logSummary.ToString()
            
        Catch ex As Exception
            Return "Error generating log summary: " + ex.Message
        End Try
    End Function

    ''' <summary>
    ''' Display comprehensive debugging information
    ''' </summary>
    Protected Sub btnShowDebugInfo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnShowDebugInfo.Click
        Try
            If m_currentUser Is Nothing Then
                ShowMessage("Please search for a user first to show debug info.", "error")
                Return
            End If
            
            Dim debugInfo As New StringBuilder()
            debugInfo.AppendLine("=== DEBUG INFORMATION ===")
            debugInfo.AppendLine()
            
            ' User Information
            debugInfo.AppendLine("CURRENT USER:")
            debugInfo.AppendLine("  ID: '" + m_currentUser.UserId + "'")
            debugInfo.AppendLine("  Length: " + m_currentUser.UserId.Length.ToString())
            debugInfo.AppendLine("  Name: '" + m_currentUser.Name + "'")
            debugInfo.AppendLine("  Email: '" + m_currentUser.Email + "'")
            debugInfo.AppendLine()
            
            ' Database Test
            debugInfo.AppendLine("DATABASE CONNECTIVITY:")
            If TestDatabaseConnectivity(m_currentUser.UserId) Then
                debugInfo.AppendLine("  ✓ User exists in database")
            Else
                debugInfo.AppendLine("  ❌ User NOT found in database")
            End If
            debugInfo.AppendLine()
            
            ' Password Generation Test
            debugInfo.AppendLine("PASSWORD GENERATION:")
            Try
                Dim testPassword As String = GenerateTemporaryPassword()
                debugInfo.AppendLine("  ✓ Password generation successful")
                debugInfo.AppendLine("  Length: " + testPassword.Length.ToString())
                debugInfo.AppendLine("  Sample: " + testPassword.Substring(0, Math.Min(4, testPassword.Length)) + "...")
            Catch passwordEx As Exception
                debugInfo.AppendLine("  ❌ Password generation failed: " + passwordEx.Message)
            End Try
            debugInfo.AppendLine()
            
            ' Hash Generation Test
            debugInfo.AppendLine("HASH GENERATION:")
            Try
                Dim salt As String = ""
                Dim hash As String = GeneratePasswordHashWithNewSalt("test123", salt)
                debugInfo.AppendLine("  ✓ Hash generation successful")
                debugInfo.AppendLine("  Hash length: " + hash.Length.ToString())
                debugInfo.AppendLine("  Salt length: " + salt.Length.ToString())
            Catch hashEx As Exception
                debugInfo.AppendLine("  ❌ Hash generation failed: " + hashEx.Message)
            End Try
            
            Dim fullDebugInfo As String = debugInfo.ToString()
            LogError("DEBUG_INFO", fullDebugInfo)
            
            ShowMessage("Debug information logged. Check application logs for full details. User exists in DB: " + TestDatabaseConnectivity(m_currentUser.UserId).ToString(), "info")
            
        Catch ex As Exception
            LogError("btnShowDebugInfo_Click", ex.Message)
            ShowMessage("Debug info generation failed: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Check what columns actually exist in the pn_pengguna table
    ''' </summary>
    ''' <returns>String indicating available column set</returns>
    Private Function CheckActualTableStructure() As String
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        
        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()
            
            ' Test for actual columns by trying different SELECT combinations
            command = New OleDbCommand()
            command.Connection = connection
            
            ' Check what we know works from SearchUserInDatabase
            LogError("CheckActualTableStructure", "Testing known working columns...")
            command.CommandText = "SELECT TOP 1 Id_PG, NAMA, email, STATUS, MODUL, AKSES FROM pn_pengguna"
            Dim reader1 As OleDbDataReader = command.ExecuteReader()
            reader1.Close()
            LogError("CheckActualTableStructure", "✓ Basic user columns exist: id_pg, nama, email, status, modul, akses")

            ' Test for password column
            Try
                command.CommandText = "SELECT TOP 1 PWD FROM pn_pengguna"
                Dim reader2 As OleDbDataReader = command.ExecuteReader()
                reader2.Close()
                LogError("CheckActualTableStructure", "✓ Password column exists: PWD")
            Catch passwordEx As Exception
                LogError("CheckActualTableStructure", "❌ Password column 'PWD' not found: " + passwordEx.Message)
                Return "NO_PASSWORD_COLUMN"
            End Try
            
            ' Test for salt column
            Try
                command.CommandText = "SELECT TOP 1 PWD, salt FROM pn_pengguna"
                Dim reader3 As OleDbDataReader = command.ExecuteReader()
                reader3.Close()
                LogError("CheckActualTableStructure", "✓ Salt column exists: salt")
            Catch saltEx As Exception
                LogError("CheckActualTableStructure", "❌ Salt column not found: " + saltEx.Message)
                Return "PASSWORD_ONLY"
            End Try
            
            ' Test for enhanced security columns
            Try
                command.CommandText = "SELECT TOP 1 PWD, salt, is_temporary, force_change FROM pn_pengguna"
                Dim reader4 As OleDbDataReader = command.ExecuteReader()
                reader4.Close()
                LogError("CheckActualTableStructure", "✓ Enhanced security columns exist: is_temporary, force_change")
            Catch enhancedEx As Exception
                LogError("CheckActualTableStructure", "❌ Enhanced security columns not found: " + enhancedEx.Message)
                Return "BASIC_SECURITY"
            End Try
            
            ' Test for date tracking column
            Try
                command.CommandText = "SELECT TOP 1 PWD, salt, is_temporary, force_change, tarikh_tukar_katalaluan FROM pn_pengguna"
                Dim reader5 As OleDbDataReader = command.ExecuteReader()
                reader5.Close()
                LogError("CheckActualTableStructure", "✓ Date tracking column exists: tarikh_tukar_katalaluan")
            Catch dateEx As Exception
                LogError("CheckActualTableStructure", "❌ Date tracking column not found: " + dateEx.Message)
                Return "ENHANCED_NO_DATE"
            End Try
            
            ' Test for admin tracking column
            Try
                command.CommandText = "SELECT TOP 1 PWD, salt, is_temporary, force_change, tarikh_tukar_katalaluan, last_changed_by FROM pn_pengguna"
                Dim reader6 As OleDbDataReader = command.ExecuteReader()
                reader6.Close()
                LogError("CheckActualTableStructure", "✓ Admin tracking column exists: last_changed_by")
                Return "FULL_ENHANCED"
            Catch adminEx As Exception
                LogError("CheckActualTableStructure", "❌ Admin tracking column not found: " + adminEx.Message)
                Return "ENHANCED_NO_ADMIN"
            End Try
            
        Catch ex As Exception
            LogError("CheckActualTableStructure", "Table structure check failed: " + ex.Message)
            Return "ERROR"
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Update password using the correct SQL for the actual table structure
    ''' </summary>
    ''' <param name="userId">User ID</param>
    ''' <param name="newPassword">New password</param>
    ''' <param name="isTemporary">Is temporary password</param>
    ''' <param name="forceChange">Force change on next login</param>
    ''' <returns>True if successful</returns>
    Private Function UpdatePasswordWithCorrectStructure(ByVal userId As String, ByVal newPassword As String, ByVal isTemporary As Boolean, ByVal forceChange As Boolean) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        
        Try
            ' First check what table structure we actually have
            Dim tableStructure As String = CheckActualTableStructure()
            LogError("UpdatePasswordWithCorrectStructure", "Detected table structure: " + tableStructure)
            
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()
            
            ' Generate hash
            Dim salt As String = ""
            Dim hashedPassword As String = GeneratePasswordHashWithNewSalt(newPassword, salt)
            
            command = New OleDbCommand()
            command.Connection = connection
              ' Use appropriate UPDATE SQL based on actual table structure
            Select Case tableStructure
                Case "FULL_ENHANCED"
                    command.CommandText = "UPDATE pn_pengguna SET PWD = ?, salt = ?, is_temporary = ?, force_change = ?, tarikh_tukar_katalaluan = ?, last_changed_by = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@password", hashedPassword)
                    command.Parameters.AddWithValue("@salt", salt)
                    command.Parameters.AddWithValue("@is_temporary", If(isTemporary, 1, 0))
                    command.Parameters.AddWithValue("@force_change", If(forceChange, 1, 0))
                    command.Parameters.AddWithValue("@date_changed", DateTime.Now)
                    command.Parameters.AddWithValue("@changed_by", If(Session("Id_PG") IsNot Nothing, Session("Id_PG").ToString(), "SYSTEM"))
                    command.Parameters.AddWithValue("@id_pg", userId.Trim())
                    
                Case "ENHANCED_NO_ADMIN"
                    command.CommandText = "UPDATE pn_pengguna SET PWD = ?, salt = ?, is_temporary = ?, force_change = ?, tarikh_tukar_katalaluan = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@password", hashedPassword)
                    command.Parameters.AddWithValue("@salt", salt)
                    command.Parameters.AddWithValue("@is_temporary", If(isTemporary, 1, 0))
                    command.Parameters.AddWithValue("@force_change", If(forceChange, 1, 0))
                    command.Parameters.AddWithValue("@date_changed", DateTime.Now)
                    command.Parameters.AddWithValue("@id_pg", userId.Trim())
                    
                Case "ENHANCED_NO_DATE"
                    command.CommandText = "UPDATE pn_pengguna SET PWD = ?, salt = ?, is_temporary = ?, force_change = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@password", hashedPassword)
                    command.Parameters.AddWithValue("@salt", salt)
                    command.Parameters.AddWithValue("@is_temporary", If(isTemporary, 1, 0))
                    command.Parameters.AddWithValue("@force_change", If(forceChange, 1, 0))
                    command.Parameters.AddWithValue("@id_pg", userId.Trim())
                    
                Case "BASIC_SECURITY"
                    command.CommandText = "UPDATE pn_pengguna SET PWD = ?, salt = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@password", hashedPassword)
                    command.Parameters.AddWithValue("@salt", salt)
                    command.Parameters.AddWithValue("@id_pg", userId.Trim())
                    
                Case "PASSWORD_ONLY"
                    command.CommandText = "UPDATE pn_pengguna SET PWD = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@password", hashedPassword)
                    command.Parameters.AddWithValue("@id_pg", userId.Trim())
                    
                Case Else
                    LogError("UpdatePasswordWithCorrectStructure", "Unknown or unsupported table structure: " + tableStructure)
                    Return False
            End Select
            
            LogError("UpdatePasswordWithCorrectStructure", "Using SQL: " + command.CommandText)
            LogError("UpdatePasswordWithCorrectStructure", "Parameter count: " + command.Parameters.Count.ToString())
            
            Dim rowsAffected As Integer = command.ExecuteNonQuery()
            LogError("UpdatePasswordWithCorrectStructure", "Rows affected: " + rowsAffected.ToString())
            
            Return rowsAffected > 0
            
        Catch ex As Exception
            LogError("UpdatePasswordWithCorrectStructure", "Update failed: " + ex.Message)
            Return False
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Validate email format via .NET 9 microservice with local fallback - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="email">Email address to validate</param>
    ''' <returns>True if valid format, False otherwise</returns>
    Private Function IsValidEmailFormatWithMicroservice(ByVal email As String) As Boolean
        Try
            ' First try local validation for quick check
            If Not IsValidEmailFormat(email) Then Return False

            ' Try microservice validation for enhanced validation           
            Try
                Using client As New WebClient()
                    client.Headers("Content-Type") = "application/json"
                    client.Headers("X-API-Key") = EMAIL_API_KEY

                    Dim response As String = client.UploadString(EMAIL_SERVICE_URL + "/validate-email", """" + email + """")

                    ' Parse simple response (assuming {"success":true/false})
                    Return response.Contains("""success"":true")
                End Using
            Catch microEx As Exception
                LogError("IsValidEmailFormatWithMicroservice", "Microservice validation failed, using local: " + microEx.Message)
                ' Fallback to local validation if microservice is unavailable
                Return IsValidEmailFormat(email)
            End Try
            
        Catch ex As Exception
            LogError("IsValidEmailFormatWithMicroservice", "Email validation error: " + ex.Message)
            Return False
        End Try
    End Function

End Class

#Region "Data Transfer Objects"

''' <summary>
''' User data structure - .NET 3.5.1 Compatible
''' Enhanced with additional properties for comprehensive user management
''' </summary>
<Serializable()>
Public Class UserData
    Private m_userId As String = ""
    Private m_name As String = ""
    Private m_email As String = ""
    Private m_status As String = ""
    Private m_module As String = ""
    Private m_access As String = ""
    Private m_registerDate As String = ""
    Private m_lastLogin As String = ""

    ''' <summary>
    ''' User ID property with validation
    ''' </summary>
    Public Property UserId() As String
        Get
            Return m_userId
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_userId = ""
            Else
                m_userId = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' User name property with validation
    ''' </summary>
    Public Property Name() As String
        Get
            Return m_name
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_name = ""
            Else
                m_name = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Email property with validation
    ''' </summary>
    Public Property Email() As String
        Get
            Return m_email
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_email = ""
            Else
                m_email = value.Trim().ToLower()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Status property
    ''' </summary>
    Public Property Status() As String
        Get
            Return m_status
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_status = ""
            Else
                m_status = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' User module property
    ''' </summary>
    Public Property UserModule() As String
        Get
            Return m_module
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_module = ""
            Else
                m_module = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Access level property
    ''' </summary>
    Public Property Access() As String
        Get
            Return m_access
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_access = ""
            Else
                m_access = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Registration date property
    ''' </summary>
    Public Property RegisterDate() As String
        Get
            Return m_registerDate
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_registerDate = "Not available"
            Else
                m_registerDate = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Last login date property
    ''' </summary>
    Public Property LastLogin() As String
        Get
            Return m_lastLogin
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_lastLogin = "Not available"
            Else
                m_lastLogin = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Constructor for UserData class
    ''' </summary>
    Public Sub New()
        ' Initialize with default values
        m_userId = ""
        m_name = ""
        m_email = ""
        m_status = ""
        m_module = ""
        m_access = ""
        m_registerDate = "Not available"
        m_lastLogin = "Not available"
    End Sub

    ''' <summary>
    ''' Validate user data completeness
    ''' </summary>
    ''' <returns>True if essential data is present, False otherwise</returns>
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrEmpty(m_userId) AndAlso Not String.IsNullOrEmpty(m_name)
    End Function

    ''' <summary>
    ''' Get formatted display name for UI
    ''' </summary>
    ''' <returns>Formatted display name</returns>
    Public Function GetDisplayName() As String
        If Not String.IsNullOrEmpty(m_name) Then
            Return m_name + " (" + m_userId + ")"
        Else
            Return m_userId
        End If
    End Function

End Class

#End Region
