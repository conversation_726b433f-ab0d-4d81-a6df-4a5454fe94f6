using System.Security.Claims;

namespace SPMJ.EmailService.Middleware
{
    public class ApiKeyAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ApiKeyAuthenticationMiddleware> _logger;
        private readonly string _apiKey;

        public ApiKeyAuthenticationMiddleware(RequestDelegate next, IConfiguration configuration, ILogger<ApiKeyAuthenticationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
            _apiKey = configuration["ApiSettings:ApiKey"] ?? "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia";
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Skip authentication for health check and swagger
            var path = context.Request.Path.ToString().ToLower();
            if (path.Contains("/health") || path.Contains("/swagger") || path.Contains("/favicon"))
            {
                await _next(context);
                return;
            }

            // Check for API key in header
            if (!context.Request.Headers.TryGetValue("X-API-Key", out var extractedApiKey))
            {
                _logger.LogWarning("Missing API key from {RemoteIP} for {Path}", 
                    context.Connection.RemoteIpAddress, context.Request.Path);
                
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("API Key is required");
                return;
            }

            if (!string.Equals(extractedApiKey, _apiKey, StringComparison.Ordinal))
            {
                _logger.LogWarning("Invalid API key from {RemoteIP} for {Path}", 
                    context.Connection.RemoteIpAddress, context.Request.Path);
                
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Invalid API Key");
                return;
            }

            // Set authenticated user context
            var claims = new[]
            {
                new Claim(ClaimTypes.Name, "SPMJ-Service"),
                new Claim(ClaimTypes.Role, "ServiceAccount")
            };
            var identity = new ClaimsIdentity(claims, "ApiKey");
            context.User = new ClaimsPrincipal(identity);

            await _next(context);
        }
    }
}
