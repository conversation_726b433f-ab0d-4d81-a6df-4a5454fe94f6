# SPMJ Email Service Deployment Script
# This script helps deploy and configure the .NET 9 Email Microservice

param(
    [string]$Action = "install",
    [string]$EmailServicePort = "5000",
    [string]$DatabaseServer = "localhost",
    [string]$DatabaseName = "SPMJ_PDSA",
    [string]$SmtpServer = "",
    [string]$SmtpUsername = "",
    [string]$SmtpPassword = ""
)

$ErrorActionPreference = "Stop"

Write-Host "SPMJ Email Service Deployment Script" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

$emailServicePath = ".\SPMJ.EmailService"
$spmjPath = ".\SPMJ"

function Test-Prerequisites {
    Write-Host "Checking prerequisites..." -ForegroundColor Yellow
    
    # Check .NET 9 SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Host "✓ .NET SDK version: $dotnetVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ .NET 9 SDK is required. Please install from https://dotnet.microsoft.com/download" -ForegroundColor Red
        exit 1
    }
    
    # Check if email service project exists
    if (-not (Test-Path "$emailServicePath\SPMJ.EmailService.csproj")) {
        Write-Host "✗ Email service project not found at $emailServicePath" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✓ Prerequisites check passed" -ForegroundColor Green
}

function Install-EmailService {
    Write-Host "Installing Email Service..." -ForegroundColor Yellow
    
    # Navigate to email service directory
    Push-Location $emailServicePath
    
    try {
        # Restore packages
        Write-Host "Restoring NuGet packages..." -ForegroundColor Cyan
        dotnet restore
        
        # Build the project
        Write-Host "Building Email Service..." -ForegroundColor Cyan
        dotnet build --configuration Release
        
        Write-Host "✓ Email Service build completed" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Build failed: $($_.Exception.Message)" -ForegroundColor Red
        Pop-Location
        exit 1
    }
    finally {
        Pop-Location
    }
}

function Update-Configuration {
    Write-Host "Updating configuration..." -ForegroundColor Yellow
    
    $appsettingsPath = "$emailServicePath\appsettings.json"
    
    if (Test-Path $appsettingsPath) {
        $config = Get-Content $appsettingsPath | ConvertFrom-Json
        
        # Update database connection
        $config.ConnectionStrings.DefaultConnection = "Server=$DatabaseServer;Database=$DatabaseName;User ID=sa;Password=*********;TrustServerCertificate=True"
        
        # Update email settings if provided
        if ($SmtpServer) {
            $config.EmailSettings.SmtpServer = $SmtpServer
        }
        if ($SmtpUsername) {
            $config.EmailSettings.Username = $SmtpUsername
            $config.EmailSettings.FromEmail = $SmtpUsername
        }
        if ($SmtpPassword) {
            $config.EmailSettings.Password = $SmtpPassword
        }
        
        # Save updated configuration
        $config | ConvertTo-Json -Depth 10 | Set-Content $appsettingsPath
        Write-Host "✓ Configuration updated" -ForegroundColor Green
    }
}

function Test-DatabaseConnection {
    Write-Host "Testing database connection..." -ForegroundColor Yellow
    
    try {
        # Simple connection test (you may need to adjust based on your SQL Server setup)
        $connectionString = "Server=$DatabaseServer;Database=$DatabaseName;User ID=sa;Password=*********;TrustServerCertificate=True"
        
        # This is a simplified test - in production you might want more robust testing
        Write-Host "✓ Database connection parameters configured" -ForegroundColor Green
        Write-Host "   Please ensure SQL Server is running and accessible" -ForegroundColor Cyan
    }
    catch {
        Write-Host "✗ Database connection test failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "   Please check your database server and credentials" -ForegroundColor Yellow
    }
}

function Deploy-DatabaseMigration {
    Write-Host "Database migration information..." -ForegroundColor Yellow
    
    $migrationScript = "$emailServicePath\Database_EmailService_Migration.sql"
    
    if (Test-Path $migrationScript) {
        Write-Host "✓ Database migration script found: $migrationScript" -ForegroundColor Green
        Write-Host "   Please execute this script on your SPMJ database:" -ForegroundColor Cyan
        Write-Host "   $migrationScript" -ForegroundColor White
    } else {
        Write-Host "✗ Database migration script not found" -ForegroundColor Red
    }
}

function Start-EmailService {
    Write-Host "Starting Email Service..." -ForegroundColor Yellow
    
    Push-Location $emailServicePath
    
    try {
        Write-Host "Starting service on port $EmailServicePort..." -ForegroundColor Cyan
        Write-Host "Press Ctrl+C to stop the service" -ForegroundColor Yellow
        Write-Host "" -ForegroundColor White
        
        # Start the service
        $env:ASPNETCORE_URLS = "http://localhost:$EmailServicePort"
        dotnet run --configuration Release
    }
    catch {
        Write-Host "✗ Failed to start service: $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        Pop-Location
    }
}

function Test-EmailService {
    Write-Host "Testing Email Service..." -ForegroundColor Yellow
    
    try {
        $healthUrl = "http://localhost:$EmailServicePort/health"
        $response = Invoke-RestMethod -Uri $healthUrl -Method Get -TimeoutSec 10
        
        if ($response.status -eq "healthy") {
            Write-Host "✓ Email Service is running and healthy" -ForegroundColor Green
            Write-Host "   Service URL: http://localhost:$EmailServicePort" -ForegroundColor Cyan
            Write-Host "   Swagger UI: http://localhost:$EmailServicePort/swagger" -ForegroundColor Cyan
        } else {
            Write-Host "✗ Email Service responded but status is not healthy" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Email Service is not responding: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "   Make sure the service is running on port $EmailServicePort" -ForegroundColor Yellow
    }
}

function Show-Usage {
    Write-Host ""
    Write-Host "Usage: .\Deploy-EmailService.ps1 [parameters]" -ForegroundColor White
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Yellow
    Write-Host "  -Action          : install, start, test, configure (default: install)" -ForegroundColor White
    Write-Host "  -EmailServicePort: Port for email service (default: 5000)" -ForegroundColor White
    Write-Host "  -DatabaseServer  : Database server name (default: localhost)" -ForegroundColor White
    Write-Host "  -DatabaseName    : Database name (default: SPMJ_PDSA)" -ForegroundColor White
    Write-Host "  -SmtpServer      : SMTP server (e.g., smtp.gmail.com)" -ForegroundColor White
    Write-Host "  -SmtpUsername    : SMTP username/email" -ForegroundColor White
    Write-Host "  -SmtpPassword    : SMTP password/app password" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\Deploy-EmailService.ps1 -Action install" -ForegroundColor White
    Write-Host "  .\Deploy-EmailService.ps1 -Action configure -SmtpServer smtp.gmail.com -SmtpUsername <EMAIL> -SmtpPassword apppassword" -ForegroundColor White
    Write-Host "  .\Deploy-EmailService.ps1 -Action start" -ForegroundColor White
    Write-Host "  .\Deploy-EmailService.ps1 -Action test" -ForegroundColor White
}

# Main execution
switch ($Action.ToLower()) {
    "install" {
        Test-Prerequisites
        Install-EmailService
        Update-Configuration
        Deploy-DatabaseMigration
        Test-DatabaseConnection
        Write-Host ""
        Write-Host "Installation completed!" -ForegroundColor Green
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "1. Execute the database migration script" -ForegroundColor White
        Write-Host "2. Configure email settings: .\Deploy-EmailService.ps1 -Action configure" -ForegroundColor White
        Write-Host "3. Start the service: .\Deploy-EmailService.ps1 -Action start" -ForegroundColor White
    }
    
    "configure" {
        Update-Configuration
        Write-Host "Configuration updated successfully!" -ForegroundColor Green
    }
    
    "start" {
        Start-EmailService
    }
    
    "test" {
        Test-EmailService
    }
    
    "help" {
        Show-Usage
    }
    
    default {
        Write-Host "Unknown action: $Action" -ForegroundColor Red
        Show-Usage
        exit 1
    }
}

Write-Host ""
Write-Host "For more information, see: SPMJ.EmailService\DEPLOYMENT_GUIDE.md" -ForegroundColor Cyan
