# OTP Verification Diagnostic Script
# This script helps diagnose why OTP verification is failing

Write-Host "=== OTP Verification Diagnostic ===" -ForegroundColor Green
Write-Host "Investigating OTP verification failures..." -ForegroundColor Yellow

# Test 1: Check if EmailService is running
Write-Host "`n1. Testing EmailService Connectivity..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:5000/health" -Method GET -TimeoutSec 5
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "✓ EmailService is running and healthy" -ForegroundColor Green
    } else {
        Write-Host "✗ EmailService health check failed: $($healthResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ EmailService is not running or not accessible: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Please start the EmailService first with: dotnet run --urls http://localhost:5000" -ForegroundColor Yellow
}

# Test 2: Test OTP Generation
Write-Host "`n2. Testing OTP Generation..." -ForegroundColor Cyan
try {
    $otpGenRequest = @{
        UserId = "test_user"
        Email = "<EMAIL>"
        Purpose = "LOGIN"
    } | ConvertTo-Json

    $otpGenResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/otp/generate" -Method POST -Body $otpGenRequest -ContentType "application/json" -TimeoutSec 10
    if ($otpGenResponse.StatusCode -eq 200) {
        $otpResult = $otpGenResponse.Content | ConvertFrom-Json
        Write-Host "✓ OTP Generation API responding: $($otpResult.message)" -ForegroundColor Green
        if ($otpResult.otpCode) {
            Write-Host "   Generated OTP Code: $($otpResult.otpCode)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "✗ OTP Generation failed: $($otpGenResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ OTP Generation API error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Check Database Schema
Write-Host "`n3. Checking Entity Framework Configuration..." -ForegroundColor Cyan

$modelsFile = "d:\2024\.NET 3.5. - Q\SPMJ.EmailService\Models\DatabaseModels.cs"
if (Test-Path $modelsFile) {
    $modelsContent = Get-Content $modelsFile -Raw
    
    # Check OtpToken model
    if ($modelsContent -match "class OtpToken") {
        Write-Host "✓ OtpToken model exists" -ForegroundColor Green
        
        # Check required properties
        $requiredProps = @("UserId", "OtpCode", "Purpose", "ExpiresAt", "Used")
        foreach ($prop in $requiredProps) {
            if ($modelsContent -match $prop) {
                Write-Host "  ✓ Property $prop exists" -ForegroundColor Green
            } else {
                Write-Host "  ✗ Property $prop missing" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "✗ OtpToken model not found" -ForegroundColor Red
    }
} else {
    Write-Host "✗ DatabaseModels.cs not found" -ForegroundColor Red
}

# Test 4: Check OTP Validation Logic
Write-Host "`n4. Analyzing OTP Validation Logic..." -ForegroundColor Cyan

$otpServiceFile = "d:\2024\.NET 3.5. - Q\SPMJ.EmailService\Services\OtpService.cs"
if (Test-Path $otpServiceFile) {
    $serviceContent = Get-Content $otpServiceFile -Raw
    
    # Check validation conditions
    if ($serviceContent -match "ValidateOtpAsync") {
        Write-Host "✓ ValidateOtpAsync method exists" -ForegroundColor Green
        
        # Check validation conditions
        if ($serviceContent -match "o\.UserId == request\.UserId") {
            Write-Host "  ✓ UserId validation present" -ForegroundColor Green
        } else {
            Write-Host "  ✗ UserId validation missing" -ForegroundColor Red
        }
        
        if ($serviceContent -match "o\.OtpCode == request\.OtpCode") {
            Write-Host "  ✓ OtpCode validation present" -ForegroundColor Green
        } else {
            Write-Host "  ✗ OtpCode validation missing" -ForegroundColor Red
        }
        
        if ($serviceContent -match "o\.Purpose == request\.Purpose") {
            Write-Host "  ✓ Purpose validation present" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Purpose validation missing" -ForegroundColor Red
        }
        
        if ($serviceContent -match "!o\.Used") {
            Write-Host "  ✓ Used flag validation present" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Used flag validation missing" -ForegroundColor Red
        }
        
        if ($serviceContent -match "o\.ExpiresAt > DateTime\.UtcNow") {
            Write-Host "  ✓ Expiration validation present" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Expiration validation missing" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ ValidateOtpAsync method not found" -ForegroundColor Red
    }
} else {
    Write-Host "✗ OtpService.cs not found" -ForegroundColor Red
}

# Test 5: Check for potential issues
Write-Host "`n5. Checking for Common Issues..." -ForegroundColor Cyan

# Check for cache issues
if ($serviceContent -match "_cache\.TryGetValue") {
    Write-Host "⚠ Cache validation detected - cache mismatches could cause issues" -ForegroundColor Yellow
    Write-Host "  Suggestion: Disable cache validation temporarily for testing" -ForegroundColor White
}

# Check for timezone issues
if ($serviceContent -match "DateTime\.UtcNow") {
    Write-Host "⚠ UTC time used - ensure database times match UTC" -ForegroundColor Yellow
}

Write-Host "`n=== DEBUGGING RECOMMENDATIONS ===" -ForegroundColor Green

Write-Host "`n1. IMMEDIATE CHECKS:" -ForegroundColor Cyan
Write-Host "   • Ensure EmailService is running on http://localhost:5000" -ForegroundColor White
Write-Host "   • Verify OTP is generated and stored in database" -ForegroundColor White
Write-Host "   • Check that the OTP code from email matches exactly" -ForegroundColor White
Write-Host "   • Ensure OTP hasn't expired (5-minute window)" -ForegroundColor White

Write-Host "`n2. DATABASE VERIFICATION:" -ForegroundColor Cyan
Write-Host "   • Check otp_tokens table for recent entries" -ForegroundColor White
Write-Host "   • Verify UserId matches session TEMP_USER_ID" -ForegroundColor White
Write-Host "   • Check Purpose is 'LOGIN' (uppercase)" -ForegroundColor White
Write-Host "   • Confirm Used = 0 and ExpiresAt > current time" -ForegroundColor White

Write-Host "`n3. TROUBLESHOOTING STEPS:" -ForegroundColor Cyan
Write-Host "   • Add logging to OTP validation process" -ForegroundColor White
Write-Host "   • Test with simple OTP validation without cache" -ForegroundColor White
Write-Host "   • Verify case sensitivity of Purpose field" -ForegroundColor White
Write-Host "   • Check for any database connection issues" -ForegroundColor White

Write-Host "`n4. COMMON CAUSES OF FAILURE:" -ForegroundColor Yellow
Write-Host "   ❌ OTP expired (5-minute limit)" -ForegroundColor Red
Write-Host "   ❌ OTP already used (marked as Used=1)" -ForegroundColor Red
Write-Host "   ❌ Purpose mismatch ('LOGIN' vs 'login')" -ForegroundColor Red
Write-Host "   ❌ UserId mismatch (session vs database)" -ForegroundColor Red
Write-Host "   ❌ Cache/database synchronization issues" -ForegroundColor Red
Write-Host "   ❌ EmailService not running or connection refused" -ForegroundColor Red

Write-Host "`nNext: Run this diagnostic, then check the specific failure point above!" -ForegroundColor Green
