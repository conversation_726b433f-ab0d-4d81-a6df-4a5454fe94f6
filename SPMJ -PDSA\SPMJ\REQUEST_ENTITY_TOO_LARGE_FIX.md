# Request Entity Too Large Fix - SPMJ PDSA

## Problem Description
Users were experiencing "request entity is too large" error when clicking 'pilih' (select) buttons in various maintenance pages, particularly in GridView controls across multiple pages like:
- PN_Negara.aspx
- PN_Tajaan.aspx  
- PN_Disiplin.aspx
- PN_Akademik.aspx
- PN_Ikhtisas.aspx
- And other maintenance pages with PILIH buttons

## Root Cause Analysis
The error occurred because ASP.NET's default request size limit (4MB) was being exceeded when:
1. **Large ViewState**: GridView controls with large datasets generate massive ViewState data
2. **Form Data Size**: Multiple controls and postback data exceeded the 4MB limit
3. **Missing Configuration**: No custom request size limits were configured in Web.config

## Solution Implemented

### 1. Web.config Changes
Added request size limit configurations in two sections:

#### A. System.Web Section (for IIS 6 and Cassini)
```xml
<system.web>
    <!-- Fix for "request entity is too large" error when clicking PILIH buttons -->
    <!-- Increase request size limits and ViewState limits -->
    <httpRuntime maxRequestLength="51200" requestLengthDiskThreshold="51200" executionTimeout="300" />
</system.web>
```

#### B. System.WebServer Section (for IIS 7+)
```xml
<system.webServer>
    <!-- Fix for IIS 7+ request size limits - matches the system.web settings -->
    <security>
        <requestFiltering>
            <requestLimits maxAllowedContentLength="52428800" />
        </requestFiltering>
    </security>
</system.webServer>
```

### 2. Configuration Details

| Setting | Old Value | New Value | Description |
|---------|-----------|-----------|-------------|
| maxRequestLength | 4096 KB (default) | 51200 KB (50MB) | Maximum request size for ASP.NET |
| requestLengthDiskThreshold | 256 KB (default) | 51200 KB (50MB) | Threshold for buffering to disk |
| executionTimeout | 90 seconds (default) | 300 seconds (5 min) | Maximum execution time |
| maxAllowedContentLength | 4096 KB (default) | 52428800 bytes (50MB) | IIS 7+ request limit |

### 3. Why These Values?
- **50MB Limit**: Generous enough to handle large ViewState and form data
- **Matching Values**: Both IIS 6 and IIS 7+ configurations set to same effective limit
- **Extended Timeout**: Allows for processing of larger requests
- **Safety Buffer**: maxAllowedContentLength slightly higher than maxRequestLength

## Alternative Solutions Considered

### Option 1: ViewState Optimization (Long-term)
- Disable ViewState on pages where not needed
- Use `EnableViewState="false"` on GridView controls
- Implement custom paging to reduce data size

### Option 2: AJAX UpdatePanels (Long-term)
- Use UpdatePanels to reduce full page postbacks
- Implement partial page updates for PILIH functionality

### Option 3: Client-Side Processing (Long-term)
- Move selection logic to JavaScript
- Use AJAX calls instead of full postbacks

## Testing Recommendations
1. **Test PILIH buttons** on all maintenance pages
2. **Verify large datasets** work correctly
3. **Monitor performance** with new timeout settings
4. **Check different browsers** for compatibility

## Deployment Steps
1. Backup current Web.config
2. Apply the Web.config changes
3. Restart IIS/Application Pool
4. Test functionality on key pages
5. Monitor application logs for any issues

## Monitoring and Maintenance
- **Watch for**: Actual request sizes in IIS logs
- **Monitor**: Application performance with larger timeout
- **Consider**: Future optimization of ViewState usage
- **Review**: Whether 50MB limit is appropriate based on usage patterns

## Files Modified
- `Web.config` - Added request size and timeout configurations

## Related Issues
This fix addresses the immediate "request entity is too large" error. The underlying ViewState size issue may benefit from future optimization work.

## Notes
- This is a configuration-based fix that increases limits
- No code changes were required
- Compatible with existing .NET 3.5 framework
- Works with both IIS 6 and IIS 7+ environments
