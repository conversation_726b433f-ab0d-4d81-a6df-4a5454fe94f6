﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm33
    Inherits System.Web.UI.Page

    Public x As String

    
    Public Sub Surat_Individu_Lulus()
        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 11pt;'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 11pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11pt; color:#151B8D;'> Our Ref &nbsp;:&nbsp; </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11pt; color:#000000;'> " + Session("mohon_no") + "</td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 11pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11pt; color:#151B8D;'> Date &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11pt; color:#000000;'> " + Tarikh(0) + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<div style='font-family: Arial; font-size: 11pt;'>"
        x += "<br/>" + StrConv(Session("tpc_nama"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("tpc_majikan"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("tpc_alamat"), VbStrConv.ProperCase)
        If Session("tpc_alamat1") = "" Or Session("tpc_alamat1") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("tpc_alamat1"), VbStrConv.ProperCase)
        If Session("tpc_alamat2") = "" Or Session("tpc_alamat2") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("tpc_alamat2"), VbStrConv.ProperCase)
        x += "<br/>" + Session("tpc_poskod") + " " + StrConv(Session("tpc_bandar"), VbStrConv.ProperCase) + ", " + StrConv(Session("tpc_negeri"), VbStrConv.ProperCase) + "."
        x += "</div>"
        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>Dear "
        If Session("tpc_jantina") = 1 Then x += "Sir," Else x += "Madam,"
        x += "</div>"
        x += "<br/><div style='font-family: Arial; font-size: 11pt;'><b>APPROVAL FOR TEMPORARY PRACTICING CERTIFICATE (TPC).</b></div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt; text-align:justify;' >Please be informed that your application for registration with Nursing Board Malaysia has been "
        x += "approved. A Temporary Practicing Certificate (TPC) shall be issued upon application to enable you to practice as "
        If Session("tpc_jenis") = 4 Then
            x += "<b>a Clinical Instructor</b>"
        ElseIf Session("tpc_jenis") = 3 Then
            x += "<b>a Nurse Educator</b>"
        Else
            x += "<b>a nurse</b>"
        End If
        x += " in " + StrConv(Session("majikan"), VbStrConv.ProperCase) + ". <b>However the issuance of registration and TPC does not permit you to practice Midwifery and Public Health / Community Health Nursing in Malaysia.</b></div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt; text-align:justify;'>"
        x += "2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;You are advised to apply for a TPC within <b> six (6) </b> months from the date of this letter "
        x += "through your employer. Failure to comply means this approval will automatically be revoked. This TPC "
        x += "is valid for a period of not exceeding <b>twelve (12)</b> months and you are required to renew your TPC <b>two</b> "
        x += "<b>(2)</b> months before the expiry date, failing which the Nursing Board Malaysia shall not process your renewal of TPC."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt; text-align:justify;'>"
        x += "3.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;On commencement of your employment in the "
        If Session("tpc_jenis") = 4 Or Session("tpc_jenis") = 3 Then x += "nursing college" Else x += "hospital"
        x += ", you have to undergo a mentoring programme for <b>3 months</b>"
        If Session("tpc_jenis") = 4 Or Session("tpc_jenis") = 3 Then x += "." Else x += " and complete the proficiency as in <b>attachment 1</b>."
        x += " A progress report from your employer shall be forwarded directly to the Secretary of Nursing Board Malaysia. "
        If Session("tpc_jantina") = 1 Then x += "<b>Please be aware that all male nurses will have to be chaperoned by a female personnel while performing procedure on female patients.</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt; text-align:justify;'>"
        x += "4.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attach herewith is an application form for Temporary Practicing Certificate (TPC).  "
        x += "On completion please return the form together with the processing payment of <b>RM 70.00</b> in the form of <b>Postal</b> "
        x += "<b>Order/Money Order/Bank Draft</b> made payable to the <b>Secretary, Nursing Board Malaysia.</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>Thank you,</div>"
        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>Yours sincerely,</div>"
        x += "<br/>"
        x += "<br/>"
        x += "<br/><div style='font-family: Arial; font-size: 11pt; text-align:left;'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ. ABD. WAHAB) </b>"
        x += "<br>Registrar,"
        x += "<br>Nursing Board Malaysia."
        x += "<br/>"
        x += "<br>cc - Human Resource Manager"
        x += "</div>"


        x += Footer_Surat()
        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select distinct NAMA, tp.NOKP as 'NO. KP/PASPORT', sm_alamat, sm_poskod, sm_bandar, dc_negeri, jantina, dc_amalan 'MAJIKAN', mohon_no, tp.j_daftar,pta.Alamat, pta.Alamat1, pta.Alamat2, pta.Poskod, pta.Bandar from tmp_tpc tp inner join tmp_tpc_majikan ttm on tp.nokp=ttm.nokp left outer join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan left outer join pn_negeri pn on pta.negeri=pn.id_negeri where " & X & " and keputusan_tkh is not null  order by nama"
        Tb = "tmp_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" And Tx_NoPd.Text.Trim = "" Then Exit Sub
        Cari("nama like '" & Tx_Nama.Text & "%' and tp.nokp like '" & Tx_NoKP.Text & "%'")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(6).Visible = False
        e.Row.Cells(7).Visible = False
        e.Row.Cells(8).Visible = False
        e.Row.Cells(9).Visible = False
        e.Row.Cells(10).Visible = False
        e.Row.Cells(11).Visible = False
        e.Row.Cells(12).Visible = False
        e.Row.Cells(13).Visible = False
        e.Row.Cells(14).Visible = False
        e.Row.Cells(15).Visible = False
        e.Row.Cells(16).Visible = False

        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("tpc_nama") = Gd.SelectedRow.Cells(2).Text
        Session("tpc_nokp") = Gd.SelectedRow.Cells(3).Text
        Session("tpc_jantina") = Gd.SelectedRow.Cells(8).Text        
        Session("majikan") = Gd.SelectedRow.Cells(9).Text
        Session("mohon_no") = Gd.SelectedRow.Cells(10).Text
        Session("tpc_jenis") = Gd.SelectedRow.Cells(11).Text
        Session("tpc_majikan") = Gd.SelectedRow.Cells(9).Text
        Session("tpc_alamat") = Gd.SelectedRow.Cells(12).Text
        Session("tpc_alamat1") = Gd.SelectedRow.Cells(13).Text
        Session("tpc_alamat2") = Gd.SelectedRow.Cells(14).Text
        Session("tpc_poskod") = Gd.SelectedRow.Cells(15).Text
        Session("tpc_bandar") = Gd.SelectedRow.Cells(16).Text
        Session("tpc_negeri") = Gd.SelectedRow.Cells(7).Text
        Surat_Individu_Lulus()
    End Sub
End Class