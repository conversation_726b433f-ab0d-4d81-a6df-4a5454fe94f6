﻿Imports iTextSharp.text.pdf
Imports iTextSharp.text
Imports System.Web

Public Class itsEvents
    Inherits PdfPageEventHelper

    Public Overrides Sub OnStartPage(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)
        Dim year As Integer
        'Dim name As String
        Dim beginDate As String
        Dim endDate As String

        'If Not (System.Web.HttpContext.Current.Session("RPT_Year") Is Nothing) And (System.Web.HttpContext.Current.Session("RPT_Processor") Is Nothing) Then

        If Not (System.Web.HttpContext.Current.Session("RPT_Year") Is Nothing) Then
            'Fixing passing value year to local varible 18062020 -OSH
            year = CInt(System.Web.HttpContext.Current.Session("RPT_Year"))
            'Add name of processor to report 18062020 - OSH
            'name = HttpContext.Current.Session("RPT_Processor")

            'Add dates for report 06072020 -OSH  
            beginDate = HttpContext.Current.Session("BEGIN_date")
            endDate = HttpContext.Current.Session("END_date")


            Dim ch As New Chunk("LAPORAN PENGELUARAN APC " & year.ToString & "   TEMPOH (" & beginDate.ToString & " SEHINGGA " & endDate.ToString & ")")
            'Dim ch2 As New Chunk("PENGELUAR " & name.ToString)
            'Add Improve Paragraph - Center 30102019 - OSH  
            Dim ph As New Phrase(ch)
            'Add 18062020 -OSH 
            'Dim ph2 As New Phrase(ch2)
            Dim p As New Paragraph()
            p.Add(ph)
            'Add 18062020 -OSH 
            'p.Add(ph2)
            p.SpacingBefore = 20
            p.SpacingAfter = 20
            p.Alignment = 1 'center
            document.Add(p)
            'document.Add(ch)

            'Add New Line 
            'Dim phrase1 As New Phrase(Environment.NewLine)
        End If
    End Sub

    'Public Overrides Sub OnEndPage(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)
    '    'Add Footer Paragraph - Center 30102019 - OSH 
    '    Dim ch As New Chunk("Mukasurat " & writer.PageNumber)
    '    Dim ph As New Phrase()
    '    Dim p As New Paragraph()
    '    ph.Add(ch)
    '    p.Add(ph)
    '    p.SpacingBefore = 20
    '    p.SpacingAfter = 20
    '    p.Alignment = 1 'center
    '    document.Add(p)

    'End Sub

  

End Class
