﻿Imports System.Data.OleDb
Imports System.Data.SqlClient
Partial Public Class P3_APC_Cari2
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)

        'Comment Original 10042020 - OSH 
        'SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.j_daftar=jp.j_daftar and jpa.ret = 0 and jpa.apc_tahun = year(getdate()) and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_tahun = year(getdate())+1 and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_tahun = year(getdate()) where jp.warganegara=1 and jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"
        'SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA'  from jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.apc_tahun = year(getdate()) where jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"


        'Fix query without cancel APC 10042020 - OSH 
        SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.j_daftar=jp.j_daftar and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.apc_tahun = year(getdate()) and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.apc_tahun = year(getdate())+1 and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_tahun = year(getdate()) where jp.warganegara=1 and jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"

        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Public Sub Memori()
        '***********************************
        '  Kena Tambah Variable untuk Id_PG
        '***********************************
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)

        'Comment Original 04012019 - OSH
        'SQL = "select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD'  from tmp_adv_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and ta.id_pg = '" & Session("Id_PG") & "' order by nama"
        SQL = " select * from ( select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD',  ta.cpd as 'CPD' from tmp_adv_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and jp.xm_jenis <> 'kpsl' and ta.id_pg = '" & Session("Id_PG") & "'
union
select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD' ,  ta.cpd as 'CPD' from tmp_adv_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and ta.id_pg = '" & Session("Id_PG") & "' inner join PN_KPSL_LANTIKAN_JB klj on jp.nokp= klj.nokp
union
select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD',  ta.cpd as 'CPD' from tmp_adv_apc ta inner join jt_penuh_old jp on ta.nokp = jp.nokp and jp.j_daftar = ta.j_daftar and ta.id_pg = '" & Session("Id_PG") & "' and ta.kpsl = '1' and ta.j_daftar = 2
union
select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD' ,  ta.cpd as 'CPD' from tmp_adv_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and jp.j_daftar = ta.j_daftar and ta.id_pg =  '" & Session("Id_PG") & "' and ta.kpsl = '1' and ta.j_daftar = 1
union 
select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD' ,  ta.cpd as 'CPD' from tmp_adv_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp  and ta.KPSL is null where ta.id_pg =  '" & Session("Id_PG") & "') a
order by a.Nama "

        Tb = "tmp_adv__apc"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd_Pilih.DataSource = List_Data.Tables(Tb)
        Gd_Pilih.DataBind()
        If Gd_Pilih.Rows.Count > 0 Then cmd_Proses.Visible = True Else cmd_Proses.Visible = False
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub
        Memori()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(1)
        e.Row.Cells(1).Width = Unit.Pixel(1)
        e.Row.Height = Unit.Pixel(1)
        e.Row.Cells(4).Width = Unit.Pixel(60)
        e.Row.Cells(5).Width = Unit.Pixel(60)
        e.Row.Cells(6).Width = Unit.Pixel(60)
        e.Row.Cells(7).Width = Unit.Pixel(50)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(2).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Cb_Jenis.SelectedIndex < 1 Then Exit Sub
        Cari("nama like '" & Tx_Nama.Text.Trim & "%' and jp.nokp like '" & Tx_NoKP.Text.Trim & "%' and nopd like '" & Tx_NoPd.Text.Trim & "%'")
    End Sub

    Private Sub Gd_Pilih_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_Pilih.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(1)
        e.Row.Cells(1).Width = Unit.Pixel(20)
        e.Row.Cells(3).Width = Unit.Pixel(50)
        e.Row.Cells(4).Width = Unit.Pixel(50)
        e.Row.VerticalAlign = VerticalAlign.Top
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
        e.Row.Cells(3).ForeColor = Drawing.Color.Maroon
        e.Row.Cells(3).Font.Bold = True
        e.Row.Font.Size = FontUnit.Point(7)

    End Sub

    Protected Sub Gd_Pilih_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd_Pilih.SelectedIndexChanged
        'If Msg(Me, "Padam Rekod Ini - " & Gd_Pilih.SelectedRow.Cells(3).Text & "?") = MsgBoxResult.Yes Then
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "delete tmp_adv_apc where nokp = '" & Gd_Pilih.SelectedRow.Cells(3).Text & "'"
        Cmd.ExecuteNonQuery()
        Cn.Close()
        Memori()
    End Sub

    Protected Sub cmd_Proses_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Proses.Click
        Response.Redirect("p3_apc_proses_j2.aspx")
    End Sub

    Protected Sub Button4_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$B") - InStr(sender.uniqueid, "$ctl") - 4)) - 2
        Session("PINDA") = True
        Session("NOKP") = Gd.Rows(i).Cells(4).Text
        Response.Redirect("P2_Penuh.aspx")
    End Sub

    Protected Sub Button3_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$B") - InStr(sender.uniqueid, "$ctl") - 4)) - 2

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Channel KPSL Nurses Custom Process 27062024 - OSH 
        'Cmd.CommandText = "select a.nokp, r.xm_jenis, w.Sektor from jt_penuh r left outer join pn_kpsl_lantikan_jb a on (r.nokp = a.nokp) inner join jt_penuh_apc l on (r.nokp = l.nokp) left outer join pn_tpt_amalan w on l.Id_Amalan = w.Id_Amalan where w.Sektor = '1'and l.ret = '0' and l.Apc_Batal = '0' and l.Apc_Tahun = YEAR(getdate())-1 and r.nama like '" & Tx_Nama.Text.Trim & "%' and r.nokp like '" & Gd.Rows(i).Cells(4).Text & "%' and r.nopd like '" & Tx_NoPd.Text.Trim & "%'"

        'Fix missing previous year bug - Ticket #294287 -30082024 - OSH 
        'Cmd.CommandText = "select top 1 a.nokp, r.xm_jenis, w.Sektor from jt_penuh r left outer join pn_kpsl_lantikan_jb a on (r.nokp = a.nokp) inner join jt_penuh_apc l on (r.nokp = l.nokp) left outer join pn_tpt_amalan w on l.Id_Amalan = w.Id_Amalan where w.Sektor = '1'and l.ret = '0' and l.Apc_Batal = '0' and l. non_ap is null and r.nama like '" & Tx_Nama.Text.Trim & "%' and r.nokp like '" & Gd.Rows(i).Cells(4).Text & "%' and r.nopd like '" & Tx_NoPd.Text.Trim & "%' order by cast(l.apc_tahun as int) desc"

        'Fix Blank Tray KPSL bug,  check tmp_avd_apc for match records 10042025 - OSH 
        If Cn.State <> ConnectionState.Open Then Cn.Open()
        Cmd.CommandText = "select * from tmp_adv_apc where nokp = '" & Gd.Rows(i).Cells(4).Text & "'"
        Rdr = Cmd.ExecuteReader
        If Rdr.Read Then
            Msg(Me, "Rekod telah wujud (KPSL JB/ JM)")
            Rdr.Close()
            Cn.Close()
        Else
            Rdr.Close()
            'Fix Half Listing 29102024 -OSH 
            Cmd.CommandText = "select top 1 a.nokp, r.xm_jenis, w.Sektor from jt_penuh r left outer join pn_kpsl_lantikan_jb a on (r.nokp = a.nokp) inner join jt_penuh_apc l on (r.nokp = l.nokp) left outer join pn_tpt_amalan w on l.Id_Amalan = w.Id_Amalan where l.ret = '0' and l.Apc_Batal = '0' and l. non_ap is null and r.nama like '" & Tx_Nama.Text.Trim & "%' and r.nokp like '" & Gd.Rows(i).Cells(4).Text & "%' and r.nopd like '" & Tx_NoPd.Text.Trim & "%' order by cast(l.apc_tahun as int) desc"

            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                'Comment Original 13122024 -OSH 
                'If Not IsDBNull(Rdr(1)) Then
                'Fixing null value error 13122014 - OSH 
                If Not IsDBNull(Rdr(1)) And Not IsDBNull(Rdr(2)) Then
                    If IsDBNull(Rdr(0)) And Rdr(1) = "KPSL" And Rdr(2) = "1" Then
                        Session("KPSL_IC") = Gd.Rows(i).Cells(4).Text.Trim 'IC
                        Response.Redirect("P3_APC_Lantik_KPSL.aspx")
                    End If
                End If
            End While
            Rdr.Close()
        End If

        'Check current or future APC exist before add tray 22052020 - OSH 
        Try
            Cmd.CommandText = "Select count (nokp) from jt_penuh_apc where ret = 0 and apc_batal = 0  and apc_tahun between year(getdate()) and Year(getdate()) + 1 And NoKP = '" & Gd.Rows(i).Cells(4).Text & "'"
            Rdr = Cmd.ExecuteReader
            If Rdr.Read And Rdr(0) = 2 Then
                Msg(Me, "APC Tahun Semasa and Hadapan Telah Wujud Atau Terdapat 2 APC Yang Aktif Pada Tahun Semasa !")
                Rdr.Close()
                Cn.Close()
            Else
                Rdr.Close() : Cn.Close()
                If Cn.State <> ConnectionState.Open Then Cn.Open()
                Cmd.CommandText = "select * from tmp_adv_apc where nokp = '" & Gd.Rows(i).Cells(4).Text & "'"
                Rdr = Cmd.ExecuteReader
                If Rdr.Read Then
                    Msg(Me, "Rekod telah wujud")
                    Rdr.Close()
                    Cn.Close()
                Else
                    Rdr.Close()
                    Cmd.CommandText = "insert tmp_adv_apc (id_pg, j_daftar, nokp) select '" & Session("Id_PG") & "', j_daftar, nokp from jt_penuh where nokp =  '" & Gd.Rows(i).Cells(4).Text & "'"
                    Cmd.ExecuteNonQuery()
                    Cn.Close()
                End If
            End If
            Memori()
        Catch ex As Exception
            Cn.Close()
            Msg(Me, ex.Message)
        End Try
    End Sub
End Class
