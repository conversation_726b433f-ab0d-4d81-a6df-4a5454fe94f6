# FORCE PASSWORD CHANGE - SYNTAX VERIFICATION SCRIPT
# Verifies all syntax fixes in ForcePasswordChange.aspx.vb

Write-Host "🔍 FORCE PASSWORD CHANGE SYNTAX VERIFICATION" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

$projectPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ"
$vbcPath = "C:\Windows\Microsoft.NET\Framework\v3.5\vbc.exe"

Write-Host "`n📁 Project Path: $projectPath" -ForegroundColor Yellow

# Check if all required files exist
$requiredFiles = @(
    "ForcePasswordChange.aspx.vb",
    "ForcePasswordChange.aspx", 
    "ForcePasswordChange.aspx.designer.vb",
    "PasswordHelper.vb",
    "EmailServiceClient.vb"
)

Write-Host "`n✅ CHECKING REQUIRED FILES..." -ForegroundColor Green
foreach ($file in $requiredFiles) {
    $filePath = Join-Path $projectPath $file
    if (Test-Path $filePath) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file - MISSING!" -ForegroundColor Red
    }
}

# Test compilation
Write-Host "`n🔨 TESTING COMPILATION..." -ForegroundColor Green
try {
    Set-Location $projectPath
    
    $compileArgs = @(
        "/target:library",
        "/reference:System.dll,System.Web.dll,System.Data.dll,System.Configuration.dll",
        "ForcePasswordChange.aspx.vb",
        "PasswordHelper.vb", 
        "EmailServiceClient.vb"
    )
    
    Write-Host "  Command: vbc.exe $($compileArgs -join ' ')" -ForegroundColor Gray
    
    $result = & $vbcPath $compileArgs 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ COMPILATION SUCCESSFUL!" -ForegroundColor Green
        
        # Check for warnings
        $warnings = $result | Where-Object { $_ -like "*warning*" }
        if ($warnings) {
            Write-Host "  ⚠️  Warnings (non-critical):" -ForegroundColor Yellow
            foreach ($warning in $warnings) {
                Write-Host "    $warning" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ✅ NO WARNINGS!" -ForegroundColor Green
        }
    } else {
        Write-Host "  ❌ COMPILATION FAILED!" -ForegroundColor Red
        Write-Host "$result" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Error running compiler: $_" -ForegroundColor Red
}

# Check syntax elements
Write-Host "`n🔍 CHECKING SYNTAX ELEMENTS..." -ForegroundColor Green

$codeContent = Get-Content "$projectPath\ForcePasswordChange.aspx.vb" -Raw

# Check for proper function closing
$functionMatches = [regex]::Matches($codeContent, "Private Function|Public Function|Protected Function")
$endFunctionMatches = [regex]::Matches($codeContent, "End Function")

Write-Host "  Functions declared: $($functionMatches.Count)" -ForegroundColor Gray
Write-Host "  End Function statements: $($endFunctionMatches.Count)" -ForegroundColor Gray

if ($functionMatches.Count -eq $endFunctionMatches.Count) {
    Write-Host "  ✅ All functions properly closed" -ForegroundColor Green
} else {
    Write-Host "  ❌ Function/End Function mismatch!" -ForegroundColor Red
}

# Check for proper imports
$requiredImports = @(
    "Imports System.Data.OleDb",
    "Imports System.Data", 
    "Imports System.Text.RegularExpressions",
    "Imports System.Configuration",
    "Imports System.Collections.Generic"
)

Write-Host "`n  📋 Required Imports:" -ForegroundColor Gray
foreach ($import in $requiredImports) {
    if ($codeContent.Contains($import)) {
        Write-Host "    ✅ $import" -ForegroundColor Green
    } else {
        Write-Host "    ❌ $import - MISSING!" -ForegroundColor Red
    }
}

# Check for control declarations
$requiredControls = @(
    "txtNewPassword As System.Web.UI.WebControls.TextBox",
    "txtConfirmPassword As System.Web.UI.WebControls.TextBox", 
    "btnChangePassword As System.Web.UI.WebControls.Button",
    "lblMessage As System.Web.UI.WebControls.Label",
    "pnlMessage As System.Web.UI.WebControls.Panel",
    "divMessage As System.Web.UI.HtmlControls.HtmlGenericControl"
)

Write-Host "`n  🎛️  Control Declarations:" -ForegroundColor Gray
foreach ($control in $requiredControls) {
    if ($codeContent.Contains($control)) {
        Write-Host "    ✅ $($control.Split(' ')[0])" -ForegroundColor Green
    } else {
        Write-Host "    ❌ $($control.Split(' ')[0]) - MISSING!" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n📊 VERIFICATION SUMMARY" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SYNTAX VERIFICATION PASSED!" -ForegroundColor Green
    Write-Host "   - All required files present" -ForegroundColor Green
    Write-Host "   - Compilation successful" -ForegroundColor Green  
    Write-Host "   - All syntax elements correct" -ForegroundColor Green
    Write-Host "   - Ready for runtime testing" -ForegroundColor Green
} else {
    Write-Host "❌ SYNTAX VERIFICATION FAILED!" -ForegroundColor Red
    Write-Host "   - Check compilation errors above" -ForegroundColor Red
}

Write-Host "`nVerification completed at $(Get-Date)" -ForegroundColor Gray
