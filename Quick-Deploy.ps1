# SPMJ Email Service - Quick Production Deployment Script
# This script performs the final deployment steps for production

param(
    [Parameter(Mandatory=$false)]
    [string]$DatabaseServer = "localhost",
    
    [Parameter(Mandatory=$false)]
    [string]$DatabaseName = "SPMJ_PDSA",
    
    [Parameter(Mandatory=$false)]
    [string]$EmailServicePort = "5000",
    
    [Parameter(Mandatory=$false)]
    [string]$SPMJUrl = "http://localhost:8080",
    
    [Parameter(Mandatory=$false)]
    [string]$SmtpServer = "",
    
    [Parameter(Mandatory=$false)]
    [string]$SmtpUsername = "",
    
    [Parameter(Mandatory=$false)]
    [string]$SmtpPassword = "",
    
    [Parameter(Mandatory=$false)]
    [string]$FromEmail = "",
    
    [switch]$SkipDatabase,
    [switch]$ConfigureEmail,
    [switch]$TestOnly,
    [switch]$ProductionMode
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 SPMJ Email Service - Quick Deployment" -ForegroundColor Green
Write-Host "=" * 50

$projectRoot = "d:\source_code\.NET 3.5. - O\SPMJ -PDSA"
$emailServicePath = "$projectRoot\SPMJ.EmailService"

# Function to check prerequisites
function Test-Prerequisites {
    Write-Host "`n📋 Checking Prerequisites..." -ForegroundColor Yellow
    
    $checks = @()
    
    # Check .NET 9
    try {
        $dotnetVersion = dotnet --version
        if ($dotnetVersion -like "9.*") {
            Write-Host "✅ .NET 9 SDK found: $dotnetVersion" -ForegroundColor Green
            $checks += $true
        } else {
            Write-Host "❌ .NET 9 SDK required, found: $dotnetVersion" -ForegroundColor Red
            $checks += $false
        }
    } catch {
        Write-Host "❌ .NET SDK not found" -ForegroundColor Red
        $checks += $false
    }
    
    # Check SQL Server connectivity
    if (-not $SkipDatabase) {
        try {
            $connectionString = "Server=$DatabaseServer;Database=master;Integrated Security=true"
            $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
            $connection.Open()
            $connection.Close()
            Write-Host "✅ SQL Server accessible: $DatabaseServer" -ForegroundColor Green
            $checks += $true
        } catch {
            Write-Host "❌ Cannot connect to SQL Server: $DatabaseServer" -ForegroundColor Red
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
            $checks += $false
        }
    }
    
    # Check project files
    if (Test-Path "$emailServicePath\Program.cs") {
        Write-Host "✅ Email Service project found" -ForegroundColor Green
        $checks += $true
    } else {
        Write-Host "❌ Email Service project not found" -ForegroundColor Red
        $checks += $false
    }
    
    if (Test-Path "$projectRoot\SPMJ\EmailServiceClient.vb") {
        Write-Host "✅ SPMJ integration files found" -ForegroundColor Green
        $checks += $true
    } else {
        Write-Host "❌ SPMJ integration files not found" -ForegroundColor Red
        $checks += $false
    }
    
    $allPassed = $checks -notcontains $false
    
    if ($allPassed) {
        Write-Host "`n✅ All prerequisites met!" -ForegroundColor Green
    } else {
        Write-Host "`n❌ Prerequisites not met. Please fix the issues above." -ForegroundColor Red
        exit 1
    }
    
    return $allPassed
}

# Function to deploy database changes
function Deploy-Database {
    if ($SkipDatabase) {
        Write-Host "`n⏭️  Skipping database deployment..." -ForegroundColor Yellow
        return
    }
    
    Write-Host "`n🗄️  Deploying Database Changes..." -ForegroundColor Yellow
    
    $migrationScript = "$emailServicePath\Database_EmailService_Migration.sql"
    
    if (-not (Test-Path $migrationScript)) {
        Write-Host "❌ Migration script not found: $migrationScript" -ForegroundColor Red
        return $false
    }
    
    try {
        $connectionString = "Server=$DatabaseServer;Database=$DatabaseName;Integrated Security=true"
        
        Write-Host "   Executing migration script..." -ForegroundColor Gray
        
        # Read and execute SQL script
        $sqlContent = Get-Content $migrationScript -Raw
        
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $sqlContent
        $command.ExecuteNonQuery()
        
        $connection.Close()
        
        Write-Host "✅ Database migration completed successfully" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Database migration failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to configure email settings
function Configure-EmailSettings {
    Write-Host "`n📧 Configuring Email Settings..." -ForegroundColor Yellow
    
    $appSettingsPath = "$emailServicePath\appsettings.json"
    
    if (-not (Test-Path $appSettingsPath)) {
        Write-Host "❌ appsettings.json not found" -ForegroundColor Red
        return $false
    }
    
    try {
        $config = Get-Content $appSettingsPath | ConvertFrom-Json
        
        # Update connection string
        $config.ConnectionStrings.DefaultConnection = "Server=$DatabaseServer;Database=$DatabaseName;Integrated Security=true"
        
        # Update email settings if provided
        if ($ConfigureEmail -and $SmtpServer -and $SmtpUsername -and $SmtpPassword) {
            $config.EmailSettings.SmtpServer = $SmtpServer
            $config.EmailSettings.Username = $SmtpUsername
            $config.EmailSettings.Password = $SmtpPassword
            $config.EmailSettings.FromEmail = if ($FromEmail) { $FromEmail } else { $SmtpUsername }
            
            Write-Host "✅ Email SMTP settings updated" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Email settings not updated (use -ConfigureEmail with SMTP parameters)" -ForegroundColor Yellow
        }
        
        # Update password reset base URL
        $config.PasswordResetSettings.BaseUrl = $SPMJUrl
        
        # Save updated configuration
        $config | ConvertTo-Json -Depth 10 | Set-Content $appSettingsPath
        
        Write-Host "✅ Configuration updated successfully" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Failed to update configuration: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to build and test email service
function Build-EmailService {
    Write-Host "`n🔨 Building Email Service..." -ForegroundColor Yellow
    
    try {
        Push-Location $emailServicePath
        
        Write-Host "   Restoring packages..." -ForegroundColor Gray
        dotnet restore | Out-Null
        
        Write-Host "   Building project..." -ForegroundColor Gray
        $buildOutput = dotnet build --configuration Release 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Build completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Build failed:" -ForegroundColor Red
            Write-Host $buildOutput -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Build error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    } finally {
        Pop-Location
    }
}

# Function to start email service
function Start-EmailService {
    Write-Host "`n🚀 Starting Email Service..." -ForegroundColor Yellow
    
    try {
        Push-Location $emailServicePath
        
        if ($ProductionMode) {
            Write-Host "   Starting in production mode on port $EmailServicePort..." -ForegroundColor Gray
            $env:ASPNETCORE_ENVIRONMENT = "Production"
        } else {
            Write-Host "   Starting in development mode on port $EmailServicePort..." -ForegroundColor Gray
            $env:ASPNETCORE_ENVIRONMENT = "Development"
        }
        
        $env:ASPNETCORE_URLS = "http://localhost:$EmailServicePort"
        
        # Start service in background
        $job = Start-Job -ScriptBlock {
            param($servicePath, $port, $environment)
            Set-Location $servicePath
            $env:ASPNETCORE_URLS = "http://localhost:$port"
            $env:ASPNETCORE_ENVIRONMENT = $environment
            dotnet run --configuration Release
        } -ArgumentList $emailServicePath, $EmailServicePort, $env:ASPNETCORE_ENVIRONMENT
        
        # Wait for service to start
        Start-Sleep -Seconds 10
        
        # Test if service is running
        try {
            $healthUrl = "http://localhost:$EmailServicePort/health"
            $response = Invoke-RestMethod -Uri $healthUrl -Method Get -TimeoutSec 5
            
            if ($response.status -eq "healthy") {
                Write-Host "✅ Email Service started successfully" -ForegroundColor Green
                Write-Host "   Service URL: http://localhost:$EmailServicePort" -ForegroundColor Cyan
                Write-Host "   Swagger UI: http://localhost:$EmailServicePort/swagger" -ForegroundColor Cyan
                Write-Host "   Job ID: $($job.Id) (use Stop-Job $($job.Id) to stop)" -ForegroundColor Gray
                return $true
            } else {
                Write-Host "❌ Service started but not healthy" -ForegroundColor Red
                return $false
            }
        } catch {
            Write-Host "❌ Service failed to start or not responding" -ForegroundColor Red
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Failed to start service: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    } finally {
        Pop-Location
    }
}

# Function to test integration
function Test-Integration {
    Write-Host "`n🧪 Testing Integration..." -ForegroundColor Yellow
    
    $baseUrl = "http://localhost:$EmailServicePort"
    $testsPassed = 0
    $totalTests = 0
    
    # Test health endpoint
    $totalTests++
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get -TimeoutSec 5
        if ($response.status -eq "healthy") {
            Write-Host "✅ Health check passed" -ForegroundColor Green
            $testsPassed++
        } else {
            Write-Host "❌ Health check failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Health check failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test password reset API
    $totalTests++
    try {
        $body = @{ UserId = "test-user"; Email = "<EMAIL>" } | ConvertTo-Json
        $response = Invoke-RestMethod -Uri "$baseUrl/api/password/reset/request" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 5
        
        # Expecting "User not found" response, which means API is working
        if ($response.message -like "*tidak dijumpai*") {
            Write-Host "✅ Password reset API responding correctly" -ForegroundColor Green
            $testsPassed++
        } else {
            Write-Host "❌ Password reset API unexpected response" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Password reset API test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test OTP API
    $totalTests++
    try {
        $body = @{ UserId = "test-user"; Email = "<EMAIL>"; Purpose = "LOGIN" } | ConvertTo-Json
        $response = Invoke-RestMethod -Uri "$baseUrl/api/otp/generate" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 5
        
        # Expecting "User not found" response, which means API is working
        if ($response.message -like "*tidak dijumpai*") {
            Write-Host "✅ OTP API responding correctly" -ForegroundColor Green
            $testsPassed++
        } else {
            Write-Host "❌ OTP API unexpected response" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ OTP API test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Summary
    Write-Host "`n📊 Test Results: $testsPassed/$totalTests tests passed" -ForegroundColor Cyan
    
    if ($testsPassed -eq $totalTests) {
        Write-Host "🎉 All integration tests passed!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠️  Some tests failed. Check the service logs." -ForegroundColor Yellow
        return $false
    }
}

# Function to show next steps
function Show-NextSteps {
    Write-Host "`n📋 Next Steps for Production:" -ForegroundColor Cyan
    Write-Host "=" * 30
    
    Write-Host "1. 📧 Configure Email Provider" -ForegroundColor Yellow
    if (-not $ConfigureEmail -or -not $SmtpServer) {
        Write-Host "   Run again with email configuration:" -ForegroundColor White
        Write-Host "   .\Quick-Deploy.ps1 -ConfigureEmail -SmtpServer smtp.gmail.com -SmtpUsername <EMAIL> -SmtpPassword your-app-password" -ForegroundColor Gray
    } else {
        Write-Host "   ✅ Email configuration completed" -ForegroundColor Green
    }
    
    Write-Host "`n2. 👥 Update User Email Addresses" -ForegroundColor Yellow
    Write-Host "   Update the pn_pengguna table with valid email addresses:" -ForegroundColor White
    Write-Host "   UPDATE pn_pengguna SET email = '<EMAIL>' WHERE userid = 'username'" -ForegroundColor Gray
    
    Write-Host "`n3. 🌐 Deploy to Production Server" -ForegroundColor Yellow
    Write-Host "   Copy the email service to your production server and configure as Windows Service" -ForegroundColor White
    
    Write-Host "`n4. 🔧 Update SPMJ Web.config" -ForegroundColor Yellow
    Write-Host "   Update the EmailServiceUrl in Web.config to point to production service" -ForegroundColor White
    
    Write-Host "`n5. ✅ End-to-End Testing" -ForegroundColor Yellow
    Write-Host "   Test the complete flow with real users and email addresses" -ForegroundColor White
    
    Write-Host "`n🎯 Access Points:" -ForegroundColor Cyan
    Write-Host "   • Email Service: http://localhost:$EmailServicePort" -ForegroundColor White
    Write-Host "   • API Documentation: http://localhost:$EmailServicePort/swagger" -ForegroundColor White
    Write-Host "   • SPMJ Application: $SPMJUrl" -ForegroundColor White
}

# Main execution
try {
    # Check if running in test mode
    if ($TestOnly) {
        Write-Host "🧪 Running in TEST MODE only" -ForegroundColor Cyan
        Test-Prerequisites
        Test-Integration
        exit 0
    }
    
    # Run deployment steps
    $success = $true
    
    $success = $success -and (Test-Prerequisites)
    if (-not $success) { exit 1 }
    
    $success = $success -and (Deploy-Database)
    if (-not $success) { exit 1 }
    
    $success = $success -and (Configure-EmailSettings)
    if (-not $success) { exit 1 }
    
    $success = $success -and (Build-EmailService)
    if (-not $success) { exit 1 }
    
    $success = $success -and (Start-EmailService)
    if (-not $success) { exit 1 }
    
    $success = $success -and (Test-Integration)
    
    if ($success) {
        Write-Host "`n🎉 DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
        Write-Host "The SPMJ Email Service is now running and ready for use." -ForegroundColor Green
    } else {
        Write-Host "`n⚠️  DEPLOYMENT COMPLETED WITH WARNINGS" -ForegroundColor Yellow
        Write-Host "Please review the issues above and complete the remaining steps." -ForegroundColor Yellow
    }
    
    Show-NextSteps
    
} catch {
    Write-Host "`n❌ DEPLOYMENT FAILED: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
