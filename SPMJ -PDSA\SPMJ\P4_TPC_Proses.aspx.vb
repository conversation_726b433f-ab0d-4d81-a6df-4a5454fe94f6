﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm27
    Inherits System.Web.UI.Page

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Public Sub Isi_TPC()
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter("", Cn)
        Dim SQL As String

        If Tx_NoPd.Text = "(BELUM BERDAFTAR)" Then
            SQL = "select dc_amalan as 'MAJIKAN', cast(tempoh_a as varchar(3))+ ' ' + tempoh_b as 'TEMPOH', tpt_amalan, tpt_amalan, '' as 'STATUS', '4' as ' ', '' as 'TAHUN', '' as 'NO. SIRI', '' as 'TARIKH TPC', '' as 'NO. RESIT', '' as 'TARIKH RESIT', '' as 'TARIKH TAMAT' from tmp_tpc_majikan ttm inner join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where nokp = '" & Session("NOKP") & "'"
            List_Adp.SelectCommand.CommandText = SQL
            List_Adp.Fill(List_Data, "tmp_tpc_majikan")
            Gd.DataSource = List_Data.Tables("tmp_tpc_majikan")
        Else
            SQL = "select dc_amalan as 'MAJIKAN', cast(tempoh_a as varchar(3))+ ' ' + tempoh_b as 'TEMPOH', id, tpt_amalan, case status when 0 then 'TAMAT' when 1 then 'AKTIF' end as 'STATUS', '4' as ' ', tpc_tahun as 'TAHUN', stuff('0000',5-len(tpc_no),len(tpc_no),tpc_no) as 'NO. SIRI', CONVERT(varchar(12), tpc_tkh, 103) as 'TARIKH TPC', tpc_noresit as 'NO. RESIT', CONVERT(varchar(12), tpc_tkhresit, 103) as 'TARIKH RESIT',CONVERT(varchar(12), tkh_tamat, 103) as 'TARIKH TAMAT' from jt_tpc_majikan jtm left outer join jt_tpc_tpc jtt on jtm.nokp=jtt.nokp and jtt.id_amalan=jtm.tpt_amalan left outer join pn_tpt_amalan pta on jtm.tpt_amalan = pta.id_amalan where jtm.nokp = '" & Session("NOKP") & "' order by jtm.id"
            List_Adp.SelectCommand.CommandText = SQL
            List_Adp.Fill(List_Data, "jt_tpc_majikan")
            Gd.DataSource = List_Data.Tables("jt_tpc_majikan")
        End If
        Gd.DataBind()

        Dim i, j As Int16
        j = 1
        For i = 0 To Gd.Rows.Count - 1
            If i = 0 Then
                Gd.Rows.Item(i).Cells(0).Text = j & "."
                j += 1
            Else
                If Gd.Rows.Item(i).Cells(1).Text = Gd.Rows.Item(i - 1).Cells(1).Text Then
                    Gd.Rows.Item(i).Cells(2).Text = ""
                    Gd.Rows.Item(i).Cells(0).Text = ""
                    Gd.Rows.Item(i).Cells(1).ForeColor = Drawing.Color.White
                Else
                    Gd.Rows.Item(i).Cells(0).Text = j & "."
                    j += 1
                End If
            End If
        Next
    End Sub

    Public Sub Daftar_TPC()
        If Tx_Tkh_Mohon.Text = "" Then Tx_Tkh_Mohon.Focus() : Exit Sub
        If Tx_Tkh.Text = "" Then Tx_Tkh.Focus() : Exit Sub
        If Tx_NoResit.Text = "" Then Tx_NoResit.Focus() : Exit Sub
        If Tx_TkhResit.Text = "" Then Tx_TkhResit.Focus() : Exit Sub
        If Not IsNumeric(Tx_Amaun.Text) Then Tx_Amaun.Focus() : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        SQL = ""
        If Tx_NoPd.Text = "" Then
            SQL += "update tmp_tpc set nopd = (select tpc+1 from pn_nopd), tkh_daftar = getdate() where nokp = '" & Tx_NoKP.Text & "'; "
            SQL += "update pn_nopd set tpc = tpc+1; "
        Else
            SQL += "update tmp_tpc set nopd = " & Tx_NoTPC.Text.Trim & ", tkh_daftar = getdate() where nokp = '" & Tx_NoKP.Text & "'; "
        End If
        SQL += "insert jt_tpc (J_Daftar,NoKP,Nama,NoPd,Tkh_Daftar,Warganegara,Taraf_Warganegara,Tkh_Lahir,Tpt_Lahir,Jantina,Umur,TP_Alamat,TP_Poskod,TP_Bandar,TP_Negeri,SM_Alamat,SM_Poskod,SM_Bandar,SM_Negeri,Emel,T_Kahwin,Id_Kolej,TKh_Latihan_Tamat,LP_Nama,LP_NoPd,LP_TkhSah,Mohon_No,Mohon_Tkh,Mohon_TkhResit,Mohon_NoResit,ss1,ss2,ss3,ss4,ss5,ss6,ss7,ss8,ss9,ss10,ss11,ss12,ss13,ss14,ss15,ss16,Keputusan_Msy,Keputusan_Tkh,Keputusan_Tkh_Tamat,Log_Status,Log_Catatan,Log_Id,Log_Tkh) select J_Daftar,NoKP,Nama,NoPd,Tkh_Daftar,Warganegara,Taraf_Warganegara,Tkh_Lahir,Tpt_Lahir,Jantina,Umur,TP_Alamat,TP_Poskod,TP_Bandar,TP_Negeri,SM_Alamat,SM_Poskod,SM_Bandar,SM_Negeri,Emel,T_Kahwin,Id_Kolej,TKh_Latihan_Tamat,LP_Nama,LP_NoPd,LP_TkhSah,Mohon_No,Mohon_Tkh,Mohon_TkhResit,Mohon_NoResit,ss1,ss2,ss3,ss4,ss5,ss6,ss7,ss8,ss9,ss10,ss11,ss12,ss13,ss14,ss15,ss16,Keputusan_Msy,Keputusan_Tkh,Keputusan_Tkh_Tamat,Log_Status,Log_Catatan,Log_Id,Log_Tkh from tmp_tpc where nokp = '" & Tx_NoKP.Text & "' and nopd is not null; "
        SQL += "insert jt_tpc_kelayakan select * from tmp_tpc_kelayakan where nokp = '" & Tx_NoKP.Text & "'; "
        SQL += "insert jt_tpc_pengalaman select * from tmp_tpc_pengalaman where nokp = '" & Tx_NoKP.Text & "'; "
        SQL += "insert jt_tpc_majikan (nokp,tpt_amalan,tempoh_a,tempoh_b,tkh_tamat,status) select *, 1 from tmp_tpc_majikan where nokp = '" & Tx_NoKP.Text & "'; "

        SQL += "insert jt_tpc_tpc (j_daftar, nokp, tpc_tahun, tpc_tkh_mohon, tpc_tkh, tpc_tkhresit, tpc_noresit, tpc_amaun, tpc_lwt_noresit, tpc_lwt_tkhresit, tpc_lwt_amaun, id_amalan, log_id, log_tkh) select " & _
            "" & Session("DAFTAR") & "," & _
            "'" & Tx_NoKP.Text.Trim & "'," & _
            "year(getdate())," & _
            "" & Chk_Tkh(Tx_Tkh_Mohon.Text) & "," & _
            "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
            "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
            "'" & Tx_NoResit.Text & "'," & _
            "" & CInt(Tx_Amaun.Text) & "," & _
            "null," & _
            "null," & _
            "null," & _
            "" & CInt(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(4).Text.Trim) & "," & _
            "'" & Session("Id_PG") & "', getdate(); "
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            SQL = "delete from tmp_tpc where nokp = '" & Tx_NoKP.Text & "';"
            SQL += "delete from tmp_tpc_kelayakan where nokp = '" & Tx_NoKP.Text & "';"
            SQL += "delete from tmp_tpc_majikan where nokp = '" & Tx_NoKP.Text & "';"
            SQL += "delete from tmp_tpc_pengalaman where nokp = '" & Tx_NoKP.Text & "';"
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Session("Msg_Tajuk") = "Pembaharuan TPC"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx", False)
        Catch ex As Exception
            Cn.Close()
            Session("Msg_Tajuk") = "Pembaharuan TPC"
            Session("Msg_Isi") = ex.Message
            Response.Redirect("p0_Mesej.aspx", False)
            'Msg(Me, ex.Message)
            Exit Sub
        End Try
        
    End Sub

    Public Sub Isi_SSemak()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select * from jt_tpc_tpc_ss where nokp = '" & Session("NOKP") & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr("ss1")) Then If Rdr("ss1") = 1 Then chk_Semak.Items(0).Selected = True
            If Not IsDBNull(Rdr("ss2")) Then If Rdr("ss2") = 1 Then chk_Semak.Items(1).Selected = True
            If Not IsDBNull(Rdr("ss3")) Then If Rdr("ss3") = 1 Then chk_Semak.Items(2).Selected = True
            If Not IsDBNull(Rdr("ss4")) Then If Rdr("ss4") = 1 Then chk_Semak.Items(3).Selected = True
            If Not IsDBNull(Rdr("ss5")) Then If Rdr("ss5") = 1 Then chk_Semak.Items(4).Selected = True
            If Not IsDBNull(Rdr("ss6")) Then If Rdr("ss6") = 1 Then chk_Semak.Items(5).Selected = True
            If Not IsDBNull(Rdr("ss7")) Then If Rdr("ss7") = 1 Then chk_Semak.Items(6).Selected = True
            If Not IsDBNull(Rdr("ss8")) Then If Rdr("ss8") = 1 Then chk_Semak.Items(7).Selected = True
        End If
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Tx_Nama.Text = Session("NAMA")
        Tx_NoKP.Text = Session("NOKP")
        Tx_NoPd.Text = Session("NOPD")

        If Tx_NoPd.Text = "(BELUM BERDAFTAR)" Then
            Panel1.Visible = True
        End If

        Isi_TPC()
        Isi_SSemak()
    End Sub

    Protected Sub cmd_Simpan_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Simpan.Click
        If Tx_NoPd.Text = "(BELUM BERDAFTAR)" Then
            Daftar_TPC()
            Exit Sub
        End If

        If Tx_Tkh_Mohon.Text = "" Then Tx_Tkh_Mohon.Focus() : Exit Sub
        If Tx_Tkh.Text = "" Then Tx_Tkh.Focus() : Exit Sub
        If Tx_NoResit.Text = "" Then Tx_NoResit.Focus() : Exit Sub
        If Tx_TkhResit.Text = "" Then Tx_TkhResit.Focus() : Exit Sub
        If Not IsNumeric(Tx_Amaun.Text) Then Tx_Amaun.Focus() : Exit Sub

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim SQL As String = ""
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        SQL += "insert jt_tpc_tpc (j_daftar, nokp, tpc_tahun, tpc_tkh_mohon, tpc_tkh, tpc_tkhresit, tpc_noresit, tpc_amaun, tpc_lwt_noresit, tpc_lwt_tkhresit, tpc_lwt_amaun, id_amalan, log_id, log_tkh) select " & _
                    "" & Session("DAFTAR") & "," & _
                    "'" & Tx_NoKP.Text.Trim & "'," & _
                    "year(getdate())," & _
                    "" & Chk_Tkh(Tx_Tkh_Mohon.Text) & "," & _
                    "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                    "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                    "'" & Tx_NoResit.Text & "'," & _
                    "" & CInt(Tx_Amaun.Text) & "," & _
                    "null," & _
                    "null," & _
                    "null," & _
                    "" & CInt(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(4).Text.Trim) & "," & _
                    "'" & Session("Id_PG") & "', getdate(); "
        'SQL += "update pn_noapc set tpc=tpc+1 where tahun=year(getdate()); "

        If CStr(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(12).Text.Trim) = "&nbsp;" Then
        Else
            SQL += "update jt_tpc_tpc set tpc_tmt=" & Chk_Tkh(CStr(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(12).Text.Trim)) & " where nokp='" & Tx_NoKP.Text.Trim & "' and tpc_tkh=" & Chk_Tkh(Tx_Tkh.Text) & "; "
        End If

        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            'cmd_Simpan.Visible = False
            'Isi_TPC()
            Session("Msg_Tajuk") = "Pembaharuan TPC"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx", False)
            'Msg(Me, "Rekod Telah Dikemaskini...")
        Catch ex As Exception
            Cn.Close()
            Session("Msg_Tajuk") = "Pembaharuan TPC"
            Session("Msg_Isi") = ex.Message
            Response.Redirect("p0_Mesej.aspx")
        End Try
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(0).Width = Unit.Pixel(50)
        'e.Row.Cells(5).Width = Unit.Pixel(50)
        'e.Row.Cells(0).Visible = False
        'e.Row.Cells(2).Visible = False
        e.Row.Cells(3).Visible = False
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(8).Visible = False
        e.Row.Cells(12).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Height = Unit.Pixel(15)
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
        e.Row.Cells(6).Font.Name = "Webdings"
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        If e.Row.Cells(5).Text = "TAMAT" Then e.Row.ForeColor = Drawing.Color.Firebrick
    End Sub

    Protected Sub cmd_Simpan0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Simpan0.Click
        Dim SQL As String = ""
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        SQL = "delete from jt_tpc_tpc_ss where nokp = '" & Tx_NoKP.Text.Trim & "'"
        SQL += "insert jt_tpc_tpc_ss (nokp, ss1, ss2 , ss3, ss4, ss5, ss6, ss7, ss8) values (" & _
                "'" & Tx_NoKP.Text.Trim & "'," & _
                "" & SSemak(0) & "," & _
                "" & SSemak(1) & "," & _
                "" & SSemak(2) & "," & _
                "" & SSemak(3) & "," & _
                "" & SSemak(4) & "," & _
                "" & SSemak(5) & "," & _
                "" & SSemak(6) & "," & _
                "" & SSemak(7) & ")"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Senarai Semak Telah Dikemaskini..")
        Catch ex As Exception
            Msg(Me, "Error!")
        End Try
    End Sub

End Class