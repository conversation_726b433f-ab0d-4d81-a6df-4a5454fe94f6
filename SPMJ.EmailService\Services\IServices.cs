using SPMJ.EmailService.Models;

namespace SPMJ.EmailService.Services;

public interface IEmailService
{
    Task<bool> SendPasswordResetEmailAsync(string email, string userName, string resetToken, string? baseUrl = null);
    Task<bool> SendPasswordResetEmailAsync(string email, string userName, string tempPassword);
    Task<bool> SendAdminPasswordEmailAsync(string email, string userName, string tempPassword, string adminName);
    Task<bool> SendOtpEmailAsync(string email, string userName, string otpCode, string purpose);
    Task<bool> SendWelcomeEmailAsync(string email, string userName, string tempPassword);
}

public interface IPasswordService
{
    Task<PasswordResetResponse> RequestPasswordResetAsync(PasswordResetRequest request);
    Task<ApiResponse<string>> ValidateResetTokenAsync(string token);
    Task<ApiResponse<bool>> CompletePasswordResetAsync(string token, string newPassword);
    Task<ApiResponse<string>> CreateAdminPasswordAsync(AdminPasswordCreateRequest request);
    Task<ApiResponse<string>> ResetAdminPasswordAsync(AdminPasswordResetRequest request);
    Task<PasswordResetResponse> SendPasswordResetEmailAsync(SendPasswordResetEmailRequest request);
    string HashPassword(string password, string salt);
    string GenerateSalt();
    bool VerifyPassword(string password, string hash, string salt);
}

public interface IOtpService
{
    Task<OtpResponse> GenerateOtpAsync(OtpRequest request);
    Task<ApiResponse<bool>> ValidateOtpAsync(OtpValidationRequest request);
    Task<bool> CleanupExpiredOtpsAsync();
}

public interface IAdminPasswordEmailService
{
    Task<EmailResponse> SendPasswordNotificationAsync(AdminPasswordNotificationRequest request);
    Task<EmailResponse> SendWelcomeEmailAsync(WelcomeEmailRequest request);
    Task<EmailResponse> SendForceResetEmailAsync(AdminPasswordNotificationRequest request);
    bool IsValidEmailFormat(string email);
}
