﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="Pwd.aspx.vb" Inherits="SPMJ.WebForm73" 
    title="SPMJ KOLEJ - Industry Standard Password Management" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        /* Industry Standard SPMJ KOLEJ Password Management Interface */
        .password-management-container {
            max-width: 800px;
            margin: 20px auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .password-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .password-header h1 {
            color: #ffffff;
            font-size: 32px;
            margin: 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .password-header .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            margin-top: 10px;
            font-weight: 300;
        }
        
        .password-content {
            background: #ffffff;
            padding: 50px;
        }
        
        .microservice-status {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .microservice-status.online {
            background: #f0fff4;
            border-color: #68d391;
        }
        
        .microservice-status.offline {
            background: #fed7d7;
            border-color: #fc8181;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-online {
            background: #38a169;
        }
        
        .status-offline {
            background: #e53e3e;
        }
        
        .security-notice {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .security-notice h3 {
            margin: 0 0 15px 0;
            font-size: 20px;
        }
        
        .security-notice p {
            margin: 0;
            font-size: 16px;
            line-height: 1.5;
        }
        
        .password-form-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: "🔐";
            margin-right: 15px;
            font-size: 28px;
        }
        
        .form-group {
            margin-bottom: 30px;
        }
        
        .form-label {
            display: block;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .form-label.required::after {
            content: " *";
            color: #e53e3e;
        }
        
        .password-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .password-input {
            width: 100%;
            padding: 18px 50px 18px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #ffffff;
            font-family: monospace;
        }
        
        .password-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .password-input.error {
            border-color: #e53e3e;
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
        }
        
        .password-input.success {
            border-color: #38a169;
            box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
        }
        
        .password-toggle {
            position: absolute;
            right: 15px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: #4a5568;
            z-index: 10;
        }
        
        .password-toggle:hover {
            color: #2d3748;
        }
        
        .password-strength {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f7fafc;
            border: 1px solid #e2e8f0;
        }
        
        .strength-meter {
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 4px;
        }
        
        .strength-weak {
            width: 25%;
            background: #e53e3e;
        }
        
        .strength-fair {
            width: 50%;
            background: #ed8936;
        }
        
        .strength-good {
            width: 75%;
            background: #38a169;
        }
        
        .strength-strong {
            width: 100%;
            background: #38a169;
        }
        
        .strength-text {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .password-requirements {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .password-requirements li {
            padding: 5px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .requirement-icon {
            margin-right: 8px;
            font-weight: bold;
        }
        
        .requirement-met {
            color: #38a169;
        }
        
        .requirement-not-met {
            color: #e53e3e;
        }
        
        .validation-message {
            margin-top: 10px;
            padding: 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .validation-error {
            background: #fed7d7;
            border: 1px solid #e53e3e;
            color: #1a202c;
        }
        
        .validation-success {
            background: #c6f6d5;
            border: 1px solid #38a169;
            color: #1a202c;
        }
        
        .action-buttons {
            display: flex;
            gap: 20px;
            margin-top: 40px;
            justify-content: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            padding: 18px 32px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 160px;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
        }
        
        .btn-primary:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
            border: none;
            padding: 18px 32px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 160px;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
            transform: translateY(-1px);
        }
        
        .message-container {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
        }
        
        .message-success {
            background: #c6f6d5;
            border: 2px solid #38a169;
            color: #1a202c;
        }
        
        .message-error {
            background: #fed7d7;
            border: 2px solid #e53e3e;
            color: #1a202c;
        }
        
        .message-info {
            background: #bee3f8;
            border: 2px solid #4299e1;
            color: #1a202c;
        }
        
        .security-tips {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .security-tips h3 {
            color: #2d3748;
            margin: 0 0 20px 0;
            font-size: 20px;
            display: flex;
            align-items: center;
        }
        
        .security-tips h3::before {
            content: "🛡️";
            margin-right: 10px;
        }
        
        .security-tips ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .security-tips li {
            padding: 8px 0;
            font-size: 15px;
            color: #4a5568;
            display: flex;
            align-items: flex-start;
        }
        
        .security-tips li::before {
            content: "✓";
            color: #38a169;
            font-weight: bold;
            margin-right: 10px;
            margin-top: 2px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #e2e8f0;
            border-top: 5px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .password-management-container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .password-content {
                padding: 30px 20px;
            }
            
            .password-form-section {
                padding: 25px 20px;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn-primary, .btn-secondary {
                width: 100%;
                max-width: 280px;
            }
        }
        
        @media (max-width: 480px) {
            .password-header h1 {
                font-size: 24px;
            }
            
            .password-header .subtitle {
                font-size: 16px;
            }
            
            .section-title {
                font-size: 20px;
            }
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="password-management-container">
        <!-- Header Section -->
        <div class="password-header">
            <h1>🔐 Industry Standard Password Management</h1>
            <div class="subtitle">SPMJ KOLEJ Secure Password Change with Microservice Integration</div>
        </div>
        
        <!-- Content Section -->
        <div class="password-content">
            <!-- ScriptManager for AJAX functionality -->

            <!-- Microservice Status -->
            <div id="microserviceStatus" class="microservice-status">
                <div class="status-indicator">
                    <div id="statusDot" class="status-dot status-offline"></div>
                    <span id="statusText">Checking Email Service Status...</span>
                </div>
                <div style="font-size: 14px; color: #4a5568;">
                    Password change notifications via email service
                </div>
            </div>
            
            <!-- Security Notice -->
            <div class="security-notice">
                <h3>🛡️ Enhanced Security Password Change</h3>
                <p>Your password is protected with industry-standard SHA256+Salt encryption. Changes are logged for security auditing and email notifications are sent to administrators.</p>
            </div>
            
            <!-- Error/Success Messages -->
            <asp:Panel ID="messagePanel" runat="server" Visible="false" CssClass="message-container">
                <asp:Label ID="messageLabel" runat="server"></asp:Label>
            </asp:Panel>
            
            <!-- Password Change Form -->
            <div class="password-form-section">
                <div class="section-title">Change Your Password</div>
                
                <!-- Current Password -->
                <div class="form-group">
                    <label class="form-label required" for="<%= Tx_Pwd.ClientID %>">
                        Current Password
                    </label>
                    <div class="password-input-container">
                        <asp:TextBox ID="Tx_Pwd" runat="server" 
                            CssClass="password-input" 
                            TextMode="Password"
                            MaxLength="255"
                            placeholder="Enter your current password"
                            autocomplete="current-password" />
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('Tx_Pwd', this)">
                            👁️
                        </button>
                    </div>
                    <div id="currentPasswordError" class="validation-message validation-error" style="display: none;">
                        Please enter your current password
                    </div>
                </div>
                
                <!-- New Password -->
                <div class="form-group">
                    <label class="form-label required" for="<%= Tx_Pwd2.ClientID %>">
                        New Password
                    </label>
                    <div class="password-input-container">
                        <asp:TextBox ID="Tx_Pwd2" runat="server" 
                            CssClass="password-input" 
                            TextMode="Password"
                            MaxLength="255"
                            placeholder="Enter your new password"
                            autocomplete="new-password" />
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('Tx_Pwd2', this)">
                            👁️
                        </button>
                    </div>
                    
                    <!-- Password Strength Indicator -->
                    <div class="password-strength">
                        <div class="strength-meter">
                            <div id="strengthFill" class="strength-fill"></div>
                        </div>
                        <div id="strengthText" class="strength-text">Enter a password to see strength</div>
                        <ul class="password-requirements">
                            <li id="length" class="requirement-not-met">
                                <span class="requirement-icon">❌</span>
                                At least 8 characters long
                            </li>
                            <li id="uppercase" class="requirement-not-met">
                                <span class="requirement-icon">❌</span>
                                Contains uppercase letter (A-Z)
                            </li>
                            <li id="lowercase" class="requirement-not-met">
                                <span class="requirement-icon">❌</span>
                                Contains lowercase letter (a-z)
                            </li>
                            <li id="number" class="requirement-not-met">
                                <span class="requirement-icon">❌</span>
                                Contains number (0-9)
                            </li>
                            <li id="special" class="requirement-not-met">
                                <span class="requirement-icon">❌</span>
                                Contains special character (!@#$%^&*)
                            </li>
                            <li id="noSequential" class="requirement-not-met">
                                <span class="requirement-icon">❌</span>
                                No sequential characters (abc, 123)
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Confirm New Password -->
                <div class="form-group">
                    <label class="form-label required" for="<%= Tx_Pwd3.ClientID %>">
                        Confirm New Password
                    </label>
                    <div class="password-input-container">
                        <asp:TextBox ID="Tx_Pwd3" runat="server" 
                            CssClass="password-input" 
                            TextMode="Password"
                            MaxLength="255"
                            placeholder="Confirm your new password"
                            autocomplete="new-password" />
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('Tx_Pwd3', this)">
                            👁️
                        </button>
                    </div>
                    <div id="confirmPasswordError" class="validation-message validation-error" style="display: none;">
                        Passwords do not match
                    </div>
                    <div id="confirmPasswordSuccess" class="validation-message validation-success" style="display: none;">
                        Passwords match ✓
                    </div>
                </div>
                
                <!-- Email Notification Option -->
                <div class="form-group">
                    <label class="form-label">
                        <asp:CheckBox ID="chkEmailNotification" runat="server" Checked="true" />
                        Send email notification of password change
                    </label>
                    <small style="color: #718096; font-size: 14px; margin-top: 8px; display: block;">
                        An email will be sent to confirm your password has been changed successfully.
                    </small>
                </div>
                
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <asp:Button ID="cmd_Cari1" runat="server" 
                        CssClass="btn-primary" 
                        Text="🔐 Change Password"
                        OnClientClick="return validatePasswordForm();" />
                    
                    <asp:Button ID="btnCancel" runat="server" 
                        CssClass="btn-secondary" 
                        Text="Cancel"
                        OnClientClick="return confirmCancel();" 
                        CausesValidation="false" />
                </div>
            </div>
            
            <!-- Security Tips -->
            <div class="security-tips">
                <h3>Password Security Best Practices</h3>
                <ul>
                    <li>Use a unique password that you don't use for other accounts</li>
                    <li>Include a mix of uppercase, lowercase, numbers, and special characters</li>
                    <li>Avoid using personal information like names, birthdays, or addresses</li>
                    <li>Don't use sequential characters or common patterns</li>
                    <li>Change your password regularly for enhanced security</li>
                    <li>Never share your password with others</li>
                    <li>Use a password manager to generate and store strong passwords</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div>Securely updating your password...</div>
        </div>
    </div>
    
    <!-- JavaScript for Enhanced Password Management -->
    <script type="text/javascript">
        // Enhanced Password Management JavaScript
        let emailServiceStatus = false;
        let passwordStrengthScore = 0;
        
        // Initialize page functionality
        window.onload = function() {
            checkEmailServiceStatus();
            setupPasswordValidation();
            setupFormValidation();
        };
          // Check email service status
        function checkEmailServiceStatus() {
            // Call server-side web method for email service health check
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'Pwd.aspx/CheckEmailServiceHealth', true);
            xhr.setRequestHeader('Content-Type', 'application/json; charset=utf-8');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    try {
                        if (xhr.status === 200) {
                            var response = JSON.parse(xhr.responseText);
                            var healthData = JSON.parse(response.d);
                            updateServiceStatus(healthData.status === 'online', healthData.message);
                        } else {
                            updateServiceStatus(false, 'Email service health check failed');
                        }
                    } catch (e) {
                        updateServiceStatus(false, 'Email service health check error');
                    }
                }
            };
            
            xhr.onerror = function() {
                updateServiceStatus(false, 'Email service connection error');
            };
            
            xhr.send('{}');
        }
          // Update service status display
        function updateServiceStatus(isOnline, message) {
            emailServiceStatus = isOnline;
            const statusElement = document.getElementById('microserviceStatus');
            const dotElement = document.getElementById('statusDot');
            const textElement = document.getElementById('statusText');
            
            if (isOnline) {
                statusElement.className = 'microservice-status online';
                dotElement.className = 'status-dot status-online';
                textElement.textContent = 'Email Service: Online ✅ (' + (message || 'Operational') + ')';
            } else {
                statusElement.className = 'microservice-status offline';
                dotElement.className = 'status-dot status-offline';
                textElement.textContent = 'Email Service: Offline ⚠️ (' + (message || 'Notifications disabled') + ')';
            }
        }
        
        // Setup password validation
        function setupPasswordValidation() {
            const newPasswordField = document.getElementById('<%= Tx_Pwd2.ClientID %>');
            const confirmPasswordField = document.getElementById('<%= Tx_Pwd3.ClientID %>');
            
            if (newPasswordField) {
                newPasswordField.addEventListener('input', function() {
                    validatePasswordStrength(this.value);
                    checkPasswordMatch();
                });
            }
            
            if (confirmPasswordField) {
                confirmPasswordField.addEventListener('input', checkPasswordMatch);
            }
        }
        
        // Validate password strength
        function validatePasswordStrength(password) {
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
                noSequential: !hasSequentialChars(password)
            };
            
            // Update requirement indicators
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(req);
                if (element) {
                    const icon = element.querySelector('.requirement-icon');
                    if (requirements[req]) {
                        element.className = 'requirement-met';
                        icon.textContent = '✅';
                    } else {
                        element.className = 'requirement-not-met';
                        icon.textContent = '❌';
                    }
                }
            });
            
            // Calculate strength score
            const metRequirements = Object.values(requirements).filter(Boolean).length;
            passwordStrengthScore = metRequirements;
            
            // Update strength meter
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            if (password.length === 0) {
                strengthFill.className = 'strength-fill';
                strengthText.textContent = 'Enter a password to see strength';
            } else if (metRequirements <= 2) {
                strengthFill.className = 'strength-fill strength-weak';
                strengthText.textContent = 'Weak Password';
                strengthText.style.color = '#e53e3e';
            } else if (metRequirements <= 4) {
                strengthFill.className = 'strength-fill strength-fair';
                strengthText.textContent = 'Fair Password';
                strengthText.style.color = '#ed8936';
            } else if (metRequirements <= 5) {
                strengthFill.className = 'strength-fill strength-good';
                strengthText.textContent = 'Good Password';
                strengthText.style.color = '#38a169';
            } else {
                strengthFill.className = 'strength-fill strength-strong';
                strengthText.textContent = 'Strong Password';
                strengthText.style.color = '#38a169';
            }
        }
        
        // Check for sequential characters
        function hasSequentialChars(password) {
            const sequential = ['abc', 'bcd', 'cde', 'def', 'efg', 'fgh', 'ghi', 'hij', 'ijk', 'jkl', 'klm', 'lmn', 'mno', 'nop', 'opq', 'pqr', 'qrs', 'rst', 'stu', 'tuv', 'uvw', 'vwx', 'wxy', 'xyz', '123', '234', '345', '456', '567', '678', '789'];
            const lower = password.toLowerCase();
            
            for (let seq of sequential) {
                if (lower.includes(seq) || lower.includes(seq.split('').reverse().join(''))) {
                    return true;
                }
            }
            
            return false;
        }
        
        // Check password match
        function checkPasswordMatch() {
            const newPassword = document.getElementById('<%= Tx_Pwd2.ClientID %>').value;
            const confirmPassword = document.getElementById('<%= Tx_Pwd3.ClientID %>').value;
            const errorDiv = document.getElementById('confirmPasswordError');
            const successDiv = document.getElementById('confirmPasswordSuccess');
            
            if (confirmPassword.length === 0) {
                errorDiv.style.display = 'none';
                successDiv.style.display = 'none';
                return;
            }
            
            if (newPassword === confirmPassword) {
                errorDiv.style.display = 'none';
                successDiv.style.display = 'block';
                return true;
            } else {
                errorDiv.style.display = 'block';
                successDiv.style.display = 'none';
                return false;
            }
        }
        
        // Setup form validation
        function setupFormValidation() {
            const currentPasswordField = document.getElementById('<%= Tx_Pwd.ClientID %>');
            const changeButton = document.getElementById('<%= cmd_Cari1.ClientID %>');
            
            if (currentPasswordField) {
                currentPasswordField.addEventListener('input', validateForm);
            }
            
            function validateForm() {
                const currentPassword = currentPasswordField.value;
                const isValid = currentPassword.length > 0 && passwordStrengthScore >= 4 && checkPasswordMatch();
                
                if (changeButton) {
                    changeButton.disabled = !isValid;
                }
            }
            
            // Set up real-time validation
            setInterval(validateForm, 500);
        }
        
        // Toggle password visibility
        function togglePasswordVisibility(fieldId, button) {
            const field = document.getElementById(fieldId);
            if (field.type === 'password') {
                field.type = 'text';
                button.textContent = '🙈';
            } else {
                field.type = 'password';
                button.textContent = '👁️';
            }
        }
        
        // Validate password form before submission
        function validatePasswordForm() {
            const currentPassword = document.getElementById('<%= Tx_Pwd.ClientID %>').value;
            const newPassword = document.getElementById('<%= Tx_Pwd2.ClientID %>').value;
            const confirmPassword = document.getElementById('<%= Tx_Pwd3.ClientID %>').value;
            
            // Clear previous error messages
            document.getElementById('currentPasswordError').style.display = 'none';
            
            // Validate current password
            if (currentPassword.length === 0) {
                document.getElementById('currentPasswordError').style.display = 'block';
                document.getElementById('<%= Tx_Pwd.ClientID %>').focus();
                return false;
            }
            
            // Validate new password strength
            if (passwordStrengthScore < 4) {
                alert('Please ensure your new password meets at least 4 of the security requirements.');
                document.getElementById('<%= Tx_Pwd2.ClientID %>').focus();
                return false;
            }
            
            // Validate password match
            if (newPassword !== confirmPassword) {
                alert('New password and confirmation do not match.');
                document.getElementById('<%= Tx_Pwd3.ClientID %>').focus();
                return false;
            }
            
            // Check if passwords are the same
            if (currentPassword === newPassword) {
                alert('New password must be different from your current password.');
                document.getElementById('<%= Tx_Pwd2.ClientID %>').focus();
                return false;
            }
            
            // Check email notification setting
            const emailNotificationChecked = document.getElementById('<%= chkEmailNotification.ClientID %>').checked;
            if (emailNotificationChecked && !emailServiceStatus) {
                const proceed = confirm('Email service is currently offline. Password will be changed but no notification email will be sent. Continue?');
                if (!proceed) {
                    return false;
                }
            }
            
            showLoading(true);
            return true;
        }
        
        // Confirm cancel action
        function confirmCancel() {
            return confirm('Are you sure you want to cancel? Any changes will be lost.');
        }
        
        // Show/hide loading overlay
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = show ? 'flex' : 'none';
            }
        }
        
        // Hide loading on page load complete
        document.addEventListener('DOMContentLoaded', function() {
            showLoading(false);
        });
    </script>
</asp:Content>

