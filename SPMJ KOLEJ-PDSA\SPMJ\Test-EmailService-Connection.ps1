#!/usr/bin/env powershell

Write-Host "=== Email Microservice Connection Test ===" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if microservice is running
Write-Host "1. Checking if microservice is running on port 5000..." -ForegroundColor Yellow
$portCheck = netstat -an | findstr ":5000"
if ($portCheck) {
    Write-Host "✅ Microservice is running on port 5000" -ForegroundColor Green
    Write-Host $portCheck
} else {
    Write-Host "❌ Microservice is NOT running on port 5000" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test 2: Health endpoint check
Write-Host "2. Testing health endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ Health endpoint responds with status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Content: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "❌ Health endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test 3: API Key endpoint check  
Write-Host "3. Testing API key authentication..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
        "Content-Type" = "application/json"
    }
    
    $testPayload = @{
        recipient = "<EMAIL>"
        subject = "Test Connection"
        body = "Testing microservice connection"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/email/send" -Method POST -Headers $headers -Body $testPayload -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ API Key authentication working - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode -eq 401) {
        Write-Host "❌ API Key authentication failed (401 Unauthorized)" -ForegroundColor Red
    } elseif ($statusCode -eq 400) {
        Write-Host "⚠️ API accepts request but validation failed (400 Bad Request) - This is expected for test data" -ForegroundColor Yellow
    } else {
        Write-Host "❌ API endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""

# Test 4: OTP endpoint check
Write-Host "4. Testing OTP endpoint..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
        "Content-Type" = "application/json"
    }
    
    $otpPayload = @{
        userId = "test123"
        email = "<EMAIL>"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/otp/generate" -Method POST -Headers $headers -Body $otpPayload -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ OTP endpoint working - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode -eq 401) {
        Write-Host "❌ OTP API Key authentication failed (401 Unauthorized)" -ForegroundColor Red
    } elseif ($statusCode -eq 400) {
        Write-Host "⚠️ OTP endpoint accepts request but validation failed (400 Bad Request) - This is expected for test data" -ForegroundColor Yellow
    } else {
        Write-Host "❌ OTP endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Email Microservice Connection Test Complete ===" -ForegroundColor Cyan
