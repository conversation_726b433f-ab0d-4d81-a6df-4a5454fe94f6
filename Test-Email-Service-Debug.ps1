# SPMJ KOLEJ Email Service Debug Test Script
# Tests the CheckEmailServiceHealth web method

Write-Host "=== EMAIL SERVICE DEBUG TEST ===" -ForegroundColor Green
Write-Host "Testing the email service health check web method..." -ForegroundColor White

# Test 1: Direct microservice health check
Write-Host "`n1. DIRECT MICROSERVICE TEST" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✓ Microservice is accessible" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
    Write-Host "  Content: $($response.Content)" -ForegroundColor Gray
} catch {
    Write-Host "✗ Microservice not accessible: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Test web method via PowerShell web request
Write-Host "`n2. WEB METHOD TEST" -ForegroundColor Cyan
try {
    $webMethodUrl = "http://localhost/SPMJ/Pwd.aspx/CheckEmailServiceHealth"
    Write-Host "Testing web method at: $webMethodUrl" -ForegroundColor Gray
    
    $headers = @{
        "Content-Type" = "application/json; charset=utf-8"
    }
    
    $body = "{}"
    
    $webResponse = Invoke-WebRequest -Uri $webMethodUrl -Method POST -Headers $headers -Body $body -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Web method is accessible" -ForegroundColor Green
    Write-Host "  Status: $($webResponse.StatusCode)" -ForegroundColor Gray
    Write-Host "  Content: $($webResponse.Content)" -ForegroundColor Gray
    
    # Try to parse the response
    try {
        $jsonResponse = $webResponse.Content | ConvertFrom-Json
        Write-Host "  Parsed JSON:" -ForegroundColor Gray
        Write-Host "    d property: $($jsonResponse.d)" -ForegroundColor Gray
        
        if ($jsonResponse.d) {
            $serviceData = $jsonResponse.d | ConvertFrom-Json
            Write-Host "    Service status: $($serviceData.status)" -ForegroundColor Gray
            Write-Host "    Service message: $($serviceData.message)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "  JSON parsing error: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "✗ Web method test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  This may be normal if IIS/web server is not running" -ForegroundColor Yellow
}

# Test 3: Check EmailServiceClient directly
Write-Host "`n3. EMAILSERVICECLIENT TEST" -ForegroundColor Cyan
try {
    # Load the assembly
    Add-Type -Path "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\EmailDebugTest.dll"
    Write-Host "✓ Assembly loaded successfully" -ForegroundColor Green
    
    # Test EmailServiceClient
    Write-Host "Testing EmailServiceClient class..." -ForegroundColor Gray
    # Note: This may not work in PowerShell due to namespace issues
    
} catch {
    Write-Host "⚠ Assembly test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "  This is expected - assembly may have dependencies" -ForegroundColor Gray
}

Write-Host "`n=== DEBUGGING RECOMMENDATIONS ===" -ForegroundColor Magenta
Write-Host "If you're still seeing 'Ralat memproses respons':" -ForegroundColor White
Write-Host "1. Open browser Developer Tools (F12)" -ForegroundColor Gray
Write-Host "2. Go to Console tab" -ForegroundColor Gray
Write-Host "3. Load the Pwd.aspx page" -ForegroundColor Gray
Write-Host "4. Look for JavaScript console messages" -ForegroundColor Gray
Write-Host "5. Check the XHR/Network tab for the web method call" -ForegroundColor Gray
Write-Host ""
Write-Host "The enhanced JavaScript now provides detailed console logging." -ForegroundColor Green

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Green
