-- Database Column Size Fix for Password Encryption
-- The issue is that the PWD column is too small for encrypted passwords
-- SHA256 Base64 hash is 44 characters, current PWD column appears to be smaller

-- Check current column sizes
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pn_pengguna' 
AND COLUMN_NAME IN ('pwd', 'salt')

-- Fix the PWD column size to accommodate encrypted passwords (minimum 100 chars)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'pn_pengguna') AND name = 'pwd')
BEGIN
    -- Get current column length
    DECLARE @currentLength INT
    SELECT @currentLength = CHARACTER_MAXIMUM_LENGTH 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'pwd'
    
    IF @currentLength < 100
    BEGIN
        ALTER TABLE pn_pengguna ALTER COLUMN pwd VARCHAR(100) NULL
        PRINT 'Increased PWD column size to VARCHAR(100) to accommodate encrypted passwords'
    END
    ELSE
    BEGIN
        PRINT 'PWD column size is already sufficient: ' + CAST(@currentLength AS VARCHAR(10))
    END
END

-- Ensure salt column is properly sized
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'pn_pengguna') AND name = 'salt')
BEGIN
    DECLARE @saltLength INT
    SELECT @saltLength = CHARACTER_MAXIMUM_LENGTH 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'salt'
    
    IF @saltLength < 100
    BEGIN
        ALTER TABLE pn_pengguna ALTER COLUMN salt VARCHAR(100) NULL
        PRINT 'Increased SALT column size to VARCHAR(100)'
    END
    ELSE
    BEGIN
        PRINT 'SALT column size is already sufficient: ' + CAST(@saltLength AS VARCHAR(10))
    END
END
ELSE
BEGIN
    ALTER TABLE pn_pengguna ADD salt VARCHAR(100) NULL
    PRINT 'Added SALT column VARCHAR(100)'
END

-- Ensure password_migrated column exists
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'pn_pengguna') AND name = 'password_migrated')
BEGIN
    ALTER TABLE pn_pengguna ADD password_migrated BIT DEFAULT 0
    PRINT 'Added password_migrated column'
END

PRINT 'Database schema update completed successfully!'
