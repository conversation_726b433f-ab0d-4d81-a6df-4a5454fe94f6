<?xml version="1.0" encoding="utf-8"?>
<configuration>  <appSettings>
    <add key="IP_App" value="localhost" />
    <add key="IP_App2" value="localhost" />
    <add key="dB" value="SPMJ_PDSA" />
    <!-- Email Service Integration Configuration for KOLEJ-PDSA -->
    <add key="EmailServiceBaseUrl" value="http://localhost:5000" />
    <add key="EmailServiceUrl" value="http://localhost:5000" />
    <add key="EmailServiceApiKey" value="SPMJ-EmailService-2024-SecureKey-MOH-Malaysia" />
    <add key="EmailServiceEnabled" value="true" />
    <add key="OtpEnabled" value="true" />
    <add key="SecureAuthenticationEnabled" value="true" />
    
    <add key="CrystalImageCleaner-AutoStart" value="true" />
    <add key="CrystalImageCleaner-Sleep" value="60000" />
    <add key="CrystalImageCleaner-Age" value="120000" />
    <add key="ChartImageHandler" value="storage=file;timeout=20;dir=c:\TempImageFiles\;" />    
  </appSettings>
    <connectionStrings>
    <!-- SPMJ KOLEJ-PDSA Database Connection Strings -->
    <add name="DefaultConnection" 
         connectionString="Provider=SQLOLEDB.1; Data Source=localhost; Initial Catalog=SPMJ_PDSA; User ID=sa; Password=*********" 
         providerName="System.Data.OleDb" />
    
    <add name="KOLEJConnection" 
         connectionString="Provider=SQLOLEDB.1; Data Source=localhost; Initial Catalog=SPMJ_PDSA; User ID=sa; Password=*********" 
         providerName="System.Data.OleDb" />
    
    <add name="SPMJConnection" 
         connectionString="Provider=SQLOLEDB.1; Data Source=localhost; Initial Catalog=SPMJ_PDSA; User ID=sa; Password=*********" 
         providerName="System.Data.OleDb" />
    
    <!-- Read-only connection for login operations -->
    <add name="LoginConnection" 
         connectionString="Provider=SQLOLEDB.1; Data Source=localhost; Initial Catalog=SPMJ_PDSA; User ID=ro; Password=*********" 
         providerName="System.Data.OleDb" />
  </connectionStrings>
  
  <system.web>
    <customErrors mode="Off"/>
    <compilation debug="true">
      <assemblies>
        <add assembly="*" />
      </assemblies>
    </compilation>
    
    <httpRuntime />
    
    <!-- Authentication -->
    <authentication mode="Forms">
      <forms loginUrl="Login_J.aspx" timeout="2880" />
    </authentication>
    
    <!-- Pages settings -->
    <pages>
      <controls>
        <add tagPrefix="ajaxToolkit" namespace="AjaxControlToolkit" assembly="AjaxControlToolkit" />
      </controls>
    </pages>
    
    <!-- Session settings -->
    <sessionState mode="InProc" cookieless="false" timeout="30" />
    
    <!-- Trust level -->
    <trust level="Full" />
    
  </system.web>
  
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="Login_J.aspx" />
        <add value="Default.aspx" />
      </files>
    </defaultDocument>
  </system.webServer>
  
</configuration>
