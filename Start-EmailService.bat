@echo off
echo Starting SPMJ Email Microservice...
echo.

cd /d "d:\source_code\.NET 3.5. - Q\SPMJ.EmailService"

echo Checking if .NET 9 is available...
dotnet --version
if errorlevel 1 (
    echo ERROR: .NET 9 SDK not found. Please install .NET 9 SDK first.
    pause
    exit /b 1
)

echo.
echo Building email service...
dotnet build
if errorlevel 1 (
    echo ERROR: Build failed. Please check the project.
    pause
    exit /b 1
)

echo.
echo Starting email service on http://localhost:5000...
echo Press Ctrl+C to stop the service.
echo.

dotnet run --urls "http://localhost:5000"
