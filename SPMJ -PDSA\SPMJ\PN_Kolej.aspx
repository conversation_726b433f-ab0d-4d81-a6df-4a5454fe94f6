﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="PN_Kolej.aspx.vb" Inherits="SPMJ.WebForm55" 
    title="SPMJ - Industry Standard College Institution Management" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        /* Industry Standard College Management Interface */
        .college-management-container {
            max-width: 1200px;
            margin: 20px auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .college-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px 30px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .college-header h1 {
            color: #ffffff;
            font-size: 28px;
            margin: 0;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .college-header .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin-top: 8px;
            font-weight: 300;
        }
        
        .college-content {
            background: #ffffff;
            padding: 40px;
        }
        
        .microservice-status {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
        }
        
        .microservice-status.online {
            background: #f0fff4;
            border-color: #68d391;
        }
        
        .microservice-status.offline {
            background: #fed7d7;
            border-color: #fc8181;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #38a169;
        }
        
        .status-offline {
            background: #e53e3e;
        }
        
        .form-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: "🏫";
            margin-right: 10px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #ffffff;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .form-select {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            background: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
        }
        
        .btn-primary:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
            border: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
        }
        
        .college-grid-section {
            margin-top: 30px;
        }
        
        .grid-container {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .grid-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 16px;
        }
        
        .college-grid {
            width: 100%;
        }
        
        .college-grid th {
            background: #f7fafc;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #2d3748;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .college-grid td {
            padding: 15px;
            border-bottom: 1px solid #e2e8f0;
            transition: background-color 0.2s ease;
        }
        
        .college-grid tr:hover td {
            background-color: #f7fafc;
        }
        
        .college-grid tr.selected td {
            background-color: #ebf8ff;
            border-left: 4px solid #4299e1;
        }
        
        .grid-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-grid {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-edit {
            background: #4299e1;
            color: white;
        }
        
        .btn-edit:hover {
            background: #3182ce;
        }
        
        .btn-delete {
            background: #e53e3e;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c53030;
        }
        
        .message-container {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .message-success {
            background: #c6f6d5;
            border: 1px solid #38a169;
            color: #1a202c;
        }
        
        .message-error {
            background: #fed7d7;
            border: 1px solid #e53e3e;
            color: #1a202c;
        }
        
        .message-info {
            background: #bee3f8;
            border: 1px solid #4299e1;
            color: #1a202c;
        }
        
        .college-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .college-management-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .college-content {
                padding: 20px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .college-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="college-management-container">
        <!-- Header Section -->
        <div class="college-header">
            <h1>🏫 Industry Standard College Institution Management</h1>
            <div class="subtitle">Comprehensive Training Institution Administration with Microservice Integration</div>
        </div>
        
        <!-- Content Section -->
        <div class="college-content">
            <!-- ScriptManager for AJAX functionality -->
            <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
            </asp:ScriptManagerProxy>
            
            <!-- Microservice Status -->
            <div id="microserviceStatus" class="microservice-status">
                <div class="status-indicator">
                    <div id="statusDot" class="status-dot status-offline"></div>
                    <span id="statusText">Checking Email Service Status...</span>
                </div>
            </div>
            
            <!-- College Statistics Dashboard -->
            <div class="college-stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalColleges">0</div>
                    <div class="stat-label">Total Institutions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeColleges">0</div>
                    <div class="stat-label">Active Institutions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="collegeTypes">0</div>
                    <div class="stat-label">Institution Types</div>
                </div>
            </div>
            
            <!-- Error/Success Messages -->
            <asp:Panel ID="messagePanel" runat="server" Visible="false" CssClass="message-container">
                <asp:Label ID="messageLabel" runat="server"></asp:Label>
            </asp:Panel>
            
            <!-- College Management Form -->
            <div class="form-section">
                <div class="section-title">College Institution Information</div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="<%= Tx_Id.ClientID %>">
                            Institution ID <span style="color: #e53e3e;">*</span>
                        </label>
                        <asp:TextBox ID="Tx_Id" runat="server" 
                            CssClass="form-input" 
                            MaxLength="10"
                            placeholder="Auto-generated ID"
                            ReadOnly="true" />
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="<%= Cb_Jenis.ClientID %>">
                            Institution Type <span style="color: #e53e3e;">*</span>
                        </label>
                        <asp:DropDownList ID="Cb_Jenis" runat="server" CssClass="form-select">
                            <asp:ListItem Value="0">Please Select Institution Type</asp:ListItem>
                            <asp:ListItem Value="1">K - Kolej (College)</asp:ListItem>
                            <asp:ListItem Value="2">S - Sekolah (School)</asp:ListItem>
                            <asp:ListItem Value="3">LN - Luar Negara (International)</asp:ListItem>
                        </asp:DropDownList>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="<%= Tx_Kolej.ClientID %>">
                        Institution Name <span style="color: #e53e3e;">*</span>
                    </label>
                    <asp:TextBox ID="Tx_Kolej" runat="server" 
                        CssClass="form-input" 
                        MaxLength="200"
                        placeholder="Enter full institution name" />
                </div>
                
                <!-- Email Notification Settings -->
                <div class="form-group">
                    <label class="form-label">
                        <asp:CheckBox ID="chkEmailNotification" runat="server" Checked="true" />
                        Send email notification for institution changes
                    </label>
                    <small style="color: #718096; font-size: 12px;">
                        Administrators will receive notifications about institution updates via email service.
                    </small>
                </div>
                
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <asp:Button ID="cmd_Cari1" runat="server" 
                        CssClass="btn-primary" 
                        Text="💾 Save Institution"
                        OnClientClick="return validateCollegeForm();" />
                    
                    <asp:Button ID="cmd_baru" runat="server" 
                        CssClass="btn-secondary" 
                        Text="🆕 New Institution"
                        Visible="false" />
                    
                    <asp:Button ID="cmd_Reset" runat="server" 
                        CssClass="btn-secondary" 
                        Text="🔄 Reset Form"
                        OnClientClick="return resetForm();" 
                        CausesValidation="false" />
                </div>
            </div>
            
            <!-- Institution List Section -->
            <div class="college-grid-section">
                <div class="grid-container">
                    <div class="grid-header">
                        📋 Training Institutions Directory
                    </div>
                    
                    <asp:GridView ID="Gd" runat="server" 
                        CssClass="college-grid"
                        AutoGenerateColumns="false"
                        AllowPaging="true"
                        PageSize="10"
                        AllowSorting="true"
                        DataKeyNames="id_kolej"
                        EmptyDataText="No training institutions found."
                        GridLines="None">
                        
                        <Columns>
                            <asp:BoundField DataField="INSTITUSI LATIHAN" HeaderText="Institution Name" 
                                SortExpression="INSTITUSI LATIHAN" ItemStyle-Width="50%" />
                            <asp:BoundField DataField="JENIS" HeaderText="Type" 
                                SortExpression="JENIS" ItemStyle-Width="15%" ItemStyle-HorizontalAlign="Center" />
                            <asp:TemplateField HeaderText="Actions" ItemStyle-Width="20%" ItemStyle-HorizontalAlign="Center">
                                <ItemTemplate>
                                    <div class="grid-actions">
                                        <asp:Button ID="btnEdit" runat="server" 
                                            CssClass="btn-grid btn-edit" 
                                            Text="✏️ Edit"
                                            CommandName="EditCollege"
                                            CommandArgument='<%# Eval("id_kolej") %>' />
                                        <asp:Button ID="btnDelete" runat="server" 
                                            CssClass="btn-grid btn-delete" 
                                            Text="🗑️ Delete"
                                            CommandName="DeleteCollege"
                                            CommandArgument='<%# Eval("id_kolej") %>'
                                            OnClientClick="return confirmDelete();" />
                                    </div>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:BoundField DataField="id_kolej" HeaderText="ID" Visible="false" />
                        </Columns>
                        
                        <HeaderStyle BackColor="#f7fafc" ForeColor="#2d3748" Font-Weight="Bold" />
                        <RowStyle BackColor="White" ForeColor="#2d3748" />
                        <AlternatingRowStyle BackColor="#f7fafc" />
                        <SelectedRowStyle BackColor="#ebf8ff" BorderColor="#4299e1" BorderWidth="2px" />
                        <PagerStyle BackColor="#e2e8f0" ForeColor="#4a5568" HorizontalAlign="Center" />
                    </asp:GridView>
                </div>
            </div>
            
            <!-- Advanced Features Section -->
            <div class="form-section" style="margin-top: 30px;">
                <div class="section-title">Advanced Institution Management</div>
                <div style="font-size: 14px; color: #4a5568; line-height: 1.6;">
                    <p><strong>🔐 Enhanced Security:</strong> All institution data is protected with industry-standard security measures.</p>
                    <p><strong>📧 Email Integration:</strong> Automatic notifications for institution changes via microservice architecture.</p>
                    <p><strong>📊 Data Validation:</strong> Real-time validation ensures data integrity and consistency.</p>
                    <p><strong>🔄 Audit Trail:</strong> Complete logging of all institution management activities.</p>
                    <p><strong>📱 Mobile Ready:</strong> Responsive design works seamlessly on all devices.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div>Processing institution data...</div>
        </div>
    </div>
    
    <!-- JavaScript for Enhanced Functionality -->
    <script type="text/javascript">
        // Enhanced College Management JavaScript
        let emailServiceStatus = false;
        
        // Initialize page functionality
        window.onload = function() {
            checkEmailServiceStatus();
            updateCollegeStats();
            setupFormValidation();
        };
        
        // Check email service status
        function checkEmailServiceStatus() {
            // Simulated microservice health check
            fetch('/api/email/health')
                .then(response => response.ok)
                .then(isOnline => {
                    updateServiceStatus(isOnline);
                })
                .catch(() => {
                    updateServiceStatus(false);
                });
        }
        
        // Update service status display
        function updateServiceStatus(isOnline) {
            emailServiceStatus = isOnline;
            const statusElement = document.getElementById('microserviceStatus');
            const dotElement = document.getElementById('statusDot');
            const textElement = document.getElementById('statusText');
            
            if (isOnline) {
                statusElement.className = 'microservice-status online';
                dotElement.className = 'status-dot status-online';
                textElement.textContent = 'Email Service: Online ✅';
            } else {
                statusElement.className = 'microservice-status offline';
                dotElement.className = 'status-dot status-offline';
                textElement.textContent = 'Email Service: Offline ⚠️ (Notifications disabled)';
            }
        }
        
        // Update college statistics
        function updateCollegeStats() {
            // Count statistics from grid
            const gridRows = document.querySelectorAll('.college-grid tr[style*="background"]');
            const totalCount = gridRows.length;
            
            // Count by type
            let collegeCount = 0, schoolCount = 0, internationalCount = 0;
            gridRows.forEach(row => {
                const typeCell = row.cells[1];
                if (typeCell) {
                    const type = typeCell.textContent.trim();
                    if (type === 'K') collegeCount++;
                    else if (type === 'S') schoolCount++;
                    else if (type === 'LN') internationalCount++;
                }
            });
            
            // Update display
            document.getElementById('totalColleges').textContent = totalCount;
            document.getElementById('activeColleges').textContent = totalCount; // Assuming all displayed are active
            document.getElementById('collegeTypes').textContent = (collegeCount > 0 ? 1 : 0) + (schoolCount > 0 ? 1 : 0) + (internationalCount > 0 ? 1 : 0);
        }
        
        // Setup form validation
        function setupFormValidation() {
            const institutionNameField = document.getElementById('<%= Tx_Kolej.ClientID %>');
            const typeField = document.getElementById('<%= Cb_Jenis.ClientID %>');
            
            if (institutionNameField) {
                institutionNameField.addEventListener('input', validateForm);
            }
            if (typeField) {
                typeField.addEventListener('change', validateForm);
            }
        }
        
        // Validate form inputs
        function validateForm() {
            const institutionName = document.getElementById('<%= Tx_Kolej.ClientID %>').value.trim();
            const institutionType = document.getElementById('<%= Cb_Jenis.ClientID %>').value;
            
            const saveButton = document.getElementById('<%= cmd_Cari1.ClientID %>');
            const isValid = institutionName.length >= 3 && institutionType !== '0';
            
            if (saveButton) {
                saveButton.disabled = !isValid;
            }
            
            return isValid;
        }
        
        // Validate college form before submission
        function validateCollegeForm() {
            const institutionName = document.getElementById('<%= Tx_Kolej.ClientID %>').value.trim();
            const institutionType = document.getElementById('<%= Cb_Jenis.ClientID %>').value;
            
            if (institutionName.length < 3) {
                alert('Institution name must be at least 3 characters long.');
                return false;
            }
            
            if (institutionType === '0') {
                alert('Please select an institution type.');
                return false;
            }
            
            // Check if email notification is enabled but service is offline
            const emailNotificationChecked = document.getElementById('<%= chkEmailNotification.ClientID %>').checked;
            if (emailNotificationChecked && !emailServiceStatus) {
                const proceed = confirm('Email service is currently offline. Institution will be saved but no notification email will be sent. Continue?');
                if (!proceed) {
                    return false;
                }
            }
            
            showLoading(true);
            return true;
        }
        
        // Reset form
        function resetForm() {
            if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
                document.getElementById('<%= Tx_Kolej.ClientID %>').value = '';
                document.getElementById('<%= Cb_Jenis.ClientID %>').selectedIndex = 0;
                document.getElementById('<%= chkEmailNotification.ClientID %>').checked = true;
                validateForm();
                return true;
            }
            return false;
        }
        
        // Confirm delete action
        function confirmDelete() {
            return confirm('Are you sure you want to delete this institution? This action cannot be undone.');
        }
        
        // Show/hide loading overlay
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = show ? 'flex' : 'none';
            }
        }
        
        // Hide loading on page load complete
        document.addEventListener('DOMContentLoaded', function() {
            showLoading(false);
        });
    </script>
</asp:Content>

