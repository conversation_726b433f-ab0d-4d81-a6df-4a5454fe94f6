Imports System.Data.OleDb
Imports System.Data
Imports System.Text.RegularExpressions
Imports System.Configuration
Imports System.Collections.Generic
Imports System.Web.Services
Imports System.Web

''' <summary>
''' SPMJ KOLEJ Password Management - Clean Implementation
''' Compatible with .NET 3.5.1
''' </summary>
Partial Public Class PasswordManagement
    Inherits System.Web.UI.Page

    ' Email service configuration
    Private ReadOnly emailServiceUrl As String = "http://localhost:5000"
    Private ReadOnly apiKey As String = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
    Private emailClient As EmailServiceClient

#Region "Page Events"

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Security check - ensure user is logged in
        If Session("Id_PG") Is Nothing OrElse Session("Id_PG").ToString().Trim() = "" Then
            Session.Abandon()
            Response.Redirect("Login_J.aspx")
            Return
        End If

        If Not IsPostBack Then
            ' Initialize email service client
            InitializeEmailService()
              ' Log access
            LogPasswordPageAccess()
        End If
    End Sub

    Protected Sub btnChangePassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnChangePassword.Click
        Try
            ' Get form data
            Dim currentPassword As String = txtCurrentPassword.Text.Trim()
            Dim newPassword As String = txtNewPassword.Text.Trim()
            Dim confirmPassword As String = txtConfirmPassword.Text.Trim()
            Dim userId As String = Session("Id_PG").ToString()

            ' Debug database schema and password storage
            DebugDatabaseSchema(userId)

            ' Validate input
            If Not ValidatePasswordInput(currentPassword, newPassword, confirmPassword) Then
                Return
            End If

            ' Verify current password
            If Not VerifyCurrentPassword(userId, currentPassword) Then
                ShowMessage("Kata laluan semasa tidak tepat.", "error")
                txtCurrentPassword.Focus()
                Return
            End If

            ' Validate password strength
            If Not ValidatePasswordStrength(newPassword, currentPassword) Then
                Return
            End If

            ' Update password
            If UpdateUserPassword(userId, newPassword) Then
                ' Send notification email
                SendPasswordChangeNotification(userId)
                
                ' Clear form
                ClearPasswordFields()
                
                ' Show success message
                ShowMessage("Kata laluan berjaya dikemaskini! Pemberitahuan telah dihantar ke e-mel anda.", "success")
                
                ' Log successful change
                LogPasswordChange(userId, True)
            Else
                ShowMessage("Ralat semasa menukar kata laluan. Sila cuba lagi.", "error")
                LogPasswordChange(userId, False)
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "error")
            System.Diagnostics.Debug.WriteLine("Password change error: " & ex.Message)
        End Try
    End Sub

#End Region

#Region "Validation Methods"

    ''' <summary>
    ''' Validate password input fields
    ''' </summary>
    Private Function ValidatePasswordInput(currentPassword As String, newPassword As String, confirmPassword As String) As Boolean
        If String.IsNullOrEmpty(currentPassword) Then
            ShowMessage("Sila masukkan kata laluan semasa.", "error")
            txtCurrentPassword.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(newPassword) Then
            ShowMessage("Sila masukkan kata laluan baharu.", "error")
            txtNewPassword.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(confirmPassword) Then
            ShowMessage("Sila sahkan kata laluan baharu.", "error")
            txtConfirmPassword.Focus()
            Return False
        End If

        If newPassword <> confirmPassword Then
            ShowMessage("Kata laluan baharu dan pengesahan tidak sama.", "error")
            txtConfirmPassword.Focus()
            Return False
        End If

        If newPassword = currentPassword Then
            ShowMessage("Kata laluan baharu mestilah berbeza daripada kata laluan semasa.", "error")
            txtNewPassword.Focus()
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' Validate password strength requirements
    ''' </summary>
    Private Function ValidatePasswordStrength(password As String, oldPassword As String) As Boolean
        Dim errors As New List(Of String)

        ' Check minimum length
        If password.Length < 8 Then
            errors.Add("Kata laluan mestilah sekurang-kurangnya 8 aksara")
        End If

        ' Check for uppercase letter
        If Not Regex.IsMatch(password, "[A-Z]") Then
            errors.Add("Kata laluan mesti mengandungi huruf besar (A-Z)")
        End If

        ' Check for lowercase letter
        If Not Regex.IsMatch(password, "[a-z]") Then
            errors.Add("Kata laluan mesti mengandungi huruf kecil (a-z)")
        End If

        ' Check for number
        If Not Regex.IsMatch(password, "[0-9]") Then
            errors.Add("Kata laluan mesti mengandungi nombor (0-9)")
        End If

        ' Check for special character
        If Not Regex.IsMatch(password, "[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]") Then
            errors.Add("Kata laluan mesti mengandungi aksara khas (!@#$%^&*)")
        End If

        If errors.Count > 0 Then
            ShowMessage("Kata laluan tidak memenuhi keperluan:<br/>• " & String.Join("<br/>• ", errors.ToArray()), "error")
            txtNewPassword.Focus()
            Return False
        End If

        Return True
    End Function

#End Region

#Region "Database Operations"    ''' <summary>
    ''' Get database connection string
    ''' </summary>
    Private Function GetConnectionString() As String
        Dim connStr As String = ""

        ' Try primary connection string
        If ConfigurationManager.ConnectionStrings("DefaultConnection") IsNot Nothing Then
            connStr = ConfigurationManager.ConnectionStrings("DefaultConnection").ConnectionString
        End If

        ' Try KOLEJ connection string
        If String.IsNullOrEmpty(connStr) AndAlso ConfigurationManager.ConnectionStrings("KOLEJConnection") IsNot Nothing Then
            connStr = ConfigurationManager.ConnectionStrings("KOLEJConnection").ConnectionString
        End If

        ' Try SPMJ connection string
        If String.IsNullOrEmpty(connStr) AndAlso ConfigurationManager.ConnectionStrings("SPMJConnection") IsNot Nothing Then
            connStr = ConfigurationManager.ConnectionStrings("SPMJConnection").ConnectionString
        End If

        ' Fallback to SPMJ_Mod ServerId if connection strings not found
        If String.IsNullOrEmpty(connStr) Then
            Try
                connStr = SPMJ_Mod.ServerId
                System.Diagnostics.Debug.WriteLine("Using SPMJ_Mod.ServerId fallback: " & connStr)
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine("SPMJ_Mod.ServerId fallback failed: " & ex.Message)
            End Try
        End If

        If String.IsNullOrEmpty(connStr) Then
            Throw New Exception("Connection string not found in web.config or SPMJ_Mod")
        End If

        Return connStr
    End Function    ''' <summary>
    ''' Verify current password against database
    ''' </summary>
    Private Function VerifyCurrentPassword(userId As String, currentPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(GetConnectionString())
            connection.Open()

            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT PWD, salt, pwd_encrypted FROM kj_pengguna WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@Id_PG", userId)

                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Verifying password for user: " & userId)

                Using reader As OleDbDataReader = command.ExecuteReader()
                    If reader.Read() Then
                        Dim storedPassword As String = reader("PWD").ToString()
                        Dim salt As String = If(IsDBNull(reader("salt")), "", reader("salt").ToString())
                        Dim isEncrypted As Boolean = If(IsDBNull(reader("pwd_encrypted")), False, Convert.ToBoolean(reader("pwd_encrypted")))

                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Stored password length: " & storedPassword.Length)
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Has salt: " & (Not String.IsNullOrEmpty(salt)).ToString())
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Is encrypted flag: " & isEncrypted.ToString())

                        ' Enhanced password verification with multiple fallbacks
                        
                        ' Method 1: Check if salt field contains full hash (new format)
                        If Not String.IsNullOrEmpty(salt) AndAlso salt.Contains("|") Then
                            Try
                                Dim parts As String() = salt.Split("|"c)
                                If parts.Length = 2 Then
                                    Dim fullHash As String = parts(0)
                                    Dim saltPart As String = parts(1)
                                    System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Using enhanced hash format")
                                    Return PasswordHelper.VerifyPassword(currentPassword, fullHash, saltPart)
                                End If
                            Catch ex As Exception
                                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Enhanced hash verification failed: " & ex.Message)
                            End Try
                        End If
                        
                        ' Method 2: Standard encrypted method
                        If isEncrypted AndAlso Not String.IsNullOrEmpty(salt) Then
                            Try
                                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Using standard encrypted method")
                                Return PasswordHelper.VerifyPassword(currentPassword, storedPassword, salt)
                            Catch ex As Exception
                                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Standard encrypted verification failed: " & ex.Message)
                            End Try
                        End If
                        
                        ' Method 3: Check if password looks encrypted (Base64)
                        If PasswordHelper.IsPasswordEncrypted(storedPassword) Then
                            Try
                                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Detected encrypted password format")
                                ' Try to extract salt from stored password or use default
                                If Not String.IsNullOrEmpty(salt) Then
                                    Return PasswordHelper.VerifyPassword(currentPassword, storedPassword, salt)
                                End If
                            Catch ex As Exception
                                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Encrypted format verification failed: " & ex.Message)
                            End Try
                        End If
                        
                        ' Method 4: Legacy plain text comparison
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Using legacy plain text comparison")
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Comparing input: '" & currentPassword & "' with stored: '" & storedPassword & "'")
                        If storedPassword = currentPassword Then
                            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Plain text password match successful")
                            Return True
                        End If
                        
                        ' Method 5: Case-insensitive comparison for legacy systems
                        If storedPassword.ToLower() = currentPassword.ToLower() Then
                            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Case-insensitive password match successful")
                            Return True
                        End If
                        
                        ' Method 6: Try with trimmed passwords
                        If storedPassword.Trim() = currentPassword.Trim() Then
                            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Trimmed password match successful")
                            Return True
                        End If
                        
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: All password verification methods failed")
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Stored password (first 5 chars): " & If(storedPassword.Length >= 5, storedPassword.Substring(0, 5), storedPassword))
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Input password (first 5 chars): " & If(currentPassword.Length >= 5, currentPassword.Substring(0, 5), currentPassword))
                        Return False
                        
                    Else
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: User not found in database: " & userId)
                        Return False
                    End If
                End Using
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Verify password error: " & ex.Message)
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Stack trace: " & ex.StackTrace)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Update user password in database
    ''' </summary>
    Private Function UpdateUserPassword(userId As String, newPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(GetConnectionString())
            connection.Open()

            ' Create encrypted password
            Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(newPassword)
            Dim hashedPassword As String = passwordEntry(0)
            Dim salt As String = passwordEntry(1)

            Using command As New OleDbCommand()
                command.Connection = connection

                ' Try enhanced update first (if columns exist)
                Try
                    command.CommandText = "UPDATE kj_pengguna SET PWD = ?, salt = ?, pwd_encrypted = 1, pwd_migrated = 1, migration_date = ? WHERE Id_PG = ?"
                    command.Parameters.Clear()
                    command.Parameters.AddWithValue("@PWD", hashedPassword.Substring(0, Math.Min(15, hashedPassword.Length))) ' Handle varchar(15) limitation
                    command.Parameters.AddWithValue("@salt", hashedPassword & "|" & salt) ' Store full hash in salt field
                    command.Parameters.AddWithValue("@migration_date", DateTime.Now)
                    command.Parameters.AddWithValue("@Id_PG", userId)

                    Dim rowsAffected As Integer = command.ExecuteNonQuery()
                    Return rowsAffected > 0

                Catch ex As Exception
                    ' Fallback to basic update
                    command.Parameters.Clear()
                    command.CommandText = "UPDATE kj_pengguna SET PWD = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@PWD", newPassword) ' Store as plain text for legacy compatibility
                    command.Parameters.AddWithValue("@Id_PG", userId)

                    Dim rowsAffected As Integer = command.ExecuteNonQuery()
                    Return rowsAffected > 0
                End Try
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Update password error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Get user email for notifications
    ''' </summary>
    Private Function GetUserEmail(userId As String) As String
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(GetConnectionString())
            connection.Open()

            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT email FROM kj_pengguna WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@Id_PG", userId)

                Dim result = command.ExecuteScalar()
                If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                    Return result.ToString()
                End If
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Get user email error: " & ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try

        Return ""
    End Function

#End Region

#Region "Email Service Integration"

    ''' <summary>
    ''' Initialize email service client
    ''' </summary>
    Private Sub InitializeEmailService()
        Try
            emailClient = New EmailServiceClient(emailServiceUrl)
            emailClient.SetApiKey(apiKey)
            System.Diagnostics.Debug.WriteLine("Email service initialized: " & emailServiceUrl)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Email service initialization failed: " & ex.Message)
            emailClient = Nothing
        End Try
    End Sub

    ''' <summary>    ''' <summary>
    ''' Send password change notification
    ''' </summary>
    Private Sub SendPasswordChangeNotification(userId As String)
        Try
            If emailClient IsNot Nothing Then
                Dim userEmail As String = GetUserEmail(userId)
                If Not String.IsNullOrEmpty(userEmail) Then
                    Dim success As Boolean = emailClient.SendPasswordChangeNotification(userId, userEmail)
                    System.Diagnostics.Debug.WriteLine("Password change notification sent: " & success.ToString())
                End If
            End If
        Catch ex As Exception
            ' Don't fail password change if email fails
            System.Diagnostics.Debug.WriteLine("Email notification error: " & ex.Message)
        End Try
    End Sub    ''' <summary>
    ''' Web method for AJAX health check
    ''' </summary>
    <WebMethod()> _
    Public Shared Function CheckEmailServiceHealth() As String
        Try
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Starting email service health check...")
            
            Dim testClient As New EmailServiceClient("http://localhost:5000")
            testClient.SetApiKey("SPMJ-EmailService-2024-SecureKey-MOH-Malaysia")
            
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Calling CheckHealth method...")
            Dim healthResult As String = testClient.CheckHealth()
            
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Health check result: " & healthResult)
              ' Improved health result parsing
            If healthResult.ToLower().Contains("healthy") OrElse healthResult.ToLower().Contains("ok") Then
                Dim successResponse As String = "{""status"":""online"",""message"":""Perkhidmatan e-mel beroperasi dengan normal""}"
                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Returning success response: " & successResponse)
                Return successResponse
            ElseIf healthResult.ToLower().Contains("error") OrElse healthResult.ToLower().Contains("failed") Then
                ' Clean up error message for JSON
                Dim cleanMessage As String = healthResult.Replace("""", "'").Replace(vbCrLf, " ").Replace(vbLf, " ").Replace(vbTab, " ")
                If cleanMessage.Length > 100 Then
                    cleanMessage = cleanMessage.Substring(0, 100) & "..."
                End If
                Dim offlineResponse As String = "{""status"":""offline"",""message"":""Perkhidmatan e-mel tidak dapat dihubungi: " & cleanMessage & """}"
                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Returning offline response: " & offlineResponse)
                Return offlineResponse
            Else
                ' Handle unexpected response format
                Dim unknownResponse As String = "{""status"":""offline"",""message"":""Respons tidak dijangka dari perkhidmatan e-mel""}"
                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Returning unknown response: " & unknownResponse)
                Return unknownResponse
            End If
            
        Catch webEx As System.Net.WebException
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Health check web exception: " & webEx.Message)
            Dim webErrorResponse As String = "{""status"":""offline"",""message"":""Perkhidmatan e-mel tidak dapat dihubungi (Rangkaian)""}"
            Return webErrorResponse
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Health check exception: " & ex.Message)
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Exception type: " & ex.GetType().Name)
            
            ' Ensure the error message is JSON-safe
            Dim safeErrorMessage As String = ex.Message.Replace("""", "'").Replace(vbCrLf, " ").Replace(vbLf, " ")
            If safeErrorMessage.Length > 50 Then
                safeErrorMessage = safeErrorMessage.Substring(0, 50) & "..."
            End If
            
            Dim errorResponse As String = "{""status"":""offline"",""message"":""Ralat sambungan: " & safeErrorMessage & """}"
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Returning error response: " & errorResponse)
            Return errorResponse
        End Try
    End Function

#End Region

#Region "Utility Methods"    ''' <summary>
    ''' Show message to user
    ''' </summary>
    Private Sub ShowMessage(message As String, messageType As String)
        Try
            ' Null check to prevent NullReferenceException
            If lblMessage IsNot Nothing Then
                lblMessage.Text = message
            End If
            
            If pnlMessage IsNot Nothing Then
                pnlMessage.Visible = True
            End If

            If divMessage IsNot Nothing Then
                Select Case messageType.ToLower()
                    Case "success"
                        divMessage.Attributes.Add("class", "message-panel message-success")
                    Case "error"
                        divMessage.Attributes.Add("class", "message-panel message-error")
                    Case "warning"
                        divMessage.Attributes.Add("class", "message-panel message-warning")
                    Case Else
                        divMessage.Attributes.Add("class", "message-panel message-warning")
                End Select
            End If
            
            ' Debug output for troubleshooting
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Pwd ShowMessage:")
            System.Diagnostics.Debug.WriteLine("  Message: " & message)
            System.Diagnostics.Debug.WriteLine("  Type: " & messageType)
            System.Diagnostics.Debug.WriteLine("  lblMessage is null: " & (lblMessage Is Nothing).ToString())
            System.Diagnostics.Debug.WriteLine("  pnlMessage is null: " & (pnlMessage Is Nothing).ToString())
            System.Diagnostics.Debug.WriteLine("  divMessage is null: " & (divMessage Is Nothing).ToString())
            
        Catch ex As Exception
            ' Fallback error handling
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Pwd ShowMessage Error: " & ex.Message)
            
            ' Try alternative display method
            Try
                If HttpContext.Current IsNot Nothing AndAlso HttpContext.Current.Response IsNot Nothing Then
                    HttpContext.Current.Response.Write("<script>alert('" & message.Replace("'", "\'") & "');</script>")
                End If
            Catch
                ' Last resort - write to debug only
                System.Diagnostics.Debug.WriteLine("CRITICAL: Cannot display message to user: " & message)
            End Try
        End Try
    End Sub

    ''' <summary>
    ''' Clear password input fields
    ''' </summary>
    Private Sub ClearPasswordFields()
        txtCurrentPassword.Text = ""
        txtNewPassword.Text = ""
        txtConfirmPassword.Text = ""
    End Sub

    ''' <summary>
    ''' Log password page access
    ''' </summary>
    Private Sub LogPasswordPageAccess()
        Try
            Dim userId As String = Session("Id_PG").ToString()
            System.Diagnostics.Debug.WriteLine("Password page accessed by user: " & userId & " at " & DateTime.Now.ToString())
        Catch ex As Exception
            ' Silent fail for logging
        End Try
    End Sub

    ''' <summary>
    ''' Log password change attempts
    ''' </summary>
    Private Sub LogPasswordChange(userId As String, success As Boolean)
        Try
            Dim status As String = If(success, "SUCCESS", "FAILED")
            System.Diagnostics.Debug.WriteLine("Password change " & status & " for user: " & userId & " at " & DateTime.Now.ToString())
        Catch ex As Exception
            ' Silent fail for logging
        End Try
    End Sub

    ''' <summary>
    ''' Debug method to check database schema and password storage
    ''' </summary>
    Private Sub DebugDatabaseSchema(userId As String)
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(GetConnectionString())
            connection.Open()

            ' First, check what columns exist in the table
            Using schemaCommand As New OleDbCommand()
                schemaCommand.Connection = connection
                schemaCommand.CommandText = "SELECT TOP 1 * FROM kj_pengguna WHERE Id_PG = ?"
                schemaCommand.Parameters.AddWithValue("@Id_PG", userId)

                Using reader As OleDbDataReader = schemaCommand.ExecuteReader()
                    If reader.Read() Then
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Database Schema Debug for user: " & userId)
                        
                        ' Check each possible password-related column
                        For i As Integer = 0 To reader.FieldCount - 1
                            Dim columnName As String = reader.GetName(i)
                            Dim columnValue As Object = reader.GetValue(i)
                              If (columnName.ToLower().Contains("pwd") OrElse _
                               columnName.ToLower().Contains("password") OrElse _
                               columnName.ToLower().Contains("salt") OrElse _
                               columnName.ToLower().Contains("encrypt")) Then
                                
                                Dim displayValue As String
                                If IsDBNull(columnValue) Then
                                    displayValue = "NULL"
                                ElseIf columnValue.ToString().Length > 20 Then
                                    displayValue = columnValue.ToString().Substring(0, 20) & "..."
                                Else
                                    displayValue = columnValue.ToString()
                                End If
                                
                                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Column '" & columnName & "' = '" & displayValue & "'")
                            End If
                        Next
                    End If
                End Using
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Schema debug error: " & ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Sub

#End Region

End Class
