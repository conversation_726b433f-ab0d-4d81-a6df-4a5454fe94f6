﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_Pelatih_Pinda3.aspx.vb" Inherits="SPMJ.P1_Pelatih_Pinda3" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            font-weight: bold;
            width: 382px;
        }
        .style4
        {
            font-family: Arial;
            font-size: 8pt;
            color: #000000;
            width: 382px;
        }
        .style5
        {
        }
        .style7
        {
            width: 382px;
            height: 27px;
        }
        .style8
        {
            height: 27px;
            }
        .style9
        {
            color: #CC0000;
        }
        .style10
        {
            width: 382px;
        }
        .style11
        {
            width: 20px;
        }
        .style12
        {
            height: 23px;
            width: 132px;
        }
        .style13
        {
            height: 23px;
            width: 20px;
        }
        .style16
        {
            height: 23px;
        }
        .style17
        {
            height: 20px;
            width: 132px;
        }
        .style18
        {
            color: #CC0000;
            width: 132px;
        }
        .style19
        {
            width: 132px;
        }
        .style20
        {
            height: 21px;
            width: 132px;
        }
        .style21
        {
            height: 27px;
            width: 132px;
        }
        .style24
        {
            width: 621px;
        }
        .style25
        {
            width: 621px;
            height: 27px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Localize ID="Localize1" runat="server"></asp:Localize>
    <div style="width:100%; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
    <table id="Table1" width="100%" style="font-variant: small-caps">
    <br>
    <tr><td>
    
    <table id="Table2" align="center" border="0" cellpadding="-1" cellspacing="0" 
        style="border: 1px solid black; WIDTH: 65%; HEIGHT: 436px; left: 0px; top: 70px; margin-left: 0px; position: static; font-family: Arial; font-size: 8pt; text-transform: none; line-height: 21px;" 
        width="100%" bgcolor="#F5F5F1">
        <tr>
            <td align="center" bgcolor="#006699" 
                valign="top" colspan="4" 
                
                style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #000000; vertical-align: middle; text-align: center; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bolder;" >
                pinda/semak rekod&nbsp; pelatih</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style10">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" valign="top">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                                        </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style17">
                &nbsp;</td>
        </tr>
        <tr style="line-height: 21px; background-color: #999966;" bgcolor="#999966">
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
                </td>
								<TD vAlign="middle" align="left" bgColor="#ffffff" 
                class="style10" 
                
                
                
                style="background-color: #999966; font-family: Arial; font-size: 8pt; color: #FFFFFF;">&nbsp; <b>Jenis 
                                    kursus</b></TD>
 
            <td align="left" bgcolor="#999966" valign="bottom" class="style9">
                <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" Width="280px" CssClass="std" 
                    AutoPostBack="True">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style18">
                </td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                </td>
            <td bgcolor="#999966" class="style1" colspan="1" 
                
                style="background-color: #999966; font-family: Arial; font-size: 8pt; color: #FFFFFF;">
                &nbsp; Kelayakan</td>
            <td bgcolor="#999966" class="style9" style="border-style: none" 
                valign="middle"><asp:DropDownList ID="Cb_Kelayakan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="1" Width="84px" 
                                AutoPostBack="True">
                            </asp:DropDownList>
                        <asp:DropDownList ID="Cb_Kelayakan0" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="1" Width="84px" 
                                AutoPostBack="True">
                            <asp:ListItem></asp:ListItem>
                            <asp:ListItem Value="1">SETARAF</asp:ListItem>
                            </asp:DropDownList>
                        </td>
            <td bgcolor="#ffffff" class="style18">
                </td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                </td>
            <td bgcolor="#999966" class="style1" colspan="1" 
                
                style="background-color: #999966; font-family: Arial; font-size: 8pt; color: #FFFFFF;">
                &nbsp; TAHUN KELAYAKAN</td>
            <td bgcolor="#999966" class="style9" style="border-style: none" 
                valign="middle">
                <asp:DropDownList ID="Cb_Thn_Kelayakan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="1" Width="84px" 
                                AutoPostBack="True">
                            </asp:DropDownList>
                        </td>
            <td bgcolor="#ffffff" class="style18">
                </td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
            <td bgcolor="#DFDFD0" 
                                         
                style="border-left-style: solid; border-left-width: 1px; border-left-color: #999966; border-bottom-style: none; border-bottom-width: 1px; border-bottom-color: #999966; border-right-style: solid; border-right-width: 1px; border-right-color: #999966;" 
                align="center" colspan="2">
                &nbsp;</td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
                                 </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
                                     <td bgcolor="#DFDFD0" 
                                         
                style="border-style: none none none solid; border-color: #999966; border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px;" 
                class="style10">
                                         &nbsp;&nbsp;</td>
            <td bgcolor="#DFDFD0" 
                                         
                                         style="border-style: none solid none none; border-color: #999966; border-left-width: 1px; border-bottom-width: 1px; border-right-width: 1px;" 
                                         align="center" colspan="1">
                            <table id="Table3x" border="0" cellpadding="-1" cellspacing="-1"                               
                                style="border: 1px solid #999966; " 
                                align="left"><tr><td>
                            <table id="Table3" border="0" cellpadding="-1" cellspacing="-1" 
                                style="text-align: left; margin-left: 0px; width: 415px;" align="left">
                                <tr style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966" 
                                    bgcolor="WhiteSmoke">
                                    <td bordercolor="#000000"                                                                                                                        
                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                        class="style11">
                                        &nbsp;</td><td                                                                                 
                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                        align="left">
                                        &nbsp;<b>SUBJEK</b>&nbsp;</td>
                                    <td align="char"                                         
                                        style="BORDER-BOTTOM: 1px solid #999966; font-weight: 700;">
                                        KEPUTUSAN</td>
                                    <td                                       
                                        
                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;">
                                        &nbsp;</td>
                                </tr>
                                <tr bgcolor="White">
                                    <td bgcolor="White" class="style13">
                                        <asp:TextBox ID="Textbox1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 219px; HEIGHT: 23px" bgcolor="White">
                                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="181px">BAHASA MELAYU</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts1" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="2" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 22px; HEIGHT: 23px">
                                    </td>
                                </tr>
                                <tr>
                                    <td bgcolor="whitesmoke" class="style13">
                                        <asp:TextBox ID="Textbox2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">2.</asp:TextBox>
                                    </td>
                                    <td bgcolor="whitesmoke" style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">BAHASA INGGERIS</asp:TextBox>
                                    </td>
                                    <td bgcolor="whitesmoke" style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts2" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="3" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td bgcolor="WhiteSmoke" style="WIDTH: 22px; HEIGHT: 23px">
                                    </td>
                                </tr>
                                <tr bgcolor="White">
                                    <td bgcolor="White" class="style13">
                                        <asp:TextBox ID="Textbox6" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">3.</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 219px; HEIGHT: 23px" bgcolor="White">
                                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">MATEMATIK</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts3" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="4" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 22px; HEIGHT: 23px">
                                    </td>
                                </tr>
                                <tr>
                                    <td bgcolor="#f5f5f5" class="style13">
                                        <asp:TextBox ID="Textbox7" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">4.</asp:TextBox>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="35" Width="181px">SAINS</asp:TextBox>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts4" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="5" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 22px; HEIGHT: 23px">
                                    </td>
                                </tr>
                                <tr bgcolor="White">
                                    <td class="style13">
                                        <asp:TextBox ID="Textbox8" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Visible="False" Width="16px">5.</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Sbj5" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Visible="False" Width="181px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts5" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="6" Visible="False" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 22px; HEIGHT: 23px" align="center">
                                        <asp:Button ID="Bt4" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                            Height="20px" tabIndex="3" Text="+" Width="24px" Visible="False" />
                                    </td>
                                </tr>
                                <tr>
                                    <td bgcolor="#f5f5f5" class="style13">
                                        <asp:TextBox ID="Textbox9" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Visible="False" Width="16px">6.</asp:TextBox>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Sbj6" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Visible="False" Width="181px">
                                        </asp:DropDownList>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts6" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="7" Visible="False" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 22px; HEIGHT: 23px">
                                        <asp:Button ID="Bt5" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                            Height="20px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                    </td>
                                </tr>
                                <tr bgcolor="White">
                                    <td class="style13">
                                        <asp:TextBox ID="Textbox10" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Visible="False" Width="16px">7.</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Sbj7" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Visible="False" Width="181px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts7" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="8" Visible="False" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 22px; HEIGHT: 23px">
                                        <asp:Button ID="Bt6" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                            Height="20px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                    </td>
                                </tr>
                                <tr>
                                    <td bgcolor="#f5f5f5" class="style13">
                                        <asp:TextBox ID="Textbox11" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Visible="False" Width="16px">8.</asp:TextBox>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Sbj8" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Visible="False" Width="181px">
                                        </asp:DropDownList>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts8" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Visible="False" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td bgcolor="#f5f5f5" style="WIDTH: 22px; HEIGHT: 22px">
                                        <asp:Button ID="Bt7" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                            Height="20px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                    </td>
                                </tr>
                                <tr bgcolor="White">
                                    <td class="style13">
                                        <asp:TextBox ID="Textbox12" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Visible="False" Width="16px">9.</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Sbj9" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Visible="False" Width="181px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts9" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Visible="False" Width="80px">
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 22px; HEIGHT: 23px">
                                        <asp:Button ID="Bt8" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                            Height="23px" tabIndex="3" Text="+" Visible="False" Width="24px" />
                                    </td>
                                </tr>
                                <tr style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966" 
                                    bgcolor="WhiteSmoke">
                                    <td bordercolor="#000000"                                                                                                                        
                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                        class="style11">
                                        &nbsp;</td><td                                                                                 
                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;" 
                                        align="left">
                                        &nbsp;<b>MAKLUMAT KHAS</b>&nbsp;</td>
                                    <td align="char"                                         
                                        style="BORDER-BOTTOM: 1px solid #999966; font-weight: 700;">
                                        KEPUTUSAN</td>
                                    <td                                       
                                        
                                        style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966;">
                                        &nbsp;</td>
                                </tr>
                                <tr bgcolor="White">
                                    <td class="style13">
                                        <asp:TextBox ID="Textbox3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">1.</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Sbj10" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Width="181px">
                                            <asp:ListItem></asp:ListItem>
                                            <asp:ListItem Value="1">MATRIKULASI</asp:ListItem>
                                            <asp:ListItem Value="2">FOUNDATION PROGRAMME</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 82px; HEIGHT: 23px">
                <asp:TextBox ID="Tx_Kpts10" runat="server" CssClass="std" Width="76px" 
                    Wrap="False"></asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 22px; HEIGHT: 23px">
                                        &nbsp;</td>
                                </tr>
                                <tr bgcolor="White">
                                    <td class="style13">
                                        <asp:TextBox ID="Textbox4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="36" Width="16px">2.</asp:TextBox>
                                    </td>
                                    <td style="WIDTH: 219px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Sbj11" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Width="181px">
                                            <asp:ListItem></asp:ListItem>
                                            <asp:ListItem Value="1">MUET</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 82px; HEIGHT: 23px">
                                        <asp:DropDownList ID="Cb_Kpts11" runat="server" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" tabIndex="18" Width="80px">
                                            <asp:ListItem></asp:ListItem>
                                            <asp:ListItem Value="0">TIADA</asp:ListItem>
                                            <asp:ListItem>1</asp:ListItem>
                                            <asp:ListItem>2</asp:ListItem>
                                            <asp:ListItem>3</asp:ListItem>
                                            <asp:ListItem>4</asp:ListItem>
                                            <asp:ListItem>5</asp:ListItem>
                                            <asp:ListItem>6</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td style="WIDTH: 22px; HEIGHT: 23px">
                                        &nbsp;</td>
                                </tr>
                            </table>
                                </td></tr></table>
                                     </td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;&nbsp;</td>
                                 </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
            <td bgcolor="#DFDFD0" 
                                         
                style="border-left-style: solid; border-left-width: 1px; border-left-color: #999966; border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #999966; border-right-style: solid; border-right-width: 1px; border-right-color: #999966;" 
                align="center" colspan="2">
                &nbsp;</td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
                                 </tr>
        <tr style="line-height: 21px">
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
                &nbsp;</td>
								<TD vAlign="top" align="center" bgColor="#ffffff" 
                class="style5" colspan="2" style="color: #CC0000">&nbsp; * maklumat mandatori&nbsp;</TD>
 
            <td align="left" bgcolor="#ffffff" valign="top" class="style12">
                &nbsp;</td>
        </tr>
        <tr style="line-height: 21px">
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style24"><span class="style9">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="White" 
                class="style10">&nbsp; Nama</td>
                                     <td bgcolor="White">
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style19">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style24"><span class="style9">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style10">&nbsp; No. kp/ tentera/ pasport</td>
            <td bgcolor="White">
                <asp:TextBox ID="Tx_NoKP" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style18">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style24"><span class="style9">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="White" 
                class="style10">&nbsp; Warganegara</TD>
            <td align="left" bgcolor="White" valign="top" class="style16">
                <asp:DropDownList ID="Cb_Warga" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" CssClass="std">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style12">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style10">&nbsp; Jantina</TD>
            <td align="left" bgcolor="white" valign="top" class="style16">
                <asp:DropDownList ID="Cb_Jantina" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" CssClass="std">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="L">LELAKI</asp:ListItem>
                    <asp:ListItem Value="P">PEREMPUAN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style12">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="White" 
                class="style10">&nbsp; Bangsa</TD>
            <td align="left" bgcolor="White" valign="top" class="style16">
                <asp:DropDownList ID="Cb_Bangsa" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="12" Width="190px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="L">MELAYU</asp:ListItem>
                    <asp:ListItem Value="P">CINA</asp:ListItem>
                    <asp:ListItem Value="India">INDIA</asp:ListItem>
                    <asp:ListItem Value="Lain-Lain">LAIN-LAIN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style12">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style10">&nbsp; Agama</TD>
            <td align="left" bgcolor="white" valign="top" class="style16">
                <asp:DropDownList ID="Cb_Agama" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="13" Width="190px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="L">ISLAM</asp:ListItem>
                    <asp:ListItem Value="P">BUDDHA</asp:ListItem>
                    <asp:ListItem Value="Hindu">HINDU</asp:ListItem>
                    <asp:ListItem Value="Kristian">KRISTIAN</asp:ListItem>
                    <asp:ListItem Value="Lain-Lain">LAIN-LAIN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style12">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="White" 
                class="style10">&nbsp; Taraf perkahwinan</TD>
            <td align="left" bgcolor="White" valign="top" class="style16">
                <asp:DropDownList ID="Cb_Kahwin" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="22" Width="190px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="L">BUJANG</asp:ListItem>
                    <asp:ListItem Value="P">BERKAHWIN</asp:ListItem>
                    <asp:ListItem Value="Duda">DUDA</asp:ListItem>
                    <asp:ListItem Value="Janda">JANDA</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style12">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style10">&nbsp; Alamat tetap</TD>
            <td align="left" bgcolor="white" valign="top" 
                id="Tx_TP_Alamat">
                <asp:TextBox ID="Tx_TP_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style17">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="White" 
                class="style10">&nbsp; Poskod</TD>
            <td align="left" bgcolor="white" valign="top">
                <asp:TextBox ID="Tx_TP_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style17">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style10">&nbsp; Bandar</TD>
            <td align="left" bgcolor="white" valign="top" class="style9">
                <asp:TextBox ID="Tx_TP_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style18">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="White" 
                class="style10">&nbsp; Negeri</TD>
            <td align="left" bgcolor="White" valign="top" class="style16">
                <asp:DropDownList ID="Cb_TP_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="193px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style20">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style10">&nbsp; No. telefon</TD>
            <td align="left" bgcolor="white" valign="top">
                <asp:TextBox ID="Tx_Tel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style17">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="White" 
                class="style10">&nbsp;
									E-mel</TD>
            <td align="left" bgcolor="White" valign="top">
                <asp:TextBox ID="Tx_Emel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style17">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style10">&nbsp; Nama waris terdekat</TD>
            <td align="left" bgcolor="white" valign="top">
                <asp:TextBox ID="Tx_W_Nama" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style17">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style24">
            </td>
								<TD vAlign="top" align="left" bgColor="White" colSpan="1"
									rowSpan="1" class="style10">&nbsp;&nbsp; Alamat Waris</TD>
            <td align="left" bgcolor="White" valign="top">
                <asp:TextBox ID="Tx_W_Alamat" runat="server" BorderStyle="Groove" 
                    Font-Names="Arial" Font-Size="8pt" Height="60px" tabIndex="26" 
                    TextMode="MultiLine" Width="283px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style17">
            </td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
								<TD bgColor="white" class="style10">&nbsp; Poskod&nbsp;</TD>
            <td bgcolor="White">
                <asp:TextBox ID="Tx_W_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
								<TD bgColor="White" class="style10">&nbsp; Bandar&nbsp;</TD>
            <td bgcolor="white">
                <asp:TextBox ID="Tx_W_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
								<TD bgColor="white" class="style10">&nbsp; Negeri&nbsp;</TD>
            <td bgcolor="white">
                <asp:DropDownList ID="Cb_W_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="193px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
								<TD bgColor="white" class="style10">&nbsp; NegARA</TD>
            <td bgcolor="white">
                <asp:DropDownList ID="Cb_Negara" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="283px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
            </td>
								<TD bgColor="White" class="style10">&nbsp; No. telefon waris</TD>
            <td bgcolor="white">
                <asp:TextBox ID="Tx_W_Tel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td bgcolor="#ffffff" class="style19">
            </td>
        </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style24"><span class="style9">*</span>
                        </td>
								<TD vAlign="top" bgColor="#ffffff" class="style10">&nbsp; Kolej/ Institusi</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="369px">
                            </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style19">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                        </td>
								<TD vAlign="top" bgColor="White" class="style10">&nbsp; Tajaan</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:DropDownList ID="Cb_Tajaan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="193px">
                            </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style19">
                        </td>
                    </tr>
                    <tr style="line-height: 21px">
                        <td align="right" bgcolor="#ffffff" class="style24"><span class="style9">*</span>
                        </td>
								<TD vAlign="top" bgColor="#ffffff" class="style10">&nbsp; Sesi Pengambilan</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-SIZE: 8pt; FONT-FAMILY: arial; FONT-VARIANT: small-caps; white-space: normal;" 
                            valign="top">
                            <asp:DropDownList ID="Cb_Sesi_Bulan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="100px">
                            </asp:DropDownList><asp:DropDownList ID="Cb_Sesi_Tahun" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="93px">
                            </asp:DropDownList>
                            </td>
                        <td bgcolor="#ffffff" class="style19">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style25"><span class="style9">*</span>
                        </td>
								<TD vAlign="top" align="left" bgColor="White" class="style7">&nbsp; Tarikh Mula Latihan</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style8">
                            <asp:TextBox ID="Tx_M_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:maskededitextender ID="Tx_M_Latihan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_M_Latihan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:maskededitextender>
                            <cc1:calendarextender ID="Tx_M_Latihan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_M_Latihan" 
                                Format="dd/MM/yyyy">
                            </cc1:calendarextender>
                            </td>
                        <td bgcolor="#ffffff" class="style21">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="White" class="style24">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style4">
                                                        &nbsp; Tarikh tamat Latihan</td>
                        <td bgcolor="#ffffff">
                            <asp:TextBox ID="Tx_T_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:maskededitextender ID="Tx_T_Latihan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_T_Latihan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:maskededitextender>
                            <cc1:calendarextender ID="Tx_T_Latihan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_T_Latihan" 
                                Format="dd/MM/yyyy">
                            </cc1:calendarextender>
                                     </td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="White" class="style24">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style4">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="White" class="style24">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style4">
                            &nbsp; Markah berterusan<br />
                            &nbsp; (jumlah keseluruhan)</td>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:TextBox ID="Tx_Markah" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                            &nbsp;</td>
                        <td bgcolor="White" class="style4">
                            &nbsp; Tindakan tatatertib</td>
                        <td bgcolor="#ffffff">
                <asp:DropDownList ID="Cb_Tatatertib" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="100px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style10">
                            &nbsp; Jumlah cuti sakit</td>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:TextBox ID="Tx_Cuti" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            &nbsp;*Sepanjang tempoh latihan</td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style10">
                            &nbsp; Jumlah cuti sakit<br />
&nbsp; yang diganti</td>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:TextBox ID="Tx_Cuti_Ganti" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            &nbsp;*Sepanjang tempoh latihan</td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                            &nbsp;</td>
                        <td bgcolor="White" class="style10" valign="top">
                            &nbsp; Sebab cuti sakit</td>
                        <td bgcolor="#ffffff">
                            <asp:TextBox ID="Tx_Cuti_Sebab" runat="server" CssClass="std" Width="283px" 
                                Height="57px" TextMode="MultiLine"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style10">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style19">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                            </td>
                        <td bgcolor="#999966" colspan="2" 
                            style="border: 1px solid #999966; color: #FFFFFF;">
                            &nbsp; SENARAI SEMAK</td>
                        <td bgcolor="#ffffff" class="style12">
                            </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                        </td>
                        <td bgcolor="#F5F5F1" colspan="2" valign="top" 
                            style="border: 1px solid #999966; line-height: normal" align="left"><table align="center"><tr>
                                <td align="left">
                                    <asp:CheckBoxList ID="chk_Semak" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" RepeatColumns="2" RepeatDirection="Horizontal" 
                                Width="429px" Height="31px">
                                <asp:ListItem VALUE="Salinan Kad Pengenalan">Salinan Kad Pengenalan</asp:ListItem>
                                <asp:ListItem VALUE="Salinan Sijil Lahir">Salinan Sijil Lahir</asp:ListItem>
                                <asp:ListItem VALUE="Salinan Sijil Warganegara">Salinan Sijil Warganegara</asp:ListItem>
                                <asp:ListItem VALUE="Salinan Sijil Kesihatan">Salinan Sijil Kesihatan</asp:ListItem>
                                <asp:ListItem VALUE="Salinan Sijil Akademik">Salinan Sijil Akademik / Sijil Latihan</asp:ListItem>
                                <asp:ListItem VALUE="Salinan KPT (Pelatih Asing)">Salinan KPT (Pelatih Asing)</asp:ListItem>
                                <asp:ListItem VALUE="Salinan IELTS/TOFEL (Pelatih Asing)">Salinan IELTS/TOFEL (Pelatih Asing)</asp:ListItem>
                                <asp:ListItem VALUE="Salinan MUET">Salinan MUET</asp:ListItem></asp:CheckBoxList></td></tr></table>
                            
                        </td>
                        <td bgcolor="#ffffff" class="style19">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                        </td>
                        <td bgcolor="#ffffff" colspan="2" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style19">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style24">
                        </td>
                        <td bgcolor="#CE0000" colspan="2" valign="top" 
                            style="border: 1px solid #CC1A1E; color: #FFFFFF;">
                            &nbsp; PERAKUAN KOLEJ</td>
                        <td bgcolor="#ffffff" class="style19">
                        </td>
                    </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle" 
                style="border: 1px solid #CE0000;">
                <table align="center" style="width: 530px"><tr style="line-height: 10px">
                    <td class="style68">
                    </td>
                    <td align="left" 
                        
                        style="font-size: 8pt; font-family: Arial; text-align: justify; color: #CE0000;" 
                        class="style68">
                    </td>
                    <td class="style68">
                    </td>
                    </tr><tr>
                        <td>
                        </td>
                        <td align="left" 
                        style="font-size: 8pt; font-family: Arial; text-align: justify; color: #CE0000;">
                    Kolej ini memperakui bahawa semua maklumat yang diberikan adalah benar. 
                    Sekiranya maklumat tersebut didapati palsu, permohonan pelajar ini akan terbatal 
                    dan kolej ini akan bertanggungjawab sepenuhnya terhadap sebarang tuntutan 
                    mahkamah oleh pihak pelajar sekiranya ada.</td>
                        <td>
                        </td>
                    </tr><tr style="line-height: 10px">
                        <td>
                            &nbsp;</td>
                        <td align="left" 
                        style="font-size: 8pt; font-family: Arial; text-align: justify; color: #CE0000;">
                            &nbsp;</td>
                        <td>
                            &nbsp;</td>
                    </tr></table>
                            
                        </td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle">
                &nbsp;</td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle">
                <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="KEMASKINI" Width="96px" />
            </td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
        <tr>
            <td bgcolor="#ffffff" class="style24">
                &nbsp;</td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle">
                &nbsp;</td>
            <td bgcolor="#ffffff" class="style19">
                &nbsp;</td>
        </tr>
    </table></td></tr></table>
    
    </br></br>
    </div>

</asp:Content>