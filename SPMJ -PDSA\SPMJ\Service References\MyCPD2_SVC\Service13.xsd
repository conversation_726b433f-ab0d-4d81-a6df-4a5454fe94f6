<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="UserDetails">
    <xs:sequence>
      <xs:element minOccurs="0" name="DateEnd" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DateStart" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Hcp" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdentityNo" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserDetails" nillable="true" type="tns:UserDetails" />
</xs:schema>