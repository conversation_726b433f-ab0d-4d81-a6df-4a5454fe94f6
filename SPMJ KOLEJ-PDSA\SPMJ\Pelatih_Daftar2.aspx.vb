﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class Pelatih_Daftar2
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Comment Original 05042022 OSH 
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        'New Landing Page05042022-OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login_j.aspx")

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Original 17082020 - OSH 
        If Cb_NoKP.SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

      
        'NEGERI
        'Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        'Rdr = Cmd.ExecuteReader()
        'Cb_TP_Negeri.Items.Clear()
        'Cb_TP_Negeri.Items.Add("")
        'Cb_W_Negeri.Items.Clear()
        'Cb_W_Negeri.Items.Add("")
        'While Rdr.Read
        '    Cb_TP_Negeri.Items.Add(Rdr(0))
        '    Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
        '    Cb_W_Negeri.Items.Add(Rdr(0))
        '    Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()

        'KURSUS
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS WHERE XM = 1  AND ID_KURSUS = 5 ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'BULAN
        Cb_Sesi_Bulan.Items.Add("(BULAN)") : Cb_Sesi_Bln.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI") : Cb_Sesi_Bln.Items.Add("JANUARI")
        Cb_Sesi_Bulan.Items.Add("FEBRUARI") : Cb_Sesi_Bln.Items.Add("FEBRUARI")
        Cb_Sesi_Bulan.Items.Add("MAC") : Cb_Sesi_Bln.Items.Add("MAC")
        Cb_Sesi_Bulan.Items.Add("APRIL") : Cb_Sesi_Bln.Items.Add("APRIL")
        Cb_Sesi_Bulan.Items.Add("MEI") : Cb_Sesi_Bln.Items.Add("MEI")
        Cb_Sesi_Bulan.Items.Add("JUN") : Cb_Sesi_Bln.Items.Add("JUN")
        Cb_Sesi_Bulan.Items.Add("JULAI") : Cb_Sesi_Bln.Items.Add("JULAI")
        Cb_Sesi_Bulan.Items.Add("OGOS") : Cb_Sesi_Bln.Items.Add("OGOS")
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER") : Cb_Sesi_Bln.Items.Add("SEPTEMBER")
        Cb_Sesi_Bulan.Items.Add("OKTOBER") : Cb_Sesi_Bln.Items.Add("OKTOBER")
        Cb_Sesi_Bulan.Items.Add("NOVEMBER") : Cb_Sesi_Bln.Items.Add("NOVEMBER")
        Cb_Sesi_Bulan.Items.Add("DISEMBER") : Cb_Sesi_Bln.Items.Add("DISEMBER")

        'TAHUN
        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        Cb_Sesi_Thn.Items.Add("(TAHUN)")
        'Comment Original 01072022 -OSH
        'For i = 0 To 1 
        'Add years to list 01072022 -OSH 
        'For i = 0 To 4
        For i = 0 To 8
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i)
            Cb_Sesi_Thn.Items.Add(Year(Now) - i)
        Next

        'BANGSA-07092013-OSH
        Cmd.CommandText = "SELECT DC_RACE,ID_RACE FROM SPMJ_REF_RACE ORDER BY ID_RACE"
        Rdr = Cmd.ExecuteReader()
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("")
        While Rdr.Read
            Cb_Bangsa.Items.Add(Rdr(0))
            Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KOLEJ
        Cb_Kolej.Items.Add(Session("Dc_KOLEJ"))
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Session("Id_KOLEJ")
        Cb_Kolej.SelectedIndex = 0

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'Add control id type loading - 18102013
        With Cb_NoKP
            'Add Army textbox Control 10082013-OSH
            If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

            'Add Army textbox Control 10082013-OSH
            If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1)
            'Comment Ori 10082013-OSH
            'If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : Fn_Negara(1)
            'Add Army textbox Control 10082013-OSH
            If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0)
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With

        'STPM OR A-LEVEL
        Cb_Aliran.Items.Clear()
        Cb_Aliran.Items.Add("(PILIHAN)")
        Cb_Aliran.Items(0).Value = 0
        Cb_Aliran.Items.Add("STPM")
        Cb_Aliran.Items(1).Value = 1
        Cb_Aliran.Items.Add("A-LEVEL")
        Cb_Aliran.Items(2).Value = 2

        'Tempatan/ antarabangsa
        Cb_Kelayakan_Taraf.Items.Clear()
        Cb_Kelayakan_Taraf.Items.Add("(PILIHAN)")
        Cb_Kelayakan_Taraf.Items(0).Value = 0
        Cb_Kelayakan_Taraf.Items.Add("-")
        Cb_Kelayakan_Taraf.Items(1).Value = 1
        Cb_Kelayakan_Taraf.Items.Add("SETARAF")
        Cb_Kelayakan_Taraf.Items(2).Value = 2


        'Load identity default 17092019 - OSH 
        'Comment Original 27112020 - OSH 
        'Cb_NoKP.SelectedValue = 0 : Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)
        'Load preset values 27112020 - OSH 
        Cb_NoKP.SelectedValue = 0 : Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1)
    End Sub
    Public Sub Fn_Negara(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"
        Else
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        Cb_W_Negara.Items.Clear()
        Cb_W_Negara.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negara.Items.Add(Rdr(0))
            Cb_W_Negara.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        If X = 1 Then
            Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI WHERE Dc_NEGERI <> 'LUAR NEGARA'ORDER BY Dc_NEGERI"
        Else
            Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI  WHERE Dc_NEGERI = 'LUAR NEGARA'"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        Cb_W_Negeri.Items.Clear()
        Cb_W_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negeri.Items.Add(Rdr(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cn.Close()
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Cb_Kelayakan_Taraf.Visible = True
        'Comment Original 12092019 - OSH 
        'Cb_Kelayakan.Items.Add("STPM") : Isi_Subjek(3)

        'Add SPTM option only 12092019 - OSH 
        Cb_Kelayakan.Items.Add("STPM")
        Panel3.Visible = True : Panel6.Visible = True : PanelSemak.Visible = True

    End Sub

    Public Sub Isi_Subjek(ByVal X As Int16)
        Cb_Luar.Visible = True ': Cb_Luar.SelectedIndex = 0 : Cb_Luarx.Visible = False
        'Cb_SbjM.Visible = True : Cb_SbjM.SelectedIndex = 0 : Cb_SbjMx.Visible = False
        Cb_SbjM.Visible = True : Cb_SbjMx.Visible = False

        'Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
        'Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
        'Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
        'Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
        'Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        'Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
        'Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

        Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
        Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
        Cb_SbjT1.Visible = True : Cb_SbjT2.Visible = True : Cb_SbjT3.Visible = True : Cb_SbjT4.Visible = True : Cb_SbjT5.Visible = True
        Cb_SbjT1x.Visible = False : Cb_SbjT2x.Visible = False : Cb_SbjT3x.Visible = False : Cb_SbjT4x.Visible = False : Cb_SbjT5x.Visible = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'PMR' order by dc_subjek"
        If X = 2 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
        If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
        'Add A-Level Subjects 29072019 -OSH 
        If X = 4 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'A-L' order by dc_subjek"
        'Add O-Level Subjects 02082019 -OSH 
        If X = 5 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'O-L' order by dc_subjek"
        If X = 0 Then Exit Sub
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Cb_Kursus.SelectedIndex <> 4 Then
                If X = 3 Or X = 4 Then  ' STPM or A-Level
                    Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
                ElseIf X = 2 Or X = 5 Then ' SPM
                    Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
                End If
                'Else
                '    Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                '    Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                '    Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                '    Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                '    Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
            End If
        End While
        Rdr.Close()
        Cn.Close()


        If X = 2 Then

            Gred_SPM(Cb_Kpts2)
            Gred_SPM(Cb_Kpts3)
            Gred_SPM(Cb_Kpts4)
            Gred_SPM(Cb_Kpts5)
            Gred_SPM(Cb_Kpts6)


        ElseIf X = 3 Then
            Gred_STPM(Cb_KptsT1)
            Gred_STPM(Cb_KptsT2)
            Gred_STPM(Cb_KptsT3)
            Gred_STPM(Cb_KptsT4)
            Gred_STPM(Cb_KptsT5)

            'Populate A-Level Grades 29072019 -OSH 
        ElseIf X = 4 Then
            Gred_O_A_Level(Cb_KptsT1)
            Gred_O_A_Level(Cb_KptsT2)
            Gred_O_A_Level(Cb_KptsT3)
            Gred_O_A_Level(Cb_KptsT4)
            Gred_O_A_Level(Cb_KptsT5)


            'Populate O-Level Grades 01082019 -OSH 
        ElseIf X = 5 Then

            Gred_O_A_Level(Cb_Kpts2)
            Gred_O_A_Level(Cb_Kpts3)
            Gred_O_A_Level(Cb_Kpts4)
            Gred_O_A_Level(Cb_Kpts5)
            Gred_O_A_Level(Cb_Kpts6)
        End If

    End Sub

    Protected Sub Cb_Kelayakan_Taraf_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan_Taraf.SelectedIndexChanged
        'Add SOP 2018 rules 19072019 - OSH
        If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedValue = 0 Then
            Cb_Sbj2.Text = "" : Cb_Sbj3.Text = "" : Cb_Sbj4.Text = ""
            Cb_Kpts2.Items.Clear() : Cb_Kpts3.Items.Clear() : Cb_Kpts4.Items.Clear() : Cb_Kpts5.Items.Clear() : Cb_Kpts6.Items.Clear()
        ElseIf Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedValue = 1 Then

            Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
            Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
            Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
            Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
            Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
            Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
            Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False


            Isi_Subjek(2) : Fn_Inggeris(1) : Fn_Lanjutan(1) 'Populate SPM grades

            If Cb_Kpts2.Enabled = False Then
                Cb_Kpts2.Enabled = True
            ElseIf Cb_Kpts3.Enabled = False And Cb_Kpts4.Enabled = False And Cb_Kpts5.Enabled = False And Cb_Sbj5.Enabled = False And Bt4.Enabled = False Then
                Cb_Kpts3.Enabled = True : Cb_Kpts4.Enabled = True : Cb_Kpts5.Enabled = True : Cb_Sbj5.Enabled = True : Bt4.Enabled = True
            End If

            'Load Identity list 17092019 - OSH 
            Cb_NoKP.Items.Add("MYKAD")
            Cb_NoKP.Items.Item(0).Value = "0"
            Cb_NoKP.Items.Add("TENTERA")
            Cb_NoKP.Items.Item(1).Value = "1"

        ElseIf Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedValue = 2 Then
            Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
            Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
            Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
            Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
            Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
            Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
            Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False


            Cb_Kpts2.Enabled = False
            Isi_Subjek(5) : Fn_Inggeris(2) : Fn_Lanjutan(0) 'Populate O-Level grades 

            'Load Identity list 17092019 - OSH 
            Cb_NoKP.Items.Add("PASSPORT")
            'Fix Pass Value 17082020 - OSH
            : Cb_NoKP.Items.Item(0).Value = "2"
            'Populate countries and states 17082020  - OSH 
            : Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0)
        End If
    End Sub

    Protected Sub Cb_Aliran_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Aliran.SelectedIndexChanged
        If Cb_Aliran.SelectedValue = 1 Then
            Isi_Subjek(3) 'SPTM
        ElseIf Cb_Aliran.SelectedValue = 2 Then
            Isi_Subjek(4) 'A-LEVEL
        End If
    End Sub

    Protected Sub Bt4_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt4.Click
        'Add + button rows for SPM subjects
        Cb_Sbj5.Visible = True
        Cb_Kpts5.Visible = True
        Bt5.Visible = True
        Bt4.Visible = False
        Textbox8.Visible = True
    End Sub

    Protected Sub Bt5_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt5.Click
        'Add + button rows for SPM subjects
        Cb_Sbj6.Visible = True
        Cb_Kpts6.Visible = True
        'Bt6.Visible = True
        Bt5.Visible = False
        Textbox9.Visible = True
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        If Chk_Subjek() = True Then Exit Sub


        'Medan Mandatori...
        Dim X As String = ""
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.Text.Trim = "-" Then X += "Warganegara, "
        'Add Mandatory for brith date & brith place 17102013-OSH 
        If Tx_Tkh_Lahir.Text.Trim = "" Then X += "Tarikh Lahir, "
        If Tx_Tp_Lahir.Text.Trim = "" Then X += "Tempat Lahir , "
        If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
        If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
        If Cb_Agama.Text.Trim = "" Then X += "Agama, "
        If Cb_Kahwin.Text.Trim = "" Then X += "Taraf Perkahwinan, "
        If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
        If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
        If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
        If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
        If Tx_Tel.Text.Trim = "" Then X += "No. Telefon, "
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        'Check No KP length
        X = ""
        With Cb_NoKP
            'Comment Original 17082020 - OSH 
            'If .SelectedIndex = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
            'If .SelectedIndex = 0 Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"

            'Fix Selected Value 17082020 - OSH 
            If .SelectedValue = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
            If .SelectedValue = 0 Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"
        End With
        If X.Trim = "" Then  Else Msg(Me, "Maklumat No. Kad Pengenalan tidak " & X) : Tx_NoKP.Focus() : Exit Sub

        'Recheck 5 credit policy on date 31 july 2013 - 17082013 - OSH
        'If Tx_M_Latihan.Text = "31/07/2010" Then
        '    If Chk_Subjek() = True Msg(Me, "Subjek 5 Kredit SPM Tidak Ditepati.") : Exit Sub
        '    End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment 03012012- query secure by categoery -OSH
        'Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"

        'Add subjects recordchecker 12112018 - OSH
        Cmd.CommandText = "select nokp from pelatih_kelayakan where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Mata Pelajaran Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        'Add 03012012/ 08012013- query secure by categoery -OSH
        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "' AND j_kursus='" & Cb_Kursus.SelectedValue & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Dim SQL As String
        'add SQL variable asign nothing 22102013-OSH
        SQL = Nothing

        Dim y, y1 As DateTime
        Dim z, z1 As String

        Try
            'Add date convert 20122021 - OSH
            'Dim y, y1 As DateTime
            'Dim z, z1 As String

            'Tarikh Lahir
            'z = Tx_Tkh_Lahir.Text.Trim
            If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
                y = DateTime.ParseExact(Tx_Tkh_Lahir.Text.Trim.ToString, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z = y.ToString("M/dd/yyyy", CultureInfo.InvariantCulture)
                z = "'" & z & "'"
            Else
                z = "NULL"
            End If


            'Tarikh Mula Latihan
            'z1 = Tx_M_Latihan.Text.Trim
            If Tx_M_Latihan.Text.Trim <> String.Empty Then
                y1 = DateTime.ParseExact(Tx_M_Latihan.Text.Trim.ToString, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z1 = y1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z1 = "'" & z1 & "'"
            Else
                z1 = "NULL"
            End If



            'Add Choose type id 11081013-OSH
            'Fixing select value 19082013 -OSH
            'If Cb_NoKP.SelectedValue = "0" Or Cb_NoKP.SelectedValue = "2" Then
            'Comment Ori 19082013- OSH
            'If Cb_NoKP.SelectedIndex = "0" Or Cb_NoKP.SelectedIndex = "2" Then

            'Fix index to value populate items 17082020 - OSH  
            If Cb_NoKP.SelectedValue = "0" Or Cb_NoKP.SelectedValue = "2" Then
                'Add insert Brith Date & Brith Place query 26032014 -OSH 
                SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                'Comment Ori 26032014- OSH
                'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                SQL += Cb_Kursus.SelectedItem.Value & ","
                If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
                SQL += "" & Cb_Kelayakan_Taraf.SelectedValue & ","
                SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "'," ' Add ARMY Number 26032014 -OSH
                'Fixing select value 19082013 -OSH
                SQL += Cb_NoKP.SelectedValue & ","
                'Comment Ori 19082013- OSH
                'SQL += Cb_NoKP.SelectedIndex & ","
                'Comment Original 18082020 - OSH 
                'SQL += Cb_Warga.SelectedItem.Value & ","
                'Fix integer to text values suiatable types 18082020 - OSH
                SQL += "'" & Cb_Warga.SelectedItem.Value & "',"
                'Add Brith Date 16092013 - OSH
                'SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & "," 'Comment Original 26112020 - OSH
                'SQL += Chk_Tkh_YMD(Tx_Tkh_Lahir.Text) & "," 'Fix date incorrect 26112020 - OSH 
                SQL += z & "," 'Fix flip date issues 20122021 - OSH 

                'Add Brith Place 16092013 - OSH
                SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                SQL += Cb_Jantina.SelectedIndex & ","
                'Comment Original 17082020 - OSH
                'SQL += Cb_Bangsa.SelectedIndex & ","
                'SQL += Cb_Agama.SelectedIndex & ","
                'Fix selected value 17082020 - OSH 
                SQL += Cb_Bangsa.SelectedValue & ","
                SQL += Cb_Agama.SelectedValue & ","
                SQL += Cb_Kahwin.SelectedIndex & ","
                SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Tx_Tel.Text.Trim & "',"
                'Comment Original 04072023 -OSH
                'SQL += "'" & Tx_Emel.Text.Trim & "',"
                'Casting email to lower case 04072023 -OSH
                SQL += "'" & Tx_Emel.Text.Trim.ToLower & "',"
                SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
                SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                'SQL += Chk_Tkh(Tx_M_Latihan.Text) & "," 'Comment Original 26112020 - OSH
                SQL += z1 & "," 'Fix flip date issues 20122021 - OSH 

                SQL += SSemak(0) & ","
                SQL += SSemak(1) & ","
                SQL += SSemak(2) & ","
                SQL += SSemak(3) & ","
                SQL += SSemak(4) & ","
                SQL += SSemak(5) & ","
                SQL += SSemak(6) & ","
                SQL += SSemak(7) & ","
                SQL += "'" & Session("Id_PG") & "',"
                SQL += "getdate()"
                SQL += ")"
                'Add AMRY ID SQL INSERT 11082013-OSH
                'Fixing select value 19082013 -OSH
            ElseIf Cb_NoKP.SelectedValue = "1" Then
                'Comment Ori 19082013- OSH
                'ElseIf Cb_NoKP.SelectedIndex = "1" Then
                'Add insert Brith Date & Brith Place query 16092013 -OSH 
                SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                'Comment Ori 160920213 -OSH
                'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                SQL += Cb_Kursus.SelectedItem.Value & ","
                If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
                SQL += "" & Cb_Kelayakan_Taraf.SelectedValue & ","
                SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "',"
                'Fixing select value 19082013 -OSH
                SQL += Cb_NoKP.SelectedValue & ","
                'Comment Ori 19082013- OSH
                'SQL += Cb_NoKP.SelectedIndex & ","
                'Comment Original 18082020 - OSH 
                'SQL += Cb_Warga.SelectedItem.Value & ","
                'Fix integer to text values suiatable types 18082020 - OSH
                SQL += "'" & Cb_Warga.SelectedItem.Value & "',"
                'Add Brith Date 16092013 - OSH
                'SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & ","
                SQL += z & "," 'Fix flip date issues 20122021 - OSH 

                'Add Brith Place 16092013 - OSH
                SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                SQL += Cb_Jantina.SelectedIndex & ","
                'SQL += Cb_Bangsa.SelectedIndex & ","
                'SQL += Cb_Agama.SelectedIndex & ","
                'Fix Selected Value 17082020 - OSH
                SQL += Cb_Bangsa.SelectedValue & ","
                SQL += Cb_Agama.SelectedValue & ","
                SQL += Cb_Kahwin.SelectedIndex & ","
                SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Tx_Tel.Text.Trim & "',"
                SQL += "'" & Tx_Emel.Text.Trim & "',"
                SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
                SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                'SQL += Chk_Tkh(Tx_M_Latihan.Text) & "," 'Comment Original 27112020 - OSH 
                'SQL += Chk_Tkh_YMD(Tx_M_Latihan.Text) & ","   'Fix date incorrect 26112020 - OSH 
                SQL += z1 & "," 'Fix flip date issues 20122021 - OSH 
                SQL += SSemak(0) & ","
                SQL += SSemak(1) & ","
                SQL += SSemak(2) & ","
                SQL += SSemak(3) & ","
                SQL += SSemak(4) & ","
                SQL += SSemak(5) & ","
                SQL += SSemak(6) & ","
                SQL += SSemak(7) & ","
                SQL += "'" & Session("Id_PG") & "',"
                SQL += "getdate()"
                SQL += ")"
            End If
            'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
            'Cmd.CommandText = SQL
            'Cmd.ExecuteNonQuery()


            If Panel5.Visible = True Then 'BI luar/setaraf
                If Cb_Luar.SelectedValue > 0 And Cb_KptsLuar.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_Luar.SelectedValue & "','" & Cb_KptsLuar.Text.ToUpper & "', 'IEL',getdate())" & vbCrLf
            End If

            If Panel4.Visible = True Then 'matrik
                If Cb_SbjM.SelectedValue > 0 And Cb_KptsM.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjM.SelectedValue & "','" & Cb_KptsM.Text.ToUpper & "', 'FL', getdate())" & vbCrLf
            End If

            If Panel3.Visible = True Then 'stpm
                If Cb_Aliran.SelectedValue = 1 Then 'STPM
                    If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "','STPM',getdate())" & vbCrLf
                    If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "','STPM',getdate())" & vbCrLf
                    If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "','STPM',getdate())" & vbCrLf
                    If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "','STPM',getdate())" & vbCrLf
                    If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "','STPM',getdate())" & vbCrLf

                ElseIf Cb_Aliran.SelectedValue = 2 Then 'A-LEVEL
                    If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "','A-L',getdate())" & vbCrLf
                    If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "','A-L',getdate())" & vbCrLf
                    If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "','A-L',getdate())" & vbCrLf
                    If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "','A-L',getdate())" & vbCrLf
                    If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "','A-L',getdate())" & vbCrLf
                End If
            End If

            If Panel2.Visible = True Then 'spm
                If Cb_Kelayakan_Taraf.SelectedValue = 1 Then 'local
                    If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','-2','" & Cb_Kpts2.SelectedItem.Text & "','SPM',getdate())" & vbCrLf
                    If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','-3','" & Cb_Kpts3.SelectedItem.Text & "','SPM',getdate())" & vbCrLf
                    If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','-4','" & Cb_Kpts4.SelectedItem.Text & "','SPM',getdate())" & vbCrLf
                    If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "','SPM',getdate())" & vbCrLf
                    If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "','SPM',getdate())" & vbCrLf

                ElseIf Cb_Kelayakan_Taraf.SelectedValue = 2 Then 'international
                    If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','-2','" & Cb_Kpts2.SelectedItem.Text & "','O-L',getdate())" & vbCrLf
                    If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','-3','" & Cb_Kpts3.SelectedItem.Text & "','O-L',getdate())" & vbCrLf
                    If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','-4','" & Cb_Kpts4.SelectedItem.Text & "','O-L',getdate())" & vbCrLf
                    If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "','O-L',getdate())" & vbCrLf
                    If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "','O-L',getdate())" & vbCrLf
                End If
            End If



            If SQL = "" Then
            Else
                'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
            End If

            Cn.Close()
            Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
            Session("Msg_Isi") = "Rekod Telah Dihantar..."
            Response.Redirect("Mesej.aspx")
        Catch ex As Exception
            Msg(Me, ex.Message)
            'Msg(Me, y.ToString + z.ToString + y1.ToString + z1.ToString)
        End Try
    End Sub

    Protected Sub Cb_Sesi_Bln_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Bln.SelectedIndexChanged
        Cb_Sesi_Bulan.SelectedIndex = Cb_Sesi_Bln.SelectedIndex
        Fn_Polisi()
    End Sub

   Public Sub Fn_Polisi()
        If Cb_Sesi_Thn.SelectedIndex > 0 Then
            If Cb_Sesi_Bln.SelectedIndex > 0 Then

                'Fix load policy check issue 11082020 - OSH 
                If Cb_Sesi_Bln.SelectedIndex > 0 And Cb_Sesi_Thn.SelectedValue > 2018 Then
                    Session("POLISI") = "GAMMA"
                ElseIf Cb_Sesi_Bln.SelectedIndex > 6 And Cb_Sesi_Thn.SelectedValue > 2017 Then
                    Session("POLISI") = "GAMMA"
                End If

                'Comment Original 111082020 - OSH   
                'If Cb_Sesi_Bln.SelectedIndex > 6 And Cb_Sesi_Thn.SelectedValue > 2017 Then
                '    Session("POLISI") = "GAMMA"
                'End If
            Else
                Exit Sub
            End If
        End If
    End Sub

 Function Chk_Subjek()
        If Cb_Kelayakan.Items.Count = 0 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        If Cb_Kursus.SelectedIndex = 8 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        Chk_Subjek = True
        Dim L, CL, PL, CLP, LP, CP As Int16, semak As String = "", Total As Double = 0, Tot As Double = 0
        Dim SC As Boolean

    
        'STANDARD CRITERIA ADDMISSION POLICY JULY 2018 16072019 - OSH
        If Session("POLISI") = "GAMMA" Then



            SC = False 'Flag language check

            'LOCAL STUDENT - DEGREE
            If Cb_Kursus.SelectedValue = "5" And Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedValue = "1" Then


                'English
                If Cb_Luar.SelectedValue > 0 And Cb_KptsLuar.Text <> "" Then
                    If Cb_Luar.SelectedValue = 1 And CInt(Cb_KptsLuar.Text) > 2 Then PL = PL + 1 : SC = True 'MUET
                    If Cb_Luar.SelectedValue = 2 And CDbl(Cb_KptsLuar.Text) > 5.4 Then PL = PL + 1 : SC = True ' IELTS
                    If Cb_Luar.SelectedValue = 3 And CInt(Cb_KptsLuar.Text) > 514 Then PL = PL + 1 : SC = True 'TOEFL PBT
                    If Cb_Luar.SelectedValue = 4 And CInt(Cb_KptsLuar.Text) > 214 Then PL = PL + 1 : SC = True 'TOEFL CBT
                    If Cb_Luar.SelectedValue = 5 And CInt(Cb_KptsLuar.Text) > 79 Then PL = PL + 1 : SC = True 'TOEFL iBT
                    'Else
                    '    Exit Function
                End If

                'Academic(Qualification)
                'ElseIf 
                If Cb_SbjM.SelectedValue > 0 And (Cb_KptsM.Text <> "") Then
                    If Cb_SbjM.SelectedValue = 1 And CDbl(Cb_KptsM.Text) > 2.4 Then LP = LP + 1 'Matriculation
                    If Cb_SbjM.SelectedValue = 2 And CDbl(Cb_KptsM.Text) > 2.4 Then LP = LP + 1 'Foundation
                    If Cb_SbjM.SelectedValue = 3 And CDbl(Cb_KptsM.Text) > 2.4 Then LP = LP + 1 'Non Nursing Diploma
                    'Else
                    '    Exit Function
                End If

                'Academic Qualification - STPM / A-LEVEL
                If Cb_Aliran.SelectedValue = 1 Then ' STPM

                    If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                    If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                    If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                    If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                    If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                    Total = Total / L

                    If Total >= 2.5 Then LP = LP + 1

                ElseIf Cb_Aliran.SelectedValue = 2 Then ' A-LEVEL

                    If Cb_KptsT1.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 6 Then L = L + 1
                    If Cb_KptsT2.SelectedIndex > 0 And Cb_KptsT2.SelectedIndex < 6 Then L = L + 1
                    If Cb_KptsT3.SelectedIndex > 0 And Cb_KptsT3.SelectedIndex < 6 Then L = L + 1
                    If Cb_KptsT4.SelectedIndex > 0 And Cb_KptsT4.SelectedIndex < 6 Then L = L + 1
                    If Cb_KptsT5.SelectedIndex > 0 And Cb_KptsT5.SelectedIndex < 6 Then L = L + 1

                    If L > 2 Then LP = LP + 1

                End If

                'Support Academic - SPM
                If SC = False Then
                    'Adjust Grding Scale 23082021 - OSH 
                    If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then PL = PL + 1 ' English  

                    'Comment Original 23082021 - OSH 
                    'If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 7 Then PL = PL + 1 ' English  
                End If
                'Adjust Grding Scale 23082021 - OSH 
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then CL = CL + 1 ' Math 
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CP = CP + 1 ' Science

                'Comment Original 23082021 - OSH 
                'If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 7 Then CL = CL + 1 ' Math 
                'If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 7 Then CP = CP + 1 ' Science  

                'Comment Original 11032021 -OSH 
                'If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 7 Then
                'improve pure science grading based on SOP 2018 11032021 - OSH 
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then
                    If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj5) = True Then CP = CP + 1
                    If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then CL = CL + 1 ' pure science or add math
                End If
                'Comment Original 11032021 -OSH 
                'If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 7 Then
                'improve pure science grading based on SOP 2018 11032021 - OSH 
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then
                    If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj6) = True Then CP = CP + 1
                    If Chk_Sbj_Math_2018(Cb_Sbj6) = True Then CL = CL + 1 ' pure science or add math 
                End If

                If PL = 1 Then 'CHECK ENGLISH
                    If LP = 1 Then 'CHECK ACADEMIC 
                        If CL = 1 Then 'CHECK MATH
                            'Fix checker bug 11082020 - OSH   
                            If CP > 0 Then ' CHECK SCIENCE 
                                If (PL + LP + CL + CP) > 3 Then 'ROUND CHECK

                                    'Comment Original 11082020 - OSH
                                    'If CP = 1 Then ' CHECK SCIENCE 
                                    'If (PL + LP + CL + CP) = 4 Then 'ROUND CHECK
                                    semak = "L"
                                Else
                                    Msg(Me, "kelayakan pelatih antarabangsa tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah  ! Sila semak maklumat kelayakan.")
                                End If
                            Else
                                Msg(Me, "subjek sains / sains tulen SPM tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah ! Sila semak maklumat kelayakan.")
                            End If
                        Else
                            Msg(Me, "subjek matematik / matematik tambahan SPM tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah ! Sila semak maklumat kelayakan.")
                        End If
                    Else
                        Msg(Me, "Kelayakan STPM / MATRIKULASI/  PROGRAM ASASI/ A-LEVEL tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah ! Sila semak maklumat kelayakan.")
                    End If
                Else
                    Msg(Me, "subjek bahasa inggeris tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah ! Sila semak maklumat kelayakan.")
                End If
            End If
        End If

        'INTERNATIONAL(STUDENT - DEGREE)
        If Cb_Kursus.SelectedValue = "5" And Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedValue = "2" Then
            SC = False 'Flag language check

            'English
            If Cb_Luar.SelectedValue > 0 And Cb_KptsLuar.Text <> "" Then
                If Cb_Luar.SelectedValue = 2 And CDbl(Cb_KptsLuar.Text) > 5.4 Then PL = PL + 1 ' IELTS
                If Cb_Luar.SelectedValue = 3 And CInt(Cb_KptsLuar.Text) > 514 Then PL = PL + 1 'TOEFL PBT
                If Cb_Luar.SelectedValue = 4 And CInt(Cb_KptsLuar.Text) > 214 Then PL = PL + 1 'TOEFL CBT
                If Cb_Luar.SelectedValue = 5 And CInt(Cb_KptsLuar.Text) > 79 Then PL = PL + 1 'TOEFL iBT
            Else
                Exit Function
            End If

            'Academic Qualification 
            If Cb_SbjM.SelectedValue > 0 And Cb_KptsM.Text <> "" Then
                If Cb_SbjM.SelectedValue = 1 And CDbl(Cb_KptsM.Text) > 2.4 Then LP = LP + 1 'Matriculation
                If Cb_SbjM.SelectedValue = 2 And CDbl(Cb_KptsM.Text) > 2.4 Then LP = LP + 1 'Foundation
                If Cb_SbjM.SelectedValue = 3 And CDbl(Cb_KptsM.Text) > 2.4 Then LP = LP + 1 'Non Nursing Diploma
                'Else
                '    Exit Function
            End If

            'Academic Qualification - STPM / A-LEVEL
            If Cb_Aliran.SelectedValue = 1 Then ' STPM

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L

                If Total >= 2.5 Then LP = LP + 1

            ElseIf Cb_Aliran.SelectedValue = 2 Then ' A-LEVEL

                If Cb_KptsT1.SelectedIndex > 0 And Cb_KptsT1.SelectedIndex < 6 Then L = L + 1
                If Cb_KptsT2.SelectedIndex > 0 And Cb_KptsT2.SelectedIndex < 6 Then L = L + 1
                If Cb_KptsT3.SelectedIndex > 0 And Cb_KptsT3.SelectedIndex < 6 Then L = L + 1
                If Cb_KptsT4.SelectedIndex > 0 And Cb_KptsT4.SelectedIndex < 6 Then L = L + 1
                If Cb_KptsT5.SelectedIndex > 0 And Cb_KptsT5.SelectedIndex < 6 Then L = L + 1

                If L > 2 Then LP = LP + 1

            End If

            'Support Academic - O-LEVEL

            If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 7 Then CLP = CLP + 1 ' Math 
            If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 7 Then CP = CP + 1 ' Science  
            If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 7 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj5) = True Then CP = CP + 1 Else If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then CLP = CLP + 1 ' pure science or add math
            If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 7 Then If Chk_Sbj_Credit_I_DIP_2018(Cb_Sbj5) = True Then CP = CP + 1 Else If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then CLP = CLP + 1 ' pure science or add math 

            If PL = 1 Then 'CHECK ENGLISH
                If LP >= 1 Then 'CHECK ACADEMIC 
                    If CLP = 1 Then 'CHECK MATH 
                        If CP >= 1 Then ' CHECK SCIENCE 
                            If (PL + LP + CLP + CP) >= 4 Then 'ROUND CHECK
                                semak = "L"
                            Else
                                Msg(Me, "kelayakan pelatih antarabangsa tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah (pelatih antabangsa) ! Sila semak maklumat kelayakan.")
                            End If
                        Else
                            Msg(Me, "subjek sains tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah (pelatih antabangsa)! Sila semak maklumat kelayakan.")
                        End If
                    Else
                        Msg(Me, "subjek matematik tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah (pelatih antabangsa)! Sila semak maklumat kelayakan.")
                    End If
                Else
                    Msg(Me, "Kelayakan  A-LEVEL/ O-LEVEL/  PROGRAM ASASI/ tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah (pelatih antabangsa)! Sila semak maklumat kelayakan.")
                End If
            Else
                Msg(Me, "subjek bahasa inggeris tidak melepasi syarat minimum kemasukan 2018 - peringkat ijazah (pelatih antabangsa) ! Sila semak maklumat kelayakan.")
            End If
        End If

        If semak = "L" Then
            Chk_Subjek = False
        End If

        Return Chk_Subjek

    End Function

    Function Chk_Sbj_Credit_I_DIP_2018(ByVal cb As DropDownList)
        Chk_Sbj_Credit_I_DIP_2018 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        If cb.SelectedItem.Text = "APPLIED SCIENCE" Then Chk_Sbj_Credit_I_DIP_2018 = True : Exit Function
        Return Chk_Sbj_Credit_I_DIP_2018
    End Function

    Function Chk_Sbj_Math_2018(ByVal cb As DropDownList)
        Chk_Sbj_Math_2018 = False

        If cb.SelectedItem.Text = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Math_2018 = True : Exit Function
        Return Chk_Sbj_Math_2018
    End Function

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        Dim X As String = ""
        'If Cb_Kursus.SelectedValue = 4 Then Panel1.Visible = True : Exit Sub
        If Cb_Sesi_Bln.SelectedIndex < 1 Then X += "Sesi Pengambilan (Bulan), "
        If Cb_Sesi_Thn.SelectedIndex < 1 Then X += "Sesi Pengambilan (Tahun), "
        'If Panel3.Visible = True And Cb_Aliran.Enabled = True Then If Cb_Aliran.SelectedIndex = 0 Then X += "Kelayakan SPTM/ A-Level, "
        'If Panel6.Visible = True Then If Cb_KptsMuet.SelectedIndex = 0 Then X += "Keputusan MUET, "
        If Cb_Luar.SelectedIndex > 0 And Cb_KptsLuar.Text = "" Then X += "Keputusan Bahasa Inggeris, "
        If Cb_SbjM.SelectedIndex > 0 And Cb_KptsM.Text = "" Then X += "Matrikulasi/Asasi, "
        'If Cb_Aliran.SelectedIndex = 2 Then If Cb_KptsT6.SelectedIndex = 0 Then X += "SPM (Matematik), "
        'If Cb_Aliran.SelectedIndex = 2 Then If Cb_KptsT7.SelectedIndex = 0 Then X += "SPM (Sains), "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        If Chk_Subjek() = True Then Exit Sub

        Panel1.Visible = True : PanelSemak.Visible = True


        Cb_Luar.Visible = False : Cb_Luarx.Visible = True : Cb_Luarx.Text = Cb_Luar.SelectedItem.Text 'bi luar negara/setaraf
        Cb_SbjM.Visible = False : Cb_SbjMx.Visible = True : Cb_SbjMx.Text = Cb_SbjM.SelectedItem.Text 'matrik/asasi

        Cb_SbjT1.Visible = False : Cb_SbjT1x.Visible = True : Cb_SbjT1x.Text = Cb_SbjT1.SelectedItem.Text
        Cb_SbjT2.Visible = False : Cb_SbjT2x.Visible = True : Cb_SbjT2x.Text = Cb_SbjT2.SelectedItem.Text
        Cb_SbjT3.Visible = False : Cb_SbjT3x.Visible = True : Cb_SbjT3x.Text = Cb_SbjT3.SelectedItem.Text
        Cb_SbjT4.Visible = False : Cb_SbjT4x.Visible = True : Cb_SbjT4x.Text = Cb_SbjT4.SelectedItem.Text
        Cb_SbjT5.Visible = False : Cb_SbjT5x.Visible = True : Cb_SbjT5x.Text = Cb_SbjT5.SelectedItem.Text 'stpm

        Cb_Sbj5.Visible = False : Cb_Sbj5x.Visible = True : Cb_Sbj5x.Text = Cb_Sbj5.SelectedItem.Text
        Cb_Sbj6.Visible = False : Cb_Sbj6x.Visible = True : Cb_Sbj6x.Text = Cb_Sbj6.SelectedItem.Text
        Cb_Sbj7.Visible = False : Cb_Sbj7x.Visible = True : Cb_Sbj7x.Text = Cb_Sbj7.SelectedItem.Text
        Cb_Sbj8.Visible = False : Cb_Sbj8x.Visible = True : Cb_Sbj8x.Text = Cb_Sbj8.SelectedItem.Text
        Cb_Sbj9.Visible = False : Cb_Sbj9x.Visible = True : Cb_Sbj9x.Text = Cb_Sbj9.SelectedItem.Text 'spm

        'Add Diasble Course Selection After Qualification Check  16082013- OSH
        Cb_Kursus.Enabled = False : Cb_Kelayakan.Enabled = False : Cb_Kelayakan_Taraf.Enabled = False
        'Comment Ori 16082013- OSH
        'Cb_Kelayakan.Enabled = False : Cb_Kelayakan_Taraf.Enabled = False
        Cb_Sesi_Bln.Enabled = False : Cb_Sesi_Thn.Enabled = False
        Panel2.Enabled = False : Panel3.Enabled = False : Panel4.Enabled = False
        Panel5.Enabled = False : Panel6.Enabled = False : PanelSemak.Enabled = False
    End Sub

    Public Sub Fn_Inggeris(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NATIVE = 1"
        Else
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NNATIVE = 1"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_Luar.Items.Clear()
        Cb_Luar.Items.Add("(PILIHAN)")
        Cb_Luar.Items.Item(0).Value = "0"
        While Rdr.Read
            Cb_Luar.Items.Add(Rdr(0))
            Cb_Luar.Items.Item(Cb_Luar.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Cb_Sesi_Thn_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Thn.SelectedIndexChanged
        Cb_Sesi_Tahun.SelectedIndex = Cb_Sesi_Thn.SelectedIndex
        Fn_Polisi()
    End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Function Chk_Tkh(ByVal X As String)
        If IsDate(X) Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
        Return X
    End Function

    Function Chk_Tkh_YMD(ByVal X As String)
        If IsDate(X) Then X = Format(CDate(X), "yyyy-MM-dd") : X = "'" & X & "'" Else X = "NULL"
        Return X
    End Function

    Protected Sub Cb_NoKP_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_NoKP.SelectedIndexChanged
        Tx_NoKP.Text = ""

        'Comment Original 17092019 - OSH
        'With Cb_NoKP
        '    'Add Army textbox Control 10082013-OSH
        '    If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)
        '    'Add Army textbox Control 10082013-OSH
        '    If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1)
        '    'Comment Ori 10082013-OSH
        '    'If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : Fn_Negara(1)
        '    'Add Army textbox Control 10082013-OSH
        '    If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0)
        '    If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        'End With

        ''Improve selection values 17092019 - OSH
        'With Cb_NoKP
        '    'MYKAD
        '    If .SelectedValue = 0 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1)
        '    'ARMY 
        '    If .SelectedValue = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1)
        '    'PASSPORT
        '    If .SelectedValue = 2 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0)
        '    If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        'End With

        'Improve selection values 16012020 - OSH
        With Cb_NoKP
            If .SelectedIndex = 0 Then Tx_Tentera.Enabled = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
            If .SelectedIndex = 1 Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan" : Tx_Tentera_TextBoxWatermarkExtender.Enabled = True : Tx_Tentera_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_Tentera_TextBoxWatermarkExtender.WatermarkText = "Nombor Tentera"
            If .SelectedIndex = 2 Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Passport"
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
    End Sub


    Public Sub Fn_Lanjutan(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NATIVE = 1"
        Else
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NNATIVE = 1"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_SbjM.Items.Clear()
        Cb_SbjM.Items.Add("(PILIHAN)")
        Cb_SbjM.Items.Item(0).Value = "0"
        While Rdr.Read
            Cb_SbjM.Items.Add(Rdr(0))
            Cb_SbjM.Items.Item(Cb_SbjM.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    'Add states switch to country for internations student 20122019 - OSH  
    Public Sub Fn_Negeri(ByVal X As Int16)
        Dim Cn5 As New OleDbConnection : Dim Cmd5 As New OleDbCommand : Dim Rdr5 As OleDbDataReader
        Cn5.ConnectionString = ServerId : Cn5.Open() : Cmd5.Connection = Cn5

        'WARGANEGARA
        If X = 1 Then
            Lb_States.Text = "NEGERI" : Lb_States.Enabled = False : Cb_W_Negeri.Enabled = False
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("")
            Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri order by dc_negeri"
        Else
            Lb_States.Text = "NEGARA" : Lb_States.Enabled = False
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False
            Cmd5.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr5 = Cmd5.ExecuteReader()
        Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
        Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False
        While Rdr5.Read
            Cb_TP_Negeri.Items.Add(Rdr5(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr5(1)
            Cb_W_Negeri.Items.Add(Rdr5(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr5(1)
        End While
        Rdr5.Close()
        Cn5.Close()
    End Sub

    Protected Sub ChkSah_CheckedChanged(sender As Object, e As EventArgs) Handles ChkSah.CheckedChanged
        'Enable Submit Button When Declaration Is Confirm  20042023 - OSH
        If ChkSah.Checked = True Then
            cmdHantar.Enabled = True
        ElseIf ChkSah.Checked = False Then
            cmdHantar.Enabled = False
        End If
    End Sub
End Class