﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm16
    Inherits System.Web.UI.Page

    Public Sub Cari()
        Dim SQL As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', '' as 'NO. PENDAFTARAN', case log_status when 1 then 'L' when 2 then 'TL' else '' end as 'STATUS' from tmp_penuh where j_daftar = " & Cb_Jenis.SelectedIndex & " order by nama"
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, "tmp_penuh")
        Gd.DataSource = List_Data.Tables("tmp_penuh")
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(4).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(30)
        'e.Row.Cells(7).Width = Unit.Pixel(150)
        e.Row.Cells(0).HorizontalAlign = HorizontalAlign.Center
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(2).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Cb_Jenis_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Jenis.SelectedIndexChanged
        Cari()
        If Gd.Rows.Count > 0 Then cmd_Jana.Visible = True Else cmd_Jana.Visible = False
    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        Dim i As Int16, chk As CheckBox, chk2 As CheckBox, SQL As String = ""
        Dim t1 As TextBox, t2 As TextBox
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        For i = 0 To Gd.Rows.Count - 1
            'jana manual
            chk = Gd.Rows.Item(i).FindControl("chk_pilih")
            chk2 = Gd.Rows.Item(i).FindControl("CheckBox1")
            If chk2.Checked Then
                t1 = Gd.Rows.Item(i).FindControl("Tx_NoPd")
                t2 = Gd.Rows.Item(i).FindControl("Tx_Tkh_Daftar")
                If t1.Text = "" Or t2.Text = "" Then
                    Msg(Me, "Data tidak lengkap!!!")                    
                Else
                    Cmd.CommandText = "select nokp from tmp_penuh where nopd=" & t1.Text & " and j_daftar=" & Cb_Jenis.SelectedIndex
                    Rdr = Cmd.ExecuteReader()
                    If Rdr.Read Then
                        Msg(Me, "No. Pendaftaran ini sudah wujud!")
                        Rdr.Close()
                        Exit Sub
                    End If
                    Rdr.Close()

                    Cmd.CommandText = "select nokp from jt_penuh where nopd=" & t1.Text & " and j_daftar=" & Cb_Jenis.SelectedIndex
                    Rdr = Cmd.ExecuteReader()
                    If Rdr.Read Then
                        Rdr.Close() : Cn.Close()
                        Msg(Me, "No. Pendaftaran ini sudah wujud!")
                        Exit Sub
                    End If
                    Rdr.Close()

                    If IsNumeric(t1.Text) Then
                        SQL = "update tmp_penuh set nopd = " & t1.Text & ", tkh_daftar = " & Chk_Tkh(t2.Text) & " where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                        SQL += "insert jt_penuh (J_Daftar,NoKP,Jenis_KP,Nama,NoPd,Tkh_Daftar,Warganegara,Tkh_Lahir,Tpt_Lahir,Jantina,Bangsa,Agama,Etnik,Umur,TP_Alamat,TP_Poskod,TP_Bandar,TP_Negeri,SM_Alamat,SM_Poskod,SM_Bandar,SM_Negeri,Tel_R,Tel_HP,Emel,T_Kahwin,Id_Kolej,Tajaan,Sesi_Bulan,Sesi_Tahun,Tkh_Latihan_Mula,TKh_Latihan_Tamat,Tkh_Periksa_Akhir,ss1,ss2,ss3,ss4,ss5,ss6,Log_Status,Log_Catatan,Log_Id,Log_Tkh) select * from tmp_penuh where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "' and nopd is not null; "
                        SQL += "insert jt_penuh_kelayakan select * from tmp_penuh_kelayakan where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                        SQL += "delete from tmp_penuh where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "' and nopd is not null; "
                        SQL += "delete from tmp_penuh_kelayakan where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                        Cmd.CommandText = SQL
                        Cmd.ExecuteNonQuery()

                        Cmd.CommandText = "select case j_daftar when 1 then 'JB-' + cast(nopd as varchar(6)) when 2 then 'JM-' + cast(nopd as varchar(6)) when 3 then 'PJ-' + cast(nopd as varchar(6)) when 4 then 'B-' + cast(nopd as varchar(6)) end from jt_penuh where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'"
                        Rdr = Cmd.ExecuteReader()
                        If Rdr.Read Then Gd.Rows.Item(i).Cells(5).Text = Rdr(0)
                        Rdr.Close()
                        chk.Enabled = False
                        cmd_Jana.Visible = False
                    End If
                End If
            Else
                Gd.Rows.Item(i).Cells(5).Text = ""
            End If

            'jana auto
            If chk.Checked And chk.Enabled Then
                Select Case Cb_Jenis.SelectedValue
                    Case 1
                        SQL = "update tmp_penuh set nopd = (select jb+1 from pn_nopd), tkh_daftar = getdate() where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                        SQL += "update pn_nopd set jb = jb+1; "
                    Case 2
                        SQL = "update tmp_penuh set nopd = (select jm+1 from pn_nopd), tkh_daftar = getdate() where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                        SQL += "update pn_nopd set jm = jm+1; "
                    Case 3
                        SQL = "update tmp_penuh set nopd = (select pj+1 from pn_nopd), tkh_daftar = getdate() where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                        SQL += "update pn_nopd set pj = pj+1; "
                    Case 4
                        SQL = "update tmp_penuh set nopd = (select b+1 from pn_nopd), tkh_daftar = getdate() where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                        SQL += "update pn_nopd set b = b+1; "
                End Select
                SQL += "insert jt_penuh (J_Daftar,NoKP,Nama,NoPd,Tkh_Daftar,Warganegara,Tkh_Lahir,Tpt_Lahir,Jantina,Bangsa,Agama,Etnik,Umur,TP_Alamat,TP_Poskod,TP_Bandar,TP_Negeri,SM_Alamat,SM_Poskod,SM_Bandar,SM_Negeri,Tel_R,Tel_HP,Emel,T_Kahwin,Id_Kolej,Tajaan,Sesi_Bulan,Sesi_Tahun,Tkh_Latihan_Mula,TKh_Latihan_Tamat,Tkh_Periksa_Akhir,ss1,ss2,ss3,ss4,ss5,ss6,Log_Status,Log_Catatan,Log_Id,Log_Tkh) select * from tmp_penuh where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "' and nopd is not null; "
                SQL += "insert jt_penuh_kelayakan select * from tmp_penuh_kelayakan where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                SQL += "delete from tmp_penuh where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "' and nopd is not null; "
                SQL += "delete from tmp_penuh_kelayakan where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'; "
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()

                Cmd.CommandText = "select case j_daftar when 1 then 'JB-' + cast(nopd as varchar(6)) when 2 then 'JM-' + cast(nopd as varchar(6)) when 3 then 'PJ-' + cast(nopd as varchar(6)) when 4 then 'B-' + cast(nopd as varchar(6)) end from jt_penuh where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "'"
                Rdr = Cmd.ExecuteReader()
                If Rdr.Read Then Gd.Rows.Item(i).Cells(5).Text = Rdr(0)
                Rdr.Close()
                chk.Enabled = False
                cmd_Jana.Visible = False
            Else
                'Gd.Rows.Item(i).Cells(4).Text = ""
            End If
        Next
        Cn.Close()
        Msg(Me, "Rekod Telah Dikemaskini..")

    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Dim chk As CheckBox = e.Row.FindControl("chk_pilih")
        Dim chk2 As CheckBox = e.Row.FindControl("CheckBox1")
        If e.Row.Cells(6).Text = "L" Then chk.Enabled = True : chk2.Enabled = True Else chk.Enabled = False : chk2.Enabled = False
    End Sub

    Protected Sub Ch_CheckedChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer, t1, t2 As TextBox, ch1 As CheckBox
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$CheckBox1") - InStr(sender.uniqueid, "$ctl") - 4))
        t1 = Gd.Rows.Item(i - 2).FindControl("tx_nopd")
        t2 = Gd.Rows.Item(i - 2).FindControl("tx_tkh_daftar")
        ch1 = Gd.Rows.Item(i - 2).FindControl("chk_pilih")
        If sender.checked Then
            Gd.Rows.Item(i - 2).Cells(1).Wrap = False '.Width = Unit.Pixel(150)
            t1.Visible = True
            t2.Visible = True
            ch1.Checked = False
        Else
            t1.Visible = False
            t2.Visible = False
        End If
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged

    End Sub

    Protected Sub tx_nopd_TextChanged(ByVal sender As Object, ByVal e As EventArgs)

    End Sub

    Protected Sub chk_pilih_CheckedChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer, t1, t2 As TextBox, ch1 As CheckBox
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$chk_pilih") - InStr(sender.uniqueid, "$ctl") - 4))
        t1 = Gd.Rows.Item(i - 2).FindControl("tx_nopd")
        t2 = Gd.Rows.Item(i - 2).FindControl("tx_tkh_daftar")
        ch1 = Gd.Rows.Item(i - 2).FindControl("CheckBox1")
        If sender.checked Then
            t1.Visible = False
            t2.Visible = False
            ch1.Checked = False
        End If
    End Sub
End Class