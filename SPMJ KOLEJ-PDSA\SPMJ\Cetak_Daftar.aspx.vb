﻿Imports CrystalDecisions.Shared

Partial Public Class Cetak_Daftar
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim Tloginfo As CrystalDecisions.Shared.TableLogOnInfo

        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        Select Case Session("Lpr_Nama")
            Case "SNPendJururawat" 'Full List of New Nurses Registration via BLESS 12032014 -OSH
                Dim report As SNPendJururawat = New SNPendJururawat
                report.SetParameterValue(0, Session("Var_1"))
                report.SetParameterValue(1, Session("Var_2"))
                report.SetParameterValue(2, Session("Var_3"))
                report.SetParameterValue(3, Session("Var_4"))


                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next

                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "Nurses_Reg_List ")


        End Select

    End Sub


End Class