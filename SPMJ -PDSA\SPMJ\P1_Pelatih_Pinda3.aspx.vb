﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class P1_Pelatih_Pinda3
    Inherits System.Web.UI.Page

    Public Sub Isi_Kelayakan()
        On Error Resume Next
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select id_subjek, keputusan from pelatih_kelayakan where nokp = '" & Session("nokp") & "'"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Rdr(0) = "-1" Then
                Cb_Kpts1.Items.FindByText(Rdr(1)).Selected = True
            ElseIf Rdr(0) = "-2" Then
                Cb_Kpts2.Items.FindByText(Rdr(1)).Selected = True
            ElseIf Rdr(0) = "-3" Then
                Cb_Kpts3.Items.FindByText(Rdr(1)).Selected = True
            ElseIf Rdr(0) = "-4" Then
                Cb_Kpts4.Items.FindByText(Rdr(1)).Selected = True
            ElseIf Rdr(0) = "-5" Then
                Cb_Sbj10.SelectedIndex = 1
                Tx_Kpts10.Text = Rdr(1)
            ElseIf Rdr(0) = "-6" Then
                Cb_Sbj10.SelectedIndex = 2
                Tx_Kpts10.Text = Rdr(1)
            ElseIf Rdr(0) = "15" Then
                Cb_Sbj11.SelectedIndex = 1
                Cb_Kpts11.Items.FindByText(Rdr(1)).Selected = True
            Else
                If Cb_Sbj5.Visible = False Then
                    Cb_Sbj5.Visible = True : Cb_Kpts5.Visible = True : Textbox8.Visible = True
                    Cb_Sbj5.Items.FindByValue(Rdr(0)).Selected = True
                    Cb_Kpts5.Items.FindByText(Rdr(1)).Selected = True
                ElseIf Cb_Sbj6.Visible = False Then
                    Cb_Sbj6.Visible = True : Cb_Kpts6.Visible = True : Textbox9.Visible = True
                    Cb_Sbj6.Items.FindByValue(Rdr(0)).Selected = True
                    Cb_Kpts6.Items.FindByText(Rdr(1)).Selected = True
                ElseIf Cb_Sbj7.Visible = False Then
                    Cb_Sbj7.Visible = True : Cb_Kpts7.Visible = True : Textbox10.Visible = True
                    Cb_Sbj7.Items.FindByValue(Rdr(0)).Selected = True
                    Cb_Kpts7.Items.FindByText(Rdr(1)).Selected = True
                ElseIf Cb_Sbj8.Visible = False Then
                    Cb_Sbj8.Visible = True : Cb_Kpts8.Visible = True : Textbox11.Visible = True
                    Cb_Sbj8.Items.FindByValue(Rdr(0)).Selected = True
                    Cb_Kpts8.Items.FindByText(Rdr(1)).Selected = True
                ElseIf Cb_Sbj9.Visible = False Then
                    Cb_Sbj9.Visible = True : Cb_Kpts9.Visible = True : Textbox12.Visible = True
                    Cb_Sbj9.Items.FindByValue(Rdr(0)).Selected = True
                    Cb_Kpts9.Items.FindByText(Rdr(1)).Selected = True
                End If
            End If
        End While
    End Sub

    Public Sub Isi_Subjek(ByVal X As String)
        Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
        Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
        Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
        Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        Bt4.Visible = False : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
        Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = '" & X & "' order by id_subjek"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        If X = "PMR" Then
            Cb_Kpts1.Items.Clear()
            Cb_Kpts1.Items.Add("")
            Cb_Kpts1.Items.Add("A")
            Cb_Kpts1.Items.Add("B")
            Cb_Kpts1.Items.Add("C")
            Cb_Kpts1.Items.Add("D")
            Cb_Kpts1.Items.Add("E")

            Cb_Kpts2.Items.Clear()
            Cb_Kpts2.Items.Add("")
            Cb_Kpts2.Items.Add("A")
            Cb_Kpts2.Items.Add("B")
            Cb_Kpts2.Items.Add("C")
            Cb_Kpts2.Items.Add("D")
            Cb_Kpts2.Items.Add("E")

            Cb_Kpts3.Items.Clear()
            Cb_Kpts3.Items.Add("")
            Cb_Kpts3.Items.Add("A")
            Cb_Kpts3.Items.Add("B")
            Cb_Kpts3.Items.Add("C")
            Cb_Kpts3.Items.Add("D")
            Cb_Kpts3.Items.Add("E")

            Cb_Kpts4.Items.Clear()
            Cb_Kpts4.Items.Add("")
            Cb_Kpts4.Items.Add("A")
            Cb_Kpts4.Items.Add("B")
            Cb_Kpts4.Items.Add("C")
            Cb_Kpts4.Items.Add("D")
            Cb_Kpts4.Items.Add("E")

            Cb_Kpts5.Items.Clear()
            Cb_Kpts5.Items.Add("")
            Cb_Kpts5.Items.Add("A")
            Cb_Kpts5.Items.Add("B")
            Cb_Kpts5.Items.Add("C")
            Cb_Kpts5.Items.Add("D")
            Cb_Kpts5.Items.Add("E")

            Cb_Kpts6.Items.Clear()
            Cb_Kpts6.Items.Add("")
            Cb_Kpts6.Items.Add("A")
            Cb_Kpts6.Items.Add("B")
            Cb_Kpts6.Items.Add("C")
            Cb_Kpts6.Items.Add("D")
            Cb_Kpts6.Items.Add("E")

            Cb_Kpts7.Items.Clear()
            Cb_Kpts7.Items.Add("")
            Cb_Kpts7.Items.Add("A")
            Cb_Kpts7.Items.Add("B")
            Cb_Kpts7.Items.Add("C")
            Cb_Kpts7.Items.Add("D")
            Cb_Kpts7.Items.Add("E")

            Cb_Kpts8.Items.Clear()
            Cb_Kpts8.Items.Add("")
            Cb_Kpts8.Items.Add("A")
            Cb_Kpts8.Items.Add("B")
            Cb_Kpts8.Items.Add("C")
            Cb_Kpts8.Items.Add("D")
            Cb_Kpts8.Items.Add("E")

            Cb_Kpts9.Items.Clear()
            Cb_Kpts9.Items.Add("")
            Cb_Kpts9.Items.Add("A")
            Cb_Kpts9.Items.Add("B")
            Cb_Kpts9.Items.Add("C")
            Cb_Kpts9.Items.Add("D")
            Cb_Kpts9.Items.Add("E")

        ElseIf X = "SPM" Then
            Cb_Kpts1.Items.Clear()
            Cb_Kpts1.Items.Add("")
            Cb_Kpts1.Items.Add("1A")
            Cb_Kpts1.Items.Add("2A")
            Cb_Kpts1.Items.Add("3B")
            Cb_Kpts1.Items.Add("4B")
            Cb_Kpts1.Items.Add("5C")
            Cb_Kpts1.Items.Add("6C")
            Cb_Kpts1.Items.Add("7D")
            Cb_Kpts1.Items.Add("8E")

            Cb_Kpts1.Items.Add("A+")
            Cb_Kpts1.Items.Add("A")
            Cb_Kpts1.Items.Add("A-")
            Cb_Kpts1.Items.Add("B+")
            Cb_Kpts1.Items.Add("B")
            Cb_Kpts1.Items.Add("C+")
            Cb_Kpts1.Items.Add("C")
            Cb_Kpts1.Items.Add("D")
            Cb_Kpts1.Items.Add("E")
            '-----------

            Cb_Kpts2.Items.Clear()
            Cb_Kpts2.Items.Add("")
            Cb_Kpts2.Items.Add("1A")
            Cb_Kpts2.Items.Add("2A")
            Cb_Kpts2.Items.Add("3B")
            Cb_Kpts2.Items.Add("4B")
            Cb_Kpts2.Items.Add("5C")
            Cb_Kpts2.Items.Add("6C")
            Cb_Kpts2.Items.Add("7D")
            Cb_Kpts2.Items.Add("8E")

            Cb_Kpts2.Items.Add("A+")
            Cb_Kpts2.Items.Add("A")
            Cb_Kpts2.Items.Add("A-")
            Cb_Kpts2.Items.Add("B+")
            Cb_Kpts2.Items.Add("B")
            Cb_Kpts2.Items.Add("C+")
            Cb_Kpts2.Items.Add("C")
            Cb_Kpts2.Items.Add("D")
            Cb_Kpts2.Items.Add("E")
            '-----------

            Cb_Kpts3.Items.Clear()
            Cb_Kpts3.Items.Add("")
            Cb_Kpts3.Items.Add("1A")
            Cb_Kpts3.Items.Add("2A")
            Cb_Kpts3.Items.Add("3B")
            Cb_Kpts3.Items.Add("4B")
            Cb_Kpts3.Items.Add("5C")
            Cb_Kpts3.Items.Add("6C")
            Cb_Kpts3.Items.Add("7D")
            Cb_Kpts3.Items.Add("8E")

            Cb_Kpts3.Items.Add("A+")
            Cb_Kpts3.Items.Add("A")
            Cb_Kpts3.Items.Add("A-")
            Cb_Kpts3.Items.Add("B+")
            Cb_Kpts3.Items.Add("B")
            Cb_Kpts3.Items.Add("C+")
            Cb_Kpts3.Items.Add("C")
            Cb_Kpts3.Items.Add("D")
            Cb_Kpts3.Items.Add("E")
            '-----------

            Cb_Kpts4.Items.Clear()
            Cb_Kpts4.Items.Add("")
            Cb_Kpts4.Items.Add("1A")
            Cb_Kpts4.Items.Add("2A")
            Cb_Kpts4.Items.Add("3B")
            Cb_Kpts4.Items.Add("4B")
            Cb_Kpts4.Items.Add("5C")
            Cb_Kpts4.Items.Add("6C")
            Cb_Kpts4.Items.Add("7D")
            Cb_Kpts4.Items.Add("8E")

            Cb_Kpts4.Items.Add("A+")
            Cb_Kpts4.Items.Add("A")
            Cb_Kpts4.Items.Add("A-")
            Cb_Kpts4.Items.Add("B+")
            Cb_Kpts4.Items.Add("B")
            Cb_Kpts4.Items.Add("C+")
            Cb_Kpts4.Items.Add("C")
            Cb_Kpts4.Items.Add("D")
            Cb_Kpts4.Items.Add("E")
            '-----------

            Cb_Kpts5.Items.Clear()
            Cb_Kpts5.Items.Add("")
            Cb_Kpts5.Items.Add("1A")
            Cb_Kpts5.Items.Add("2A")
            Cb_Kpts5.Items.Add("3B")
            Cb_Kpts5.Items.Add("4B")
            Cb_Kpts5.Items.Add("5C")
            Cb_Kpts5.Items.Add("6C")
            Cb_Kpts5.Items.Add("7D")
            Cb_Kpts5.Items.Add("8E")

            Cb_Kpts5.Items.Add("A+")
            Cb_Kpts5.Items.Add("A")
            Cb_Kpts5.Items.Add("A-")
            Cb_Kpts5.Items.Add("B+")
            Cb_Kpts5.Items.Add("B")
            Cb_Kpts5.Items.Add("C+")
            Cb_Kpts5.Items.Add("C")
            Cb_Kpts5.Items.Add("D")
            Cb_Kpts5.Items.Add("E")
            '-----------

            Cb_Kpts6.Items.Clear()
            Cb_Kpts6.Items.Add("")
            Cb_Kpts6.Items.Add("1A")
            Cb_Kpts6.Items.Add("2A")
            Cb_Kpts6.Items.Add("3B")
            Cb_Kpts6.Items.Add("4B")
            Cb_Kpts6.Items.Add("5C")
            Cb_Kpts6.Items.Add("6C")
            Cb_Kpts6.Items.Add("7D")
            Cb_Kpts6.Items.Add("8E")

            Cb_Kpts6.Items.Add("A+")
            Cb_Kpts6.Items.Add("A")
            Cb_Kpts6.Items.Add("A-")
            Cb_Kpts6.Items.Add("B+")
            Cb_Kpts6.Items.Add("B")
            Cb_Kpts6.Items.Add("C+")
            Cb_Kpts6.Items.Add("C")
            Cb_Kpts6.Items.Add("D")
            Cb_Kpts6.Items.Add("E")
            '-----------

            Cb_Kpts7.Items.Clear()
            Cb_Kpts7.Items.Add("")
            Cb_Kpts7.Items.Add("1A")
            Cb_Kpts7.Items.Add("2A")
            Cb_Kpts7.Items.Add("3B")
            Cb_Kpts7.Items.Add("4B")
            Cb_Kpts7.Items.Add("5C")
            Cb_Kpts7.Items.Add("6C")
            Cb_Kpts7.Items.Add("7D")
            Cb_Kpts7.Items.Add("8E")

            Cb_Kpts7.Items.Add("A+")
            Cb_Kpts7.Items.Add("A")
            Cb_Kpts7.Items.Add("A-")
            Cb_Kpts7.Items.Add("B+")
            Cb_Kpts7.Items.Add("B")
            Cb_Kpts7.Items.Add("C+")
            Cb_Kpts7.Items.Add("C")
            Cb_Kpts7.Items.Add("D")
            Cb_Kpts7.Items.Add("E")
            '-----------

            Cb_Kpts8.Items.Clear()
            Cb_Kpts8.Items.Add("")
            Cb_Kpts8.Items.Add("1A")
            Cb_Kpts8.Items.Add("2A")
            Cb_Kpts8.Items.Add("3B")
            Cb_Kpts8.Items.Add("4B")
            Cb_Kpts8.Items.Add("5C")
            Cb_Kpts8.Items.Add("6C")
            Cb_Kpts8.Items.Add("7D")
            Cb_Kpts8.Items.Add("8E")

            Cb_Kpts8.Items.Add("A+")
            Cb_Kpts8.Items.Add("A")
            Cb_Kpts8.Items.Add("A-")
            Cb_Kpts8.Items.Add("B+")
            Cb_Kpts8.Items.Add("B")
            Cb_Kpts8.Items.Add("C+")
            Cb_Kpts8.Items.Add("C")
            Cb_Kpts8.Items.Add("D")
            Cb_Kpts8.Items.Add("E")
            '-----------

            Cb_Kpts9.Items.Clear()
            Cb_Kpts9.Items.Add("")
            Cb_Kpts9.Items.Add("1A")
            Cb_Kpts9.Items.Add("2A")
            Cb_Kpts9.Items.Add("3B")
            Cb_Kpts9.Items.Add("4B")
            Cb_Kpts9.Items.Add("5C")
            Cb_Kpts9.Items.Add("6C")
            Cb_Kpts9.Items.Add("7D")
            Cb_Kpts9.Items.Add("8E")

            Cb_Kpts9.Items.Add("A+")
            Cb_Kpts9.Items.Add("A")
            Cb_Kpts9.Items.Add("A-")
            Cb_Kpts9.Items.Add("B+")
            Cb_Kpts9.Items.Add("B")
            Cb_Kpts9.Items.Add("C+")
            Cb_Kpts9.Items.Add("C")
            Cb_Kpts9.Items.Add("D")
            Cb_Kpts9.Items.Add("E")
            '-----------

        ElseIf X = "STPM" Then
            Cb_Kpts1.Items.Clear()
            Cb_Kpts1.Items.Add("")
            Cb_Kpts1.Items.Add("A")
            Cb_Kpts1.Items.Add("A-")
            Cb_Kpts1.Items.Add("B+")
            Cb_Kpts1.Items.Add("B")
            Cb_Kpts1.Items.Add("B-")
            Cb_Kpts1.Items.Add("C+")
            Cb_Kpts1.Items.Add("C")
            Cb_Kpts1.Items.Add("C-")
            Cb_Kpts1.Items.Add("D+")
            Cb_Kpts1.Items.Add("D")
            Cb_Kpts1.Items.Add("D-")

            Cb_Kpts2.Items.Clear()
            Cb_Kpts2.Items.Add("")
            Cb_Kpts2.Items.Add("A")
            Cb_Kpts2.Items.Add("A-")
            Cb_Kpts2.Items.Add("B+")
            Cb_Kpts2.Items.Add("B")
            Cb_Kpts2.Items.Add("B-")
            Cb_Kpts2.Items.Add("C+")
            Cb_Kpts2.Items.Add("C")
            Cb_Kpts2.Items.Add("C-")
            Cb_Kpts2.Items.Add("D+")
            Cb_Kpts2.Items.Add("D")
            Cb_Kpts2.Items.Add("D-")

            Cb_Kpts3.Items.Clear()
            Cb_Kpts3.Items.Add("")
            Cb_Kpts3.Items.Add("A")
            Cb_Kpts3.Items.Add("A-")
            Cb_Kpts3.Items.Add("B+")
            Cb_Kpts3.Items.Add("B")
            Cb_Kpts3.Items.Add("B-")
            Cb_Kpts3.Items.Add("C+")
            Cb_Kpts3.Items.Add("C")
            Cb_Kpts3.Items.Add("C-")
            Cb_Kpts3.Items.Add("D+")
            Cb_Kpts3.Items.Add("D")
            Cb_Kpts3.Items.Add("D-")

            Cb_Kpts4.Items.Clear()
            Cb_Kpts4.Items.Add("")
            Cb_Kpts4.Items.Add("A")
            Cb_Kpts4.Items.Add("A-")
            Cb_Kpts4.Items.Add("B+")
            Cb_Kpts4.Items.Add("B")
            Cb_Kpts4.Items.Add("B-")
            Cb_Kpts4.Items.Add("C+")
            Cb_Kpts4.Items.Add("C")
            Cb_Kpts4.Items.Add("C-")
            Cb_Kpts4.Items.Add("D+")
            Cb_Kpts4.Items.Add("D")
            Cb_Kpts4.Items.Add("D-")

            Cb_Kpts5.Items.Clear()
            Cb_Kpts5.Items.Add("")
            Cb_Kpts5.Items.Add("A")
            Cb_Kpts5.Items.Add("A-")
            Cb_Kpts5.Items.Add("B+")
            Cb_Kpts5.Items.Add("B")
            Cb_Kpts5.Items.Add("B-")
            Cb_Kpts5.Items.Add("C+")
            Cb_Kpts5.Items.Add("C")
            Cb_Kpts5.Items.Add("C-")
            Cb_Kpts5.Items.Add("D+")
            Cb_Kpts5.Items.Add("D")
            Cb_Kpts5.Items.Add("D-")

            Cb_Kpts6.Items.Clear()
            Cb_Kpts6.Items.Add("")
            Cb_Kpts6.Items.Add("A")
            Cb_Kpts6.Items.Add("A-")
            Cb_Kpts6.Items.Add("B+")
            Cb_Kpts6.Items.Add("B")
            Cb_Kpts6.Items.Add("B-")
            Cb_Kpts6.Items.Add("C+")
            Cb_Kpts6.Items.Add("C")
            Cb_Kpts6.Items.Add("C-")
            Cb_Kpts6.Items.Add("D+")
            Cb_Kpts6.Items.Add("D")
            Cb_Kpts6.Items.Add("D-")

            Cb_Kpts7.Items.Clear()
            Cb_Kpts7.Items.Add("")
            Cb_Kpts7.Items.Add("A")
            Cb_Kpts7.Items.Add("A-")
            Cb_Kpts7.Items.Add("B+")
            Cb_Kpts7.Items.Add("B")
            Cb_Kpts7.Items.Add("B-")
            Cb_Kpts7.Items.Add("C+")
            Cb_Kpts7.Items.Add("C")
            Cb_Kpts7.Items.Add("C-")
            Cb_Kpts7.Items.Add("D+")
            Cb_Kpts7.Items.Add("D")
            Cb_Kpts7.Items.Add("D-")

            Cb_Kpts8.Items.Clear()
            Cb_Kpts8.Items.Add("")
            Cb_Kpts8.Items.Add("A")
            Cb_Kpts8.Items.Add("A-")
            Cb_Kpts8.Items.Add("B+")
            Cb_Kpts8.Items.Add("B")
            Cb_Kpts8.Items.Add("B-")
            Cb_Kpts8.Items.Add("C+")
            Cb_Kpts8.Items.Add("C")
            Cb_Kpts8.Items.Add("C-")
            Cb_Kpts8.Items.Add("D+")
            Cb_Kpts8.Items.Add("D")
            Cb_Kpts8.Items.Add("D-")

            Cb_Kpts9.Items.Clear()
            Cb_Kpts9.Items.Add("")
            Cb_Kpts9.Items.Add("A")
            Cb_Kpts9.Items.Add("A-")
            Cb_Kpts9.Items.Add("B+")
            Cb_Kpts9.Items.Add("B")
            Cb_Kpts9.Items.Add("B-")
            Cb_Kpts9.Items.Add("C+")
            Cb_Kpts9.Items.Add("C")
            Cb_Kpts9.Items.Add("C-")
            Cb_Kpts9.Items.Add("D+")
            Cb_Kpts9.Items.Add("D")
            Cb_Kpts9.Items.Add("D-")
        Else

        End If
    End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Private Sub Isi()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Cmd.CommandText = "select p.*, kr.* from pelatih p inner join pn_kursus kr on p.j_kursus = kr.id_kursus where p.nokp = '" & Session("nokp") & "'"
        Cmd.CommandText = "select p.*, kj.*, tj.*, kr.*, wg.*, ng.*, ng2.dc_negeri as 'w_negeri' from pelatih p " & _
                            "left outer join pn_kursus kr on p.j_kursus = kr.id_kursus " & _
                            "left outer join pn_kolej kj on p.id_kolej = kj.id_kolej " & _
                            "left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan " & _
                            "left outer join pn_negara wg on p.warganegara = wg.id_negara " & _
                            "left outer join pn_negeri ng on p.tp_negeri = ng.id_negeri " & _
                            "left outer join pn_negeri ng2 on p.w_negeri = ng2.id_negeri " & _
                            "where p.nokp = '" & Session("nokp") & "' and " & _
                            "p.j_kursus ='" & Session("j_kursus") & "' " 'add parameter type course 31052012
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Cb_Kursus.Items.FindByText(Rdr("dc_kursus")).Selected = True
            If Not IsDBNull(Rdr("kelayakan")) Then Cb_Kelayakan.Items.Add(Rdr("kelayakan")) : Cb_Kelayakan.SelectedIndex = 0
            If Not IsDBNull(Rdr("setaraf")) Then Cb_Kelayakan0.SelectedIndex = Rdr("setaraf")
            If Not IsDBNull(Rdr("tahun_lyk")) Then Cb_Thn_Kelayakan.Items.FindByText(Rdr("tahun_lyk")).Selected = True
            Tx_Nama.Text = Rdr("nama") : Tx_Nama.Enabled = False
            Tx_NoKP.Text = Rdr("nokp") : Tx_NoKP.Enabled = False
            If Not IsDBNull(Rdr("dc_negara")) Then Cb_Warga.Items.FindByText(Rdr("dc_negara")).Selected = True
            If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") = "" Then Cb_Jantina.SelectedIndex = 0 Else Cb_Jantina.SelectedIndex = Rdr("jantina")
            Cb_Bangsa.SelectedIndex = Rdr("bangsa")
            Cb_Agama.SelectedIndex = Rdr("agama")
            Cb_Kahwin.SelectedIndex = Rdr("t_kahwin")
            Tx_TP_Alamat.Text = Rdr("tp_alamat")
            Tx_TP_Poskod.Text = Rdr("tp_poskod")
            Tx_TP_Bandar.Text = Rdr("tp_bandar")
            If Not IsDBNull(Rdr("dc_negeri")) Then Cb_TP_Negeri.Items.FindByText(Rdr("dc_negeri")).Selected = True
            Tx_Tel.Text = Rdr("tel")
            Tx_Emel.Text = Rdr("emel")
            Tx_W_Nama.Text = Rdr("w_nama")
            Tx_W_Alamat.Text = Rdr("w_alamat")
            Tx_W_Poskod.Text = Rdr("w_poskod")
            Tx_W_Bandar.Text = Rdr("w_bandar")
            If Not IsDBNull(Rdr("w_negeri")) Then Cb_W_Negeri.Items.FindByText(Rdr("w_negeri")).Selected = True
            If Not IsDBNull(Rdr("w_negara")) Then Cb_Negara.SelectedValue = Rdr("w_negara")
            Tx_W_Tel.Text = Rdr("w_tel")
            If Not IsDBNull(Rdr("dc_kolej")) Then Cb_Kolej.Items.FindByText(Rdr("dc_kolej")).Selected = True
            If Not IsDBNull(Rdr("dc_tajaan")) Then Cb_Tajaan.Items.FindByText(Rdr("dc_tajaan")).Selected = True
            If Not IsDBNull(Rdr("sesi_bulan")) Then Cb_Sesi_Bulan.SelectedIndex = Rdr("sesi_bulan")
            If Not IsDBNull(Rdr("sesi_tahun")) Then Cb_Sesi_Tahun.Items.FindByText(Rdr("sesi_tahun")).Selected = True
            If Not IsDBNull(Rdr("tkh_latihan_mula")) Then Tx_M_Latihan.Text = Rdr("tkh_latihan_mula")

            If Not IsDBNull(Rdr("markah")) Then Tx_Markah.Text = Rdr("markah")
            If Not IsDBNull(Rdr("cuti")) Then Tx_Cuti.Text = Rdr("cuti")
            If Not IsDBNull(Rdr("cuti_sebab")) Then Tx_Cuti_Sebab.Text = Rdr("cuti_sebab")
            If Not IsDBNull(Rdr("tatatertib")) Then Cb_Tatatertib.SelectedIndex = Rdr("tatatertib")

            If Not IsDBNull(Rdr("ss1")) Then If Rdr("ss1") = 1 Then chk_Semak.Items(0).Selected = True
            If Not IsDBNull(Rdr("ss2")) Then If Rdr("ss2") = 1 Then chk_Semak.Items(1).Selected = True
            If Not IsDBNull(Rdr("ss3")) Then If Rdr("ss3") = 1 Then chk_Semak.Items(2).Selected = True
            If Not IsDBNull(Rdr("ss4")) Then If Rdr("ss4") = 1 Then chk_Semak.Items(3).Selected = True
            If Not IsDBNull(Rdr("ss5")) Then If Rdr("ss5") = 1 Then chk_Semak.Items(4).Selected = True
            If Not IsDBNull(Rdr("ss6")) Then If Rdr("ss6") = 1 Then chk_Semak.Items(5).Selected = True
            If Not IsDBNull(Rdr("ss7")) Then If Rdr("ss7") = 1 Then chk_Semak.Items(6).Selected = True
            If Not IsDBNull(Rdr("ss8")) Then If Rdr("ss8") = 1 Then chk_Semak.Items(7).Selected = True
        End If
        Rdr.Close()

        If Cb_Kursus.SelectedIndex = 4 Then
            Cb_Kelayakan.Enabled = False

        Else
            Isi_Subjek(Cb_Kelayakan.SelectedItem.Text)
            Isi_Kelayakan()
        End If
        Bt4.Enabled = False
        Bt5.Enabled = False
        Bt6.Enabled = False
        Bt7.Enabled = False
        Bt8.Enabled = False

        Cb_Kursus.Enabled = False
        Cb_Kelayakan.Enabled = False
        Cb_Kelayakan0.Enabled = False
        Cb_Thn_Kelayakan.Enabled = False
        Cb_Kpts1.Enabled = False
        Cb_Kpts2.Enabled = False
        Cb_Kpts3.Enabled = False
        Cb_Kpts4.Enabled = False
        Cb_Kpts5.Enabled = False
        Cb_Kpts6.Enabled = False
        Cb_Kpts7.Enabled = False
        Cb_Kpts8.Enabled = False
        Cb_Kpts9.Enabled = False
        Cb_Sbj10.Enabled = False
        Cb_Sbj11.Enabled = False
        Tx_Kpts10.Enabled = False
        Cb_Kpts11.Enabled = False
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Pelatih_Pinda", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cb_Thn_Kelayakan.Items.Clear()
        Cb_Thn_Kelayakan.Items.Add("")
        For i = Now.Year To Now.Year - 60 Step -1
            Cb_Thn_Kelayakan.Items.Add(i)
        Next

        'WARGANEGARA
        Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY ID_NEGARA"
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        Cb_Negara.Items.Clear()
        Cb_Negara.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
            Cb_Negara.Items.Add(Rdr(0))
            Cb_Negara.Items.Item(Cb_Negara.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        Cb_W_Negeri.Items.Clear()
        Cb_W_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negeri.Items.Add(Rdr(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KURSUS
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cb_Sesi_Bulan.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI")
        Cb_Sesi_Bulan.Items.Add("FEBRUARI")
        Cb_Sesi_Bulan.Items.Add("MAC")
        Cb_Sesi_Bulan.Items.Add("APRIL")
        Cb_Sesi_Bulan.Items.Add("MEI")
        Cb_Sesi_Bulan.Items.Add("JUN")
        Cb_Sesi_Bulan.Items.Add("JULAI")
        Cb_Sesi_Bulan.Items.Add("OGOS")
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER")
        Cb_Sesi_Bulan.Items.Add("OKTOBER")
        Cb_Sesi_Bulan.Items.Add("NOVEMBER")
        Cb_Sesi_Bulan.Items.Add("DISEMBER")

        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        For i = 0 To 6 ' increase  length to 6 from 5 03082012 
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i)
        Next

        'KOLEJ
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cn.Close()

        Cb_Tatatertib.Items.Add("YA")
        Cb_Tatatertib.Items.Add("TIDAK")

        Isi()
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        'If Chk_Subjek(False) = True Then Exit Sub

        'Mandatori...
        Dim X As String = ""
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        If Tx_Markah.Text = "" And (Cb_Kursus.SelectedValue = "1" Or Cb_Kursus.SelectedValue = "5" Or Cb_Kursus.SelectedValue = "8") Then X += " Jumlah CPGA 0.00- 4.00, "
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub


        ' Check dan calculate mc leave rule 09082012
        Dim y As Integer
        If Tx_M_Latihan.Text.Trim <> "" And Tx_T_Latihan.Text.Trim = "" Then X += "Tarikh Tamat Latihan,"
        If Tx_Cuti.Text.Trim <> "" And Tx_Cuti_Ganti.Text.Trim <> "" Then y = Tx_Cuti.Text.Trim - Tx_Cuti_Ganti.Text.Trim
        If y < 0 Then X += "Jumlah Cuti "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X)) : Msg(Me, "Sila semak maklumat berikut: " & X) : Exit Sub

        'If Tx_Cuti.Text = "" Then Tx_Cuti.Text = "0"
        'If Tx_Cuti_Ganti.Text = "" Then Tx_Cuti_Ganti.Text = "0"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Dim SQL As String
            SQL = "update pelatih set " & _
                                "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', " & _
                                "nokp = '" & Tx_NoKP.Text.Trim & "', " & _
                                "warganegara = '" & Cb_Warga.SelectedItem.Value & "', " & _
                                "jantina = '" & Cb_Jantina.SelectedIndex & "', " & _
                                "bangsa = '" & Cb_Bangsa.SelectedIndex & "', " & _
                                "agama = '" & Cb_Agama.SelectedIndex & "', " & _
                                "t_kahwin = '" & Cb_Kahwin.SelectedIndex & "', " & _
                                "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim) & "', " & _
                                "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "', " & _
                                "tp_bandar = '" & Tx_TP_Bandar.Text.Trim & "', " & _
                                "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "', " & _
                                "tel = '" & Tx_Tel.Text.Trim & "', " & _
                                "emel = '" & Tx_Emel.Text.Trim & "', " & _
                                "w_nama = '" & Apo(Tx_W_Nama.Text.Trim) & "', " & _
                                "w_alamat = '" & Apo(Tx_W_Alamat.Text.Trim) & "', " & _
                                "w_poskod = '" & Tx_W_Poskod.Text.Trim & "'," & _
                                "w_bandar = '" & Tx_W_Bandar.Text.Trim & "'," & _
                                "w_negeri = '" & Cb_W_Negeri.SelectedItem.Value & "'," & _
                                "w_negara = '" & Cb_Negara.SelectedItem.Value & "'," & _
                                "w_tel = '" & Tx_W_Tel.Text.Trim & "'," & _
                                "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," & _
                                "sesi_bulan = '" & Cb_Sesi_Bulan.SelectedIndex & "'," & _
                                "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
                                "tkh_latihan_mula = " & Chk_Tkh(Tx_M_Latihan.Text) & "," & _
                                "tkh_latihan_tamat = " & Chk_Tkh(Tx_T_Latihan.Text) & "," & _
                                "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "', " & _
                                "markah = '" & Tx_Markah.Text & "', " & _
                                "cuti = '" & Tx_Cuti.Text & "', " & _
                                "cuti_ganti = '" & Tx_Cuti_Ganti.Text & "', " & _
                                "cuti_sebab = '" & Tx_Cuti_Sebab.Text & "', " & _
                                "tatatertib = '" & Cb_Tatatertib.SelectedIndex & "', " & _
                                "ss1 = " & SSemak(0) & ", " & _
                                "ss2 = " & SSemak(1) & ", " & _
                                "ss3 = " & SSemak(2) & ", " & _
                                "ss4 = " & SSemak(3) & ", " & _
                                "ss5 = " & SSemak(4) & ", " & _
                                "ss6 = " & SSemak(5) & ", " & _
                                "ss7 = " & SSemak(6) & ", " & _
                                "ss8 = " & SSemak(7) & ", " & _
                                "log_id = '" & Session("Id_PG") & "', " & _
                                "log_tkh = getdate()  " & _
                                "where nokp ='" & Rdr("nokp") & "'and " & _
                                "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "' and " & _
                                "j_kursus ='" & Session("j_kursus") & "'" 'improvent condition query college and courses 23082012 

            Rdr.Close()
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Session("Msg_Tajuk") = "Pinda Rekod Pelatih"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx")
        Else
            Rdr.Close()
        End If
        Cn.Close()

        'Try
        '    Sql = "insert pelatih (j_kursus, nama, nokp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values (" & _
        '        "" & Cb_Kursus.SelectedItem.Value & "," & _
        '        "'" & Tx_Nama.Text.Trim & "'," & _
        '        "'" & Tx_NoKP.Text.Trim & "'," & _
        '        "" & Cb_Warga.SelectedItem.Value & "," & _
        '        "" & Cb_Jantina.SelectedIndex & "," & _
        '        "" & Cb_Bangsa.SelectedIndex & "," & _
        '        "" & Cb_Agama.SelectedIndex & "," & _
        '        "" & Cb_Kahwin.SelectedIndex & "," & _
        '        "'" & Tx_TP_Alamat.Text.Trim & "'," & _
        '        "'" & Tx_TP_Poskod.Text.Trim & "'," & _
        '        "'" & Tx_TP_Bandar.Text.Trim & "'," & _
        '        "'" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
        '        "'" & Tx_Tel.Text.Trim & "'," & _
        '        "'" & Tx_Emel.Text.Trim & "'," & _
        '        "'" & Tx_W_Nama.Text.Trim & "'," & _
        '        "'" & Tx_W_Alamat.Text.Trim & "'," & _
        '        "'" & Tx_W_Poskod.Text.Trim & "'," & _
        '        "'" & Tx_W_Bandar.Text.Trim & "'," & _
        '        "'" & Cb_W_Negeri.SelectedItem.Value & "'," & _
        '        "'" & Tx_W_Tel.Text.Trim & "'," & _
        '        "'" & Cb_Kolej.SelectedItem.Value & "'," & _
        '        "'" & Cb_Tajaan.SelectedItem.Value & "'," & _
        '        "" & Cb_Sesi_Bulan.SelectedIndex & "," & _
        '        "'" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
        '        "" & Chk_Tkh(Tx_M_Latihan.Text) & "," & _
        '        "" & SSemak(0) & "," & _
        '        "" & SSemak(1) & "," & _
        '        "" & SSemak(2) & "," & _
        '        "" & SSemak(3) & "," & _
        '        "" & SSemak(4) & "," & _
        '        "" & SSemak(5) & "," & _
        '        "" & SSemak(6) & "," & _
        '        "" & SSemak(7) & "," & _
        '        "'sa'," & _
        '        "getdate()" & _
        '        ")"
        '    Msg(Me, Sql, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
        '    Cmd.CommandText = Sql
        '    Cmd.ExecuteNonQuery()
        '    Cn.Close()
        'Catch ex As Exception
        '    Msg(Me, ex.Message)
        'End Try
    End Sub
End Class