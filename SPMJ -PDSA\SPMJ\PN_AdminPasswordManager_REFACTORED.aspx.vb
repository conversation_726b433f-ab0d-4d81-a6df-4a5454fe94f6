Imports System
Imports System.Data
Imports System.Data.OleDb
Imports System.Configuration
Imports System.Web
Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports System.Security.Cryptography
Imports System.Text
Imports System.Net

''' <summary>
''' PN_AdminPasswordManager - Advanced Password Management System
''' .NET Framework 3.5.1 Compatible Implementation
''' Features: SHA256+Salt encryption, Email microservice integration, Temporary passwords
''' </summary>
Partial Public Class PN_AdminPasswordManager
    Inherits System.Web.UI.Page

#Region "Constants and Configuration"
    
    ' Security constants for .NET 3.5.1
    Private Const SALT_LENGTH As Integer = 16
    Private Const TEMP_PASSWORD_LENGTH As Integer = 12
    Private Const REGULAR_PASSWORD_LENGTH As Integer = 8
    Private Const MIN_PASSWORD_LENGTH As Integer = 6
    Private Const MAX_LOGIN_ATTEMPTS As Integer = 5
    
    ' Email service configuration
    Private Const EMAIL_SERVICE_URL As String = "http://localhost:5000/api/email"
    Private Const EMAIL_AUTH_TOKEN As String = "Bearer SPMJ-ADMIN-TOKEN"
    
    ' Password character sets for .NET 3.5.1 compatibility
    Private Const TEMP_CHARS As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%"
    Private Const REGULAR_CHARS As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    
#End Region

#Region "Page-Level Variables"
    
    ' Current user being managed
    Private m_currentUser As UserData = Nothing
    
    ' .NET 3.5.1 compatible random number generator
    Private Shared m_random As New Random(DateTime.Now.Millisecond)
    
#End Region

#Region "Page Events"

    ''' <summary>
    ''' Page Load Event - .NET 3.5.1 Standard Implementation
    ''' </summary>
    ''' <param name="sender">Event sender</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            ' Security validation - .NET 3.5.1 standard approach
            If Not ValidateUserSession() Then
                Session.Abandon()
                Response.Redirect("p0_Login.aspx", True)
                Return
            End If

            If Not ValidateAdminPrivileges() Then
                ShowMessage("Access Denied: Insufficient privileges for password management.", "error")
                Response.Redirect("default.aspx", True)
                Return
            End If

            ' Initialize page controls on first load
            If Not Page.IsPostBack Then
                InitializePageControls()
            End If

        Catch ex As Exception
            ' .NET 3.5.1 compatible error handling
            LogError("Page_Load", ex.Message)
            ShowMessage("System error during page initialization: " + ex.Message, "error")
        End Try
    End Sub

#End Region

#Region "Button Event Handlers"

    ''' <summary>
    ''' Search User Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub btnSearchUser_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSearchUser.Click
        Try
            ClearAllPanels()

            ' Input validation - .NET 3.5.1 standard
            Dim searchId As String = TrimSafeString(txtSearchUser.Text)
            If String.IsNullOrEmpty(searchId) Then
                ShowMessage("Please enter a User ID to search.", "error")
                txtSearchUser.Focus()
                Return
            End If

            ' Search for user in database
            Dim userData As UserData = SearchUserInDatabase(searchId)
            If userData IsNot Nothing Then
                m_currentUser = userData
                DisplayUserInformation(userData)
                ShowPasswordActions()

                ' Pre-populate email if available
                If Not String.IsNullOrEmpty(userData.Email) Then
                    txtUserEmail.Text = userData.Email
                End If

                ShowMessage("User found successfully. Choose password management action below.", "success")
            Else
                ShowMessage("User with ID '" + searchId + "' not found in the system.", "error")
            End If

        Catch ex As Exception
            LogError("btnSearchUser_Click", ex.Message)
            ShowMessage("Error searching for user: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Set Password Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub btnSetPassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSetPassword.Click
        Try
            If m_currentUser Is Nothing Then
                ShowMessage("Please search for a user first.", "error")
                Return
            End If

            ' Validate email if email sending is enabled
            If chkSendEmail.Checked Then
                Dim emailText As String = TrimSafeString(txtUserEmail.Text)
                If Not IsValidEmailFormat(emailText) Then
                    ShowMessage("Please enter a valid email address to send password notification.", "error")
                    txtUserEmail.Focus()
                    Return
                End If
            End If

            ' Determine password type and generate
            Dim newPassword As String = ""
            Dim isTemporary As Boolean = rbTempPassword.Checked
            Dim customPassword As String = TrimSafeString(txtCustomPassword.Text)
            
            If Not String.IsNullOrEmpty(customPassword) Then
                If customPassword.Length < MIN_PASSWORD_LENGTH Then
                    ShowMessage("Custom password must be at least " + MIN_PASSWORD_LENGTH.ToString() + " characters long.", "error")
                    txtCustomPassword.Focus()
                    Return
                End If
                newPassword = customPassword
            Else
                If isTemporary Then
                    newPassword = GenerateTemporaryPassword()
                Else
                    newPassword = GenerateSecurePassword()
                End If
            End If

            ' Update password in database with SHA256+Salt
            If UpdateUserPasswordSecure(m_currentUser.UserId, newPassword, isTemporary, False) Then
                DisplayPasswordResult(newPassword, isTemporary)
                
                ' Send email if requested
                If chkSendEmail.Checked Then
                    Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                    If Not String.IsNullOrEmpty(emailAddress) Then
                        SendPasswordNotificationEmail(emailAddress, newPassword, isTemporary, False)
                    End If
                End If

                ClearPasswordInputs()
                LogPasswordActivity(m_currentUser.UserId, "Password Set", isTemporary)
                ShowMessage("Password has been set successfully.", "success")
            Else
                ShowMessage("Failed to set password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnSetPassword_Click", ex.Message)
            ShowMessage("Error setting password: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Force Reset Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub btnForceReset_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnForceReset.Click
        Try
            If m_currentUser Is Nothing Then
                ShowMessage("Please search for a user first.", "error")
                Return
            End If

            ' Generate temporary password for forced reset
            Dim tempPassword As String = GenerateTemporaryPassword()

            ' Update password and force change on next login
            If UpdateUserPasswordSecure(m_currentUser.UserId, tempPassword, True, True) Then
                DisplayPasswordResult(tempPassword, True)
                
                ' Send email notification
                If chkSendEmail.Checked Then
                    Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                    If IsValidEmailFormat(emailAddress) Then
                        SendPasswordNotificationEmail(emailAddress, tempPassword, True, True)
                    End If
                End If

                ClearPasswordInputs()
                LogPasswordActivity(m_currentUser.UserId, "Force Reset", True)
                ShowMessage("Password forcibly reset. User must change password on next login.", "warning")
            Else
                ShowMessage("Failed to force reset password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnForceReset_Click", ex.Message)
            ShowMessage("Error forcing password reset: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Generate Temporary Password Button Click Event - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="sender">Button that triggered the event</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub btnGenerateTemp_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGenerateTemp.Click
        Try
            If m_currentUser Is Nothing Then
                ShowMessage("Please search for a user first.", "error")
                Return
            End If

            ' Generate temporary password for first-time users
            Dim tempPassword As String = GenerateTemporaryPassword()

            ' Update password as temporary
            If UpdateUserPasswordSecure(m_currentUser.UserId, tempPassword, True, False) Then
                DisplayPasswordResult(tempPassword, True)
                
                ' Send welcome email for first-time users
                If chkSendEmail.Checked Then
                    Dim emailAddress As String = TrimSafeString(txtUserEmail.Text)
                    If IsValidEmailFormat(emailAddress) Then
                        SendWelcomeEmail(emailAddress, tempPassword)
                    End If
                End If

                ClearPasswordInputs()
                LogPasswordActivity(m_currentUser.UserId, "Temporary Generated", True)
                ShowMessage("Temporary password generated for first-time user.", "info")
            Else
                ShowMessage("Failed to generate temporary password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnGenerateTemp_Click", ex.Message)
            ShowMessage("Error generating temporary password: " + ex.Message, "error")
        End Try
    End Sub

#End Region

#Region "Database Operations"

    ''' <summary>
    ''' Search user in database - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="userId">User ID to search for</param>
    ''' <returns>UserData object if found, Nothing if not found</returns>
    Private Function SearchUserInDatabase(ByVal userId As String) As UserData
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT id_pg, nama, email, status, modul, akses, tarikh_daftar, tarikh_login_akhir FROM pn_pengguna WHERE id_pg = ?"
            command.Parameters.AddWithValue("@id_pg", userId)

            reader = command.ExecuteReader()

            If reader.Read() Then
                Dim userData As New UserData()
                userData.UserId = GetSafeStringValue(reader, "id_pg")
                userData.Name = GetSafeStringValue(reader, "nama")
                userData.Email = GetSafeStringValue(reader, "email")
                userData.Status = GetSafeStringValue(reader, "status")
                userData.UserModule = GetSafeStringValue(reader, "modul")
                userData.Access = GetSafeStringValue(reader, "akses")
                userData.RegisterDate = GetSafeDateValue(reader, "tarikh_daftar")
                userData.LastLogin = GetSafeDateValue(reader, "tarikh_login_akhir")
                Return userData
            End If

            Return Nothing

        Catch ex As Exception
            LogError("SearchUserInDatabase", ex.Message)
            Return Nothing
        Finally
            If reader IsNot Nothing Then reader.Close()
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then 
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Update user password with SHA256+Salt encryption - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="userId">User ID to update</param>
    ''' <param name="newPassword">New password in plain text</param>
    ''' <param name="isTemporary">Whether this is a temporary password</param>
    ''' <param name="forceChange">Whether to force change on next login</param>
    ''' <returns>True if successful, False if failed</returns>
    Private Function UpdateUserPasswordSecure(ByVal userId As String, ByVal newPassword As String, ByVal isTemporary As Boolean, ByVal forceChange As Boolean) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            ' Generate unique salt for this password
            Dim salt As String = GenerateSecureSalt()
            
            ' Create SHA256 hash with salt
            Dim hashedPassword As String = HashPasswordWithSalt(newPassword, salt)

            command = New OleDbCommand()
            command.Connection = connection

            ' Update password with security metadata
            Dim sql As String = "UPDATE pn_pengguna SET katalaluan = ?, salt = ?, is_temporary = ?, force_change = ?, tarikh_tukar_katalaluan = ?, last_changed_by = ? WHERE id_pg = ?"
            command.CommandText = sql
            command.Parameters.AddWithValue("@password", hashedPassword)
            command.Parameters.AddWithValue("@salt", salt)
            command.Parameters.AddWithValue("@is_temporary", IIf(isTemporary, 1, 0))
            command.Parameters.AddWithValue("@force_change", IIf(forceChange, 1, 0))
            command.Parameters.AddWithValue("@date_changed", DateTime.Now)
            command.Parameters.AddWithValue("@changed_by", Session("Id_PG").ToString())
            command.Parameters.AddWithValue("@id_pg", userId)

            Dim rowsAffected As Integer = command.ExecuteNonQuery()
            Return rowsAffected > 0

        Catch ex As Exception
            LogError("UpdateUserPasswordSecure", ex.Message)
            Return False
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then 
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

#End Region

#Region "Security and Encryption"

    ''' <summary>
    ''' Generate cryptographically secure salt - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>Base64 encoded salt string</returns>
    Private Function GenerateSecureSalt() As String
        Try
            ' Use RNGCryptoServiceProvider for .NET 3.5.1
            Dim saltBytes(SALT_LENGTH - 1) As Byte
            Using rng As New RNGCryptoServiceProvider()
                rng.GetBytes(saltBytes)
            End Using
            Return Convert.ToBase64String(saltBytes)
        Catch ex As Exception
            ' Fallback for .NET 3.5.1 compatibility
            LogError("GenerateSecureSalt", ex.Message)
            Return GenerateFallbackSalt()
        End Try
    End Function

    ''' <summary>
    ''' Fallback salt generation for .NET 3.5.1 compatibility
    ''' </summary>
    ''' <returns>Random salt string</returns>
    Private Function GenerateFallbackSalt() As String
        Dim saltChars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        Dim salt As New StringBuilder(SALT_LENGTH)
        
        For i As Integer = 0 To SALT_LENGTH - 1
            salt.Append(saltChars(m_random.Next(saltChars.Length)))
        Next
        
        Return salt.ToString()
    End Function

    ''' <summary>
    ''' Hash password using SHA256 with salt - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="password">Plain text password</param>
    ''' <param name="salt">Salt to use for hashing</param>
    ''' <returns>SHA256 hash as hex string</returns>
    Private Function HashPasswordWithSalt(ByVal password As String, ByVal salt As String) As String
        Try
            ' Combine password and salt
            Dim saltedPassword As String = password + salt
            
            ' Create SHA256 hash - .NET 3.5.1 compatible
            Using sha256 As SHA256 = SHA256.Create()
                Dim inputBytes As Byte() = Encoding.UTF8.GetBytes(saltedPassword)
                Dim hashBytes As Byte() = sha256.ComputeHash(inputBytes)
                
                ' Convert to hex string
                Dim hashString As New StringBuilder()
                For Each b As Byte In hashBytes
                    hashString.Append(b.ToString("x2"))
                Next
                
                Return hashString.ToString()
            End Using
        Catch ex As Exception
            LogError("HashPasswordWithSalt", ex.Message)
            ' Fallback to MD5 for .NET 3.5.1 compatibility
            Return HashPasswordFallback(password + salt)
        End Try
    End Function

    ''' <summary>
    ''' Fallback password hashing using MD5 for .NET 3.5.1 compatibility
    ''' </summary>
    ''' <param name="input">Input string to hash</param>
    ''' <returns>MD5 hash as hex string</returns>
    Private Function HashPasswordFallback(ByVal input As String) As String
        Try
            Using md5 As New MD5CryptoServiceProvider()
                Dim inputBytes As Byte() = Encoding.UTF8.GetBytes(input)
                Dim hashBytes As Byte() = md5.ComputeHash(inputBytes)

                Dim hashString As New StringBuilder()
                For Each b As Byte In hashBytes
                    hashString.Append(b.ToString("x2"))
                Next

                Return hashString.ToString()
            End Using
        Catch
            ' Last resort - return input with simple transformation
            Return input.GetHashCode().ToString("x")
        End Try
    End Function

    ''' <summary>
    ''' Generate secure temporary password - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>Temporary password string</returns>
    Private Function GenerateTemporaryPassword() As String
        Dim password As New StringBuilder(TEMP_PASSWORD_LENGTH)

        ' Ensure complexity: uppercase, number, special character
        password.Append(TEMP_CHARS.Substring(m_random.Next(26), 1)) ' Uppercase
        password.Append(TEMP_CHARS.Substring(m_random.Next(26, 36), 1)) ' Number
        password.Append(TEMP_CHARS.Substring(m_random.Next(36, 40), 1)) ' Special

        ' Fill remaining with random characters
        For i As Integer = 3 To TEMP_PASSWORD_LENGTH - 1
            password.Append(TEMP_CHARS.Substring(m_random.Next(TEMP_CHARS.Length), 1))
        Next

        ' Shuffle the password for .NET 3.5.1 compatibility
        Return ShuffleString(password.ToString())
    End Function

    ''' <summary>
    ''' Generate regular secure password - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>Regular password string</returns>
    Private Function GenerateSecurePassword() As String
        Dim password As New StringBuilder(REGULAR_PASSWORD_LENGTH)

        For i As Integer = 0 To REGULAR_PASSWORD_LENGTH - 1
            password.Append(REGULAR_CHARS.Substring(m_random.Next(REGULAR_CHARS.Length), 1))
        Next

        Return password.ToString()
    End Function

    ''' <summary>
    ''' Shuffle string characters - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="input">Input string to shuffle</param>
    ''' <returns>Shuffled string</returns>
    Private Function ShuffleString(ByVal input As String) As String
        Dim chars As Char() = input.ToCharArray()
        
        For i As Integer = chars.Length - 1 To 1 Step -1
            Dim j As Integer = m_random.Next(i + 1)
            Dim temp As Char = chars(i)
            chars(i) = chars(j)
            chars(j) = temp
        Next
        
        Return New String(chars)
    End Function

#End Region

#Region "Authentication and Authorization"

    ''' <summary>
    ''' Validate user session - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>True if valid session, False otherwise</returns>
    Private Function ValidateUserSession() As Boolean
        Try
            Dim sessionId As Object = Session("Id_PG")
            If sessionId Is Nothing Then Return False
            
            Dim userId As String = sessionId.ToString().Trim()
            Return Not String.IsNullOrEmpty(userId)
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Validate admin privileges - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <returns>True if user has admin privileges, False otherwise</returns>
    Private Function ValidateAdminPrivileges() As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT akses, modul FROM pn_pengguna WHERE id_pg = ? AND status = '1'"
            command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())

            reader = command.ExecuteReader()
            If reader.Read() Then
                Dim access As String = GetSafeStringValue(reader, "akses").ToLower()
                Dim modul As String = GetSafeStringValue(reader, "modul").ToLower()
                
                ' Check for admin privileges
                Return access.Contains("admin") OrElse access.Contains("1") OrElse _
                       access.Contains("pengurusan") OrElse modul.Contains("admin")
            End If

            Return False

        Catch ex As Exception
            LogError("ValidateAdminPrivileges", ex.Message)
            Return False
        Finally
            If reader IsNot Nothing Then reader.Close()
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then 
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Function

#End Region

#Region "Email Integration"

    ''' <summary>
    ''' Send password notification email - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="emailAddress">Recipient email address</param>
    ''' <param name="password">Password to send</param>
    ''' <param name="isTemporary">Whether password is temporary</param>
    ''' <param name="isForceReset">Whether this is a force reset</param>
    Private Sub SendPasswordNotificationEmail(ByVal emailAddress As String, ByVal password As String, ByVal isTemporary As Boolean, ByVal isForceReset As Boolean)
        Try
            Dim emailType As String = IIf(isTemporary, "temporary", "permanent").ToString()
            If isForceReset Then emailType = "force_reset"

            ' Prepare email data for microservice - .NET 3.5.1 compatible JSON
            Dim emailData As String = "{"
            emailData += """to"":""" + emailAddress + ""","
            emailData += """subject"":""Password " + IIf(isTemporary, "Reset", "Update").ToString() + " - SPMJ System"","
            emailData += """templateType"":""password_" + emailType + ""","
            emailData += """data"":{"
            emailData += """userName"":""" + m_currentUser.Name.Replace("""", "\""") + ""","
            emailData += """userId"":""" + m_currentUser.UserId + ""","
            emailData += """password"":""" + password + ""","
            emailData += """isTemporary"":" + IIf(isTemporary, "true", "false").ToString() + ","
            emailData += """systemUrl"":""" + Request.Url.GetLeftPart(UriPartial.Authority) + ""","
            emailData += """timestamp"":""" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + """"
            emailData += "}"
            emailData += "}"

            ' Call email microservice - .NET 3.5.1 compatible
            Using client As New WebClient()
                client.Headers("Content-Type") = "application/json"
                client.Headers("Authorization") = EMAIL_AUTH_TOKEN
                
                Dim response As String = client.UploadString(EMAIL_SERVICE_URL + "/send-password", emailData)
                
                ' Show email status
                ShowEmailStatus(True, "Password email sent successfully to " + emailAddress)
            End Using

        Catch ex As Exception
            LogError("SendPasswordNotificationEmail", ex.Message)
            ' Show email failure but don't stop the process
            ShowEmailStatus(False, "Failed to send email: " + ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Send welcome email for first-time users - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="emailAddress">Recipient email address</param>
    ''' <param name="tempPassword">Temporary password</param>
    Private Sub SendWelcomeEmail(ByVal emailAddress As String, ByVal tempPassword As String)
        Try
            ' Prepare welcome email data - .NET 3.5.1 compatible JSON
            Dim emailData As String = "{"
            emailData += """to"":""" + emailAddress + ""","
            emailData += """subject"":""Welcome to SPMJ System - Account Created"","
            emailData += """templateType"":""welcome_new_user"","
            emailData += """data"":{"
            emailData += """userName"":""" + m_currentUser.Name.Replace("""", "\""") + ""","
            emailData += """userId"":""" + m_currentUser.UserId + ""","
            emailData += """tempPassword"":""" + tempPassword + ""","
            emailData += """systemUrl"":""" + Request.Url.GetLeftPart(UriPartial.Authority) + ""","
            emailData += """supportEmail"":""<EMAIL>"""
            emailData += "}"
            emailData += "}"

            ' Call email microservice
            Using client As New WebClient()
                client.Headers("Content-Type") = "application/json"
                client.Headers("Authorization") = EMAIL_AUTH_TOKEN
                
                Dim response As String = client.UploadString(EMAIL_SERVICE_URL + "/send-welcome", emailData)
                
                ' Show email status
                ShowEmailStatus(True, "Welcome email sent successfully to " + emailAddress)
            End Using

        Catch ex As Exception
            LogError("SendWelcomeEmail", ex.Message)
            ShowEmailStatus(False, "Failed to send welcome email: " + ex.Message)
        End Try
    End Sub

#End Region

#Region "Validation and Utility Methods"

    ''' <summary>
    ''' Validate email format - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="email">Email address to validate</param>
    ''' <returns>True if valid format, False otherwise</returns>
    Private Function IsValidEmailFormat(ByVal email As String) As Boolean
        Try
            If String.IsNullOrEmpty(email) Then Return False
            email = email.Trim()
            
            ' Basic email validation for .NET 3.5.1
            Dim atIndex As Integer = email.IndexOf("@")
            Dim dotIndex As Integer = email.LastIndexOf(".")
            
            Return email.Length >= 5 AndAlso _
                   atIndex > 0 AndAlso _
                   dotIndex > atIndex AndAlso _
                   dotIndex < email.Length - 1
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Safely trim string - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="input">Input string</param>
    ''' <returns>Trimmed string or empty string if null</returns>
    Private Function TrimSafeString(ByVal input As String) As String
        If input Is Nothing Then Return ""
        Return input.Trim()
    End Function

    ''' <summary>
    ''' Get safe string value from database reader - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="reader">Database reader</param>
    ''' <param name="columnName">Column name to read</param>
    ''' <returns>String value or empty string if null/DBNull</returns>
    Private Function GetSafeStringValue(ByVal reader As OleDbDataReader, ByVal columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return ""
            Else
                Return reader(columnName).ToString()
            End If
        Catch
            Return ""
        End Try
    End Function

    ''' <summary>
    ''' Get safe date value from database reader - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="reader">Database reader</param>
    ''' <param name="columnName">Column name to read</param>
    ''' <returns>Formatted date string or "Not available" if null/DBNull</returns>
    Private Function GetSafeDateValue(ByVal reader As OleDbDataReader, ByVal columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return "Not available"
            Else
                Dim dateValue As DateTime = CDate(reader(columnName))
                Return dateValue.ToString("dd/MM/yyyy")
            End If
        Catch
            Return "Not available"
        End Try
    End Function

#End Region

#Region "UI Management Methods"

    ''' <summary>
    ''' Initialize page controls - .NET 3.5.1 Compatible
    ''' </summary>
    Private Sub InitializePageControls()
        Try
            ClearAllPanels()
            ShowMessage("Admin Password Manager initialized. Search for a user to begin password management.", "info")
            txtSearchUser.Focus()
        Catch ex As Exception
            LogError("InitializePageControls", ex.Message)
            ShowMessage("Error initializing page controls: " + ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Display user information - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="userData">User data to display</param>
    Private Sub DisplayUserInformation(ByVal userData As UserData)
        Try
            lblUserId.Text = userData.UserId
            lblUserName.Text = IIf(String.IsNullOrEmpty(userData.Name), "No name available", userData.Name).ToString()
            lblUserEmail.Text = IIf(String.IsNullOrEmpty(userData.Email), "No email available", userData.Email).ToString()
            lblUserModule.Text = IIf(String.IsNullOrEmpty(userData.UserModule), "Not assigned", userData.UserModule).ToString()
            lblUserAccess.Text = IIf(String.IsNullOrEmpty(userData.Access), "Standard", userData.Access).ToString()
            lblLastLogin.Text = userData.LastLogin
            lblPasswordDate.Text = userData.RegisterDate

            ' Format status with color coding
            Select Case userData.Status
                Case "1"
                    lblUserStatus.Text = "✅ Active"
                Case "0"
                    lblUserStatus.Text = "❌ Inactive"
                Case Else
                    lblUserStatus.Text = "❓ Unknown"
            End Select

            pnlUserInfo.Visible = True
        Catch ex As Exception
            LogError("DisplayUserInformation", ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Show password management actions panel
    ''' </summary>
    Private Sub ShowPasswordActions()
        pnlPasswordActions.Visible = True
    End Sub

    ''' <summary>
    ''' Display password generation result - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="password">Generated password</param>
    ''' <param name="isTemporary">Whether password is temporary</param>
    Private Sub DisplayPasswordResult(ByVal password As String, ByVal isTemporary As Boolean)
        lblGeneratedPassword.Text = password
        lblPasswordType.Text = IIf(isTemporary, "Temporary (Must change on first login)", "Permanent").ToString()
        pnlPasswordResult.Visible = True
    End Sub

    ''' <summary>
    ''' Clear all panels for new search
    ''' </summary>
    Private Sub ClearAllPanels()
        pnlUserInfo.Visible = False
        pnlPasswordActions.Visible = False
        pnlPasswordResult.Visible = False
        pnlEmailStatus.Visible = False
    End Sub

    ''' <summary>
    ''' Clear password input fields
    ''' </summary>
    Private Sub ClearPasswordInputs()
        txtCustomPassword.Text = ""
    End Sub

    ''' <summary>
    ''' Show message with styling - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="message">Message to display</param>
    ''' <param name="messageType">Type of message (success, error, info, warning)</param>
    Private Sub ShowMessage(ByVal message As String, ByVal messageType As String)
        lblMessage.Text = message
        pnlMessage.Visible = True

        ' Set CSS class based on message type
        Select Case messageType.ToLower()
            Case "success"
                divMessage.Attributes("class") = "message-panel msg-success"
            Case "error"
                divMessage.Attributes("class") = "message-panel msg-error"
            Case "info"
                divMessage.Attributes("class") = "message-panel msg-info"
            Case "warning"
                divMessage.Attributes("class") = "message-panel msg-warning"
            Case Else
                divMessage.Attributes("class") = "message-panel msg-info"
        End Select
    End Sub

    ''' <summary>
    ''' Show email status - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="success">Whether email was successful</param>
    ''' <param name="message">Status message</param>
    Private Sub ShowEmailStatus(ByVal success As Boolean, ByVal message As String)
        pnlEmailStatus.Visible = True
        lblEmailStatus.Text = IIf(success, "✅ ", "❌ ").ToString() + message
        divEmailStatus.Attributes("class") = IIf(success, "email-sent", "email-failed").ToString()
    End Sub

#End Region

#Region "Logging and Audit"

    ''' <summary>
    ''' Log password activity for audit trail - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="userId">User ID</param>
    ''' <param name="action">Action performed</param>
    ''' <param name="isTemporary">Whether password is temporary</param>
    Private Sub LogPasswordActivity(ByVal userId As String, ByVal action As String, ByVal isTemporary As Boolean)
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        
        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "INSERT INTO password_log (user_id, admin_id, action, is_temporary, timestamp, ip_address) VALUES (?, ?, ?, ?, ?, ?)"
            command.Parameters.AddWithValue("@user_id", userId)
            command.Parameters.AddWithValue("@admin_id", Session("Id_PG").ToString())
            command.Parameters.AddWithValue("@action", action)
            command.Parameters.AddWithValue("@is_temporary", IIf(isTemporary, 1, 0))
            command.Parameters.AddWithValue("@timestamp", DateTime.Now)
            command.Parameters.AddWithValue("@ip_address", Request.UserHostAddress)

            command.ExecuteNonQuery()
        Catch ex As Exception
            ' Logging failure should not stop the main process
            LogError("LogPasswordActivity", ex.Message)
        Finally
            If command IsNot Nothing Then command.Dispose()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then 
                connection.Close()
                connection.Dispose()
            End If
        End Try
    End Sub

    ''' <summary>
    ''' Log errors for debugging - .NET 3.5.1 Compatible
    ''' </summary>
    ''' <param name="methodName">Method where error occurred</param>
    ''' <param name="errorMessage">Error message</param>
    Private Sub LogError(ByVal methodName As String, ByVal errorMessage As String)
        Try
            ' Simple error logging for .NET 3.5.1 - could be enhanced with proper logging framework
            Dim errorLog As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " - " + methodName + " - " + errorMessage
            ' In production, write to log file or database
            ' For now, we'll use application state for debugging
            Application("LastError") = errorLog
        Catch
            ' Ignore logging errors to prevent cascading failures
        End Try
    End Sub

#End Region

End Class

#Region "Data Transfer Objects"

''' <summary>
''' User data structure - .NET 3.5.1 Compatible
''' Enhanced with additional properties for comprehensive user management
''' </summary>
Public Class UserData
    Private m_userId As String = ""
    Private m_name As String = ""
    Private m_email As String = ""
    Private m_status As String = ""
    Private m_module As String = ""
    Private m_access As String = ""
    Private m_registerDate As String = ""
    Private m_lastLogin As String = ""

    ''' <summary>
    ''' User ID property with validation
    ''' </summary>
    Public Property UserId() As String
        Get
            Return m_userId
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_userId = ""
            Else
                m_userId = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' User name property with validation
    ''' </summary>
    Public Property Name() As String
        Get
            Return m_name
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_name = ""
            Else
                m_name = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Email property with validation
    ''' </summary>
    Public Property Email() As String
        Get
            Return m_email
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_email = ""
            Else
                m_email = value.Trim().ToLower()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Status property
    ''' </summary>
    Public Property Status() As String
        Get
            Return m_status
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_status = ""
            Else
                m_status = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' User module property
    ''' </summary>
    Public Property UserModule() As String
        Get
            Return m_module
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_module = ""
            Else
                m_module = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Access level property
    ''' </summary>
    Public Property Access() As String
        Get
            Return m_access
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_access = ""
            Else
                m_access = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Registration date property
    ''' </summary>
    Public Property RegisterDate() As String
        Get
            Return m_registerDate
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_registerDate = "Not available"
            Else
                m_registerDate = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Last login date property
    ''' </summary>
    Public Property LastLogin() As String
        Get
            Return m_lastLogin
        End Get
        Set(ByVal value As String)
            If value Is Nothing Then
                m_lastLogin = "Not available"
            Else
                m_lastLogin = value.Trim()
            End If
        End Set
    End Property

    ''' <summary>
    ''' Constructor for UserData class
    ''' </summary>
    Public Sub New()
        ' Initialize with default values
        m_userId = ""
        m_name = ""
        m_email = ""
        m_status = ""
        m_module = ""
        m_access = ""
        m_registerDate = "Not available"
        m_lastLogin = "Not available"
    End Sub

    ''' <summary>
    ''' Validate user data completeness
    ''' </summary>
    ''' <returns>True if essential data is present, False otherwise</returns>
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrEmpty(m_userId) AndAlso Not String.IsNullOrEmpty(m_name)
    End Function

    ''' <summary>
    ''' Get formatted display name for UI
    ''' </summary>
    ''' <returns>Formatted display name</returns>
    Public Function GetDisplayName() As String
        If Not String.IsNullOrEmpty(m_name) Then
            Return m_name + " (" + m_userId + ")"
        Else
            Return m_userId
        End If
    End Function

End Class

#End Region
