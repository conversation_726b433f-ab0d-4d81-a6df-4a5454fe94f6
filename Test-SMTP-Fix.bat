@echo off
echo ============================================================
echo SPMJ Email Microservice SMTP Fix Test
echo ============================================================
echo.

echo [TEST 1] Testing microservice health...
curl -s -w "Status: %%{http_code}\n" http://localhost:5000/api/admin/password/health
echo.

echo [TEST 2] Testing SMTP connectivity (Fixed SSL/TLS)...
curl -s -w "Status: %%{http_code}\n" http://localhost:5000/api/admin/password/test-smtp
echo.

echo [TEST 3] Testing email validation with API key...
curl -s -w "Status: %%{http_code}\n" ^
     -H "Content-Type: application/json" ^
     -H "X-API-Key: SPMJ-EmailService-2024-SecureKey-MOH-Malaysia" ^
     -d "\"<EMAIL>\"" ^
     http://localhost:5000/api/admin/password/validate-email
echo.

echo [TEST 4] Testing password notification email (should work now)...
curl -s -w "Status: %%{http_code}\n" ^
     -H "Content-Type: application/json" ^
     -H "X-API-Key: SPMJ-EmailService-2024-SecureKey-MOH-Malaysia" ^
     -d "{\"to\":\"<EMAIL>\",\"subject\":\"TEST - Password Reset - SPMJ System\",\"templateType\":\"force_reset\",\"data\":{\"userName\":\"Test User\",\"userId\":\"820228115693\",\"password\":\"TempPass123\",\"isTemporary\":true,\"systemUrl\":\"http://localhost:8080\",\"timestamp\":\"2025-06-15 14:30:45\",\"adminId\":\"ADMIN001\",\"adminName\":\"Test Administrator\"}}" ^
     http://localhost:5000/api/admin/password/send-force-reset
echo.

echo ============================================================
echo SMTP Fix Test Results
echo.
echo Expected Results After Fix:
echo   Test 1: Status 200 (Health check OK)
echo   Test 2: Status 200 (SMTP connection working)
echo   Test 3: Status 200 (Email validation working) 
echo   Test 4: Status 200 (Email sent successfully)
echo.
echo If Test 2 and 4 show Status 200, SMTP fix is working!
echo ============================================================
pause
