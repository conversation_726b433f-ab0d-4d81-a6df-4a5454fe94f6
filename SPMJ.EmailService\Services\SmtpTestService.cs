using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;

namespace SPMJ.EmailService.Services;

public class SmtpTestService
{
    public static async Task<bool> TestSmtpConnectionAsync(string smtpServer, int port, string username, string password)
    {
        try
        {
            using var client = new SmtpClient();
            
            // Use correct SSL options based on port
            SecureSocketOptions secureOptions;
            if (port == 465)
            {
                secureOptions = SecureSocketOptions.SslOnConnect;
            }
            else if (port == 587)
            {
                secureOptions = SecureSocketOptions.StartTls;
            }
            else
            {
                secureOptions = SecureSocketOptions.StartTls; // Default to StartTls
            }
            
            Console.WriteLine($"Testing connection to {smtpServer}:{port} with {secureOptions}...");
            
            await client.ConnectAsync(smtpServer, port, secureOptions);
            Console.WriteLine("Connection successful!");
            
            if (!string.IsNullOrEmpty(username))
            {
                Console.WriteLine("Testing authentication...");
                await client.AuthenticateAsync(username, password);
                Console.WriteLine("Authentication successful!");
            }
            
            await client.DisconnectAsync(true);
            Console.WriteLine("SMTP test completed successfully!");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SMTP test failed: {ex.Message}");
            return false;
        }
    }
}
