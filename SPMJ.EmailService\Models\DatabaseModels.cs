using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SPMJ.EmailService.Models;

[Table("pn_pengguna")]
public class PnPengguna
{
    [Key]
    [Column("id_pg")]
    public string IdPg { get; set; } = string.Empty;

    [Column("pwd")]
    public string? Pwd { get; set; }

    [Column("salt")]
    public string? Salt { get; set; }

    [Column("password_migrated")]
    public bool? PasswordMigrated { get; set; }

    [Column("nama")]
    public string? Nama { get; set; }

    [Column("email")]
    public string? Email { get; set; }    [Column("status")]
    public byte? Status { get; set; }

    [Column("modul")]
    public string? Modul { get; set; }

    [Column("akses")]
    public byte? Akses { get; set; }
}

[Table("password_reset_tokens")]
public class PasswordResetToken
{
    [Key]
    public int Id { get; set; }

    [Required]
    [Column("user_id")]
    public string UserId { get; set; } = string.Empty;

    [Required]
    [Column("token")]
    public string Token { get; set; } = string.Empty;

    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("expires_at")]
    public DateTime ExpiresAt { get; set; }

    [Column("used")]
    public bool Used { get; set; } = false;

    [Column("used_at")]
    public DateTime? UsedAt { get; set; }
}

[Table("otp_tokens")]
public class OtpToken
{
    [Key]
    public int Id { get; set; }

    [Required]
    [Column("user_id")]
    public string UserId { get; set; } = string.Empty;

    [Required]
    [Column("otp_code")]
    public string OtpCode { get; set; } = string.Empty;

    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("expires_at")]
    public DateTime ExpiresAt { get; set; }

    [Column("used")]
    public bool Used { get; set; } = false;

    [Column("used_at")]
    public DateTime? UsedAt { get; set; }

    [Column("purpose")]
    public string Purpose { get; set; } = string.Empty; // LOGIN, PASSWORD_RESET, etc.
}
