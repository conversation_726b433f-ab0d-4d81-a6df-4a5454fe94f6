﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="PN_Siri2.aspx.vb" Inherits="SPMJ.PN_Siri2" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td width="600"></td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td width="600" align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style1">penyelenggaraan&nbsp; - Siri Peperiksaan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="150px">TAHUN</asp:TextBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Tx_Tahun" runat="server" CssClass="std" Width="95px" 
                            Wrap="False"></asp:TextBox>
                        <asp:TextBox ID="Tx_Id" runat="server" CssClass="std" Width="50px" 
                            Wrap="False" Visible="False"></asp:TextBox>
                    </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="150px">SIRI PEPERIKSAAN</asp:TextBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Tx_Siri" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                    </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox ID="Cb_Sbj42" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="150px">JENIS PEPERIKSAAN</asp:TextBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:DropDownList ID="Cb_Jenis" runat="server" AutoPostBack="True" 
                            Font-Names="Arial" Font-Size="8pt" Width="339px">
                            <asp:ListItem></asp:ListItem>
                            <asp:ListItem>PEPERIKSAAN LJM SYARAT BAGI KEMASUKAN DALAM BAHAGIAN AM DAFTAR</asp:ListItem>
                            <asp:ListItem>PEPERIKSAAN LJM &amp; LBM BAGI JURURAWAT MASYARAKAT</asp:ListItem>
                            <asp:ListItem>PEPERIKSAAN LJM BAGI PENOLONG JURURAWAT</asp:ListItem>
                            <asp:ListItem>PEPERIKSAAN LBM BAGI DIPLOMA LANJUTAN KEBIDANAN (ADVANCED DIPLOMA 
                            IN MIDWIFERY )</asp:ListItem>
                        </asp:DropDownList>
                    </td>
            <td>&nbsp;</td>
        </tr>
         <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox 
                    ID="TextBox1" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="150px" Enabled="False" 
                            ReadOnly="True">TARIKH TAMAT SARINGAN</asp:TextBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Tx_Tkh_Saring_T" runat="server" CssClass="std" 
                    Width="80px"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="MaskedEditExtender1" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Saring_T" 
                            UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="CalendarExtender1" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh_Saring_T">
                        </cc1:CalendarExtender>
                        </td>
            <td>&nbsp;</td>
        </tr>
         <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox 
                    ID="TextBox2" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="150px" Enabled="False" 
                            ReadOnly="True">TARIKH KELUAR KEPUTUSAN</asp:TextBox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Tx_Tkh_Papar" runat="server" CssClass="std" 
                    Width="80px"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="Tx_Tkh_Papar_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Papar" 
                            UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_Papar_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh_Papar">
                        </cc1:CalendarExtender>
                        </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"></asp:TextBox>
                                        <asp:TextBox ID="Cb_Sbj22" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="100px" Enabled="False" 
                            ReadOnly="True">TARIKH</asp:TextBox>
                                        <asp:TextBox ID="Cb_Sbj23" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="95px" Enabled="False" 
                            ReadOnly="True">MASA</asp:TextBox>
                                        <asp:TextBox ID="Cb_Sbj38" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="95px" Enabled="False" 
                            ReadOnly="True">MARKAH LULUS</asp:TextBox>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" valign="top">&nbsp;
                <asp:Panel ID="Panel4" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;<asp:TextBox ID="Cb_Sbj1" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px">TEORI</asp:TextBox><asp:TextBox ID="Tx_Tkh_JB" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox><cc1:MaskedEditExtender ID="Tx_Tkh_JB_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_JB" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_JB_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_JB">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_JB" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                    <asp:TextBox ID="Tx_Mk_JB" runat="server" CssClass="std" Width="50px" 
                            Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel5" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj10" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px">TEORI</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_BIT" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_BIT_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_BIT" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_BIT_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_BIT">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_BIT" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                    <asp:TextBox ID="Tx_Mk_BI" runat="server" CssClass="std" Width="50px" 
                            Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel6" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj12" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">OSCE</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_BIO" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_BIO_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_BIO" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_BIO_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_BIO">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_BIO" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel7" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj13" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">VIVA</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_BIV" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_BIV_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_BIV" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_BIV_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_BIV">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_BIV" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                </asp:Panel>                
                <asp:Panel ID="Panel8" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj17" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TEORI</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_PJT1" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_PJT1_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_PJT1" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_PJT1_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_PJT1">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_PJT1" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                    <asp:TextBox ID="Tx_Mk_PJ" runat="server" CssClass="std" Width="50px" 
                            Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel9" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj19" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">KERTAS II</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_PJT2" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_PJT2_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_PJT2" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_PJT2_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_PJT2">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_PJT2" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel10" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj21" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">PRAKTIKAL</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_PJP" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_PJP_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_PJP" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_PJP_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_PJP">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_PJP" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel11" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj34" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TEORI</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_JMT1" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_JMT1_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_JMT1" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_JMT1_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_JMT1">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_JMT1" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                    <asp:TextBox ID="Tx_Mk_JM" runat="server" CssClass="std" Width="50px" 
                            Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel12" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj35" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">KERTAS II</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_JMT2" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_JMT2_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_JMT2" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_JMT2_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_JMT2">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_JMT2" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                </asp:Panel><asp:Panel ID="Panel13" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj36" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">OSCE</asp:TextBox>
                    <asp:TextBox ID="Tx_Tkh_JMO" 
                    runat="server" CssClass="std" Width="95px"></asp:TextBox>
                    <cc1:MaskedEditExtender ID="Tx_Tkh_JMO_MaskedEditExtender" runat="server" 
                                                                
    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" 
                    Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_JMO" 
                                                                
    UserDateFormat="DayMonthYear">
                    </cc1:MaskedEditExtender>
                    <cc1:CalendarExtender ID="Tx_Tkh_JMO_CalendarExtender" runat="server" 
                                                                Enabled="True" 
                    Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                
    TargetControlID="Tx_Tkh_JMO">
                    </cc1:CalendarExtender>
                    <asp:TextBox ID="Tx_Ms_JMO" runat="server" 
                    CssClass="std" Width="95px" 
                                                     Wrap="False"></asp:TextBox>
                </asp:Panel> 

            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" valign="top">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>        
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj37" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"></asp:TextBox>
                <asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="80px" />
            &nbsp;<asp:Button ID="cmd_Cari2" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="BARU" Width="80px" />
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td width="600">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td width="600">
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="600px" GridLines="Horizontal" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:CommandField ButtonType="Button" SelectText="PILIH" 
                                    ShowSelectButton="True">
                                    <ControlStyle Font-Names="Arial" Font-Size="8pt" Width="50px" />
                                </asp:CommandField>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="True" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
            </td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td width="600">
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
