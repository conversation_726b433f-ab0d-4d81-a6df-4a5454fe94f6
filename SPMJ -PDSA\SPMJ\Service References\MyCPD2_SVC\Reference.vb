﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System
Imports System.Runtime.Serialization

Namespace MyCPD2_SVC
    
    <System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******"),  _
     System.Runtime.Serialization.DataContractAttribute(Name:="UserDetails", [Namespace]:="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD"),  _
     System.SerializableAttribute()>  _
    Partial Public Class UserDetails
        Inherits Object
        Implements System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged
        
        <System.NonSerializedAttribute()>  _
        Private extensionDataField As System.Runtime.Serialization.ExtensionDataObject
        
        <System.Runtime.Serialization.OptionalFieldAttribute()>  _
        Private DateEndField As String
        
        <System.Runtime.Serialization.OptionalFieldAttribute()>  _
        Private DateStartField As String
        
        <System.Runtime.Serialization.OptionalFieldAttribute()>  _
        Private HcpField As String
        
        <System.Runtime.Serialization.OptionalFieldAttribute()>  _
        Private IdentityNoField As String
        
        <Global.System.ComponentModel.BrowsableAttribute(false)>  _
        Public Property ExtensionData() As System.Runtime.Serialization.ExtensionDataObject Implements System.Runtime.Serialization.IExtensibleDataObject.ExtensionData
            Get
                Return Me.extensionDataField
            End Get
            Set
                Me.extensionDataField = value
            End Set
        End Property
        
        <System.Runtime.Serialization.DataMemberAttribute()>  _
        Public Property DateEnd() As String
            Get
                Return Me.DateEndField
            End Get
            Set
                If (Object.ReferenceEquals(Me.DateEndField, value) <> true) Then
                    Me.DateEndField = value
                    Me.RaisePropertyChanged("DateEnd")
                End If
            End Set
        End Property
        
        <System.Runtime.Serialization.DataMemberAttribute()>  _
        Public Property DateStart() As String
            Get
                Return Me.DateStartField
            End Get
            Set
                If (Object.ReferenceEquals(Me.DateStartField, value) <> true) Then
                    Me.DateStartField = value
                    Me.RaisePropertyChanged("DateStart")
                End If
            End Set
        End Property
        
        <System.Runtime.Serialization.DataMemberAttribute()>  _
        Public Property Hcp() As String
            Get
                Return Me.HcpField
            End Get
            Set
                If (Object.ReferenceEquals(Me.HcpField, value) <> true) Then
                    Me.HcpField = value
                    Me.RaisePropertyChanged("Hcp")
                End If
            End Set
        End Property
        
        <System.Runtime.Serialization.DataMemberAttribute()>  _
        Public Property IdentityNo() As String
            Get
                Return Me.IdentityNoField
            End Get
            Set
                If (Object.ReferenceEquals(Me.IdentityNoField, value) <> true) Then
                    Me.IdentityNoField = value
                    Me.RaisePropertyChanged("IdentityNo")
                End If
            End Set
        End Property
        
        Public Event PropertyChanged As System.ComponentModel.PropertyChangedEventHandler Implements System.ComponentModel.INotifyPropertyChanged.PropertyChanged
        
        Protected Sub RaisePropertyChanged(ByVal propertyName As String)
            Dim propertyChanged As System.ComponentModel.PropertyChangedEventHandler = Me.PropertyChangedEvent
            If (Not (propertyChanged) Is Nothing) Then
                propertyChanged(Me, New System.ComponentModel.PropertyChangedEventArgs(propertyName))
            End If
        End Sub
    End Class
    
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******"),  _
     System.ServiceModel.ServiceContractAttribute(ConfigurationName:="MyCPD2_SVC.IService1")>  _
    Public Interface IService1
        
        <System.ServiceModel.OperationContractAttribute(Action:="http://tempuri.org/IService1/UserInfo", ReplyAction:="http://tempuri.org/IService1/UserInfoResponse")>  _
        Function UserInfo(ByVal userdet As MyCPD2_SVC.UserDetails) As System.Data.DataSet
        
        <System.ServiceModel.OperationContractAttribute(Action:="http://tempuri.org/IService1/PointByCategory", ReplyAction:="http://tempuri.org/IService1/PointByCategoryResponse")>  _
        Function PointByCategory(ByVal userdet As MyCPD2_SVC.UserDetails) As System.Data.DataSet
        
        <System.ServiceModel.OperationContractAttribute(Action:="http://tempuri.org/IService1/CumulativePoint", ReplyAction:="http://tempuri.org/IService1/CumulativePointResponse")>  _
        Function CumulativePoint(ByVal userdet As MyCPD2_SVC.UserDetails) As String
        
        <System.ServiceModel.OperationContractAttribute(Action:="http://tempuri.org/IService1/ActualPoint", ReplyAction:="http://tempuri.org/IService1/ActualPointResponse")>  _
        Function ActualPoint(ByVal userdet As MyCPD2_SVC.UserDetails) As String
        
        <System.ServiceModel.OperationContractAttribute(Action:="http://tempuri.org/IService1/HcpList", ReplyAction:="http://tempuri.org/IService1/HcpListResponse")>  _
        Function HcpList() As System.Data.DataSet
        
        <System.ServiceModel.OperationContractAttribute(Action:="http://tempuri.org/IService1/PointByYear", ReplyAction:="http://tempuri.org/IService1/PointByYearResponse")>  _
        Function PointByYear(ByVal userdet As MyCPD2_SVC.UserDetails) As System.Data.DataSet
    End Interface
    
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")>  _
    Public Interface IService1Channel
        Inherits MyCPD2_SVC.IService1, System.ServiceModel.IClientChannel
    End Interface
    
    <System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")>  _
    Partial Public Class Service1Client
        Inherits System.ServiceModel.ClientBase(Of MyCPD2_SVC.IService1)
        Implements MyCPD2_SVC.IService1
        
        Public Sub New()
            MyBase.New
        End Sub
        
        Public Sub New(ByVal endpointConfigurationName As String)
            MyBase.New(endpointConfigurationName)
        End Sub
        
        Public Sub New(ByVal endpointConfigurationName As String, ByVal remoteAddress As String)
            MyBase.New(endpointConfigurationName, remoteAddress)
        End Sub
        
        Public Sub New(ByVal endpointConfigurationName As String, ByVal remoteAddress As System.ServiceModel.EndpointAddress)
            MyBase.New(endpointConfigurationName, remoteAddress)
        End Sub
        
        Public Sub New(ByVal binding As System.ServiceModel.Channels.Binding, ByVal remoteAddress As System.ServiceModel.EndpointAddress)
            MyBase.New(binding, remoteAddress)
        End Sub
        
        Public Function UserInfo(ByVal userdet As MyCPD2_SVC.UserDetails) As System.Data.DataSet Implements MyCPD2_SVC.IService1.UserInfo
            Return MyBase.Channel.UserInfo(userdet)
        End Function
        
        Public Function PointByCategory(ByVal userdet As MyCPD2_SVC.UserDetails) As System.Data.DataSet Implements MyCPD2_SVC.IService1.PointByCategory
            Return MyBase.Channel.PointByCategory(userdet)
        End Function
        
        Public Function CumulativePoint(ByVal userdet As MyCPD2_SVC.UserDetails) As String Implements MyCPD2_SVC.IService1.CumulativePoint
            Return MyBase.Channel.CumulativePoint(userdet)
        End Function
        
        Public Function ActualPoint(ByVal userdet As MyCPD2_SVC.UserDetails) As String Implements MyCPD2_SVC.IService1.ActualPoint
            Return MyBase.Channel.ActualPoint(userdet)
        End Function
        
        Public Function HcpList() As System.Data.DataSet Implements MyCPD2_SVC.IService1.HcpList
            Return MyBase.Channel.HcpList
        End Function
        
        Public Function PointByYear(ByVal userdet As MyCPD2_SVC.UserDetails) As System.Data.DataSet Implements MyCPD2_SVC.IService1.PointByYear
            Return MyBase.Channel.PointByYear(userdet)
        End Function
    End Class
End Namespace
