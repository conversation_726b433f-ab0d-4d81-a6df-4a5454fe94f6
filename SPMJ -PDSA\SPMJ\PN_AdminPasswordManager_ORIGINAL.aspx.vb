Imports System
Imports System.Data
Imports System.Data.OleDb
Imports System.Configuration
Imports System.Web
Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports System.Security.Cryptography
Imports System.Text
Imports System.Net

''' <summary>
''' PN_AdminPasswordManager - Advanced Password Management System
''' .NET Framework 3.5.1 Compatible Implementation
''' Features: SHA256+Salt encryption, Email microservice integration, Temporary passwords
''' </summary>
Partial Public Class PN_AdminPasswordManager
    Inherits System.Web.UI.Page

#Region "Constants and Configuration"
    
    ' Security constants for .NET 3.5.1
    Private Const SALT_LENGTH As Integer = 16
    Private Const TEMP_PASSWORD_LENGTH As Integer = 12
    Private Const REGULAR_PASSWORD_LENGTH As Integer = 8
    Private Const MIN_PASSWORD_LENGTH As Integer = 6
    Private Const MAX_LOGIN_ATTEMPTS As Integer = 5
    
    ' Email service configuration
    Private Const EMAIL_SERVICE_URL As String = "http://localhost:5000/api/email"
    Private Const EMAIL_AUTH_TOKEN As String = "Bearer SPMJ-ADMIN-TOKEN"
    
    ' Password character sets for .NET 3.5.1 compatibility
    Private Const TEMP_CHARS As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%"
    Private Const REGULAR_CHARS As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    
#End Region

#Region "Page-Level Variables"
    
    ' Current user being managed
    Private m_currentUser As UserData = Nothing
    
    ' .NET 3.5.1 compatible random number generator
    Private Shared m_random As New Random(DateTime.Now.Millisecond)
    
#End Region

#Region "Page Events"

    ''' <summary>
    ''' Page Load Event - .NET 3.5.1 Standard Implementation
    ''' </summary>
    ''' <param name="sender">Event sender</param>
    ''' <param name="e">Event arguments</param>
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load        Try
            ' Security validation - .NET 3.5.1 standard approach
            If Not ValidateUserSession() Then
                Session.Abandon()
                Response.Redirect("p0_Login.aspx", True)
                Return
            End If

            If Not ValidateAdminPrivileges() Then
                ShowMessage("Access Denied: Insufficient privileges for password management.", "error")
                Response.Redirect("default.aspx", True)
                Return
            End If

            ' Initialize page controls on first load
            If Not Page.IsPostBack Then
                InitializePageControls()
            End If

        Catch ex As Exception
            ' .NET 3.5.1 compatible error handling
            LogError("Page_Load", ex.Message)
            ShowMessage("System error during page initialization: " + ex.Message, "error")
        End Try
    End Sub

#End Region

#Region "Button Event Handlers"

        Catch ex As Exception
            LogError("Page_Load", ex)
            ShowMessage("System error during page initialization: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Initialize page with security notice
    ''' </summary>
    Private Sub InitializePage()
        Try
            ClearAllPanels()
            ShowMessage("Admin Password Manager initialized. Search for a user to begin password management.", "info")
            txtSearchUser.Focus()
        Catch ex As Exception
            LogError("InitializePage", ex)
            ShowMessage("Error initializing page: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Search User Button Click Event
    ''' </summary>
    Protected Sub btnSearchUser_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSearchUser.Click
        Try
            ClearAllPanels()

            ' Validate input
            If String.IsNullOrEmpty(txtSearchUser.Text) OrElse txtSearchUser.Text.Trim() = "" Then
                ShowMessage("Please enter a User ID to search.", "error")
                txtSearchUser.Focus()
                Return
            End If

            ' Search for user with enhanced data retrieval
            Dim userData As UserData = SearchUserInDatabase(txtSearchUser.Text.Trim())

            If userData IsNot Nothing Then
                currentUser = userData
                DisplayUserInformation(userData)
                ShowPasswordActions()

                ' Pre-populate email if available
                If Not String.IsNullOrEmpty(userData.Email) Then
                    txtUserEmail.Text = userData.Email
                End If

                ShowMessage("User found successfully. Choose password management action below.", "success")
            Else
                ShowMessage("User with ID '" & txtSearchUser.Text.Trim() & "' not found in the system.", "error")
            End If

        Catch ex As Exception
            LogError("btnSearchUser_Click", ex)
            ShowMessage("Error searching for user: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Set Password Button Click Event
    ''' </summary>
    Protected Sub btnSetPassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSetPassword.Click
        Try
            If currentUser Is Nothing Then
                ShowMessage("Please search for a user first.", "error")
                Return
            End If

            ' Validate email if email sending is enabled
            If chkSendEmail.Checked AndAlso Not IsValidEmail(txtUserEmail.Text.Trim()) Then
                ShowMessage("Please enter a valid email address to send password notification.", "error")
                txtUserEmail.Focus()
                Return
            End If

            ' Determine password type and generate
            Dim newPassword As String = ""
            Dim isTemporary As Boolean = rbTempPassword.Checked
            
            If Not String.IsNullOrEmpty(txtCustomPassword.Text.Trim()) Then
                newPassword = txtCustomPassword.Text.Trim()
                If newPassword.Length < 6 Then
                    ShowMessage("Custom password must be at least 6 characters long.", "error")
                    txtCustomPassword.Focus()
                    Return
                End If
            Else
                If isTemporary Then
                    newPassword = GenerateTemporaryPassword()
                Else
                    newPassword = GenerateSecurePassword()
                End If
            End If

            ' Update password in database with SHA256+Salt
            If UpdateUserPasswordSecure(currentUser.UserId, newPassword, isTemporary) Then
                ShowPasswordResult(newPassword, isTemporary)
                
                ' Send email if requested
                If chkSendEmail.Checked AndAlso Not String.IsNullOrEmpty(txtUserEmail.Text.Trim()) Then
                    SendPasswordEmail(txtUserEmail.Text.Trim(), newPassword, isTemporary)
                End If

                ClearPasswordInputs()
                LogPasswordChange(currentUser.UserId, "Password Set", isTemporary)
            Else
                ShowMessage("Failed to set password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnSetPassword_Click", ex)
            ShowMessage("Error setting password: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Force Reset Button Click Event
    ''' </summary>
    Protected Sub btnForceReset_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnForceReset.Click
        Try
            If currentUser Is Nothing Then
                ShowMessage("Please search for a user first.", "error")
                Return
            End If

            ' Generate temporary password for forced reset
            Dim tempPassword As String = GenerateTemporaryPassword()

            ' Update password and force change on next login
            If UpdateUserPasswordSecure(currentUser.UserId, tempPassword, True, True) Then
                ShowPasswordResult(tempPassword, True)
                
                ' Send email notification
                If chkSendEmail.Checked AndAlso IsValidEmail(txtUserEmail.Text.Trim()) Then
                    SendPasswordEmail(txtUserEmail.Text.Trim(), tempPassword, True, True)
                End If

                ClearPasswordInputs()
                LogPasswordChange(currentUser.UserId, "Force Reset", True)
                ShowMessage("Password forcibly reset. User must change password on next login.", "warning")
            Else
                ShowMessage("Failed to force reset password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnForceReset_Click", ex)
            ShowMessage("Error forcing password reset: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Generate Temporary Password Button Click Event
    ''' </summary>
    Protected Sub btnGenerateTemp_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGenerateTemp.Click
        Try
            If currentUser Is Nothing Then
                ShowMessage("Please search for a user first.", "error")
                Return
            End If

            ' Generate temporary password for first-time users
            Dim tempPassword As String = GenerateTemporaryPassword()

            ' Update password as temporary
            If UpdateUserPasswordSecure(currentUser.UserId, tempPassword, True) Then
                ShowPasswordResult(tempPassword, True)
                
                ' Send welcome email for first-time users
                If chkSendEmail.Checked AndAlso IsValidEmail(txtUserEmail.Text.Trim()) Then
                    SendWelcomeEmail(txtUserEmail.Text.Trim(), tempPassword)
                End If

                ClearPasswordInputs()
                LogPasswordChange(currentUser.UserId, "Temporary Generated", True)
                ShowMessage("Temporary password generated for first-time user.", "info")
            Else
                ShowMessage("Failed to generate temporary password. Please try again.", "error")
            End If

        Catch ex As Exception
            LogError("btnGenerateTemp_Click", ex)
            ShowMessage("Error generating temporary password: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Search user in database with enhanced information retrieval
    ''' </summary>
    Private Function SearchUserInDatabase(ByVal userId As String) As UserData
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT id_pg, nama, email, status, modul, akses, tarikh_daftar, tarikh_login_akhir FROM pn_pengguna WHERE id_pg = ?"
            command.Parameters.AddWithValue("@id_pg", userId)

            reader = command.ExecuteReader()

            If reader.Read() Then
                Dim userData As New UserData()
                userData.UserId = GetSafeStringValue(reader, "id_pg")
                userData.Name = GetSafeStringValue(reader, "nama")
                userData.Email = GetSafeStringValue(reader, "email")
                userData.Status = GetSafeStringValue(reader, "status")
                userData.UserModule = GetSafeStringValue(reader, "modul")
                userData.Access = GetSafeStringValue(reader, "akses")
                userData.RegisterDate = GetSafeDateValue(reader, "tarikh_daftar")
                userData.LastLogin = GetSafeDateValue(reader, "tarikh_login_akhir")
                Return userData
            End If

            Return Nothing

        Catch ex As Exception
            LogError("SearchUserInDatabase", ex)
            Return Nothing
        Finally
            If reader IsNot Nothing Then reader.Close()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
        End Try
    End Function

    ''' <summary>
    ''' Update user password with SHA256+Salt encryption
    ''' </summary>
    Private Function UpdateUserPasswordSecure(ByVal userId As String, ByVal newPassword As String, ByVal isTemporary As Boolean, Optional ByVal forceChange As Boolean = False) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            ' Generate unique salt for this password
            Dim salt As String = GenerateSalt()
            
            ' Create SHA256 hash with salt
            Dim hashedPassword As String = HashPasswordSHA256WithSalt(newPassword, salt)

            command = New OleDbCommand()
            command.Connection = connection

            ' Update password with security metadata
            Dim sql As String = "UPDATE pn_pengguna SET katalaluan = ?, salt = ?, is_temporary = ?, force_change = ?, tarikh_tukar_katalaluan = ? WHERE id_pg = ?"
            command.CommandText = sql
            command.Parameters.AddWithValue("@password", hashedPassword)
            command.Parameters.AddWithValue("@salt", salt)
            command.Parameters.AddWithValue("@is_temporary", IIf(isTemporary, 1, 0))
            command.Parameters.AddWithValue("@force_change", IIf(forceChange, 1, 0))
            command.Parameters.AddWithValue("@date_changed", DateTime.Now)
            command.Parameters.AddWithValue("@id_pg", userId)

            Dim rowsAffected As Integer = command.ExecuteNonQuery()
            Return rowsAffected > 0

        Catch ex As Exception
            LogError("UpdateUserPasswordSecure", ex)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
        End Try
    End Function

    ''' <summary>
    ''' Generate cryptographically secure salt
    ''' </summary>
    Private Function GenerateSalt() As String
        Try
            Dim saltBytes(SALT_LENGTH - 1) As Byte
            Using rng As New RNGCryptoServiceProvider()
                rng.GetBytes(saltBytes)
            End Using
            Return Convert.ToBase64String(saltBytes)
        Catch
            ' Fallback for .NET 3.5.1 compatibility
            Dim random As New Random(DateTime.Now.Millisecond)
            Dim salt As String = ""
            For i As Integer = 1 To SALT_LENGTH
                salt = salt & Chr(random.Next(65, 90)) ' A-Z
            Next
            Return salt
        End Try
    End Function

    ''' <summary>
    ''' Hash password using SHA256 with salt
    ''' </summary>
    Private Function HashPasswordSHA256WithSalt(ByVal password As String, ByVal salt As String) As String
        Try
            ' Combine password and salt
            Dim saltedPassword As String = password & salt
            
            ' Create SHA256 hash
            Using sha256 As New SHA256CryptoServiceProvider()
                Dim inputBytes As Byte() = Encoding.UTF8.GetBytes(saltedPassword)
                Dim hashBytes As Byte() = sha256.ComputeHash(inputBytes)
                
                ' Convert to hex string
                Dim hashString As New StringBuilder()
                For Each b As Byte In hashBytes
                    hashString.Append(b.ToString("x2"))
                Next
                
                Return hashString.ToString()
            End Using
        Catch ex As Exception
            LogError("HashPasswordSHA256WithSalt", ex)
            ' Fallback to simple hashing if SHA256 fails
            Return HashPasswordSimple(password & salt)
        End Try
    End Function

    ''' <summary>
    ''' Generate secure temporary password
    ''' </summary>
    Private Function GenerateTemporaryPassword() As String
        Const tempChars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%"
        Dim random As New Random(DateTime.Now.Millisecond)
        Dim password As String = ""

        ' Ensure complexity: uppercase, number, special character
        password = password & tempChars.Substring(random.Next(26), 1) ' Uppercase
        password = password & tempChars.Substring(random.Next(26, 36), 1) ' Number
        password = password & tempChars.Substring(random.Next(36, 40), 1) ' Special

        ' Fill remaining with random characters
        For i As Integer = 4 To TEMP_PASSWORD_LENGTH
            password = password & tempChars.Substring(random.Next(tempChars.Length), 1)
        Next

        ' Shuffle the password
        Return ShuffleString(password)
    End Function

    ''' <summary>
    ''' Generate regular secure password
    ''' </summary>
    Private Function GenerateSecurePassword() As String
        Const passwordChars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        Dim random As New Random(DateTime.Now.Millisecond)
        Dim password As String = ""

        For i As Integer = 1 To REGULAR_PASSWORD_LENGTH
            password = password & passwordChars.Substring(random.Next(passwordChars.Length), 1)
        Next

        Return password
    End Function

    ''' <summary>
    ''' Send password via email microservice
    ''' </summary>
    Private Sub SendPasswordEmail(ByVal emailAddress As String, ByVal password As String, ByVal isTemporary As Boolean, Optional ByVal isForceReset As Boolean = False)
        Try
            Dim emailType As String = IIf(isTemporary, "temporary", "permanent").ToString()
            If isForceReset Then emailType = "force_reset"

            ' Prepare email data for microservice
            Dim emailData As String = "{"
            emailData = emailData & """to"":""" & emailAddress & ""","
            emailData = emailData & """subject"":""Password " & IIf(isTemporary, "Reset", "Update").ToString() & " - SPMJ System"","
            emailData = emailData & """templateType"":""password_" & emailType & ""","
            emailData = emailData & """data"":{"
            emailData = emailData & """userName"":""" & currentUser.Name & ""","
            emailData = emailData & """userId"":""" & currentUser.UserId & ""","
            emailData = emailData & """password"":""" & password & ""","
            emailData = emailData & """isTemporary"":" & IIf(isTemporary, "true", "false").ToString() & ","
            emailData = emailData & """systemUrl"":""" & Request.Url.GetLeftPart(UriPartial.Authority) & ""","
            emailData = emailData & """timestamp"":""" & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") & """"
            emailData = emailData & "}"
            emailData = emailData & "}"

            ' Call email microservice
            Dim client As New WebClient()
            client.Headers("Content-Type") = "application/json"
            client.Headers("Authorization") = "Bearer SPMJ-ADMIN-TOKEN" ' Add your auth token
            
            Dim response As String = client.UploadString(EMAIL_SERVICE_URL & "/send-password", emailData)
            
            ' Show email status
            pnlEmailStatus.Visible = True
            divEmailStatus.Attributes("class") = "email-sent"
            lblEmailStatus.Text = "✅ Password email sent successfully to " & emailAddress

        Catch ex As Exception
            LogError("SendPasswordEmail", ex)
            ' Show email failure but don't stop the process
            pnlEmailStatus.Visible = True
            divEmailStatus.Attributes("class") = "email-failed"
            lblEmailStatus.Text = "❌ Failed to send email: " & ex.Message
        End Try
    End Sub

    ''' <summary>
    ''' Send welcome email for first-time users
    ''' </summary>
    Private Sub SendWelcomeEmail(ByVal emailAddress As String, ByVal tempPassword As String)
        Try
            ' Prepare welcome email data
            Dim emailData As String = "{"
            emailData = emailData & """to"":""" & emailAddress & ""","
            emailData = emailData & """subject"":""Welcome to SPMJ System - Account Created"","
            emailData = emailData & """templateType"":""welcome_new_user"","
            emailData = emailData & """data"":{"
            emailData = emailData & """userName"":""" & currentUser.Name & ""","
            emailData = emailData & """userId"":""" & currentUser.UserId & ""","
            emailData = emailData & """tempPassword"":""" & tempPassword & ""","
            emailData = emailData & """systemUrl"":""" & Request.Url.GetLeftPart(UriPartial.Authority) & ""","
            emailData = emailData & """supportEmail"":""<EMAIL>"""
            emailData = emailData & "}"
            emailData = emailData & "}"

            ' Call email microservice
            Dim client As New WebClient()
            client.Headers("Content-Type") = "application/json"
            client.Headers("Authorization") = "Bearer SPMJ-ADMIN-TOKEN"
            
            Dim response As String = client.UploadString(EMAIL_SERVICE_URL & "/send-welcome", emailData)
            
            ' Show email status
            pnlEmailStatus.Visible = True
            divEmailStatus.Attributes("class") = "email-sent"
            lblEmailStatus.Text = "✅ Welcome email sent successfully to " & emailAddress

        Catch ex As Exception
            LogError("SendWelcomeEmail", ex)
            pnlEmailStatus.Visible = True
            divEmailStatus.Attributes("class") = "email-failed"
            lblEmailStatus.Text = "❌ Failed to send welcome email: " & ex.Message
        End Try
    End Sub

    ''' <summary>
    ''' Enhanced admin authorization check
    ''' </summary>
    Private Function IsAdminAuthorized() As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT akses, modul FROM pn_pengguna WHERE id_pg = ? AND status = '1'"
            command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())

            Dim reader As OleDbDataReader = command.ExecuteReader()
            If reader.Read() Then
                Dim access As String = GetSafeStringValue(reader, "akses").ToLower()
                Dim modul As String = GetSafeStringValue(reader, "modul").ToLower()
                
                ' Check for admin privileges
                Return access.Contains("admin") OrElse access.Contains("1") OrElse _
                       access.Contains("pengurusan") OrElse modul.Contains("admin")
            End If

            Return False

        Catch
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
        End Try
    End Function

    ''' <summary>
    ''' Enhanced user authentication check
    ''' </summary>
    Private Function IsUserAuthenticated() As Boolean
        Return Session("Id_PG") IsNot Nothing AndAlso Session("Id_PG").ToString().Trim() <> ""
    End Function

    ''' <summary>
    ''' Log password change activities
    ''' </summary>
    Private Sub LogPasswordChange(ByVal userId As String, ByVal action As String, ByVal isTemporary As Boolean)
        Try
            Dim connection As New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            Dim command As New OleDbCommand()
            command.Connection = connection
            command.CommandText = "INSERT INTO password_log (user_id, admin_id, action, is_temporary, timestamp, ip_address) VALUES (?, ?, ?, ?, ?, ?)"
            command.Parameters.AddWithValue("@user_id", userId)
            command.Parameters.AddWithValue("@admin_id", Session("Id_PG").ToString())
            command.Parameters.AddWithValue("@action", action)
            command.Parameters.AddWithValue("@is_temporary", IIf(isTemporary, 1, 0))
            command.Parameters.AddWithValue("@timestamp", DateTime.Now)
            command.Parameters.AddWithValue("@ip_address", Request.UserHostAddress)

            command.ExecuteNonQuery()
            connection.Close()
        Catch
            ' Logging failure should not stop the main process
        End Try
    End Sub

    ''' <summary>
    ''' Helper method to shuffle string characters
    ''' </summary>
    Private Function ShuffleString(ByVal input As String) As String
        Dim chars As Char() = input.ToCharArray()
        Dim random As New Random(DateTime.Now.Millisecond)
        
        For i As Integer = chars.Length - 1 To 1 Step -1
            Dim j As Integer = random.Next(i + 1)
            Dim temp As Char = chars(i)
            chars(i) = chars(j)
            chars(j) = temp
        Next
        
        Return New String(chars)
    End Function

    ''' <summary>
    ''' Validate email format - Enhanced
    ''' </summary>
    Private Function IsValidEmail(ByVal email As String) As Boolean
        Try
            If String.IsNullOrEmpty(email) Then Return False
            Return email.Contains("@") AndAlso email.Contains(".") AndAlso _
                   email.Length >= 5 AndAlso email.IndexOf("@") > 0 AndAlso _
                   email.LastIndexOf(".") > email.IndexOf("@")
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Fallback simple password hashing for compatibility
    ''' </summary>
    Private Function HashPasswordSimple(ByVal password As String) As String
        Try
            Dim md5Provider As New System.Security.Cryptography.MD5CryptoServiceProvider()
            Dim inputBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(password)
            Dim hashBytes As Byte() = md5Provider.ComputeHash(inputBytes)

            Dim hashString As String = ""
            For Each b As Byte In hashBytes
                hashString = hashString & b.ToString("x2")
            Next

            Return hashString
        Catch
            Return password ' Last resort fallback
        End Try
    End Function

    ''' <summary>
    ''' Get safe string value from database reader
    ''' </summary>
    Private Function GetSafeStringValue(ByVal reader As OleDbDataReader, ByVal columnName As String) As String
        Try
            Dim value As Object = reader(columnName)
            If value Is DBNull.Value OrElse value Is Nothing Then
                Return ""
            Else
                Return value.ToString()
            End If
        Catch
            Return ""
        End Try
    End Function

    ''' <summary>
    ''' Get safe date value from database reader
    ''' </summary>
    Private Function GetSafeDateValue(ByVal reader As OleDbDataReader, ByVal columnName As String) As String
        Try
            Dim value As Object = reader(columnName)
            If value Is DBNull.Value OrElse value Is Nothing Then
                Return "Not available"
            Else
                Return CDate(value).ToString("dd/MM/yyyy")
            End If
        Catch
            Return "Not available"
        End Try
    End Function

    ''' <summary>
    ''' Display enhanced user information
    ''' </summary>
    Private Sub DisplayUserInformation(ByVal userData As UserData)
        lblUserId.Text = userData.UserId
        lblUserName.Text = IIf(String.IsNullOrEmpty(userData.Name), "No name available", userData.Name).ToString()
        lblUserEmail.Text = IIf(String.IsNullOrEmpty(userData.Email), "No email available", userData.Email).ToString()
        lblUserModule.Text = IIf(String.IsNullOrEmpty(userData.UserModule), "Not assigned", userData.UserModule).ToString()
        lblUserAccess.Text = IIf(String.IsNullOrEmpty(userData.Access), "Standard", userData.Access).ToString()
        lblLastLogin.Text = userData.LastLogin
        lblPasswordDate.Text = userData.RegisterDate

        ' Format status with color coding
        Select Case userData.Status
            Case "1"
                lblUserStatus.Text = "✅ Active"
            Case "0"
                lblUserStatus.Text = "❌ Inactive"
            Case Else
                lblUserStatus.Text = "❓ Unknown"
        End Select

        pnlUserInfo.Visible = True
    End Sub

    ''' <summary>
    ''' Show password management actions
    ''' </summary>
    Private Sub ShowPasswordActions()
        pnlPasswordActions.Visible = True
    End Sub

    ''' <summary>
    ''' Show password generation result
    ''' </summary>
    Private Sub ShowPasswordResult(ByVal password As String, ByVal isTemporary As Boolean)
        lblGeneratedPassword.Text = password
        lblPasswordType.Text = IIf(isTemporary, "Temporary (Must change on first login)", "Permanent").ToString()
        pnlPasswordResult.Visible = True
    End Sub

    ''' <summary>
    ''' Clear all panels for new search
    ''' </summary>
    Private Sub ClearAllPanels()
        pnlUserInfo.Visible = False
        pnlPasswordActions.Visible = False
        pnlPasswordResult.Visible = False
        pnlEmailStatus.Visible = False
    End Sub

    ''' <summary>
    ''' Clear password input fields
    ''' </summary>
    Private Sub ClearPasswordInputs()
        txtCustomPassword.Text = ""
    End Sub

    ''' <summary>
    ''' Show enhanced message with styling
    ''' </summary>
    Private Sub ShowMessage(ByVal message As String, ByVal messageType As String)
        lblMessage.Text = message
        pnlMessage.Visible = True

        ' Set CSS class based on message type
        Select Case messageType.ToLower()
            Case "success"
                divMessage.Attributes("class") = "message-panel msg-success"
            Case "error"
                divMessage.Attributes("class") = "message-panel msg-error"
            Case "info"
                divMessage.Attributes("class") = "message-panel msg-info"
            Case "warning"
                divMessage.Attributes("class") = "message-panel msg-warning"
            Case Else
                divMessage.Attributes("class") = "message-panel msg-info"
        End Select
    End Sub

    ''' <summary>
    ''' Log errors for debugging
    ''' </summary>
    Private Sub LogError(ByVal methodName As String, ByVal ex As Exception)
        Try
            ' Simple error logging - could be enhanced with proper logging framework
            Dim errorLog As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") & " - " & methodName & " - " & ex.Message
            ' In production, write to log file or database
        Catch
            ' Ignore logging errors
        End Try
    End Sub

    ''' <summary>
    ''' Redirect to login page
    ''' </summary>
    Private Sub RedirectToLogin()
        Session.Abandon()
        Response.Redirect("p0_Login.aspx")
    End Sub

    ''' <summary>
    ''' Redirect to home page
    ''' </summary>
    Private Sub RedirectToHome()
        Response.Redirect("default.aspx")
    End Sub

End Class

''' <summary>
''' Enhanced User data structure for comprehensive user management
''' </summary>
Public Class UserData
    Private _userId As String = ""
    Private _name As String = ""
    Private _email As String = ""
    Private _status As String = ""
    Private _module As String = ""
    Private _access As String = ""
    Private _registerDate As String = ""
    Private _lastLogin As String = ""

    Public Property UserId() As String
        Get
            Return _userId
        End Get
        Set(ByVal value As String)
            _userId = value
        End Set
    End Property

    Public Property Name() As String
        Get
            Return _name
        End Get
        Set(ByVal value As String)
            _name = value
        End Set
    End Property

    Public Property Email() As String
        Get
            Return _email
        End Get
        Set(ByVal value As String)
            _email = value
        End Set
    End Property

    Public Property Status() As String
        Get
            Return _status
        End Get
        Set(ByVal value As String)
            _status = value
        End Set
    End Property

    Public Property UserModule() As String
        Get
            Return _module
        End Get
        Set(ByVal value As String)
            _module = value
        End Set
    End Property

    Public Property Access() As String
        Get
            Return _access
        End Get
        Set(ByVal value As String)
            _access = value
        End Set
    End Property

    Public Property RegisterDate() As String
        Get
            Return _registerDate
        End Get
        Set(ByVal value As String)
            _registerDate = value
        End Set
    End Property

    Public Property LastLogin() As String
        Get
            Return _lastLogin
        End Get
        Set(ByVal value As String)
            _lastLogin = value
        End Set
    End Property
End Class
