using Microsoft.Extensions.Options;
using MimeKit;
using MailKit.Net.Smtp;
using MailKit.Security;
using SPMJ.EmailService.Models;
using System.Text.RegularExpressions;

namespace SPMJ.EmailService.Services;

public class AdminPasswordEmailService : IAdminPasswordEmailService
{
    private readonly EmailSettings _emailSettings;
    private readonly ILogger<AdminPasswordEmailService> _logger;

    public AdminPasswordEmailService(
        IOptions<EmailSettings> emailSettings, 
        ILogger<AdminPasswordEmailService> logger)
    {
        _emailSettings = emailSettings.Value;
        _logger = logger;
    }

    public async Task<EmailResponse> SendPasswordNotificationAsync(AdminPasswordNotificationRequest request)
    {
        try
        {
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress(_emailSettings.FromName, _emailSettings.FromEmail));
            message.To.Add(new MailboxAddress("", request.To));
            message.Subject = request.Subject;            var htmlBody = GeneratePasswordNotificationTemplate(request);
            message.Body = new TextPart("html") { Text = htmlBody };

            using var client = new SmtpClient();
            
            // Fix SMTP connection for Gmail and similar providers
            SecureSocketOptions secureOptions;
            if (_emailSettings.SmtpPort == 465)
            {
                secureOptions = SecureSocketOptions.SslOnConnect;
            }
            else if (_emailSettings.SmtpPort == 587)
            {
                secureOptions = SecureSocketOptions.StartTls;
            }
            else
            {
                secureOptions = _emailSettings.UseSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls;
            }
            
            await client.ConnectAsync(_emailSettings.SmtpServer, _emailSettings.SmtpPort, secureOptions);
            
            if (!string.IsNullOrEmpty(_emailSettings.Username))
            {
                await client.AuthenticateAsync(_emailSettings.Username, _emailSettings.Password);
            }

            await client.SendAsync(message);
            await client.DisconnectAsync(true);

            _logger.LogInformation("Password notification email sent successfully to {Email} for user {UserId}", 
                request.To, request.Data.UserId);

            return new EmailResponse
            {
                Success = true,
                Message = "Password notification email sent successfully",
                EmailId = Guid.NewGuid().ToString()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send password notification email to {Email}", request.To);
            return new EmailResponse
            {
                Success = false,
                Message = $"Failed to send email: {ex.Message}"
            };
        }
    }

    public async Task<EmailResponse> SendWelcomeEmailAsync(WelcomeEmailRequest request)
    {
        try
        {
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress(_emailSettings.FromName, _emailSettings.FromEmail));
            message.To.Add(new MailboxAddress("", request.To));
            message.Subject = request.Subject;            var htmlBody = GenerateWelcomeEmailTemplate(request);
            message.Body = new TextPart("html") { Text = htmlBody };

            using var client = new SmtpClient();
            
            // Fix SMTP connection for Gmail and similar providers
            SecureSocketOptions secureOptions;
            if (_emailSettings.SmtpPort == 465)
            {
                secureOptions = SecureSocketOptions.SslOnConnect;
            }
            else if (_emailSettings.SmtpPort == 587)
            {
                secureOptions = SecureSocketOptions.StartTls;
            }
            else
            {
                secureOptions = _emailSettings.UseSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls;
            }
            
            await client.ConnectAsync(_emailSettings.SmtpServer, _emailSettings.SmtpPort, secureOptions);
            
            if (!string.IsNullOrEmpty(_emailSettings.Username))
            {
                await client.AuthenticateAsync(_emailSettings.Username, _emailSettings.Password);
            }

            await client.SendAsync(message);
            await client.DisconnectAsync(true);

            _logger.LogInformation("Welcome email sent successfully to {Email} for user {UserId}", 
                request.To, request.Data.UserId);

            return new EmailResponse
            {
                Success = true,
                Message = "Welcome email sent successfully",
                EmailId = Guid.NewGuid().ToString()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send welcome email to {Email}", request.To);
            return new EmailResponse
            {
                Success = false,
                Message = $"Failed to send welcome email: {ex.Message}"
            };
        }
    }

    public async Task<EmailResponse> SendForceResetEmailAsync(AdminPasswordNotificationRequest request)
    {
        // Override template type and subject for force reset
        request.TemplateType = "force_reset";
        request.Subject = "Password Reset - SPMJ System (Action Required)";
        
        return await SendPasswordNotificationAsync(request);
    }

    public bool IsValidEmailFormat(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            // Use regex for more robust email validation
            var emailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 
                RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(email.Trim());
        }
        catch
        {
            return false;
        }
    }    private string GeneratePasswordNotificationTemplate(AdminPasswordNotificationRequest request)
    {
        var data = request.Data;
        var templateType = request.TemplateType?.ToLower() ?? "";
        
        // Handle password recovery template
        if (templateType == "password_recovery")
        {
            return GeneratePasswordRecoveryTemplate(request);
        }
        
        // Handle force reset template
        if (templateType == "force_reset")
        {
            return GenerateForceResetTemplate(request);
        }
        
        // Handle password change notification template
        if (templateType == "password_change_notification")
        {
            return GeneratePasswordChangeNotificationTemplate(request);
        }
        
        // Default password notification template
        var isTemporary = data.IsTemporary;
        var actionType = isTemporary ? "Reset" : "Update";
        var passwordType = isTemporary ? "sementara" : "baharu";
        var changeRequired = isTemporary ? 
            "<p><strong>⚠️ PENTING: Anda dikehendaki menukar kata laluan ini pada log masuk seterusnya.</strong></p>" : 
            "";

        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>SPMJ - Password {actionType}</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0;'>
    <div style='max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;'>
        <!-- Header -->
        <div style='background: linear-gradient(135deg, #2c5282 0%, #3182ce 100%); color: white; padding: 30px 20px; text-align: center;'>
            <h1 style='margin: 0; font-size: 24px; font-weight: bold;'>SPMJ System</h1>
            <p style='margin: 5px 0 0 0; opacity: 0.9;'>Sistem Pengurusan Maklumat Jabatan</p>
        </div>
        
        <!-- Content -->
        <div style='padding: 30px 20px;'>
            <h2 style='color: #2c5282; margin-top: 0; font-size: 20px;'>Password {actionType} Notification</h2>
            
            <p>Assalamualaikum <strong>{data.UserName}</strong>,</p>
            
            <p>Kata laluan {passwordType} telah ditetapkan untuk akaun SPMJ anda oleh pentadbir sistem.</p>
            
            <div style='background-color: #f8fafc; border-left: 4px solid #2c5282; padding: 15px; margin: 20px 0; border-radius: 4px;'>
                <p style='margin: 0; font-weight: bold; color: #2c5282;'>Maklumat Akaun:</p>
                <p style='margin: 5px 0;'><strong>ID Pengguna:</strong> {data.UserId}</p>
                <p style='margin: 5px 0;'><strong>Kata Laluan:</strong> <code style='background-color: #e2e8f0; padding: 2px 6px; border-radius: 3px; font-family: monospace;'>{data.Password}</code></p>
                <p style='margin: 5px 0;'><strong>Tarikh/Masa:</strong> {data.Timestamp}</p>
                <p style='margin: 5px 0;'><strong>Diurus oleh:</strong> {data.AdminName} ({data.AdminId})</p>
            </div>
            
            {changeRequired}
            
            <div style='margin: 25px 0; text-align: center;'>
                <a href='{data.SystemUrl}' 
                   style='background-color: #2c5282; color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 6px; display: inline-block; font-weight: bold; text-transform: uppercase;
                          letter-spacing: 0.5px;'>
                    Log Masuk ke SPMJ
                </a>
            </div>
            
            <div style='background-color: #fef5e7; border: 1px solid #f6ad55; border-radius: 4px; padding: 15px; margin: 20px 0;'>
                <p style='margin: 0; font-weight: bold; color: #c05621;'>🔐 Nota Keselamatan:</p>
                <ul style='margin: 10px 0; padding-left: 20px; color: #744210;'>
                    <li>Jangan kongsi kata laluan ini dengan sesiapa</li>
                    <li>Log keluar selepas menggunakan sistem</li>
                    <li>Hubungi pentadbir jika anda tidak meminta perubahan ini</li>
                </ul>
            </div>
            
            <p style='margin-top: 30px;'>Jika anda menghadapi sebarang masalah, sila hubungi pentadbir sistem.</p>
            
            <p>Wassalam,<br>
            <strong>Pasukan SPMJ</strong></p>
        </div>
        
        <!-- Footer -->
        <div style='background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e2e8f0;'>
            <p style='margin: 0; font-size: 12px; color: #718096;'>
                Email ini dijana secara automatik pada {DateTime.Now:dd/MM/yyyy HH:mm:ss}<br>
                Sila jangan balas email ini.
            </p>
        </div>
    </div>
</body>
</html>";
    }

    /// <summary>
    /// Generate password recovery email template
    /// </summary>
    private string GeneratePasswordRecoveryTemplate(AdminPasswordNotificationRequest request)
    {
        var data = request.Data;
        var recoveryReason = data.AdminName ?? "Pemulihan kata laluan yang diminta pengguna";

        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>SPMJ - Pemulihan Kata Laluan</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0;'>
    <div style='max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;'>
        <!-- Header -->
        <div style='background: linear-gradient(135deg, #d53f8c 0%, #ed64a6 100%); color: white; padding: 30px 20px; text-align: center;'>
            <h1 style='margin: 0; font-size: 24px; font-weight: bold;'>🔐 SPMJ System</h1>
            <p style='margin: 5px 0 0 0; opacity: 0.9;'>Pemulihan Kata Laluan</p>
        </div>
        
        <!-- Content -->
        <div style='padding: 30px 20px;'>
            <h2 style='color: #d53f8c; margin-top: 0; font-size: 20px;'>Kata Laluan Sementara Baharu</h2>
            
            <p>Assalamualaikum <strong>{data.UserName}</strong>,</p>
            
            <p>Kami telah menerima permintaan untuk menetapkan semula kata laluan anda. Kata laluan sementara baharu telah dijana untuk akaun SPMJ anda.</p>
            
            <div style='background-color: #fed7e2; border: 2px solid #d53f8c; border-radius: 8px; padding: 20px; margin: 25px 0;'>
                <p style='margin: 0; font-weight: bold; color: #97266d; font-size: 16px;'>🔑 Maklumat Log Masuk Baharu:</p>
                <p style='margin: 15px 0 5px 0;'><strong>ID Pengguna:</strong></p>
                <p style='margin: 0; background-color: #fff5f8; padding: 8px 12px; border-radius: 4px; font-family: monospace; font-size: 18px; font-weight: bold; color: #97266d;'>{data.UserId}</p>
                <p style='margin: 15px 0 5px 0;'><strong>Kata Laluan Sementara:</strong></p>
                <p style='margin: 0; background-color: #fff5f8; padding: 8px 12px; border-radius: 4px; font-family: monospace; font-size: 18px; font-weight: bold; color: #97266d;'>{data.Password}</p>
                <p style='margin: 15px 0 5px 0;'><strong>Tarikh/Masa Dijana:</strong> {data.Timestamp}</p>
            </div>
            
            <div style='background-color: #fef5e7; border-left: 4px solid #f6ad55; padding: 15px; margin: 20px 0; border-radius: 4px;'>
                <p style='margin: 0; font-weight: bold; color: #c05621;'>⚠️ PENTING - Tindakan Diperlukan:</p>
                <ul style='margin: 10px 0; padding-left: 20px; color: #744210;'>
                    <li><strong>Kata laluan ini adalah SEMENTARA</strong> - anda MESTI menukarnya pada log masuk pertama</li>
                    <li>Anda akan diarahkan untuk menetapkan kata laluan baharu selepas log masuk</li>
                    <li>Kata laluan sementara ini akan tamat tempoh dalam 24 jam</li>
                </ul>
            </div>
            
            <div style='margin: 25px 0; text-align: center;'>
                <a href='{data.SystemUrl}' 
                   style='background-color: #d53f8c; color: white; padding: 15px 40px; text-decoration: none; 
                          border-radius: 6px; display: inline-block; font-weight: bold; text-transform: uppercase;
                          letter-spacing: 0.5px; font-size: 16px;'>
                    Log Masuk Sekarang
                </a>
            </div>
            
            <div style='background-color: #fef5e7; border: 1px solid #f6ad55; border-radius: 4px; padding: 15px; margin: 20px 0;'>
                <p style='margin: 0; font-weight: bold; color: #c05621;'>🔐 Nota Keselamatan:</p>
                <ul style='margin: 10px 0; padding-left: 20px; color: #744210;'>
                    <li>Jika anda TIDAK meminta pemulihan kata laluan ini, sila hubungi pentadbir sistem dengan segera</li>
                    <li>Jangan kongsi kata laluan sementara ini dengan sesiapa</li>
                    <li>Pastikan anda log keluar selepas menggunakan sistem</li>
                    <li>Gunakan kata laluan yang kuat untuk kata laluan baharu anda</li>
                </ul>
            </div>
            
            <div style='background-color: #edf2f7; border-radius: 4px; padding: 15px; margin: 20px 0;'>
                <p style='margin: 0; font-weight: bold; color: #2d3748;'>📝 Sebab Pemulihan:</p>
                <p style='margin: 5px 0; color: #4a5568;'>{recoveryReason}</p>
            </div>
            
            <p style='margin-top: 30px;'>Jika anda menghadapi sebarang masalah semasa log masuk atau menukar kata laluan, sila hubungi pentadbir sistem.</p>
            
            <p>Wassalam,<br>
            <strong>Pasukan Keselamatan SPMJ</strong></p>
        </div>
        
        <!-- Footer -->
        <div style='background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e2e8f0;'>
            <p style='margin: 0; font-size: 12px; color: #718096;'>
                Email pemulihan kata laluan dijana secara automatik pada {DateTime.Now:dd/MM/yyyy HH:mm:ss}<br>
                Sila jangan balas email ini.<br>
                Jika anda tidak meminta pemulihan ini, sila abaikan email ini atau hubungi pentadbir.
            </p>
        </div>
    </div>
</body>
</html>";
    }

    /// <summary>
    /// Generate force reset email template
    /// </summary>
    private string GenerateForceResetTemplate(AdminPasswordNotificationRequest request)
    {
        var data = request.Data;

        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>SPMJ - Reset Kata Laluan Paksa</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0;'>
    <div style='max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;'>
        <!-- Header -->
        <div style='background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%); color: white; padding: 30px 20px; text-align: center;'>
            <h1 style='margin: 0; font-size: 24px; font-weight: bold;'>⚠️ SPMJ System</h1>
            <p style='margin: 5px 0 0 0; opacity: 0.9;'>Reset Kata Laluan Paksa</p>
        </div>
        
        <!-- Content -->
        <div style='padding: 30px 20px;'>
            <h2 style='color: #e53e3e; margin-top: 0; font-size: 20px;'>Tindakan Segera Diperlukan</h2>
            
            <p>Assalamualaikum <strong>{data.UserName}</strong>,</p>
            
            <p><strong>Kata laluan anda telah direset oleh pentadbir sistem atas sebab keselamatan.</strong> Anda mesti menggunakan kata laluan sementara di bawah untuk log masuk dan menetapkan kata laluan baharu.</p>
            
            <div style='background-color: #fed7d7; border: 2px solid #e53e3e; border-radius: 8px; padding: 20px; margin: 25px 0;'>
                <p style='margin: 0; font-weight: bold; color: #c53030; font-size: 16px;'>🔑 Maklumat Log Masuk:</p>
                <p style='margin: 15px 0 5px 0;'><strong>ID Pengguna:</strong></p>
                <p style='margin: 0; background-color: #fff5f5; padding: 8px 12px; border-radius: 4px; font-family: monospace; font-size: 18px; font-weight: bold; color: #c53030;'>{data.UserId}</p>
                <p style='margin: 15px 0 5px 0;'><strong>Kata Laluan Sementara:</strong></p>
                <p style='margin: 0; background-color: #fff5f5; padding: 8px 12px; border-radius: 4px; font-family: monospace; font-size: 18px; font-weight: bold; color: #c53030;'>{data.Password}</p>
                <p style='margin: 15px 0 5px 0;'><strong>Direset oleh:</strong> {data.AdminName} ({data.AdminId})</p>
                <p style='margin: 5px 0;'><strong>Tarikh/Masa:</strong> {data.Timestamp}</p>
            </div>
            
            <div style='background-color: #fef5e7; border: 2px solid #f6ad55; border-radius: 4px; padding: 15px; margin: 20px 0;'>
                <p style='margin: 0; font-weight: bold; color: #c05621; font-size: 16px;'>🚨 TINDAKAN SEGERA DIPERLUKAN:</p>
                <ol style='margin: 10px 0; padding-left: 20px; color: #744210;'>
                    <li><strong>Log masuk SEKARANG</strong> menggunakan kata laluan sementara di atas</li>
                    <li><strong>Tukar kata laluan</strong> kepada kata laluan baharu yang kuat</li>
                    <li><strong>Kata laluan sementara akan tamat tempoh dalam 2 jam</strong></li>
                    <li>Akaun anda akan dikunci jika tidak digunakan dalam tempoh ini</li>
                </ol>
            </div>
            
            <div style='margin: 25px 0; text-align: center;'>
                <a href='{data.SystemUrl}' 
                   style='background-color: #e53e3e; color: white; padding: 15px 40px; text-decoration: none; 
                          border-radius: 6px; display: inline-block; font-weight: bold; text-transform: uppercase;
                          letter-spacing: 0.5px; font-size: 16px; animation: pulse 2s infinite;'>
                    LOG MASUK SEKARANG
                </a>
            </div>
            
            <div style='background-color: #fef5e7; border: 1px solid #f6ad55; border-radius: 4px; padding: 15px; margin: 20px 0;'>
                <p style='margin: 0; font-weight: bold; color: #c05621;'>🔐 Nota Keselamatan Kritikal:</p>
                <ul style='margin: 10px 0; padding-left: 20px; color: #744210;'>
                    <li>Reset ini dilakukan atas sebab keselamatan</li>
                    <li>JANGAN kongsi kata laluan sementara ini dengan sesiapa</li>
                    <li>Gunakan kata laluan baharu yang kuat (minimum 8 aksara, campuran huruf, nombor, simbol)</li>
                    <li>Jika anda tidak dijangka menerima email ini, hubungi pentadbir SEGERA</li>
                </ul>
            </div>
            
            <p style='margin-top: 30px; color: #e53e3e; font-weight: bold;'>Abaikan email ini adalah tidak disyorkan kerana akaun anda mungkin terjejas.</p>
            
            <p>Wassalam,<br>
            <strong>Pasukan Keselamatan SPMJ</strong></p>
        </div>
        
        <!-- Footer -->
        <div style='background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e2e8f0;'>
            <p style='margin: 0; font-size: 12px; color: #718096;'>
                Email keselamatan dijana secara automatik pada {DateTime.Now:dd/MM/yyyy HH:mm:ss}<br>
                Sila jangan balas email ini.<br>
                Untuk bantuan, hubungi pentadbir sistem di jabatan anda.
            </p>
        </div>
    </div>
</body>
</html>";
    }

    private string GenerateWelcomeEmailTemplate(WelcomeEmailRequest request)
    {
        var data = request.Data;

        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Selamat Datang ke SPMJ</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0;'>
    <div style='max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;'>
        <!-- Header -->
        <div style='background: linear-gradient(135deg, #38a169 0%, #48bb78 100%); color: white; padding: 30px 20px; text-align: center;'>
            <h1 style='margin: 0; font-size: 28px; font-weight: bold;'>🎉 Selamat Datang!</h1>
            <p style='margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;'>Akaun SPMJ anda telah berjaya dibuat</p>
        </div>
        
        <!-- Content -->
        <div style='padding: 30px 20px;'>
            <p>Assalamualaikum <strong>{data.UserName}</strong>,</p>
            
            <p>Selamat datang ke <strong>Sistem Pengurusan Maklumat Jabatan (SPMJ)</strong>! Akaun anda telah berjaya dibuat dan anda kini boleh mula menggunakan sistem.</p>
            
            <div style='background-color: #f0fff4; border: 2px solid #38a169; border-radius: 8px; padding: 20px; margin: 25px 0;'>
                <h3 style='margin-top: 0; color: #2f855a; font-size: 18px;'>📋 Maklumat Log Masuk Anda</h3>
                <p style='margin: 10px 0;'><strong>ID Pengguna:</strong> <code style='background-color: #e6fffa; padding: 3px 8px; border-radius: 4px; font-family: monospace; font-size: 16px;'>{data.UserId}</code></p>
                <p style='margin: 10px 0;'><strong>Kata Laluan Sementara:</strong> <code style='background-color: #e6fffa; padding: 3px 8px; border-radius: 4px; font-family: monospace; font-size: 16px;'>{data.TempPassword}</code></p>
            </div>
            
            <div style='background-color: #fff5f5; border-left: 4px solid #f56565; padding: 15px; margin: 20px 0; border-radius: 4px;'>
                <p style='margin: 0; font-weight: bold; color: #c53030;'>⚠️ PENTING - Sila Baca:</p>
                <ul style='margin: 10px 0; padding-left: 20px; color: #742a2a;'>
                    <li><strong>Kata laluan ini adalah sementara</strong> - anda MESTI menukarnya pada log masuk pertama</li>
                    <li>Simpan maklumat ini dengan selamat</li>
                    <li>Jangan kongsi kata laluan dengan sesiapa</li>
                </ul>
            </div>
            
            <div style='margin: 30px 0; text-align: center;'>
                <a href='{data.SystemUrl}' 
                   style='background: linear-gradient(135deg, #38a169 0%, #48bb78 100%); color: white; padding: 15px 40px; 
                          text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; 
                          text-transform: uppercase; letter-spacing: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
                    🚀 Mula Menggunakan SPMJ
                </a>
            </div>
            
            <div style='background-color: #f7fafc; border: 1px solid #cbd5e0; border-radius: 6px; padding: 20px; margin: 25px 0;'>
                <h4 style='margin-top: 0; color: #2d3748; font-size: 16px;'>📞 Perlukan Bantuan?</h4>
                <p style='margin: 10px 0;'>Jika anda menghadapi sebarang masalah atau mempunyai pertanyaan:</p>
                <ul style='margin: 10px 0; padding-left: 20px;'>
                    <li>📧 Email: <a href='mailto:{data.SupportEmail}' style='color: #3182ce;'>{data.SupportEmail}</a></li>
                    <li>🏢 Hubungi pentadbir sistem di jabatan anda</li>
                    <li>📖 Rujuk manual pengguna yang disediakan</li>
                </ul>
            </div>
            
            <p style='margin-top: 30px;'>Terima kasih dan selamat menggunakan sistem SPMJ!</p>
            
            <p>Wassalam,<br>
            <strong>Pasukan Pembangun SPMJ</strong></p>
        </div>
        
        <!-- Footer -->
        <div style='background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e2e8f0;'>
            <p style='margin: 0; font-size: 12px; color: #718096;'>
                Email ini dijana secara automatik pada {DateTime.Now:dd/MM/yyyy HH:mm:ss}<br>
                Sila jangan balas email ini.<br>
                © 2025 SPMJ System. Hak cipta terpelihara.
            </p>        </div>
    </div>
</body>
</html>";
    }

    /// <summary>
    /// Generate password change notification template
    /// </summary>
    private string GeneratePasswordChangeNotificationTemplate(AdminPasswordNotificationRequest request)
    {
        var data = request.Data;
        
        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>SPMJ - Password Changed</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 0;'>
    <div style='max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;'>
        <!-- Header -->
        <div style='background: linear-gradient(135deg, #38a169 0%, #48bb78 100%); color: white; padding: 30px 20px; text-align: center;'>
            <h1 style='margin: 0; font-size: 24px; font-weight: bold;'>✅ Password Changed</h1>
            <p style='margin: 5px 0 0 0; opacity: 0.9;'>SPMJ {data.SystemName} System</p>
        </div>
        
        <!-- Content -->
        <div style='padding: 30px 20px;'>
            <h2 style='color: #38a169; margin-top: 0; font-size: 20px;'>Password Successfully Changed</h2>
            
            <p>Assalamualaikum <strong>{data.UserName}</strong>,</p>
            
            <p>Kami ingin memaklumkan bahawa kata laluan akaun SPMJ anda telah berjaya dikemaskini.</p>
            
            <div style='background-color: #f0fff4; border-left: 4px solid #38a169; padding: 15px; margin: 20px 0; border-radius: 4px;'>
                <p style='margin: 0; font-weight: bold; color: #38a169;'>📋 Maklumat Perubahan:</p>
                <p style='margin: 5px 0;'><strong>ID Pengguna:</strong> {data.UserId}</p>
                <p style='margin: 5px 0;'><strong>Tarikh/Masa:</strong> {data.Timestamp}</p>
                <p style='margin: 5px 0;'><strong>Sistem:</strong> {data.SystemName}</p>
            </div>
            
            <div style='background-color: #fef5e7; border: 1px solid #f6e05e; border-radius: 6px; padding: 15px; margin: 20px 0;'>
                <p style='margin: 0; font-weight: bold; color: #744210;'>🔒 Keselamatan Akaun:</p>
                <ul style='margin: 10px 0; padding-left: 20px; color: #744210;'>
                    <li>Kata laluan anda telah dikemaskini dengan selamat</li>
                    <li>Jika anda tidak membuat perubahan ini, sila hubungi pentadbir sistem segera</li>
                    <li>Sentiasa pastikan kata laluan anda adalah kuat dan selamat</li>
                </ul>
            </div>
            
            <div style='background-color: #f7fafc; border: 1px solid #cbd5e0; border-radius: 6px; padding: 20px; margin: 25px 0;'>
                <h4 style='margin-top: 0; color: #2d3748; font-size: 16px;'>📞 Perlukan Bantuan?</h4>
                <p style='margin: 10px 0;'>Jika anda mempunyai pertanyaan atau kebimbangan:</p>
                <ul style='margin: 10px 0; padding-left: 20px;'>
                    <li>📧 Email: <a href='mailto:{data.SupportEmail}' style='color: #3182ce;'>{data.SupportEmail}</a></li>
                    <li>🏢 Hubungi pentadbir sistem di jabatan anda</li>
                </ul>
            </div>
            
            <p style='margin-top: 30px;'>Terima kasih kerana menggunakan sistem SPMJ dengan selamat!</p>
            
            <p>Wassalam,<br>
            <strong>Pasukan Sistem SPMJ</strong></p>
        </div>
        
        <!-- Footer -->
        <div style='background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e2e8f0;'>
            <p style='margin: 0; font-size: 12px; color: #718096;'>
                Notifikasi automatik - {DateTime.Now:dd/MM/yyyy HH:mm:ss}<br>
                Sila jangan balas email ini.<br>
                © 2025 SPMJ System. Hak cipta terpelihara.
            </p>
        </div>
    </div>
</body>
</html>";
    }
}
