-- Database Migration Script for Email Service Integration
-- Execute this script on your SPMJ database to add email service tables

-- Add email column to pn_pengguna if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'pn_pengguna') AND name = 'email')
BEGIN
    ALTER TABLE pn_pengguna ADD email VARCHAR(255) NULL
    PRINT 'Added email column to pn_pengguna table'
END
ELSE
BEGIN
    PRINT 'Email column already exists in pn_pengguna table'
END

-- Create password_reset_tokens table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'password_reset_tokens')
BEGIN
    CREATE TABLE password_reset_tokens (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        user_id VARCHAR(12) NOT NULL,
        token VARCHAR(255) NOT NULL UNIQUE,
        created_at DATETIME2 DEFAULT GETUTCDATE(),
        expires_at DATETIME2 NOT NULL,
        used BIT DEFAULT 0,
        used_at DATETIME2 NULL,
        FOREIGN KEY (user_id) REFERENCES pn_pengguna(id_pg)
    )
    
    -- Create indexes for performance
    CREATE INDEX IX_password_reset_tokens_user_id_used ON password_reset_tokens(user_id, used)
    CREATE INDEX IX_password_reset_tokens_expires_at ON password_reset_tokens(expires_at)
    
    PRINT 'Created password_reset_tokens table with indexes'
END
ELSE
BEGIN
    PRINT 'password_reset_tokens table already exists'
END

-- Create otp_tokens table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'otp_tokens')
BEGIN
    CREATE TABLE otp_tokens (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        user_id VARCHAR(12) NOT NULL,
        otp_code VARCHAR(10) NOT NULL,
        created_at DATETIME2 DEFAULT GETUTCDATE(),
        expires_at DATETIME2 NOT NULL,
        used BIT DEFAULT 0,
        used_at DATETIME2 NULL,
        purpose VARCHAR(50) NOT NULL DEFAULT 'LOGIN',
        FOREIGN KEY (user_id) REFERENCES pn_pengguna(id_pg)
    )
    
    -- Create indexes for performance
    CREATE INDEX IX_otp_tokens_user_id_purpose_used ON otp_tokens(user_id, purpose, used)
    CREATE INDEX IX_otp_tokens_expires_at ON otp_tokens(expires_at)
    CREATE INDEX IX_otp_tokens_otp_code_purpose ON otp_tokens(otp_code, purpose)
    
    PRINT 'Created otp_tokens table with indexes'
END
ELSE
BEGIN
    PRINT 'otp_tokens table already exists'
END

-- Create email_audit_log table for tracking email sending
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'email_audit_log')
BEGIN
    CREATE TABLE email_audit_log (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        user_id VARCHAR(12) NOT NULL,
        email_address VARCHAR(255) NOT NULL,
        email_type VARCHAR(50) NOT NULL, -- PASSWORD_RESET, OTP, ADMIN_PASSWORD, WELCOME
        sent_at DATETIME2 DEFAULT GETUTCDATE(),
        success BIT NOT NULL,
        error_message VARCHAR(1000) NULL,
        admin_id VARCHAR(50) NULL, -- For admin-initiated emails
        FOREIGN KEY (user_id) REFERENCES pn_pengguna(id_pg)
    )
    
    -- Create indexes for performance
    CREATE INDEX IX_email_audit_log_user_id_sent_at ON email_audit_log(user_id, sent_at)
    CREATE INDEX IX_email_audit_log_email_type_sent_at ON email_audit_log(email_type, sent_at)
    
    PRINT 'Created email_audit_log table with indexes'
END
ELSE
BEGIN
    PRINT 'email_audit_log table already exists'
END

-- Add sample email addresses for testing (optional)
-- UPDATE pn_pengguna SET email = '<EMAIL>' WHERE id_pg = 'test_user1' AND email IS NULL
-- UPDATE pn_pengguna SET email = '<EMAIL>' WHERE id_pg = 'admin' AND email IS NULL

PRINT 'Email Service database migration completed successfully!'
PRINT ''
PRINT 'Next steps:'
PRINT '1. Update email addresses in pn_pengguna table for users who need email functionality'
PRINT '2. Configure email settings in the .NET 9 microservice appsettings.json'
PRINT '3. Test the microservice endpoints'
PRINT '4. Integrate with .NET 3.5 application using HttpWebRequest'
