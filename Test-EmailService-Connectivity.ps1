# Test Email Service Connectivity
Write-Host "Testing SPMJ-PDSA Email Microservice Connectivity..." -ForegroundColor Green

$serviceUrl = "http://localhost:5000"

try {
    Write-Host "Checking email service health at: $serviceUrl" -ForegroundColor Yellow
    
    # Test health endpoint
    $healthUrl = "$serviceUrl/api/health"
    $response = Invoke-WebRequest -Uri $healthUrl -Method GET -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Email service is running and healthy" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor White
    } else {
        Write-Host "❌ Email service health check failed" -ForegroundColor Red
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Cannot connect to email service at $serviceUrl" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible issues:" -ForegroundColor Yellow
    Write-Host "1. Email microservice is not running" -ForegroundColor White
    Write-Host "2. Service is running on a different port" -ForegroundColor White
    Write-Host "3. Firewall blocking the connection" -ForegroundColor White
    Write-Host "4. Service URL configuration incorrect" -ForegroundColor White
    Write-Host ""
    Write-Host "Please start the email microservice or update the configuration." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Testing complete." -ForegroundColor Green
