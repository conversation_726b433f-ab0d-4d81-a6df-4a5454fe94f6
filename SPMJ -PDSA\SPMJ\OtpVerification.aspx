<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="OtpVerification.aspx.vb" Inherits="SPMJ.OtpVerification" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>SPMJ - <PERSON>gesahan OTP</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 450px;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c5282;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 600;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 18px;
            text-align: center;
            letter-spacing: 5px;
            transition: border-color 0.3s;
            box-sizing: border-box;
            font-family: monospace;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #ccc;
            transform: none;
            cursor: not-allowed;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
        }
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .info-box {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .info-box h4 {
            margin: 0 0 10px 0;
            color: #2c5282;
        }
        .info-box p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
        .countdown {
            text-align: center;
            margin: 15px 0;
            font-size: 14px;
            color: #666;
        }
        .countdown.warning {
            color: #e53e3e;
            font-weight: bold;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
    <script type="text/javascript">
        var countdownTimer;
        var remainingTime = 300; // 5 minutes in seconds

        function startCountdown() {
            countdownTimer = setInterval(function() {
                remainingTime--;
                updateCountdownDisplay();
                
                if (remainingTime <= 0) {
                    clearInterval(countdownTimer);
                    document.getElementById('<%= ButtonVerifyOtp.ClientID %>').disabled = true;
                    document.getElementById('countdownDiv').innerHTML = '<span class="warning">Kod OTP telah luput. Sila minta kod baru.</span>';
                }
            }, 1000);
        }

        function updateCountdownDisplay() {
            var minutes = Math.floor(remainingTime / 60);
            var seconds = remainingTime % 60;
            var timeStr = minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
            
            var countdownDiv = document.getElementById('countdownDiv');
            if (remainingTime <= 60) {
                countdownDiv.innerHTML = '<span class="warning">Kod akan luput dalam ' + timeStr + '</span>';
            } else {
                countdownDiv.innerHTML = 'Kod akan luput dalam ' + timeStr;
            }
        }

        function onLoad() {
            startCountdown();
        }

        // Auto-format OTP input
        function formatOtpInput(input) {
            var value = input.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 6) value = value.substring(0, 6); // Limit to 6 digits
            input.value = value;
            
            // Auto-submit when 6 digits entered
            if (value.length === 6) {
                setTimeout(function() {
                    document.getElementById('<%= ButtonVerifyOtp.ClientID %>').click();
                }, 100);
            }
        }
    </script>
</head>
<body onload="onLoad()">
    <form id="form1" runat="server">
        <div class="container">
            <div class="header">
                <h1>Pengesahan OTP</h1>
                <p>Sistem Pengurusan Maklumat Jururawat</p>
            </div>

            <asp:Panel ID="PanelMessage" runat="server" Visible="false">
                <div class="alert" id="alertDiv" runat="server">
                    <asp:Label ID="LabelMessage" runat="server"></asp:Label>
                </div>
            </asp:Panel>

            <div class="info-box">
                <h4>Kod Pengesahan Dihantar</h4>
                <p>Kod OTP 6 digit telah dihantar ke alamat email yang berdaftar dengan akaun anda. Sila masukkan kod tersebut untuk melengkapkan proses log masuk.</p>
            </div>

            <div class="form-group">
                <label for="TextBoxOtp">Kod OTP (6 digit)</label>
                <asp:TextBox ID="TextBoxOtp" runat="server" CssClass="form-control" MaxLength="6" 
                           onkeyup="formatOtpInput(this)" placeholder="000000" autocomplete="off"></asp:TextBox>
            </div>

            <div class="countdown" id="countdownDiv">
                Kod akan luput dalam 5:00
            </div>

            <asp:Button ID="ButtonVerifyOtp" runat="server" Text="Sahkan OTP" CssClass="btn" />
            <asp:Button ID="ButtonResendOtp" runat="server" Text="Hantar Semula OTP" CssClass="btn btn-secondary" />

            <div class="back-link">
                <a href="p0_Login.aspx">← Kembali ke halaman log masuk</a>
            </div>
        </div>
    </form>
</body>
</html>
