﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class LP_STAT_XM
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        'KATEGORI
        Cb_Kategori.Items.Clear()
        Cb_Kategori.Items.Add("(SEMUA SEKTOR)")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = ""
        Cb_Kategori.Items.Add("KERAJAAN")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "1"
        Cb_Kategori.Items.Add("IPTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "3"
        Cb_Kategori.Items.Add("SWASTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "2"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where (jenis < 3) order by dc_kolej"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA KOLEJ)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'KURSUS
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("(SEMUA JENIS PENDAFTARAN)")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
        Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
        Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
        Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
        Cb_Kursus.Items.Add("KEBIDANAN I")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim SQL, X, Tahun, Siri, Id_XM As String
        Dim Jwtn, Jwtn2, Jwtn3 As Integer
        Tahun = "0" : Siri = "0"
        'Id XM
        X = "" : Id_XM = ""
        If Cb_Kolej.SelectedIndex > 0 Then X += " where p.id_kolej=" & Cb_Kolej.SelectedValue
        If Cb_Kursus.SelectedIndex > 0 Then
            If X.Length = 0 Then X += " where " Else X += " and "
            X += " j_xm=" & Cb_Kursus.SelectedIndex
        End If
        If Tx_Tahun.Text <> "" And Tx_Siri.Text <> "" Then
            If X.Length = 0 Then
                X += " where tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            Else
                X += " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            End If
        End If

        If X = "" Then
        Else
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "select top 1 px.id_xm, tahun, siri from xm_calon xc  inner join pn_xm px on xc.id_xm=px.id_xm inner join pelatih p on p.nokp=xc.nokp " & _
                   " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                   "left join pn_kolej pk on pk.id_kolej=xc.id_pusat " & X & " order by tahun desc, siri desc, j_xm desc"
            Cmd.CommandText = SQL : Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then Id_XM = Rdr(0) : Tahun = Rdr(1) : Siri = Rdr(2) : X = " and xc.id_xm=" & Id_XM Else X = ""
            Rdr.Close() : Cn.Close()
        End If

        SQL = ""
        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL += "" Else SQL_pilih += " and pnk.jenis=" & Cb_Kategori.SelectedIndex 'Kategori
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL += ""
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " and ( pnk.id_KOLEJ>=0 and  pnk.id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += "  and ( pnk.id_KOLEJ>=101 and  pnk.id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += "  and ( pnk.id_KOLEJ>=200  and  pnk.JENIS<3) " ' Cb_Kategori.SelectedValue
        End If
        If Cb_Kolej.SelectedIndex < 1 Then SQL += "" Else SQL += " and pnk.id_kolej=" & Cb_Kolej.SelectedValue 'Kolej
        If Cb_Kursus.SelectedIndex < 1 Then
            SQL += ""
        Else
            If Cb_Kursus.SelectedIndex = 1 Then Jwtn = 1 : Jwtn2 = 5 : Jwtn3 = 8
            If Cb_Kursus.SelectedIndex = 2 Then Jwtn = 2 : Jwtn2 = 2 : Jwtn3 = 2
            If Cb_Kursus.SelectedIndex = 3 Then Jwtn = 3 : Jwtn2 = 3 : Jwtn3 = 3
            If Cb_Kursus.SelectedIndex = 4 Then Jwtn = 4 : Jwtn2 = 4 : Jwtn3 = 4
            SQL += " and (p.j_kursus = " & Jwtn & " or p.j_kursus = " & Jwtn2 & " or p.j_kursus = " & Jwtn3 & ")"
        End If

        'AG
        If Tx_AG1.Text.Trim = "" Then
        Else
            If Tx_AG2.Text.Trim = "" Then
            Else
                If Not IsNumeric(Tx_AG1.Text) Or Not IsNumeric(Tx_AG2.Text) Then Exit Sub
                SQL += " and xc.ag between " & Tx_AG1.Text & " and " & Tx_AG2.Text
            End If
        End If

        SQL = X + SQL
        Try
            Reset()
            Calon_Baru(SQL) : Calon_Lulus(SQL) : Calon_Gagal(SQL) : Calon_Hadir(SQL)
            If tx_Lulus.Text > 0 Then
                'Comment Ori 06072018 - OSH
                'tx_Perlus.Text = Math.Round((tx_Lulus.Text / (tx_Calon.Text - tx_THadir.Text)) * 100, 1) & " %"

                'Adjust 2 decimal point 06072018 - OSH
                tx_Perlus.Text = Math.Round((tx_Lulus.Text / (tx_Calon.Text - tx_THadir.Text)) * 100, 2) & " %"

            Else
                tx_Perlus.Text = tx_Lulus.Text & " %"
            End If
            If tx_Gagal.Text > 0 Then
                'Comment Ori 06072018 - OSH
                'tx_Pergal.Text = Math.Round((tx_Gagal.Text / (tx_Calon.Text - tx_THadir.Text)) * 100, 1) & " %"

                'Adjust 2 decimal point 06072018 - OSH
                tx_Pergal.Text = Math.Round((tx_Gagal.Text / (tx_Calon.Text - tx_THadir.Text)) * 100, 2) & " %"

            Else
                tx_Pergal.Text = tx_Gagal.Text & " %"
            End If

            If tx_THadir.Text > 0 Then
                'Comment Ori 06072018 - OSH
                'tx_PerHad.Text = Math.Round((tx_THadir.Text / (tx_Calon.Text)) * 100, 1) & " %"

                'Adjust 2 decimal point 06072018 - OSH
                tx_PerHad.Text = Math.Round((tx_THadir.Text / (tx_Calon.Text)) * 100, 2) & " %"

            Else
                tx_PerHad.Text = tx_THadir.Text & " %"
            End If

            tx_CalonDuduk.Text = tx_Calon.Text - tx_THadir.Text

            Max(SQL) : Min(SQL) : Mean(SQL) : Med(SQL) : Mode(SQL) : StandDev(SQL)
            Freq_A(SQL)

            Tx_Tahun.Text = Tahun
            Tx_Siri.Text = Siri
        Catch ex As Exception
            Response.Redirect("LP_STAT_XM.aspx")
        End Try
    End Sub

    Public Sub Reset()
        tx_Calon.Text = "0" : tx_Lulus.Text = "0" : tx_Gagal.Text = "0" : tx_Perlus.Text = "0" : tx_Pergal.Text = "0"
        tx_Max.Text = "0" : tx_Min.Text = "0" : tx_Mean.Text = "0" : tx_Med.Text = "0" : tx_Mod.Text = "0" : tx_StanDev.Text = "0"
        tx_F1.Text = "0" : tx_F2.Text = "0" : tx_F3.Text = "0" : tx_F4.Text = "0" : tx_F5.Text = "0" : tx_F6.Text = "0" : tx_F7.Text = "0" : tx_F8.Text = "0" : tx_F9.Text = "0" : tx_F10.Text = "0"
        tx_RF1.Text = "0" : tx_RF2.Text = "0" : tx_RF3.Text = "0" : tx_RF4.Text = "0" : tx_RF5.Text = "0" : tx_RF6.Text = "0" : tx_RF7.Text = "0" : tx_RF8.Text = "0" : tx_RF9.Text = "0" : tx_RF10.Text = "0"
        tx_CF1.Text = "0" : tx_CF2.Text = "0" : tx_CF3.Text = "0" : tx_CF4.Text = "0" : tx_CF5.Text = "0" : tx_CF6.Text = "0" : tx_CF7.Text = "0" : tx_CF8.Text = "0" : tx_CF9.Text = "0" : tx_CF10.Text = "0"
    End Sub

    Public Sub Calon_Baru(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim mark As Integer
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Ori 15092015 -OSH
        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                         "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null" & X & ""

        mark = Cmd.ExecuteScalar
        tx_Calon.Text = mark
        Cn.Close()
    End Sub

    Public Sub Calon_Lulus(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim mark, mark2 As Double
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Ori 15092015 -OSH
        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and xc.keputusan='L' " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                         "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and xc.keputusan='L' and p.status is null " & X & ""
        mark = Cmd.ExecuteScalar
        tx_Lulus.Text = mark
        If mark > 0 Then
            mark2 = tx_Calon.Text
            'Comment Ori 06072018 -OSH
            'mark2 = Math.Round((mark / mark2) * 100, 1)

            'Adjust 2 decimal point 06072018 -OSH
            mark2 = Math.Round((mark / mark2) * 100, 2)
            tx_Perlus.Text = mark2 & " %"
        Else
            tx_Perlus.Text = mark & " %"
        End If
        Cn.Close()
    End Sub

    Public Sub Calon_Gagal(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim mark, mark2 As Double
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Ori 15092015 -OSH
        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and xc.keputusan='G' " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                       "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and xc.keputusan='G' and p.status is null " & X & ""
        mark = Cmd.ExecuteScalar
        tx_Gagal.Text = mark
        If mark > 0 Then
            mark2 = tx_Calon.Text
            'Comment original 06072018 - OSH
            'mark2 = Math.Round((mark / mark2) * 100, 1)

            'Adjust 2 decimal poiint 06072018 -OSH
            mark2 = Math.Round((mark / mark2) * 100, 2)
            tx_Pergal.Text = mark2 & " %"
        Else
            tx_Pergal.Text = mark & " %"
        End If
        Cn.Close()
    End Sub

    'edit 2011 03 07
    Public Sub Calon_Hadir(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim mark, mark2 As Double
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and xc.keputusan='T' " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                       "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and xc.keputusan='T' and p.status is null" & X & ""
        mark = Cmd.ExecuteScalar
        tx_THadir.Text = mark
        If mark > 0 Then
            mark2 = tx_Calon.Text
            'Comment Ori 06072018 - OSH
            'mark2 = Math.Round((mark / mark2) * 100, 1)

            'Adjust 2 decimal poiint 06072018 -OSH
            mark2 = Math.Round((mark / mark2) * 100, 2)
            tx_PerHad.Text = mark2 & " %"
        Else
            tx_PerHad.Text = mark & " %"
        End If
        Cn.Close()
    End Sub

    Public Sub Max(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim mark As Double
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select isnull(max(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select isnull(max(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                        "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null" & X & ""
        mark = Cmd.ExecuteScalar
        tx_Max.Text = mark
        Cn.Close()
    End Sub

    Public Sub Min(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim mark As Double
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select isnull(min(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and not(xc.keputusan ='T') " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select isnull(min(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                      "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and not(xc.keputusan ='T') and p.status is null" & X & ""
        mark = Cmd.ExecuteScalar
        tx_Min.Text = mark
        Cn.Close()
    End Sub

    Public Sub Mean(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim mark As Double
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select isnull(sum(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select isnull(sum(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null " & X & ""
        mark = Cmd.ExecuteScalar
        If mark > 0 Then tx_Mean.Text = Math.Round(mark / CInt(tx_Calon.Text), 2) Else tx_Mean.Text = ""
        Cn.Close()
    End Sub

    Public Sub Med(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet : Dim mark As Double = 0

        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select ((select isnull(max(markah_jum),'0') as max from (select top 50 percent xc.markah_jum from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & " order by xc.markah_jum) as A)+(select min(markah_jum) " & _
        '                  "as min from (select top 50 percent xc.markah_jum from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & " order by xc.markah_jum desc) as B ))/2 as med"

        'Fixing multiple records display per canidate 15092015 - OSH

        Cmd.CommandText = "select ((select isnull(max(markah_jum),'0') as max from (select top 50 percent xc.markah_jum from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                        "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null " & X & " order by xc.markah_jum) as A)+(select min(markah_jum) " & _
                        "as min from (select top 50 percent xc.markah_jum from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                        "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null " & X & " order by xc.markah_jum desc) as B ))/2 as med"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            mark = Rdr.Item(0)
        End While
        Rdr.Close()
        tx_Med.Text = Math.Round(mark, 2)
        Cn.Close()
    End Sub

    Public Sub Mode(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet : Dim mark As String = ""

        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select COUNT(*) AS freq, cast(xc.markah_jum as decimal(6,1)) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & " group by xc.markah_jum " & _
        '                  "having count(*)>=All(select COUNT(*) AS freq from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & " group by xc.markah_jum)"

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select COUNT(*) AS freq, cast(xc.markah_jum as decimal(6,1)) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                       "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null" & X & " group by xc.markah_jum " & _
                       "having count(*)>=All(select COUNT(*) AS freq from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                       "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null" & X & " group by xc.markah_jum)"

        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            mark += Rdr.Item(1) & " , "
        End While
        If mark.Trim = "" Then  Else mark = Mid(mark, 1, Len(mark) - 2)
        Rdr.Close()
        Cn.Close()
        tx_Mod.Text = mark
    End Sub

    Public Sub StandDev(ByVal X As String)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet : Dim mark As Double

        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select isnull(stdev(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null " & X & ""

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select isnull(stdev(xc.markah_jum),'0') from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and p.status is null" & X & ""


        mark = Cmd.ExecuteScalar()
        Cn.Close()
        If mark > 0 Then tx_StanDev.Text = Math.Round(mark, 2) Else tx_StanDev.Text = 0
    End Sub

    Public Sub Freq_A(ByVal X As String)
        Dim a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, t As Double
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'Comment Ori 15092015 -OSH 
        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 80 and 100 " & X & ""
        'a1 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 75 and 79.99 " & X & ""
        'a2 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 70 and 74.99 " & X & ""
        'a3 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and not(xc.keputusan='T') and xc.markah_jum between 65 and 69.99 " & X & ""
        'a4 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 60 and 64.99 " & X & ""
        'a5 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 55 and 59.99 " & X & ""
        'a6 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 50 and 54.99 " & X & ""
        'a7 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and not(xc.keputusan='T') and xc.markah_jum between 47 and 49.99 " & X & ""
        'a8 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 44 and 46.99 " & X & ""
        'a9 = Cmd.ExecuteScalar

        'Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
        '                  "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and  not(xc.keputusan='T') and xc.markah_jum between 0 and 43.99 " & X & ""




        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                        "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 80 and 100 and p.status is null" & X & ""
        a1 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 75 and 79.99 and p.status is null" & X & ""
        a2 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 70 and 74.99 and p.status is null" & X & ""
        a3 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and not(xc.keputusan='T') and xc.markah_jum between 65 and 69.99 and p.status is null" & X & ""
        a4 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 60 and 64.99 and p.status is null" & X & ""
        a5 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 55 and 59.99and p.status is null " & X & ""
        a6 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 50 and 54.99 and p.status is null" & X & ""
        a7 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and not(xc.keputusan='T') and xc.markah_jum between 47 and 49.99 and p.status is null" & X & ""
        a8 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null and  not(xc.keputusan='T') and xc.markah_jum between 44 and 46.99 and p.status is null" & X & ""
        a9 = Cmd.ExecuteScalar

        Cmd.CommandText = "select count(p.nokp) from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join " & _
                          "pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and  not(xc.keputusan='T') and xc.markah_jum between 0 and 43.99 and p.status is null" & X & ""
        a10 = Cmd.ExecuteScalar

        tx_F1.Text = a1 : tx_F2.Text = a2 : tx_F3.Text = a3 : tx_F4.Text = a4 : tx_F5.Text = a5
        tx_F6.Text = a6 : tx_F7.Text = a7 : tx_F8.Text = a8 : tx_F9.Text = a9 : tx_F10.Text = a10

        t = a1 + a2 + a3 + a4 + a5 + a6 + a7 + a8 + a9 + a10
        tx_RF1.Text = Math.Round(a1 / t, 2) : tx_RF2.Text = Math.Round(a2 / t, 2) : tx_RF3.Text = Math.Round(a3 / t, 2) : tx_RF4.Text = Math.Round(a4 / t, 2) : tx_RF5.Text = Math.Round(a5 / t, 2)
        tx_RF6.Text = Math.Round(a6 / t, 2) : tx_RF7.Text = Math.Round(a7 / t, 2) : tx_RF8.Text = Math.Round(a8 / t, 2) : tx_RF9.Text = Math.Round(a9 / t, 2) : tx_RF10.Text = Math.Round(a10 / t, 2)

        a1 = tx_RF1.Text : a2 = tx_RF2.Text : a3 = tx_RF3.Text : a4 = tx_RF4.Text : a5 = tx_RF5.Text
        a6 = tx_RF6.Text : a7 = tx_RF7.Text : a8 = tx_RF8.Text : a9 = tx_RF9.Text : a10 = tx_RF10.Text

        tx_CF1.Text = a1
        If CDbl(tx_CF1.Text) > 1 Then tx_CF1.Text = CInt(tx_CF1.Text)
        tx_CF2.Text = a1 + a2
        If CDbl(tx_CF2.Text) > 1 Then tx_CF2.Text = CInt(tx_CF2.Text)
        tx_CF3.Text = a1 + a2 + a3
        If CDbl(tx_CF3.Text) > 1 Then tx_CF3.Text = CInt(tx_CF3.Text)
        tx_CF4.Text = a1 + a2 + a3 + a4
        If CDbl(tx_CF4.Text) > 1 Then tx_CF4.Text = CInt(tx_CF4.Text)
        tx_CF5.Text = a1 + a2 + a3 + a4 + a5
        If CDbl(tx_CF5.Text) > 1 Then tx_CF5.Text = CInt(tx_CF5.Text)
        tx_CF6.Text = a1 + a2 + a3 + a4 + a5 + a6
        If CDbl(tx_CF6.Text) > 1 Then tx_CF6.Text = CInt(tx_CF6.Text)
        tx_CF7.Text = a1 + a2 + a3 + a4 + a5 + a6 + a7
        If CDbl(tx_CF7.Text) > 1 Then tx_CF7.Text = CInt(tx_CF7.Text)
        tx_CF8.Text = a1 + a2 + a3 + a4 + a5 + a6 + a7 + a8
        If CDbl(tx_CF8.Text) > 1 Then tx_CF8.Text = CInt(tx_CF8.Text)
        tx_CF9.Text = a1 + a2 + a3 + a4 + a5 + a6 + a7 + a8 + a9
        If CDbl(tx_CF9.Text) > 1 Then tx_CF9.Text = CInt(tx_CF9.Text)
        tx_CF10.Text = a1 + a2 + a3 + a4 + a5 + a6 + a7 + a8 + a9 + a10
        If CDbl(tx_CF10.Text) > 1 Then tx_CF10.Text = CInt(tx_CF10.Text)
    End Sub

    Protected Sub Cb_Kategori_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kategori.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String = ""

        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL = "< 3" Else SQL = "=" & Cb_Kategori.SelectedValue
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL = "jenis< 3"
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " ( id_KOLEJ>=0 and  id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += " ( id_KOLEJ>=101 and  id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += " ( id_KOLEJ>=200  and  JENIS<3) " ' Cb_Kategori.SelectedValue
        End If

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where " & SQL & " order by dc_kolej" 'jenis
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Cetak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cetak.Click
        Dim Tajuk, Tajuk2, Tajuk3 As String

        If Tx_AG1.Text = "" And Tx_AG2.Text = "" Then Tajuk3 = "" Else Tajuk3 = " ANGKA GILIRAN ( " & Tx_AG1.Text & " - " & Tx_AG2.Text & " )"
        Tajuk = "STATISTIK KEPUTUSAN PEPERIKSAAN BAGI " & Cb_Kategori.SelectedItem.Text
        Tajuk2 = Cb_Kursus.SelectedItem.Text & _
        IIf(Tx_Tahun.Text > "", " TAHUN " & Tx_Tahun.Text, "") & _
        IIf(Tx_Siri.Text > "", " SIRI " & Tx_Siri.Text, "") & _
        ", " & Cb_Kolej.SelectedItem.Text & Tajuk3

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:70%;'>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr></tr>"
        '1st
        Header += "<tr><td colspan='4' style='vertical-align: middle; text-align: left;'>MAKLUMAT AM </td></tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;JUMLAH CALON KESELURUHAN: " & tx_Calon.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;JUMLAH CALON MENDUDUKI: " & tx_CalonDuduk.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;JUMLAH LULUS : " & tx_Lulus.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;JUMLAH GAGAL : " & tx_Gagal.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;JUMLAH TIDAK HADIR : " & tx_THadir.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;PERATUS LULUS : " & tx_Perlus.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;PERATUS GAGAL : " & tx_Pergal.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;PERATUS TIDAK HADIR : " & tx_PerHad.Text & "</td> "
        Header += "</tr>"
        Header += "<tr></tr>"
        '2nd
        Header += "<tr><td colspan='4' style='vertical-align: middle; text-align: left;'>LAPORAN STATISTIK</td></tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;MARKAH TERTINGGI : " & tx_Max.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;MARKAH TERENDAH : " & tx_Min.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;MEAN : " & tx_Mean.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;MEDIAN : " & tx_Med.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;MODE : " & tx_Mod.Text & "</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='4'style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;STANDARD DEVIATION : " & tx_StanDev.Text & "</td> "
        Header += "</tr>"
        Header += "<tr></tr>"
        '3rd
        Header += "<tr><td colspan='4' style='vertical-align: middle; text-align: left;'>LAPORAN PENILAIAN FREKUENSI </td></tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED A)</td><td colspan='3'>F  : " & tx_F1.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF1.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF1.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED A-) </td><td colspan='3'>F  : " & tx_F2.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF2.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF2.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED B+) </td><td colspan='3'>F  : " & tx_F3.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF3.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF3.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED B) </td><td colspan='3'>F  : " & tx_F4.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF4.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF4.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED B-) </td><td colspan='3'>F  : " & tx_F5.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF5.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF5.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED C+) </td><td colspan='3'>F  : " & tx_F6.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF6.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF6.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED C) </td><td colspan='3'>F  : " & tx_F7.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF7.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF7.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED C-) </td><td colspan='3'>F  : " & tx_F8.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF8.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF8.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED D+) </td><td colspan='3'>F  : " & tx_F9.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF9.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF9.Text & "</tr>"
        Header += "<tr><td style='vertical-align: middle; text-align: left;'>&nbsp;&nbsp;&nbsp;(GRED D) </td><td colspan='3'>F  : " & tx_F10.Text & "</td></tr>"
        Header += "<tr><td></td><td colspan='3'>RF : " & tx_RF10.Text & "</tr>"
        Header += "<tr><td></td><td colspan='3'>CF : " & tx_CF10.Text & "</tr>"

        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub
End Class