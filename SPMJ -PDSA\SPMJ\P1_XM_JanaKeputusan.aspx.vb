﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm10
    Inherits System.Web.UI.Page

    Public Sub Cari()
        If Cb_Kursus.SelectedIndex < 1 Then Cb_Kursus.Focus() : Exit Sub
        Dim SQL As String = ""
        'Comment Ori 15092015 - OSH 
        'If Cb_Kursus.SelectedValue = 1 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH', case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN, case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = 1 or j_kursus = 5 or j_kursus = 8) and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = 1 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'ElseIf Cb_Kursus.SelectedValue = 2 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as  'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN,case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 2 and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = 2 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'ElseIf Cb_Kursus.SelectedValue = 3 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM',case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN,case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 3 and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = 3 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'ElseIf Cb_Kursus.SelectedValue = 4 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN, case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 4 and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = 4 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'End If

        ''Fixing multiple records display per canidate 15092015 -OSH  
        If Cb_Kursus.SelectedValue = 1 Then
            SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH', case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN, case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = 1 or j_kursus = 5 or j_kursus = 8) and xc.status_ulang=0 and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 1 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        ElseIf Cb_Kursus.SelectedValue = 2 Then
            SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as  'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN,case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 2 and xc.status_ulang=0 and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 2 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        ElseIf Cb_Kursus.SelectedValue = 3 Then
            SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM',case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN,case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 3 and xc.status_ulang=0  and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 3 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        ElseIf Cb_Kursus.SelectedValue = 4 Then
            SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN, case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 4 and xc.status_ulang=0 and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 4 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        End If

        'Fixing exam id column display issue 28032017 -OSH   
        'Gd.Columns(5).Visible = False

        ''Fixing multiple records display per canidate and fields without exam id 28032017 -OSH  
        'If Cb_Kursus.SelectedValue = 1 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH', case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN, case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = 1 or j_kursus = 5 or j_kursus = 8) and xc.status_ulang=0 and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 1 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'ElseIf Cb_Kursus.SelectedValue = 2 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as  'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN,case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 2 and xc.status_ulang=0 and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 2 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'ElseIf Cb_Kursus.SelectedValue = 3 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN,case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 3 and xc.status_ulang=0  and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 3 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'ElseIf Cb_Kursus.SelectedValue = 4 Then
        '    SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ',case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'RESPON TUNGGAL', case xc.KEPUTUSAN  when 'T' then '' else cast(xc.rt as varchar(3)) end  as 'JUMLAH MARKAH',  case xc.KEPUTUSAN when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' end as KEPUTUSAN, case xc.ULANGAN when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as ULANGAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where j_kursus = 4 and xc.status_ulang=0 and p.status is null and xc.id_xm in (select id_xm from pn_xm where j_xm = 4 and status=1) order by cast(xc.ag as integer), p.id_kolej"
        'End If


        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)

        'Fixing SqlDataAdapter Query Timeout Issues 28032017 - OSH
        List_Adp.SelectCommand.CommandTimeout = 180
        List_Adp.Fill(List_Data, "pelatih")
        Gd.DataSource = List_Data.Tables("pelatih")
        DataBind()
        Gd.Visible = True
    End Sub

    Public Sub Statistik()        
        Dim i As Int16, SQL As String = "", L As Double = 0, G As Double = 0, T As Double = 0, Jum_Markah As Int16, Jum_Markah1 As Int16

        If Cb_Kursus.SelectedIndex = 1 Then Jum_Markah = 8
        If Cb_Kursus.SelectedIndex = 2 Then Jum_Markah = 8 '12
        If Cb_Kursus.SelectedIndex = 3 Then Jum_Markah = 8 '11
        If Cb_Kursus.SelectedIndex = 4 Then Jum_Markah = 8 : Jum_Markah1 = 13 '7

        For i = 0 To Gd.Rows.Count - 1
            If Gd.Rows.Item(i).Cells(Jum_Markah).Text = "L" Then
                L += 1                
            ElseIf Gd.Rows.Item(i).Cells(Jum_Markah).Text = "G" Then
                G += 1
                Gd.Rows.Item(i).ForeColor = Gd.HeaderStyle.BackColor                
            ElseIf Gd.Rows.Item(i).Cells(Jum_Markah).Text = "T" Then
                T += 1
            End If
        Next

        If L = 0 And G = 0 Then Exit Sub

        Tx_Calon.Text = Gd.Rows.Count
        Tx_Calon_L.Text = L & " (" & Math.Round(CDbl(L / (CInt(Tx_Calon.Text) - T) * 100), 1) & "%)"
        Tx_Calon_G.Text = G & " (" & Math.Round((100 - CDbl(L / (CInt(Tx_Calon.Text) - T) * 100)), 1) & "%)"
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Jana_Keputusan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If Not IsNumeric(Cb_Kursus.SelectedValue) Then Exit Sub
        e.Row.Cells(5).Visible = False 'id_xm
        e.Row.Cells(1).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(6).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(7).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(8).HorizontalAlign = HorizontalAlign.Center        
    End Sub


    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        If Cb_Kursus.SelectedIndex < 1 Then Msg(Me, "Sila pilih Jenis Kursus!") : Cb_Kursus.Focus() : Exit Sub
        Tx_Calon.Text = "" : Tx_Calon_G.Text = "" : Tx_Calon_L.Text = ""
        Cari()
        Statistik()

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select markah_min from pn_xm where j_xm = " & Cb_Kursus.SelectedValue & " and status = 1"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Tx_Lulus.Text = (Rdr(0))
        Else
            Tx_Lulus.Text = "TIADA"
        End If
        Rdr.Close()
        Cn.Close()
        'If Cb_Kursus.SelectedIndex = 4 Then TextBox1.Visible = True Else TextBox1.Visible = False
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = CStr(e.Row.RowIndex + 1) & "."
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        If Tx_Lulus.Text.Trim = "" Then Exit Sub

        Dim i As Int16, SQL As String = "", L As Double = 0, G As Double = 0, T As Double = 0, Jum_Markah As Int16, Jum_Markah1 As Int16
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        If Cb_Kursus.SelectedIndex = 1 Then Jum_Markah = 7
        If Cb_Kursus.SelectedIndex = 2 Then Jum_Markah = 7 '12
        If Cb_Kursus.SelectedIndex = 3 Then Jum_Markah = 7 '11
        If Cb_Kursus.SelectedIndex = 4 Then Jum_Markah = 7 : Jum_Markah1 = 12 '7

        For i = 0 To Gd.Rows.Count - 1
            'If Gd.Rows.Item(i).Cells(Jum_Markah).Text = "&nbsp;" Then Gd.Rows.Item(i).Cells(Jum_Markah).Text = ""
            'If Gd.Rows.Item(i).Cells(Jum_Markah).Text = "&nbsp;" Then Gd.Rows.Item(i).Cells(Jum_Markah).Text = ""
            If Gd.Rows.Item(i).Cells(Jum_Markah).Text >= Tx_Lulus.Text Then
                'Add ';' to sql query  pass canidate list 24092012-OSH
                'SQL += "update xm_calon set markah_jum = " & Gd.Rows.Item(i).Cells(Jum_Markah).Text & ", keputusan = 'L' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm = '" & Gd.Rows.Item(i).Cells(5).Text & "'; " & vbCrLf
                'comment ori pass canidate sql query without ending ;   24092012 -OSH Reopen enabling Counter population 19122012 -OSH
                SQL += "update xm_calon set markah_jum = " & Gd.Rows.Item(i).Cells(Jum_Markah).Text & ", keputusan = 'L' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm = '" & Gd.Rows.Item(i).Cells(5).Text & "'" & vbCrLf
                L += 1
            ElseIf IIf(Gd.Rows.Item(i).Cells(Jum_Markah).Text = "&nbsp;", "", Gd.Rows.Item(i).Cells(Jum_Markah).Text) >= "0" _
            And Not (Gd.Rows.Item(i).Cells(8).Text = "TIDAK HADIR") _
            And (Not (Gd.Rows.Item(i).Cells(Jum_Markah).Text = "" _
            Or Gd.Rows.Item(i).Cells(Jum_Markah).Text = "&nbsp;") _
            Or (Gd.Rows.Item(i).Cells(Jum_Markah).Text < Tx_Lulus.Text)) Then
                'Add ';' to sql query  fail canidate list 24092012-OSH
                'SQL += "update xm_calon set markah_jum = " & Gd.Rows.Item(i).Cells(Jum_Markah).Text & ", keputusan = 'G' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm = '" & Gd.Rows.Item(i).Cells(5).Text & "'" & vbCrLf
                'comment ori fail canidate sql query without ending ;   24092012 -OSH - Reopen enabling Counter population 19122012 -OSH
                SQL += "update xm_calon set markah_jum = " & Gd.Rows.Item(i).Cells(Jum_Markah).Text & ", keputusan = 'G' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm = '" & Gd.Rows.Item(i).Cells(5).Text & "'" & vbCrLf
                G += 1
                Gd.Rows.Item(i).ForeColor = Gd.HeaderStyle.BackColor

            ElseIf Gd.Rows.Item(i).Cells(8).Text = "TIDAK HADIR" Then
                '    SQL += "update xm_calon set markah_jum = " & Gd.Rows.Item(i).Cells(Jum_Markah).Text & ", keputusan = 'T' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm = '" & Gd.Rows.Item(i).Cells(5).Text & "'" & vbCrLf
                T += 1
                '    Gd.Rows.Item(i).ForeColor = Gd.HeaderStyle.BackColor
            End If


        Next

        If SQL = "" Then Exit Sub

        'SQL += "update pn_xm set markah_min = " & CInt(Tx_Lulus.Text) & " where id_xm = " & Gd.Rows.Item(0).Cells(5).Text
        'Display Total Result Count
        Tx_Calon.Text = Gd.Rows.Count
        'COMMENT Ori 06072018 -OSH
        'Tx_Calon_L.Text = L & " (" & Math.Round(CDbl(L / (CInt(Tx_Calon.Text) - T) * 100), 1) & "%)"
        'Tx_Calon_G.Text = G & " (" & Math.Round(CDbl(G / (CInt(Tx_Calon.Text) - T) * 100), 1) & "%)"
        'Tx_Calon_T.Text = T & " (" & Math.Round(CDbl(T / CInt(Tx_Calon.Text) * 100), 1) & "%)"

        'Adjust 2 decimal point 06072018 - OSH
        Tx_Calon_L.Text = L & " (" & Math.Round(CDbl(L / (CInt(Tx_Calon.Text) - T) * 100), 2) & "%)"
        Tx_Calon_G.Text = G & " (" & Math.Round(CDbl(G / (CInt(Tx_Calon.Text) - T) * 100), 2) & "%)"
        Tx_Calon_T.Text = T & " (" & Math.Round(CDbl(T / CInt(Tx_Calon.Text) * 100), 2) & "%)"
        Tx_Menduduki.Text = Gd.Rows.Count - T
        Try
            Cmd.CommandText = SQL
            'Fixing query timeout 28032017 - OSH
            Cmd.CommandTimeout = 180
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Msg(Me, "Rekod Telah Dikemaskini...")
            Cari()
            Statistik()
        Catch ex As Exception
            Msg(Me, ex.Message)
            Cn.Close()
        End Try
    End Sub

    

    Protected Sub cmd_excel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_excel.Click
        GridViewExportUtil.Export("Laporan.xls", Gd)
    End Sub
End Class