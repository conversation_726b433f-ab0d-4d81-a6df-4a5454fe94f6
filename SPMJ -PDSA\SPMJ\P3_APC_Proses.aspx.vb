﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm19
    Inherits System.Web.UI.Page    

    'Public Sub Kepala_Surat(ByVal Z As Int16)
    '    x = "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word'>"
    '    x += "<head><style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
    '    x += "<style type='text/css'><!-- .indent_3x { padding-left: 400pt;padding-right: 50pt;} --></style>"
    '    x += "<style> "
    '    x += "@page Section1 {mso-footer:f1;}"
    '    x += "div.Section1{page:Section1;}"
    '    x += "p<PERSON>, li.<PERSON><PERSON><PERSON>, div.<PERSON>oFooter{"
    '    x += "mso-pagination:widow-orphan;"
    '    x += "tab-stops:center 216.0pt right 432.0pt;}"
    '    x += "</style>"
    '    x += "</head><body><div class='Section1'>"

    '    'Header

    '    'x += "<table width='100%' style='font-family: Arial; font-size: 9px;background-color:#cfcfcf;'><tr>"
    '    x += "<table width='100%' style='border: none;mso-border-bottom-alt:solid windowtext .5pt;margin-left: 0px; font-family: Arial; font-size: 8pt;'><tr>"
    '    x += "<td><img width=110 height=88 src='" & s & "jata.png'></img></td>"
    '    x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'>"
    '    If Z = 1 Then
    '        x += "<b>LEMBAGA JURURAWAT MALAYSIA</b>"
    '        x += "<br/><i>NURSING BOARD MALAYSIA</i>"
    '    Else
    '        x += "<b>LEMBAGA BIDAN MALAYSIA</b>"
    '        x += "<br/><i>MIDWIVES BOARD MALAYSIA</i>"
    '    End If
    '    x += "<br/><b>KEMENTERIAN KESIHATAN MALAYSIA</b>"
    '    x += "<br/><i>MINISTRY OF HEALTH MALAYSIA</i>"
    '    x += "<br/><b>Aras 3, Blok E1, Kompleks E, Presint 1</b>"
    '    x += "<br/><i>Level 3, Block E1, Parcel E, Precinct 1</i>"
    '    x += "<br/><b>Pusat Pentadbiran Kerajaan Persekutuan</b>"
    '    x += "<br/><i>Federal Government Administrative Centre</i>"
    '    x += "<br/><b>62590 Putrajaya</b></p></div></td>"
    '    x += "<td style='width:21.64%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'>"
    '    If Z = 1 Then
    '        x += "<div align ='center'><img width=80 height=100 src='" & s & "ljm2.gif'></img>"
    '    Else
    '        x += "<div align ='center'><img width=100 height=100 src='" & s & "bidan.jpg'></img>"
    '    End If
    '    'x += "<v:shape ><v:imagedata src='" & s & "ljm2.gif'/>"
    '    x += "<br/>TEL :603-88831339"
    '    x += "<br/>FAX :603-88831329 </div>"
    '    x += "</td>"
    '    x += "</tr></table>"
    '    ' <div align="center"></div>
    'End Sub

    'Public Sub footer_surat()
    '    'X += "<br clear=all style='page-break-before:always'>"
    '    X += "<br clear=all style='page-break-before:always'>"
    '    X += "<div style='mso-element:footer' id='f1'>"
    '    'x += "<p class='MsoFooter'>"
    '    'x += "<span><img src='" & s & "img1.png'><img src='" & s & "img2.png'><img src='" & s & "img3.jpg'><img src='" & s & "img4.png'></span></p>"
    '    X += "<p class=MsoFooter style='border:none;mso-border-top-alt:solid windowtext 0.5pt;"
    '    X += "padding:0in;mso-padding-alt:0in 0in 1.0pt 0in'><span style='mso-no-proof:yes'>"
    '    X += " <!--[if gte vml 1]>"
    '    X += "<v:shape style='position:absolute;left:0;text-align:left;margin-left:315pt;margin-top:3.75pt;width:48.6pt;"
    '    X += " height:44.2pt;z-index:-2' wrapcoords='-257 0 -257 21300 21600 21300 21600 0 -257 0'>"
    '    X += " <v:imagedata src='" & s & "imgd.png' o:title=''/>"
    '    X += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2049' type='#_x0000_t75'"
    '    X += " style='position:absolute;left:0;text-align:left;margin-left:178.2pt;"
    '    X += " margin-top:5.75pt;width:55.2pt;height:44.2pt;z-index:-4' wrapcoords='-237 0 -237 21375 21600 21375 21600 0 -237 0'"
    '    X += " o:allowoverlap='f'>"
    '    X += " <v:imagedata src='" & s & "imgb.png' o:title=''/>"
    '    X += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2050' type='#_x0000_t75'"
    '    X += " style='position:absolute;left:0;text-align:left;margin-left:251.4pt;"
    '    X += " margin-top:7.35pt;width:49.2pt;height:40.6pt;z-index:-3' wrapcoords='-304 0 -304 21375 21600 21375 21600 0 -304 0'>"
    '    X += " <v:imagedata src='" & s & "imgc.gif' o:title=''/>"
    '    X += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2052' type='#_x0000_t75'"
    '    X += " style='position:absolute;left:0;text-align:left;margin-left:97.8pt;"
    '    X += " margin-top:7.35pt;width:60pt;height:44.2pt;z-index:-1' wrapcoords='-225 0 -225 21373 21600 21373 21600 0 -225 0'"
    '    X += " o:allowoverlap='f'>"
    '    X += " <v:imagedata src='" & s & "imga.png' o:title=''/>"
    '    X += "</v:shape><![endif]--></span></p>"
    '    X += "</div>"
    '    X += "</div>"
    '    X += "</body>"
    '    X += "</html>"
    'End Sub

    Public Sub Isi_APC()
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) 'NO APC TAHUN HADAPAN', case jpa3.apc_no when 0 then 'RET ' + cast(jpa3.apc_tahun as varchar(4)) end as 'PGKL. NAMA'  from tmp_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and ta.id_pg='" & Session("Id_PG") & "' left outer join jt_penuh_apc jpa on ta.nokp = jpa.nokp and jpa.apc_tahun = year(getdate()) and jpa.ret = 0 and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on ta.nokp = jpa2.nokp and jpa2.apc_tahun = year(getdate())+1 and jpa2.ret = 0  and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on ta.nokp = jpa3.nokp and jpa3.ret=1 and jpa3.apc_tahun=year(getdate()) order by nama"
        Tb = "tmp_apc"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Public Sub Padam_APC()
        Dim SQL As String = ""
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        SQL = "delete from tmp_apc where id_pg = '" & Session("Id_PG") & "'"
        If SQL = "" Then Exit Sub
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
        Catch ex As Exception
            Cn.Close()
            Msg(Me, ex.Message)
        End Try
    End Sub

    Public Sub Memori()
        '***********************************
        '  Kena Tambah Variable untuk Id_PG
        '***********************************
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) 'NO APC TAHUN HADAPAN', case jpa3.apc_no when 0 then 'RET ' + cast(jpa3.apc_tahun as varchar(4)) end as 'PGKL. NAMA'  from tmp_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and ta.id_pg='" & Session("Id_PG") & "' left outer join jt_penuh_apc jpa on ta.nokp = jpa.nokp and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) and jpa.ret = 0 and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on ta.nokp = jpa2.nokp and jpa2.apc_tahun = year(getdate())+1 and jpa2.ret = 0 and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on ta.nokp = jpa3.nokp and jpa3.ret=1 and jpa3.apc_tahun=year(getdate()) order by nama"
        Tb = "tmp_apc"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'If IsPostBack Then Exit Sub
        Memori()
        'Load year options 15072016 -OSH
        'Isi_Tahun()

    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(30)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        If Cb_Tpt_Amalan.SelectedIndex < 1 Then Cb_Tpt_Amalan.Focus() : Exit Sub
        If Tx_Tkh.Text = "" Then Tx_Tkh.Focus() : Exit Sub
        If Tx_Amaun.Text.Trim = "" Then Tx_Amaun.Text = "0"
        Dim i As Int16, NoKP As String = "", SQL As String = ""

        For i = 0 To Gd.Rows.Count - 1
            'If Cb_Tahun.SelectedValue = "a" Then If Gd.Rows.Item(i).Cells(5).Text = "&nbsp;" Then  Else Msg(Me, "Sila pastikan tiada rekod dengan No. APC Tahun Semasa!") : Exit Sub
            'If Cb_Tahun.SelectedValue = "b" Then If Gd.Rows.Item(i).Cells(6).Text = "&nbsp;" Then  Else Msg(Me, "Sila pastikan tiada rekod dengan No. APC Tahun Hadapan!") : Exit Sub
            ' Comment Original 29032017 -OSH 
            If Cb_Tahun.SelectedIndex = 0 Then If Gd.Rows.Item(i).Cells(5).Text = "&nbsp;" Then  Else Msg(Me, "Sila pastikan tiada rekod dengan No. APC Tahun Semasa!") : Exit Sub
            If Cb_Tahun.SelectedIndex = 1 Then If Gd.Rows.Item(i).Cells(6).Text = "&nbsp;" Then  Else Msg(Me, "Sila pastikan tiada rekod dengan No. APC Tahun Hadapan!") : Exit Sub
        Next

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

       

        'If Cb_Tpt_Amalan.SelectedIndex < 1 Then Cb_Tpt_Amalan.Focus() : Exit Sub
        'If Tx_Tkh.Text = "" Then Tx_Tkh.Focus() : Exit Sub
        'If Tx_Amaun.Text.Trim = "" Then Tx_Amaun.Text = "0"
        'Dim i As Int16, NoKP As String = "", SQL As String = ""

        'For i = 0 To Gd.Rows.Count - 1
        '    If Cb_Tahun.SelectedIndex = 0 Then If Gd.Rows.Item(i).Cells(5).Text = "&nbsp;" Then  Else Msg(Me, "Sila pastikan tiada rekod dengan No. APC Tahun Semasa!") : Exit Sub
        '    If Cb_Tahun.SelectedIndex = 1 Then If Gd.Rows.Item(i).Cells(6).Text = "&nbsp;" Then  Else Msg(Me, "Sila pastikan tiada rekod dengan No. APC Tahun Hadapan!") : Exit Sub
        'Next

        'Try
        '    ' ProsesAPC(ByVal D As Integer,
        '    ' ByVal T As String,
        '    ' ByVal R As String, 
        '    'ByVal AD As Date, 
        '    'ByVal RD As Date, 
        '    'ByVal M As Integer,
        '    ' ByVal PD As Date,
        '    ' ByVal PR As String,
        '    ' ByVal PM As Integer,
        '    ' ByVal W As Integer,
        '    ' ByVal I As String)


        'Catch ex As Exception
        '    Msg(Me, ex.Message)
        '    Memori()

        Try
            'Fixing Select Year Process 29032017 - OSH 
            'If Cb_Tahun.SelectedValue = "a" Then
            'Comment Original 29032017 -OSH
            If Cb_Tahun.SelectedIndex = 0 Then
                For i = 0 To Gd.Rows.Count - 1
                    If Left(Gd.Rows.Item(i).Cells(4).Text.Trim, 2) = "JB" Then
                        'Comment Ori - OSH 18012013
                        'SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh) select " & _
                        '        "1," & _
                        '        "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                        '        "(select year(getdate()))," & _
                        '        "(select jb+1 from pn_noapc where tahun = year(getdate()))," & _
                        '        "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                        '        "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                        '        "'" & Tx_NoResit.Text & "'," & _
                        '        "" & CInt(Tx_Amaun.Text) & "," & _
                        '        "null," & _
                        '        "null," & _
                        '        "null," & _
                        '        "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                        '        "0," & _
                        '        "'" & Session("Id_PG") & "', getdate(); "

                        ' RN Current Year
                        'improvement apc counter per person - OSH 18012013
                        SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh, bil_apc) select " & _
                                "1," & _
                                "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                                "(select year(getdate()))," & _
                                "(select jb+1 from pn_noapc where tahun = year(getdate()))," & _
                                "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                                "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                                "'" & Tx_NoResit.Text & "'," & _
                                "" & CInt(Tx_Amaun.Text) & "," & _
                                "null," & _
                                "null," & _
                                "null," & _
                                "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                                "0," & _
                                "'" & Session("Id_PG") & "', " & _
                                " getdate()," & _
                                " (select count (nokp) from jt_penuh_apc where ret =0 and nokp= '" & Gd.Rows.Item(i).Cells(3).Text.Trim & "')"

                        SQL += "update pn_noapc set jb=jb+1 where tahun = year(getdate()); "
                        NoKP += "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "',"
                    ElseIf Left(Gd.Rows.Item(i).Cells(4).Text.Trim, 2) = "JM" Then
                        'Comment Ori - OSH 18012013
                        'SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh) select " & _
                        '        "2," & _
                        '        "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                        '        "(select year(getdate()))," & _
                        '        "(select jm+1 from pn_noapc where tahun = year(getdate()))," & _
                        '        "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                        '        "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                        '        "'" & Tx_NoResit.Text & "'," & _
                        '        "" & CInt(Tx_Amaun.Text) & "," & _
                        '        "null," & _
                        '        "null," & _
                        '        "null," & _
                        '        "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                        '        "0," & _
                        '        "'" & Session("Id_PG") & "', getdate(); "

                        ' CM Current Year
                        'improvement apc counter per person - OSH 18012013
                        SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh, bil_apc) select " & _
                        "2," & _
                        "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                        "(select year(getdate()))," & _
                        "(select jm+1 from pn_noapc where tahun = year(getdate()))," & _
                        "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                        "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                        "'" & Tx_NoResit.Text & "'," & _
                        "" & CInt(Tx_Amaun.Text) & "," & _
                        "null," & _
                        "null," & _
                        "null," & _
                        "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                        "0," & _
                        "'" & Session("Id_PG") & "', " & _
                        " getdate()," & _
                        " (select count (nokp) from jt_penuh_apc where ret = 0 and nokp= '" & Gd.Rows.Item(i).Cells(3).Text.Trim & "');"

                        SQL += "update pn_noapc set jm=jm+1 where tahun = year(getdate()); "
                        NoKP += "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "',"
                    ElseIf Left(Gd.Rows.Item(i).Cells(4).Text.Trim, 2) = "PJ" Then
                        'Comment Ori - OSH 18012013
                        'SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh) select " & _
                        '        "3," & _
                        '        "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                        '        "(select year(getdate()))," & _
                        '        "(select jm+1 from pn_noapc where tahun = year(getdate()))," & _
                        '        "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                        '        "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                        '        "'" & Tx_NoResit.Text & "'," & _
                        '        "" & CInt(Tx_Amaun.Text) & "," & _
                        '        "null," & _
                        '        "null," & _
                        '        "null," & _
                        '        "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                        '        "0," & _
                        '        "'" & Session("Id_PG") & "', getdate(); "

                        ' AN Current Year
                        'improvement apc counter per person - OSH 18012013
                        SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh, bil_apc) select " & _
                             "3," & _
                             "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                             "(select year(getdate()))," & _
                             "(select jm+1 from pn_noapc where tahun = year(getdate()))," & _
                             "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                             "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                             "'" & Tx_NoResit.Text & "'," & _
                             "" & CInt(Tx_Amaun.Text) & "," & _
                             "null," & _
                             "null," & _
                             "null," & _
                             "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                             "0," & _
                             "'" & Session("Id_PG") & "' , " & _
                             " getdate()," & _
                           " (select count (nokp) from jt_penuh_apc where ret = 0 and nokp= '" & Gd.Rows.Item(i).Cells(3).Text.Trim & "');"

                        SQL += "update pn_noapc set jm=jm+1 where tahun = year(getdate()); "
                        NoKP += "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "',"
                    End If
                Next
            Else
                For i = 0 To Gd.Rows.Count - 1
                    If Left(Gd.Rows.Item(i).Cells(4).Text.Trim, 2) = "JB" Then
                        'Comment Ori - OSH 18012013
                        'SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh) select " & _
                        '       "1," & _
                        '       "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                        '       "(select year(getdate())+1)," & _
                        '       "(select jb+1 from pn_noapc where tahun = year(getdate())+1)," & _
                        '       "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                        '       "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                        '       "'" & Tx_NoResit.Text & "'," & _
                        '       "" & CInt(Tx_Amaun.Text) & "," & _
                        '       "null," & _
                        '       "null," & _
                        '       "null," & _
                        '       "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                        '       "0," & _
                        '       "'" & Session("Id_PG") & "', getdate(); "

                        'RN Future Year
                        'improvement apc counter per person - OSH 18012013
                        SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh, bil_apc) select " & _
                              "1," & _
                              "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                              "(select year(getdate())+1)," & _
                              "(select jb+1 from pn_noapc where tahun = year(getdate())+1)," & _
                              "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                              "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                              "'" & Tx_NoResit.Text & "'," & _
                              "" & CInt(Tx_Amaun.Text) & "," & _
                              "null," & _
                              "null," & _
                              "null," & _
                              "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                              "0," & _
                              "'" & Session("Id_PG") & "', " & _
                              " getdate()," & _
                              " (select count (nokp) from jt_penuh_apc where ret = 0 and nokp= '" & Gd.Rows.Item(i).Cells(3).Text.Trim & "');"


                        SQL += "update pn_noapc set jb=jb+1 where tahun = year(getdate())+1; "
                        NoKP += "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "',"
                    ElseIf Left(Gd.Rows.Item(i).Cells(4).Text.Trim, 2) = "JM" Then
                        'Comment Ori - OSH 18012013
                        'SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh) select " & _
                        '       "2," & _
                        '       "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                        '       "(select year(getdate())+1)," & _
                        '       "(select jm+1 from pn_noapc where tahun = year(getdate())+1)," & _
                        '       "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                        '       "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                        '       "'" & Tx_NoResit.Text & "'," & _
                        '       "" & CInt(Tx_Amaun.Text) & "," & _
                        '       "null," & _
                        '       "null," & _
                        '       "null," & _
                        '       "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                        '       "0," & _
                        '       "'" & Session("Id_PG") & "', getdate(); "

                        'CN Future Year
                        'improvement apc counter per person - OSH 18012013
                        SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh, bil_apc) select " & _
                               "2," & _
                               "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                               "(select year(getdate())+1)," & _
                               "(select jm+1 from pn_noapc where tahun = year(getdate())+1)," & _
                               "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                               "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                               "'" & Tx_NoResit.Text & "'," & _
                               "" & CInt(Tx_Amaun.Text) & "," & _
                               "null," & _
                               "null," & _
                               "null," & _
                               "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                               "0," & _
                               "'" & Session("Id_PG") & "', " & _
                               " getdate()," & _
                               " (select count (nokp) from jt_penuh_apc where ret = 0 and nokp= '" & Gd.Rows.Item(i).Cells(3).Text.Trim & "');"

                        SQL += "update pn_noapc set jm=jm+1 where tahun = year(getdate())+1; "

                        NoKP += "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "',"
                    ElseIf Left(Gd.Rows.Item(i).Cells(4).Text.Trim, 2) = "PJ" Then
                        'Comment Ori - OSH 18012013
                        'SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh) select " & _
                        '        "3," & _
                        '        "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                        '        "(select year(getdate())+1)," & _
                        '        "(select jm+1 from pn_noapc where tahun = year(getdate())+1)," & _
                        '        "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                        '        "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                        '        "'" & Tx_NoResit.Text & "'," & _
                        '        "" & CInt(Tx_Amaun.Text) & "," & _
                        '        "null," & _
                        '        "null," & _
                        '        "null," & _
                        '        "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                        '        "0," & _
                        '        "'" & Session("Id_PG") & "', getdate(); "

                        'AN Future Year
                        'improvement apc counter per person - OSH 18012013
                        SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret, log_id, log_tkh, bil_apc) select " & _
                             "3," & _
                             "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "'," & _
                             "(select year(getdate())+1)," & _
                             "(select jm+1 from pn_noapc where tahun = year(getdate())+1)," & _
                             "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
                             "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                             "'" & Tx_NoResit.Text & "'," & _
                             "" & CInt(Tx_Amaun.Text) & "," & _
                             "null," & _
                             "null," & _
                             "null," & _
                             "" & Cb_Tpt_Amalan.SelectedValue & "," & _
                             "0," & _
                             "'" & Session("Id_PG") & "', " & _
                             " getdate()," & _
                             " (select count (nokp) from jt_penuh_apc where ret = 0 and nokp= '" & Gd.Rows.Item(i).Cells(3).Text.Trim & "');"

                        SQL += "update pn_noapc set jm=jm+1 where tahun = year(getdate())+1; "
                        NoKP += "'" & Gd.Rows.Item(i).Cells(3).Text.Trim & "',"
                    End If
                Next
            End If
            Session("NoKP_APC") = "(" + NoKP + "'~~~')"
            'Add Timeout Infinate 14082013-OSH
            Cmd.CommandTimeout = "0"
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            If SQL = "" Then Exit Sub
            Cn.Close()

            'CariAPC(ByVal X As Integer, ByVal K As Integer, ByVal I As String, ByVal P As Char)


            Isi_APC()
            If Not chk1.Checked Then Padam_APC()
            cmdHantar.Visible = False
            Gd.Focus()
            Msg(Me, "Rekod Telah Dikemaskini...")
            NoKP_APC = Session("NoKP_APC")
            Response.Redirect("P3_APC_Mesej.aspx")

        Catch ex As Exception
            Cn.Close()
            Msg(Me, ex.Message)
            Memori()
        End Try
    End Sub

    Protected Sub Cb_Tpt_Amalan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Tpt_Amalan.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Comment ori 08012012 -OSH
        'Cmd.CommandText = "select * from pn_tpt_amalan where id_amalan = '" & Cb_Tpt_Amalan.SelectedValue & "'"

        'Improvement 08012012- add active status -OSH
        Cmd.CommandText = "select * from pn_tpt_amalan where id_amalan = '" & Cb_Tpt_Amalan.SelectedValue & "' and Status_Rekod ='1'"

        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr("alamat")) Then Tx_Amalan_Alamat.Text = Rdr("alamat")
            If Not IsDBNull(Rdr("poskod")) Then Tx_Amalan_Poskod.Text = Rdr("poskod")
            If Not IsDBNull(Rdr("bandar")) Then Tx_Amalan_Bandar.Text = Rdr("bandar")
            'If Not IsDBNull(Rdr("dc_negeri")) Then Tx_Amalan_Negeri.Text = Rdr("dc_negeri")
            If Not IsDBNull(Rdr("tel")) Then Tx_Amalan_Tel.Text = Rdr("tel")
            If Not IsDBNull(Rdr("fax")) Then Tx_Amalan_Fax.Text = Rdr("fax")
            'If Not IsDBNull(Rdr("sekt")) Then Tx_Amalan_Sektor.Text = Rdr("sekt")
        End If
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Cb_Sektor_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sektor.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cb_Negeri.Items.Clear()
        Cb_Negeri.Items.Add("")
        Cmd.CommandText = "select id_negeri, dc_negeri from pn_negeri where id_negeri in (select distinct negeri from pn_tpt_amalan where sektor = " & Cb_Sektor.SelectedValue & ") order by dc_negeri"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Cb_Negeri.Items.Add(Rdr(1))
            Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = Rdr(0)
        End While
        Rdr.Close()
        Cn.Close()

        Tx_Amalan_Alamat.Text = ""
        Tx_Amalan_Poskod.Text = ""
        Tx_Amalan_Bandar.Text = ""
        Tx_Amalan_Tel.Text = ""
        Tx_Amalan_Fax.Text = ""
        Cb_Tpt_Amalan.Items.Clear()
    End Sub

    Protected Sub Cb_Negeri_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Negeri.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cb_Tpt_Amalan.Items.Clear()
        Cb_Tpt_Amalan.Items.Add("")
        'comment ori 08012013 -OSH 
        'Cmd.CommandText = "select id_amalan, dc_amalan from pn_tpt_amalan where sektor = " & Cb_Sektor.SelectedValue & " and negeri = " & Cb_Negeri.SelectedValue & " order by dc_amalan"

        'Improvement sort by active records only 08012013 -OSH
        Cmd.CommandText = "select id_amalan, dc_amalan from pn_tpt_amalan where status_rekod='1' and sektor = " & Cb_Sektor.SelectedValue & " and negeri = " & Cb_Negeri.SelectedValue & " order by dc_amalan"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Cb_Tpt_Amalan.Items.Add(Rdr(1))
            Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(0)
        End While
        Rdr.Close()
        Cn.Close()
        Tx_Amalan_Alamat.Text = ""
        Tx_Amalan_Poskod.Text = ""
        Tx_Amalan_Bandar.Text = ""
        Tx_Amalan_Tel.Text = ""
        Tx_Amalan_Fax.Text = ""
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "delete from tmp_apc where nokp = '" & Gd.SelectedRow.Cells(3).Text & "'"
        Cmd.ExecuteNonQuery()
        Memori()
    End Sub

    Protected Sub cmdxls_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdxls.Click
        'GridViewExportUtil.Export("Laporan.xls", Gd)
        Dim x As String = ""
        'x+=header_surat(1)
        'x = "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word'>"
        x += "<html><head>"
        x += "</head><body><div>"

        x += "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        x += "<tr>"
        x += "<td style='vertical-align: middle; text-align: center;'>#</td>"
        x += "<td style='vertical-align: middle; text-align: center;'>NAMA</td>"
        x += "<td style='vertical-align: middle; text-align: center;'>NO. KP</td>"
        x += "<td style='vertical-align: middle; text-align: center;'>NO. PD</td>"
        x += "<td style='vertical-align: middle; text-align: center;'>NO. APC TAHUN SEMASA</td>"
        x += "<td style='vertical-align: middle; text-align: center;'>NO. APC TAHUN HADAPAN</td>"
        x += "<td style='vertical-align: middle; text-align: center;'>PGKL. NAMA</td>"
        x += "</tr>"
        Dim i As Int16
        For i = 0 To Gd.Rows.Count - 1
            x += "<tr>"
            x += "<td style='vertical-align: middle; text-align: right;'>" & Gd.Rows.Item(i).Cells(1).Text & "</td>"
            x += "<td style='vertical-align: middle; text-align: left;'>" & Gd.Rows.Item(i).Cells(2).Text & "</td>"
            x += "<td style='vertical-align: middle; text-align: right;'>=text(" & Gd.Rows.Item(i).Cells(3).Text & ",""######000000"")</td>"
            x += "<td style='vertical-align: middle; text-align: right;'>" & Gd.Rows.Item(i).Cells(4).Text & "</td>"
            x += "<td style='vertical-align: middle; text-align: right;'>" & Gd.Rows.Item(i).Cells(5).Text & "</td>"
            x += "<td style='vertical-align: middle; text-align: right;'>" & Gd.Rows.Item(i).Cells(6).Text & "</td>"
            x += "<td style='vertical-align: middle; text-align: right;'>" & Gd.Rows.Item(i).Cells(7).Text & "</td>"
            x += "</tr>"
        Next
        x += "</table></div></body></html>"

        Dim fileName As String = "Laporan.xls"
        Response.Clear()
        Response.Buffer = True
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Protected Sub Cb_Tahun_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Tahun.SelectedIndexChanged
        chk1.Checked = False
        If Cb_Tahun.SelectedIndex = 1 Then chk1.Visible = False Else chk1.Visible = True
    End Sub

    'Comment for future improvement 29032017 - OSH
    'Public Sub ProsesAPC(ByVal D As Integer, ByVal T As String, ByVal R As String, ByVal AD As Date, ByVal RD As Date, ByVal M As Integer, ByVal PD As Date, ByVal PR As String, ByVal PM As Integer, ByVal W As Integer, ByVal I As String)
    '    'Connection 
    '    Dim Cn As New SqlConnection(ServerId_SQL)

    '    'SQL command set to Do_APC_Process stored procedure
    '    Dim cmdAPC As SqlCommand = New SqlCommand("Do_APC_Process", Cn)

    '    ' Set sql command as store procedure type
    '    cmdAPC.CommandType = CommandType.StoredProcedure

    '    'assign input and output parameters  variable as  store procedure varibles  
    '    Dim JDaftar As SqlParameter = cmdAPC.Parameters.Add("@j_daftar", SqlDbType.Int)
    '    JDaftar.Direction = ParameterDirection.Input
    '    Dim JTahun As SqlParameter = cmdAPC.Parameters.Add("@Type_Year", SqlDbType.Char, 1)
    '    JTahun.Direction = ParameterDirection.Input
    '    Dim Resit As SqlParameter = cmdAPC.Parameters.Add("@apc_noresit", SqlDbType.VarChar, 12)
    '    Resit.Direction = ParameterDirection.Input
    '    Dim TkhAPC As SqlParameter = cmdAPC.Parameters.Add("@apc_date ", SqlDbType.Date)
    '    TkhAPC.Direction = ParameterDirection.Input
    '    Dim TkhResit As SqlParameter = cmdAPC.Parameters.Add("@apc_resit_date ", SqlDbType.Date)
    '    TkhResit.Direction = ParameterDirection.Input
    '    Dim AMT As SqlParameter = cmdAPC.Parameters.Add("@apc_amount", SqlDbType.SmallInt)
    '    AMT.Direction = ParameterDirection.Input
    '    Dim TkhDenda As SqlParameter = cmdAPC.Parameters.Add("@pena_date", SqlDbType.Date)
    '    TkhDenda.Direction = ParameterDirection.Input
    '    Dim ResitDenda As SqlParameter = cmdAPC.Parameters.Add("@pena_noresit", SqlDbType.VarChar, 12)
    '    ResitDenda.Direction = ParameterDirection.Input
    '    Dim AMTDenda As SqlParameter = cmdAPC.Parameters.Add("@pena_amount", SqlDbType.SmallInt)
    '    AMTDenda.Direction = ParameterDirection.Input
    '    Dim Kerja As SqlParameter = cmdAPC.Parameters.Add("@work_id", SqlDbType.Int)
    '    Kerja.Direction = ParameterDirection.Input
    '    Dim IdProses As SqlParameter = cmdAPC.Parameters.Add("@id_pg", SqlDbType.VarChar, 14)
    '    IdProses.Direction = ParameterDirection.Input
    '    Dim Row As SqlParameter = cmdAPC.Parameters.Add("@RowCount", SqlDbType.Int)
    '    Row.Direction = ParameterDirection.Output


    '    'Assign value to paramaters variables
    '    JDaftar.Value = D
    '    JTahun.Value = T
    '    Resit.Value = R
    '    TkhAPC.Value = AD
    '    TkhResit.Value = RD
    '    AMT.Value = M
    '    TkhDenda.Value = PD
    '    ResitDenda.Value = PR
    '    AMTDenda.Value = PM
    '    Kerja.Value = W
    '    IdProses.Value = I

    '    Cn.Close()
    'End Sub


    Public Sub Isi_Tahun()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select desc_apc, id_apc from pn_apc_type order by id_apc asc"
        Rdr = Cmd.ExecuteReader()
        Cb_Tahun.Items.Clear()
        Cb_Tahun.Items.Add("")
        While Rdr.Read
            Cb_Tahun.Items.Add(Rdr(0))
            Cb_Tahun.Items.Item(Cb_Tahun.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub
End Class