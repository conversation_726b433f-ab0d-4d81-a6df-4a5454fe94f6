﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_APC_Jawatan
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 5 Step -1
            Cb_Tahun.Items.Add(i)
        Next

    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim L, P, X, T, T1, T2, T3, T4, T5, T6 As Long
        <PERSON>, Tajuk2 As String

        Tajuk = "Laporan Statistik Pengeluaran APC Pada " & tahun
        <PERSON>juk2 = "mengikut <PERSON>ege<PERSON>, Jawatan, dan <PERSON>"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='20' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='20' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: left;'>NEGERI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JURURAWAT BERDAFTAR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SISTER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>MATRON</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PENGAJAR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JK MENTAL</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Header += "<tr style='vertical-align: middle; text-align: center;'>"
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'NEGERI
        Cmd.CommandText = "SELECT Id_NEGERI, Dc_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "Gred")
        Rdr.Close()

        T1 = 0 : T2 = 0 : T3 = 0 : T4 = 0 : T5 = 0 : T6 = 0
        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td>" + dr.Item(1) + "</td> "

            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=1 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=1 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            T = T + L + P + X : T1 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=2 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=2 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            T = T + L + P + X : T2 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=3 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=3 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=3)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            T = T + L + P + X : T3 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=4 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=4 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=4)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            T = T + L + P + X : T4 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=5 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=5 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=5)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            T = T + L + P + X : T5 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=0 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=0 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gelaran_jwtn=0)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            T = T + L + P + X : T6 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"
            Header += "    <td>" & T & "</td>"

            Header += "</tr>"
        Next
        Header += "</tr>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T1 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T2 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T3 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T4 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T5 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T6 & "</td>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>" & T1 + T2 + T3 + T4 + T5 + T6 & "</td>"
        Header += "</tr>"

        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()

    End Sub


End Class