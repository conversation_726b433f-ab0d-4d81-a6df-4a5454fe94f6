@echo off
echo Testing PN_AdminPasswordManager page access...
echo.

REM Check if the key files exist
echo Checking file structure:
if exist "PN_AdminPasswordManager.aspx" (
    echo ✓ ASPX file exists
) else (
    echo ✗ ASPX file missing
    goto :error
)

if exist "PN_AdminPasswordManager.aspx.vb" (
    echo ✓ Code-behind exists
) else (
    echo ✗ Code-behind missing
    goto :error
)

if exist "PN_AdminPasswordManager.aspx.designer.vb" (
    echo ✓ Designer file exists
) else (
    echo ✗ Designer file missing
    goto :error
)

echo.
echo Checking page directive:
findstr /C:"Inherits=\"PN_AdminPasswordManager\"" PN_AdminPasswordManager.aspx >nul
if %ERRORLEVEL%==0 (
    echo ✓ Page directive is correct
) else (
    echo ✗ Page directive issue
    goto :error
)

echo.
echo Checking class declaration:
findstr /C:"Partial Public Class PN_AdminPasswordManager" PN_AdminPasswordManager.aspx.vb >nul
if %ERRORLEVEL%==0 (
    echo ✓ Class declaration found
) else (
    echo ✗ Class declaration issue
    goto :error
)

echo.
echo ===============================================
echo ✓ ALL CHECKS PASSED
echo ===============================================
echo.
echo The PN_AdminPasswordManager page should now work correctly.
echo.
echo Next steps:
echo 1. Start your web application
echo 2. Navigate to: /PN_AdminPasswordManager.aspx
echo 3. The page should load without parser errors
echo.
goto :end

:error
echo.
echo ===============================================
echo ✗ ISSUES DETECTED
echo ===============================================
echo.
echo Please check the files and try again.
echo.

:end
pause
