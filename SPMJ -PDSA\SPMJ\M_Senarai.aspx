﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Majikan.Master" CodeBehind="M_Senarai.aspx.vb" Inherits="SPMJ.WebForm32" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style3
        {
            height: 23px;
            width: 600px;
        }
        .style4
        {
            width: 600px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td width="=" rowspan="5">&nbsp;</td>
            <td class="style3"></td>
            <td rowspan="5">&nbsp;</td>
        </tr>
        <tr>
            <td class="style4">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                </td>
        </tr>
        <tr>
            <td class="style3" align="center" bgcolor="#7EA851" 
                style="font-family: Arial; font-size: 8pt; font-weight: bolder; color: #FFFFFF;">
                Senarai Jururawat</td>
        </tr>
        <tr>
            <td class="style4">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="600px" GridLines="Horizontal" BorderColor="#719548">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="#F4F4F4" Height="21px" />
                    <Columns>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="False" />
                    <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td>
        </tr>
    
    
        <tr>
            <td class="style4">
                <br />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
