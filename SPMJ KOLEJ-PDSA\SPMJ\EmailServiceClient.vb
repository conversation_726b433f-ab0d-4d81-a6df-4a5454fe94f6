Imports System.Net
Imports System.IO
Imports System.Text
Imports System.Web.Script.Serialization
Imports System.Collections.Generic

''' <summary>
''' Client for communicating with the .NET 9 Email Microservice
''' Provides email functionality for .NET 3.5 KOLEJ-PDSA application
''' </summary>
Public Class EmailServiceClient
    Private ReadOnly _baseUrl As String
    Private ReadOnly _timeout As Integer = 30000 ' 30 seconds
    Private _apiKey As String = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
    
    Public Sub New(baseUrl As String)
        _baseUrl = baseUrl.TrimEnd("/"c)
        
        ' Only enable SSL for HTTPS connections (.NET 3.5 compatible)
        If _baseUrl.StartsWith("https://", StringComparison.OrdinalIgnoreCase) Then
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 Or SecurityProtocolType.Tls
        End If
          ' For localhost connections, bypass certificate validation if needed
        If _baseUrl.Contains("localhost") OrElse _baseUrl.Contains("127.0.0.1") Then
            ServicePointManager.ServerCertificateValidationCallback = Function(sender As Object, certificate As Object, chain As Object, sslPolicyErrors As Object) True
        End If
    End Sub
    
    ''' <summary>
    ''' Set API key for authentication
    ''' </summary>
    Public Sub SetApiKey(apiKey As String)
        If Not String.IsNullOrEmpty(apiKey) Then
            _apiKey = apiKey
        End If
    End Sub

#Region "Health Check"
    
    ''' <summary>
    ''' Check if email service is available and healthy
    ''' </summary>
    Public Function CheckHealth() As String
        Try
            System.Diagnostics.Debug.WriteLine("Checking email service health at: " & _baseUrl)
            
            Dim healthUrl As String = _baseUrl & "/health"
            System.Diagnostics.Debug.WriteLine("Health check URL: " & healthUrl)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(healthUrl), HttpWebRequest)
            request.Method = "GET"
            request.Timeout = 10000 ' 10 seconds for health check
            request.KeepAlive = False ' Disable keep-alive for .NET 3.5 compatibility
            
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
                System.Diagnostics.Debug.WriteLine("Added API key header for health check")
            End If
            
            System.Diagnostics.Debug.WriteLine("Sending health check request...")
            
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                System.Diagnostics.Debug.WriteLine("Health check response status: " & response.StatusCode.ToString())
                
                If response.StatusCode = HttpStatusCode.OK Then
                    Using reader As New StreamReader(response.GetResponseStream())
                        Dim responseContent As String = reader.ReadToEnd()
                        System.Diagnostics.Debug.WriteLine("Health check response content: " & responseContent)
                        System.Diagnostics.Debug.WriteLine("✅ Email service is healthy")
                        Return "healthy"
                    End Using
                Else
                    System.Diagnostics.Debug.WriteLine("⚠️ Email service health check failed: HTTP " & CInt(response.StatusCode).ToString())
                    Return "unhealthy: HTTP " & CInt(response.StatusCode).ToString()
                End If
            End Using            
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("❌ Email service health check web exception: " & webEx.Message)
            System.Diagnostics.Debug.WriteLine("WebException Status: " & webEx.Status.ToString())
            
            If webEx.Response IsNot Nothing Then
                Using errorResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                    System.Diagnostics.Debug.WriteLine("Error HTTP Status: " & errorResponse.StatusCode.ToString())
                    Using errorReader As New StreamReader(errorResponse.GetResponseStream())
                        Dim errorContent As String = errorReader.ReadToEnd()
                        System.Diagnostics.Debug.WriteLine("Error Response Content: " & errorContent)
                    End Using
                End Using
            End If
            
            Return "error: " & webEx.Message & " (Status: " & webEx.Status.ToString() & ")"
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Email service health check exception: " & ex.Message)
            System.Diagnostics.Debug.WriteLine("Exception Type: " & ex.GetType().Name)
            System.Diagnostics.Debug.WriteLine("Stack Trace: " & ex.StackTrace)
            Return "error: " & ex.Message
        End Try
    End Function

#End Region

#Region "OTP Methods"

    ''' <summary>
    ''' Generate OTP for user authentication
    ''' </summary>
    Public Function GenerateOTP(userId As String, userEmail As String, purpose As String) As EmailServiceResponse
        Try
            If String.IsNullOrEmpty(userEmail) Then
                System.Diagnostics.Debug.WriteLine("Cannot generate OTP - email address is empty")
                Dim errorResponse As New EmailServiceResponse()
                errorResponse.Success = False
                errorResponse.Message = "Email address is required for OTP generation"
                Return errorResponse
            End If
            
            Dim otpUrl As String = _baseUrl & "/api/otp/generate"
            System.Diagnostics.Debug.WriteLine("Generating OTP at: " & otpUrl)
            
            ' Create request data as Dictionary for .NET 3.5 compatibility
            Dim requestData As New Dictionary(Of String, Object)
            requestData.Add("UserId", userId)
            requestData.Add("Email", userEmail)
            requestData.Add("Purpose", purpose)
            
            Dim serializer As New JavaScriptSerializer()
            Dim jsonData As String = serializer.Serialize(requestData)
            
            System.Diagnostics.Debug.WriteLine("OTP Request JSON: " & jsonData)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(otpUrl), HttpWebRequest)
            request.Method = "POST"
            request.ContentType = "application/json"
            request.Timeout = _timeout
            
            ' Add API key header
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
            End If
            
            ' Send request data
            Using writer As New StreamWriter(request.GetRequestStream())
                writer.Write(jsonData)
            End Using
            
            ' Get response
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                Using reader As New StreamReader(response.GetResponseStream())
                    Dim responseText As String = reader.ReadToEnd()
                    System.Diagnostics.Debug.WriteLine("OTP Generate Response: " & responseText)
                    
                    If response.StatusCode = HttpStatusCode.OK Then
                        Dim successResponse As New EmailServiceResponse()
                        successResponse.Success = True
                        successResponse.Message = "OTP generated successfully"
                        Return successResponse
                    Else
                        Dim failResponse As New EmailServiceResponse()
                        failResponse.Success = False
                        failResponse.Message = "Failed to generate OTP: HTTP " & CInt(response.StatusCode).ToString()
                        Return failResponse
                    End If
                End Using
            End Using
            
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("❌ OTP generation web exception: " & webEx.Message)
            Dim errorResponse As New EmailServiceResponse()
            errorResponse.Success = False
            errorResponse.Message = "Network error during OTP generation: " & webEx.Message
            Return errorResponse
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ OTP generation exception: " & ex.Message)
            Dim errorResponse2 As New EmailServiceResponse()
            errorResponse2.Success = False
            errorResponse2.Message = "Error generating OTP: " & ex.Message
            Return errorResponse2
        End Try
    End Function

    ''' <summary>
    ''' Validate OTP code
    ''' </summary>
    Public Function ValidateOTP(userId As String, otpCode As String, purpose As String) As EmailServiceResponse
        Try
            If String.IsNullOrEmpty(otpCode) Then
                Dim errorResponse As New EmailServiceResponse()
                errorResponse.Success = False
                errorResponse.Message = "OTP code is required"
                Return errorResponse
            End If
            
            Dim validateUrl As String = _baseUrl & "/api/otp/validate"
            System.Diagnostics.Debug.WriteLine("Validating OTP at: " & validateUrl)
            
            ' Create request data as Dictionary for .NET 3.5 compatibility
            Dim requestData As New Dictionary(Of String, Object)
            requestData.Add("UserId", userId)
            requestData.Add("OtpCode", otpCode)
            requestData.Add("Purpose", purpose)
            
            Dim serializer As New JavaScriptSerializer()
            Dim jsonData As String = serializer.Serialize(requestData)
            
            System.Diagnostics.Debug.WriteLine("OTP Validate Request JSON: " & jsonData)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(validateUrl), HttpWebRequest)
            request.Method = "POST"
            request.ContentType = "application/json"
            request.Timeout = _timeout
            
            ' Add API key header
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
            End If
            
            ' Send request data
            Using writer As New StreamWriter(request.GetRequestStream())
                writer.Write(jsonData)
            End Using
            
            ' Get response
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                Using reader As New StreamReader(response.GetResponseStream())
                    Dim responseText As String = reader.ReadToEnd()
                    System.Diagnostics.Debug.WriteLine("OTP Validate Response: " & responseText)
                    
                    If response.StatusCode = HttpStatusCode.OK Then
                        Dim successResponse As New EmailServiceResponse()
                        successResponse.Success = True
                        successResponse.Message = "OTP validated successfully"
                        Return successResponse
                    Else
                        Dim failResponse As New EmailServiceResponse()
                        failResponse.Success = False
                        failResponse.Message = "Invalid OTP code"
                        Return failResponse
                    End If
                End Using
            End Using
            
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("❌ OTP validation web exception: " & webEx.Message)
            If webEx.Response IsNot Nothing Then
                Using errorResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                    If errorResponse.StatusCode = HttpStatusCode.BadRequest Then
                        Dim badRequestResponse As New EmailServiceResponse()
                        badRequestResponse.Success = False
                        badRequestResponse.Message = "Invalid or expired OTP code"
                        Return badRequestResponse
                    End If
                End Using
            End If
            Dim networkErrorResponse As New EmailServiceResponse()
            networkErrorResponse.Success = False
            networkErrorResponse.Message = "Network error during OTP validation: " & webEx.Message
            Return networkErrorResponse
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ OTP validation exception: " & ex.Message)
            Dim errorResponse As New EmailServiceResponse()
            errorResponse.Success = False
            errorResponse.Message = "Error validating OTP: " & ex.Message
            Return errorResponse
        End Try
    End Function

#End Region

#Region "Password Change Notification"

    ''' <summary>
    ''' Send password change notification via microservice
    ''' </summary>
    Public Function SendPasswordChangeNotification(userId As String, userEmail As String) As Boolean
        Try
            If String.IsNullOrEmpty(userEmail) Then
                System.Diagnostics.Debug.WriteLine("Cannot send notification - email address is empty")
                Return False
            End If
              
            Dim notificationUrl As String = _baseUrl & "/api/admin/password/send-notification"
            System.Diagnostics.Debug.WriteLine("Sending notification to: " & notificationUrl)
            
            ' Create request data as Dictionary for .NET 3.5 compatibility
            Dim data As New Dictionary(Of String, Object)
            data.Add("userId", userId)
            data.Add("userName", userId)
            data.Add("password", "***")
            data.Add("isTemporary", False)
            data.Add("systemName", "KOLEJ-PDSA")
            data.Add("systemUrl", "http://localhost:8080")
            data.Add("timestamp", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            data.Add("supportEmail", "<EMAIL>")
            data.Add("adminId", "SYSTEM")
            data.Add("adminName", "System Auto-Notification")
            
            Dim requestData As New Dictionary(Of String, Object)
            requestData.Add("to", userEmail)
            requestData.Add("subject", "Password Changed - SPMJ KOLEJ System")
            requestData.Add("templateType", "password_change_notification")
            requestData.Add("data", data)
            
            Dim serializer As New JavaScriptSerializer()
            Dim jsonData As String = serializer.Serialize(requestData)
            
            System.Diagnostics.Debug.WriteLine("Request JSON: " & jsonData)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(notificationUrl), HttpWebRequest)
            request.Method = "POST"
            request.ContentType = "application/json"
            request.Timeout = _timeout
            
            ' Add API key header
            If Not String.IsNullOrEmpty(_apiKey) Then
                request.Headers.Add("X-API-Key", _apiKey)
                System.Diagnostics.Debug.WriteLine("Added API key to notification request")
            Else
                System.Diagnostics.Debug.WriteLine("⚠️ No API key provided for notification request")
            End If
            
            ' Send request data
            Using writer As New StreamWriter(request.GetRequestStream())
                writer.Write(jsonData)
            End Using
            
            ' Get response
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                If response.StatusCode = HttpStatusCode.OK Then
                    System.Diagnostics.Debug.WriteLine("✅ Password change notification sent successfully")
                    Return True
                Else
                    System.Diagnostics.Debug.WriteLine("⚠️ Notification response: HTTP " & CInt(response.StatusCode).ToString())
                    Return False
                End If
            End Using
            
        Catch webEx As WebException
            System.Diagnostics.Debug.WriteLine("❌ Notification web exception: " & webEx.Message)
            If webEx.Response IsNot Nothing Then
                Using errorResponse As HttpWebResponse = CType(webEx.Response, HttpWebResponse)
                    System.Diagnostics.Debug.WriteLine("Error status: HTTP " & CInt(errorResponse.StatusCode).ToString())
                End Using
            End If
            Return False
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Notification exception: " & ex.Message)
            Return False
        End Try
    End Function

#End Region

End Class

''' <summary>
''' Response structure for email service communication (.NET 3.5 compatible)
''' </summary>
Public Class EmailServiceResponse
    Private _success As Boolean
    Private _message As String
    Private _data As Object
    
    Public Property Success() As Boolean
        Get
            Return _success
        End Get
        Set(ByVal value As Boolean)
            _success = value
        End Set
    End Property
    
    Public Property Message() As String
        Get
            Return _message
        End Get
        Set(ByVal value As String)
            _message = value
        End Set
    End Property
    
    Public Property Data() As Object
        Get
            Return _data
        End Get
        Set(ByVal value As Object)
            _data = value
        End Set
    End Property
End Class
