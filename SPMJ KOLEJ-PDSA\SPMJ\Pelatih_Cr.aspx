﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="Pelatih_Cr.aspx.vb" Inherits="SPMJ.WebForm71" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 28px;
        }
        .style2
        {
            height: 3px;
        }
        .style3
        {
            height: 10px;
        }
        .style4
        {
            width: 181px;
        }
        .style5
        {
            height: 28px;
            width: 181px;
        }
        .style6
        {
            height: 10px;
            width: 181px;
        }
        .style7
        {
            height: 3px;
            width: 181px;
        }
        .style8
        {
            width: 683px;
        }
        .style9
        {
            height: 28px;
            width: 683px;
        }
        .style10
        {
            height: 10px;
            width: 683px;
        }
        .style11
        {
            height: 3px;
            width: 683px;
        }
        .style12
        {
            width: 181px;
            height: 65px;
        }
        .style13
        {
            width: 683px;
            height: 65px;
        }
        .style14
        {
            height: 65px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; background-image: url('Image/Bg_Sgt.gif'); background-attachment: fixed;">
    
        <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style4"></td>
            <td class="style8"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style4">&nbsp;</td>
            <td class="style8">
                
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style5"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#5D7B9D" class="style9">Pinda/Semak Rekod Pelatih - Carian Rekod Pelatih</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td 
                
                style="border-width: 1px; border-right-style: solid; border-left-style: solid; border-color: #5D7B9D" 
                bgcolor="White" class="style10">&nbsp;&nbsp;</td>
            <td class="style3"></td>
        </tr>
        <tr>
            <td class="style12"></td>
            <td 
                
                style="border-width: 1px; border-right-style: solid; border-left-style: solid; border-color: #5D7B9D" 
                bgcolor="White" class="style13">
                
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="110px">KOLEJ/INSTITUSI</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kolej" runat="server" 
                            Font-Names="Arial" Font-Size="8pt" Width="500px">
                        </asp:DropDownList>
                        <br />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="110px">JENIS KURSUS</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="300px" AutoPostBack="True">
                        </asp:DropDownList>
                        <br />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="110px">SESI PENGAMBILAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Sesi" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="156px">
                        </asp:DropDownList>
                    
            </td>
            <td align="center" class="style14"></td>
        </tr>
        <tr>
            <td class="style7"></td>
            <td 
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style11">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="16px" 
            tabIndex="36" Width="110px">NAMA</asp:TextBox>
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="150px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style4">&nbsp;</td>
            <td 
                             
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style8">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px">NO. KP/PASPORT</asp:TextBox>
                <asp:TextBox ID="Tx_NoKP" runat="server" CssClass="std" Width="150px" 
                    Wrap="False"></asp:TextBox>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style4">&nbsp;</td>
            <td 
                             
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style8">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="76px"></asp:TextBox>
                <asp:Button ID="cmdHantar1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td 
                
                style="border-bottom-style: solid; border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style10">&nbsp;&nbsp;</td>
            <td class="style3"></td>
        </tr>

        <tr>
            <td class="style4">&nbsp;</td>
            <td class="style8">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>

        <tr>
            <td class="style4">&nbsp;</td>
            <td class="style8">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" Visible="False" BorderColor="#5D7B9D">
                    <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="#F7F6F3" />
                    <Columns>
                        <asp:TemplateField ShowHeader="False">
                            <ItemTemplate>
                                <asp:Button ID="Button1" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="PILIH" Width="60px" />
                            </ItemTemplate>
                            <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                            <HeaderStyle Width="65px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="True" />
                    <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td>
            <td>&nbsp;</td>
        </tr>

        <tr>
            <td class="style4">&nbsp;</td>
            <td class="style8">
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>

    </table>
    
    </div>
</asp:Content>

