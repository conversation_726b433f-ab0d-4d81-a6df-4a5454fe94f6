# Test SPMJ KOLEJ Password Change Notification Integration
Write-Host "🧪 Testing SPMJ KOLEJ Password Change Notification" -ForegroundColor Cyan

$apiKey = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
$baseUrl = "http://localhost:5000"

# Test data for password change notification
$testData = @{
    to = "<EMAIL>"
    subject = "Password Changed - SPMJ KOLEJ System"
    templateType = "password_change_notification"
    data = @{
        userId = "TEST001"
        userName = "Test User"
        password = "***"
        isTemporary = $false
        systemName = "KOLEJ-PDSA"
        systemUrl = "http://localhost:8080"
        timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        supportEmail = "<EMAIL>"
        adminId = "SYSTEM"
        adminName = "System Auto-Notification"
    }
}

# Convert to JSON
$jsonData = $testData | ConvertTo-Json -Depth 5

Write-Host "📤 Sending test notification..." -ForegroundColor Yellow
Write-Host "Endpoint: $baseUrl/api/admin/password/send-notification"
Write-Host "Data: $jsonData"

try {
    # Send POST request
    $headers = @{
        "Content-Type" = "application/json"
        "X-API-Key" = $apiKey
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/api/admin/password/send-notification" -Method Post -Body $jsonData -Headers $headers -TimeoutSec 10
    
    Write-Host "✅ Response received:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor Gray
    
    if ($response.success) {
        Write-Host "🎉 Password change notification test PASSED!" -ForegroundColor Green
    } else {
        Write-Host "❌ Password change notification test FAILED: $($response.message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails) {
        Write-Host "Error details: $($_.ErrorDetails.Message)" -ForegroundColor Gray
    }
}

Write-Host "`n🔍 Testing EmailServiceClient.vb integration..." -ForegroundColor Yellow

# Check if the VB.NET client code looks correct
$clientPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\EmailServiceClient.vb"
if (Test-Path $clientPath) {
    $content = Get-Content $clientPath -Raw
    
    if ($content -match "/api/admin/password/send-notification") {
        Write-Host "✅ EmailServiceClient endpoint: CORRECT" -ForegroundColor Green
    } else {
        Write-Host "❌ EmailServiceClient endpoint: INCORRECT" -ForegroundColor Red
    }
    
    if ($content -match 'X-API-Key') {
        Write-Host "✅ EmailServiceClient API Key: CONFIGURED" -ForegroundColor Green
    } else {
        Write-Host "❌ EmailServiceClient API Key: MISSING" -ForegroundColor Red
    }
    
    if ($content -match 'password_change_notification') {
        Write-Host "✅ EmailServiceClient template type: CONFIGURED" -ForegroundColor Green
    } else {
        Write-Host "❌ EmailServiceClient template type: MISSING" -ForegroundColor Red
    }
} else {
    Write-Host "❌ EmailServiceClient.vb not found" -ForegroundColor Red
}

Write-Host "`n✅ Integration test completed!" -ForegroundColor Cyan
