Partial Public Class TestAdminPasswordManager
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            LabelTime.Text = DateTime.Now.ToString()
            LabelSessionId.Text = Session.SessionID
            
            If Session("Id_PG") IsNot Nothing Then
                LabelUserId.Text = Session("Id_PG").ToString()
            Else
                LabelUserId.Text = "Not logged in"
            End If
            
        Catch ex As Exception
            LabelTime.Text = "Error: " & ex.Message
        End Try
    End Sub

End Class
