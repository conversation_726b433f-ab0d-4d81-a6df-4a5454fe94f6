﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P4_Cetak5.aspx.vb" Inherits="SPMJ.WebForm51" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
        .style3
        {
            height: 31px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td width="600"></td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td width="600" align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style1">&nbsp;cetak surat keputusan mesyuarat bagi 
                kelulusan tpc (imigresen)</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="16px" 
            tabIndex="36" Width="59px">JENIS TPC</asp:TextBox>
                <asp:DropDownList ID="Cb_Jenis" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Width="450px" CssClass="std">
                    <asp:ListItem Value="1">JURURAWAT TERLATIH, INSTRUKTOR KLINIKAL &amp; PENGAJAR 
                    JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT TERLATIH</asp:ListItem>
                    <asp:ListItem Value="3">INSTRUKTOR KLINIKAL &amp; PENGAJAR JURURAWAT</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style3"></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style3">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px"></asp:TextBox>
                <asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CETAK" Width="60px" />
            </td>
            <td class="style3"></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td width="600">&nbsp;</td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <br />
                        <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>