# Test Script for OTP Data Type Fix Validation
# This script validates that the Entity Framework data type fixes resolve the casting errors

Write-Host "=== SPMJ OTP Data Type Fix Validation ===" -ForegroundColor Green
Write-Host "Testing the fixes for System.InvalidCastException in OTP verification" -ForegroundColor Yellow

# Test 1: Verify Entity Framework Models
Write-Host "`n1. Checking Entity Framework Model Updates..." -ForegroundColor Cyan

$modelsFile = "d:\2024\.NET 3.5. - Q\SPMJ.EmailService\Models\DatabaseModels.cs"
if (Test-Path $modelsFile) {
    $content = Get-Content $modelsFile -Raw
    
    # Check if Status property is now byte? instead of string?
    if ($content -match "public byte\? Status \{ get; set; \}") {
        Write-Host "✓ Status property correctly updated to byte?" -ForegroundColor Green
    } else {
        Write-Host "✗ Status property still incorrect type" -ForegroundColor Red
    }
    
    # Check if Akses property is now byte? instead of string?
    if ($content -match "public byte\? Akses \{ get; set; \}") {
        Write-Host "✓ Akses property correctly updated to byte?" -ForegroundColor Green
    } else {
        Write-Host "✗ Akses property still incorrect type" -ForegroundColor Red
    }
} else {
    Write-Host "✗ DatabaseModels.cs not found" -ForegroundColor Red
}

# Test 2: Verify EF Context Configuration
Write-Host "`n2. Checking Entity Framework Context Configuration..." -ForegroundColor Cyan

$contextFile = "d:\2024\.NET 3.5. - Q\SPMJ.EmailService\Data\SPMJContext.cs"
if (Test-Path $contextFile) {
    $content = Get-Content $contextFile -Raw
    
    # Check if Status column type is configured as tinyint
    if ($content -match "Status.*HasColumnType.*tinyint") {
        Write-Host "✓ Status column type correctly configured as tinyint" -ForegroundColor Green
    } else {
        Write-Host "✗ Status column type configuration missing or incorrect" -ForegroundColor Red
    }
    
    # Check if Akses column type is configured as tinyint
    if ($content -match "Akses.*HasColumnType.*tinyint") {
        Write-Host "✓ Akses column type correctly configured as tinyint" -ForegroundColor Green
    } else {
        Write-Host "✗ Akses column type configuration missing or incorrect" -ForegroundColor Red
    }
} else {
    Write-Host "✗ SPMJContext.cs not found" -ForegroundColor Red
}

# Test 3: Verify Service Updates
Write-Host "`n3. Checking Service Status Comparisons..." -ForegroundColor Cyan

$otpServiceFile = "d:\2024\.NET 3.5. - Q\SPMJ.EmailService\Services\OtpService.cs"
if (Test-Path $otpServiceFile) {
    $content = Get-Content $otpServiceFile -Raw
    
    # Check if Status comparisons use numeric values instead of strings
    if ($content -match "Status == 1" -and $content -notmatch "Status == `"1`"") {
        Write-Host "✓ OtpService Status comparisons updated to numeric" -ForegroundColor Green
    } else {
        Write-Host "✗ OtpService still uses string comparisons" -ForegroundColor Red
    }
} else {
    Write-Host "✗ OtpService.cs not found" -ForegroundColor Red
}

$passwordServiceFile = "d:\2024\.NET 3.5. - Q\SPMJ.EmailService\Services\PasswordService.cs"
if (Test-Path $passwordServiceFile) {
    $content = Get-Content $passwordServiceFile -Raw
    
    # Check if Status comparisons use numeric values instead of strings
    if ($content -match "Status == 1" -and $content -notmatch "Status == `"1`"") {
        Write-Host "✓ PasswordService Status comparisons updated to numeric" -ForegroundColor Green
    } else {
        Write-Host "✗ PasswordService still uses string comparisons" -ForegroundColor Red
    }
} else {
    Write-Host "✗ PasswordService.cs not found" -ForegroundColor Red
}

# Test 4: Check Build Status
Write-Host "`n4. Checking Build Status..." -ForegroundColor Cyan

Push-Location "d:\2024\.NET 3.5. - Q\SPMJ.EmailService"
try {
    $buildResult = dotnet build --no-restore 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ EmailService builds successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Build failed:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Build test failed: $($_.Exception.Message)" -ForegroundColor Red
}
Pop-Location

# Test 5: Verify Main Application Fix
Write-Host "`n5. Checking Main Application OTP Page..." -ForegroundColor Cyan

$otpPageFile = "d:\2024\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\OtpVerification.aspx.vb"
if (Test-Path $otpPageFile) {
    $content = Get-Content $otpPageFile -Raw
    
    # Check if database connection uses SPMJ_Mod.ServerId
    if ($content -match "SPMJ_Mod\.ServerId") {
        Write-Host "✓ OtpVerification.aspx.vb uses correct database connection reference" -ForegroundColor Green
    } else {
        Write-Host "✗ OtpVerification.aspx.vb still has incorrect database connection" -ForegroundColor Red
    }
} else {
    Write-Host "✗ OtpVerification.aspx.vb not found" -ForegroundColor Red
}

Write-Host "`n=== Data Type Fix Validation Complete ===" -ForegroundColor Green
Write-Host "The casting error should now be resolved with these changes:" -ForegroundColor Yellow
Write-Host "• Entity models updated to use byte? for Status and Akses" -ForegroundColor White
Write-Host "• EF Context configured with correct tinyint column types" -ForegroundColor White
Write-Host "• Service comparisons updated from string to numeric" -ForegroundColor White
Write-Host "• Database connection reference corrected in main application" -ForegroundColor White

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Deploy the updated microservice to production" -ForegroundColor White
Write-Host "2. Test the complete OTP verification flow" -ForegroundColor White
Write-Host "3. Monitor for any remaining casting errors" -ForegroundColor White
