﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_StatXM.aspx.vb"
    Inherits="SPMJ.P1_StatXM" %>

<%-- <%@ Register Assembly="CrystalDecisions.Web, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
    Namespace="CrystalDecisions.Web" TagPrefix="CR" %> --%>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
        <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
            <tr>
                <td>
                </td>
                <td width="600">
                </td>
                <td>
                </td>
            </tr>
            <tr>
                <td>
                    &nbsp;
                </td>
                <td width="600">
                    <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                    </asp:ScriptManagerProxy>
                </td>
                <td>
                    &nbsp;
                </td>
            </tr>
            <tr>
                <td class="style1">
                </td>
                <td width="600" align="center" style="font-variant: small-caps; font-family: Arial;
                    font-size: 8pt; color: #FFFFFF; font-weight: bold;" bgcolor="#719548" class="style1">
                    Cetakan - STATISTIK KEPUTUSAN PEPERIKSAAN
                </td>
                <td class="style1">
                </td>
            </tr>
            <tr>
                <td>
                </td>
                <td width="600" style="border-right-style: solid; border-left-style: solid; border-right-width: 1px;
                    border-left-width: 1px; border-color: #003300" bgcolor="White">
                    &nbsp;
                </td>
                <td>
                </td>
            </tr>
            <tr>
                <td>
                    &nbsp;
                </td>
                <td width="600" style="border-right-style: solid; border-left-style: solid; border-right-width: 1px;
                    border-left-width: 1px; border-color: #003300" bgcolor="White">
                    <asp:UpdatePanel ID="UpdatePanel9" runat="server">
                        <ContentTemplate>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <asp:TextBox ID="Cb_Sbj17" runat="server" BackColor="Transparent" BorderColor="Black"
                                BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" Font-Size="8pt" Height="17px"
                                TabIndex="36" Width="120px" Enabled="False" 
                            ReadOnly="True">JENIS PENDAFTARAN</asp:TextBox>
                            <asp:DropDownList ID="Cb_Jenis" runat="server" Font-Names="Arial" Font-Size="8pt"
                                Width="200px" AutoPostBack="True">
                                <asp:ListItem></asp:ListItem>
                                <asp:ListItem Value="1">JURURAWAT BERDAFTAR (JB)</asp:ListItem>
                                <asp:ListItem Value="2">JURURAWAT MASYARAKAT (JM)</asp:ListItem>
                                <asp:ListItem Value="3">PENOLONG JURURAWAT (PJ)</asp:ListItem>
                                <asp:ListItem Value="4">KEBIDANAN I</asp:ListItem>
                            </asp:DropDownList>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </td>
                <td align="center">
                    &nbsp;
                </td>
                <tr>
                    <td>
                        &nbsp;
                    </td>
                    <td width="600" style="border-right-style: solid; border-left-style: solid; border-right-width: 1px;
                        border-left-width: 1px; border-color: #003300" bgcolor="White">
                        <asp:UpdatePanel ID="UpdatePanel10" runat="server">
                            <ContentTemplate>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:TextBox ID="Cb_Sbj19" runat="server" BackColor="Transparent" BorderColor="Black"
                                    BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" Font-Size="8pt" Height="17px"
                                    TabIndex="36" Width="120px" Enabled="False" 
                            ReadOnly="True">TARIKH KEPUTUSAN</asp:TextBox>
                                <asp:TextBox ID="Tx_Tkh" runat="server" AutoPostBack="True" CssClass="std" Width="95px"></asp:TextBox>
                                <cc1:MaskedEditExtender ID="Tx_Tkh_MaskedEditExtender" runat="server" CultureAMPMPlaceholder=""
                                    CultureCurrencySymbolPlaceholder="" CultureDateFormat="" CultureDatePlaceholder=""
                                    CultureDecimalPlaceholder="" CultureName="en-GB" CultureThousandsPlaceholder=""
                                    CultureTimePlaceholder="" Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh"
                                    UserDateFormat="DayMonthYear">
                                </cc1:MaskedEditExtender>
                                <cc1:CalendarExtender ID="Tx_Tkh_CalendarExtender" runat="server" Enabled="True"
                                    Format="dd/MM/yyyy" PopupPosition="Right" TargetControlID="Tx_Tkh">
                                </cc1:CalendarExtender>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </td>
                    <td align="center">
                        &nbsp;
                    </td>
                </tr>
                <tr>
                    <td>
                    </td>
                    <td width="600" style="border-right-style: solid; border-left-style: solid; border-right-width: 1px;
                        border-left-width: 1px; border-color: #003300" bgcolor="White">
                        &nbsp;
                    </td>
                    <td>
                    </td>
                </tr>
                <tr>
                    <td>
                        &nbsp;
                    </td>
                    <td width="600" style="border-right-style: solid; border-left-style: solid; border-right-width: 1px;
                        border-left-width: 1px; border-color: #003300" bgcolor="White">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" BorderColor="Black"
                            BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" Font-Size="8pt" Height="17px"
                            TabIndex="36" Width="157px"></asp:TextBox>
                        <asp:Button ID="cmd_Jana" runat="server" Font-Names="Arial" Font-Size="8pt" Height="20px"
                            TabIndex="3" Text="JANA" Width="80px" />
                        &nbsp;
                    </td>
                    <td>
                        &nbsp;
                    </td>
                </tr>
                <tr>
                    <td>
                        &nbsp;
                    </td>
                    <td width="600" style="border-right-style: solid; border-left-style: solid; border-right-width: 1px;
                        border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;"
                        bgcolor="White">
                        &nbsp;&nbsp;
                    </td>
                    <td>
                        &nbsp;
                    </td>
                </tr>
                <tr>
                    <td>
                        &nbsp;
                    </td>
                    <td width="600">
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                    </td>
                    <td>
                        &nbsp;
                    </td>
                </tr>
        </table>
    </div>
</asp:Content>
