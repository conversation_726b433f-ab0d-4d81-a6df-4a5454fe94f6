﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm23
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        If Cb_Status.SelectedIndex = 0 Then
            SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', 'Tpc-' + cast(nopd as varchar(6)) as 'NO. PENDAFTARAN' from jt_tpc where " & X & " order by nama"
            Tb = "jt_tpc"
        Else
            SQL = "select NAMA, NOKP as 'NO. KP/PASPORT' from tmp_tpc where " & X & " order by nama"
            Tb = "tmp_tpc"
        End If

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub

    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(1).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(30)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Session("NOKP") = Gd.SelectedRow.Cells(3).Text
        Session("PINDA") = True
        If Cb_Status.SelectedIndex = 0 Then
            'Comment Original 14122023 - OSH 
            Response.Redirect("p4_penuh.aspx")
            'Fix Button issue 14122023 - OSH 
            'Response.Redirect("p4_penuh_J.aspx")
        Else
            Response.Redirect("p4_daftar.aspx")
        End If
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" And Tx_NoPd.Text.Trim = "" Then Exit Sub
        If Cb_Status.SelectedIndex = 0 Then
            Cari("nama like '" & Tx_Nama.Text & "%' and nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%'")
        Else
            Cari("nama like '" & Tx_Nama.Text & "%' and nokp like '" & Tx_NoKP.Text & "%'")
        End If

        'Cari("nama like '" & Tx_Nama.Text & "%'")
    End Sub

    Protected Sub Cb_Sbj1_TextChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sbj1.TextChanged

    End Sub
End Class