﻿Imports System.Net
Imports System.IO

Module TestHealthCheck
    Sub Main()
        Try
            Dim baseUrl As String = "http://localhost:5000"
            
            ' Simulate the CheckHealth method
            Dim url As String = baseUrl & "/health"
            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
            request.Method = "GET"
            request.Timeout = 5000
            
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                Using reader As New StreamReader(response.GetResponseStream())
                    Dim responseText As String = reader.ReadToEnd()
                    Dim isHealthy As Boolean = responseText.Contains("healthy") AndAlso response.StatusCode = 200
                    
                    Console.WriteLine("Health Check Response: " & responseText)
                    Console.WriteLine("Contains 'healthy': " & responseText.Contains("healthy"))
                    Console.WriteLine("Status Code: " & response.StatusCode)
                    Console.WriteLine("Is Healthy: " & isHealthy)
                    
                    If isHealthy Then
                        Console.WriteLine("JSON Result: {""status"":""online"",""message"":""Email service is operational""}")
                    Else
                        Console.WriteLine("JSON Result: {""status"":""offline"",""message"":""Email service is not responding""}")
                    End If
                End Using
            End Using
            
        Catch ex As Exception
            Console.WriteLine("Error: " & ex.Message)
            Console.WriteLine("JSON Result: {""status"":""offline"",""message"":""Email service connection failed: " & ex.Message & """}")
        End Try
    End Sub
End Module
