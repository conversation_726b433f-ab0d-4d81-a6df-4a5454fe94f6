Imports System.Data.OleDb
Imports System.Text.RegularExpressions
Imports System.Configuration
Imports System.Collections.Generic
Imports System.Web.Services

''' <summary>
''' SPMJ KOLEJ Password Management - Clean Implementation
''' Compatible with .NET 3.5.1
''' </summary>
Partial Public Class PasswordManagement
    Inherits System.Web.UI.Page

    ' Email service configuration
    Private ReadOnly emailServiceUrl As String = "http://localhost:5000"
    Private ReadOnly apiKey As String = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
    Private emailClient As EmailServiceClient

#Region "Page Events"

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Security check - ensure user is logged in
        If Session("Id_PG") Is Nothing OrElse Session("Id_PG").ToString().Trim() = "" Then
            Session.Abandon()
            Response.Redirect("Login_J.aspx")
            Return
        End If

        If Not IsPostBack Then
            ' Initialize email service client
            InitializeEmailService()
            
            ' Log access
            LogPasswordPageAccess()
        End If
    End Sub

    Protected Sub btnChangePassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnChangePassword.Click
        Try
            ' Get form data
            Dim currentPassword As String = txtCurrentPassword.Text.Trim()
            Dim newPassword As String = txtNewPassword.Text.Trim()
            Dim confirmPassword As String = txtConfirmPassword.Text.Trim()
            Dim userId As String = Session("Id_PG").ToString()

            ' Validate input
            If Not ValidatePasswordInput(currentPassword, newPassword, confirmPassword) Then
                Return
            End If

            ' Verify current password
            If Not VerifyCurrentPassword(userId, currentPassword) Then
                ShowMessage("Kata laluan semasa tidak tepat.", "error")
                txtCurrentPassword.Focus()
                Return
            End If

            ' Validate password strength
            If Not ValidatePasswordStrength(newPassword, currentPassword) Then
                Return
            End If

            ' Update password
            If UpdateUserPassword(userId, newPassword) Then
                ' Send notification email
                SendPasswordChangeNotification(userId)
                
                ' Clear form
                ClearPasswordFields()
                
                ' Show success message
                ShowMessage("Kata laluan berjaya dikemaskini! Pemberitahuan telah dihantar ke e-mel anda.", "success")
                
                ' Log successful change
                LogPasswordChange(userId, True)
            Else
                ShowMessage("Ralat semasa menukar kata laluan. Sila cuba lagi.", "error")
                LogPasswordChange(userId, False)
            End If

        Catch ex As Exception
            ShowMessage("Ralat sistem: " & ex.Message, "error")
            System.Diagnostics.Debug.WriteLine("Password change error: " & ex.Message)
        End Try
    End Sub

#End Region

#Region "Validation Methods"

    ''' <summary>
    ''' Validate password input fields
    ''' </summary>
    Private Function ValidatePasswordInput(currentPassword As String, newPassword As String, confirmPassword As String) As Boolean
        If String.IsNullOrEmpty(currentPassword) Then
            ShowMessage("Sila masukkan kata laluan semasa.", "error")
            txtCurrentPassword.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(newPassword) Then
            ShowMessage("Sila masukkan kata laluan baharu.", "error")
            txtNewPassword.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(confirmPassword) Then
            ShowMessage("Sila sahkan kata laluan baharu.", "error")
            txtConfirmPassword.Focus()
            Return False
        End If

        If newPassword <> confirmPassword Then
            ShowMessage("Kata laluan baharu dan pengesahan tidak sama.", "error")
            txtConfirmPassword.Focus()
            Return False
        End If

        If newPassword = currentPassword Then
            ShowMessage("Kata laluan baharu mestilah berbeza daripada kata laluan semasa.", "error")
            txtNewPassword.Focus()
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' Validate password strength requirements
    ''' </summary>
    Private Function ValidatePasswordStrength(password As String, oldPassword As String) As Boolean
        Dim errors As New List(Of String)

        ' Check minimum length
        If password.Length < 8 Then
            errors.Add("Kata laluan mestilah sekurang-kurangnya 8 aksara")
        End If

        ' Check for uppercase letter
        If Not Regex.IsMatch(password, "[A-Z]") Then
            errors.Add("Kata laluan mesti mengandungi huruf besar (A-Z)")
        End If

        ' Check for lowercase letter
        If Not Regex.IsMatch(password, "[a-z]") Then
            errors.Add("Kata laluan mesti mengandungi huruf kecil (a-z)")
        End If

        ' Check for number
        If Not Regex.IsMatch(password, "[0-9]") Then
            errors.Add("Kata laluan mesti mengandungi nombor (0-9)")
        End If

        ' Check for special character
        If Not Regex.IsMatch(password, "[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]") Then
            errors.Add("Kata laluan mesti mengandungi aksara khas (!@#$%^&*)")
        End If

        If errors.Count > 0 Then
            ShowMessage("Kata laluan tidak memenuhi keperluan:<br/>• " & String.Join("<br/>• ", errors.ToArray()), "error")
            txtNewPassword.Focus()
            Return False
        End If

        Return True
    End Function

#End Region

#Region "Database Operations"

    ''' <summary>
    ''' Get database connection string
    ''' </summary>
    Private Function GetConnectionString() As String
        Dim connStr As String = ""

        ' Try primary connection string
        If ConfigurationManager.ConnectionStrings("DefaultConnection") IsNot Nothing Then
            connStr = ConfigurationManager.ConnectionStrings("DefaultConnection").ConnectionString
        End If

        ' Try KOLEJ connection string
        If String.IsNullOrEmpty(connStr) AndAlso ConfigurationManager.ConnectionStrings("KOLEJConnection") IsNot Nothing Then
            connStr = ConfigurationManager.ConnectionStrings("KOLEJConnection").ConnectionString
        End If

        ' Try SPMJ connection string
        If String.IsNullOrEmpty(connStr) AndAlso ConfigurationManager.ConnectionStrings("SPMJConnection") IsNot Nothing Then
            connStr = ConfigurationManager.ConnectionStrings("SPMJConnection").ConnectionString
        End If

        If String.IsNullOrEmpty(connStr) Then
            Throw New Exception("Connection string not found in web.config")
        End If

        Return connStr
    End Function

    ''' <summary>
    ''' Verify current password against database
    ''' </summary>
    Private Function VerifyCurrentPassword(userId As String, currentPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(GetConnectionString())
            connection.Open()

            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT PWD, salt, pwd_encrypted FROM kj_pengguna WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@Id_PG", userId)

                Using reader As OleDbDataReader = command.ExecuteReader()
                    If reader.Read() Then
                        Dim storedPassword As String = reader("PWD").ToString()
                        Dim salt As String = If(IsDBNull(reader("salt")), "", reader("salt").ToString())
                        Dim isEncrypted As Boolean = If(IsDBNull(reader("pwd_encrypted")), False, Convert.ToBoolean(reader("pwd_encrypted")))

                        ' Handle different password storage methods
                        If isEncrypted AndAlso Not String.IsNullOrEmpty(salt) Then
                            ' New encrypted method
                            Return PasswordHelper.VerifyPassword(currentPassword, storedPassword, salt)
                        Else
                            ' Legacy plain text or simple hash
                            Return storedPassword = currentPassword
                        End If
                    End If
                End Using
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Verify password error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try

        Return False
    End Function

    ''' <summary>
    ''' Update user password in database
    ''' </summary>
    Private Function UpdateUserPassword(userId As String, newPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(GetConnectionString())
            connection.Open()

            ' Create encrypted password
            Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(newPassword)
            Dim hashedPassword As String = passwordEntry(0)
            Dim salt As String = passwordEntry(1)

            Using command As New OleDbCommand()
                command.Connection = connection

                ' Try enhanced update first (if columns exist)
                Try
                    command.CommandText = "UPDATE kj_pengguna SET PWD = ?, salt = ?, pwd_encrypted = 1, pwd_migrated = 1, migration_date = ? WHERE Id_PG = ?"
                    command.Parameters.Clear()
                    command.Parameters.AddWithValue("@PWD", hashedPassword.Substring(0, Math.Min(15, hashedPassword.Length))) ' Handle varchar(15) limitation
                    command.Parameters.AddWithValue("@salt", hashedPassword & "|" & salt) ' Store full hash in salt field
                    command.Parameters.AddWithValue("@migration_date", DateTime.Now)
                    command.Parameters.AddWithValue("@Id_PG", userId)

                    Dim rowsAffected As Integer = command.ExecuteNonQuery()
                    Return rowsAffected > 0

                Catch ex As Exception
                    ' Fallback to basic update
                    command.Parameters.Clear()
                    command.CommandText = "UPDATE kj_pengguna SET PWD = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@PWD", newPassword) ' Store as plain text for legacy compatibility
                    command.Parameters.AddWithValue("@Id_PG", userId)

                    Dim rowsAffected As Integer = command.ExecuteNonQuery()
                    Return rowsAffected > 0
                End Try
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Update password error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Get user email for notifications
    ''' </summary>
    Private Function GetUserEmail(userId As String) As String
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(GetConnectionString())
            connection.Open()

            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT email FROM kj_pengguna WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@Id_PG", userId)

                Dim result = command.ExecuteScalar()
                If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                    Return result.ToString()
                End If
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Get user email error: " & ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try

        Return ""
    End Function

#End Region

#Region "Email Service Integration"

    ''' <summary>
    ''' Initialize email service client
    ''' </summary>
    Private Sub InitializeEmailService()
        Try
            emailClient = New EmailServiceClient(emailServiceUrl)
            emailClient.SetApiKey(apiKey)
            System.Diagnostics.Debug.WriteLine("Email service initialized: " & emailServiceUrl)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Email service initialization failed: " & ex.Message)
            emailClient = Nothing
        End Try
    End Sub

    ''' <summary>
    ''' Send password change notification
    ''' </summary>
    Private Sub SendPasswordChangeNotification(userId As String)
        Try
            If emailClient IsNot Nothing Then
                Dim userEmail As String = GetUserEmail(userId)
                If Not String.IsNullOrEmpty(userEmail) Then
                    Dim success As Boolean = emailClient.SendPasswordChangeNotification(userId, userEmail)
                    System.Diagnostics.Debug.WriteLine("Password change notification sent: " & success.ToString())
                End If
            End If
        Catch ex As Exception
            ' Don't fail password change if email fails
            System.Diagnostics.Debug.WriteLine("Email notification error: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Web method for AJAX health check
    ''' </summary>
    <WebMethod()>
    Public Shared Function CheckEmailServiceHealth() As String
        Try
            Dim testClient As New EmailServiceClient("http://localhost:5000")
            testClient.SetApiKey("SPMJ-EmailService-2024-SecureKey-MOH-Malaysia")
            
            Dim healthResult As String = testClient.CheckHealth()
            
            If healthResult.Contains("healthy") Then
                Return "{""status"":""online"",""message"":""Perkhidmatan e-mel beroperasi dengan normal""}"
            Else
                Return "{""status"":""offline"",""message"":""Perkhidmatan e-mel tidak dapat dihubungi""}"
            End If
            
        Catch ex As Exception
            Return "{""status"":""offline"",""message"":""Ralat sambungan: " & ex.Message.Replace("""", "'") & """}"
        End Try
    End Function

#End Region

#Region "Utility Methods"

    ''' <summary>
    ''' Show message to user
    ''' </summary>
    Private Sub ShowMessage(message As String, messageType As String)
        lblMessage.Text = message
        pnlMessage.Visible = True

        Select Case messageType.ToLower()
            Case "success"
                divMessage.Attributes("class") = "message-panel message-success"
            Case "error"
                divMessage.Attributes("class") = "message-panel message-error"
            Case "warning"
                divMessage.Attributes("class") = "message-panel message-warning"
            Case Else
                divMessage.Attributes("class") = "message-panel message-warning"
        End Select
    End Sub

    ''' <summary>
    ''' Clear password input fields
    ''' </summary>
    Private Sub ClearPasswordFields()
        txtCurrentPassword.Text = ""
        txtNewPassword.Text = ""
        txtConfirmPassword.Text = ""
    End Sub

    ''' <summary>
    ''' Log password page access
    ''' </summary>
    Private Sub LogPasswordPageAccess()
        Try
            Dim userId As String = Session("Id_PG").ToString()
            System.Diagnostics.Debug.WriteLine("Password page accessed by user: " & userId & " at " & DateTime.Now.ToString())
        Catch ex As Exception
            ' Silent fail for logging
        End Try
    End Sub

    ''' <summary>
    ''' Log password change attempts
    ''' </summary>
    Private Sub LogPasswordChange(userId As String, success As Boolean)
        Try
            Dim status As String = If(success, "SUCCESS", "FAILED")
            System.Diagnostics.Debug.WriteLine("Password change " & status & " for user: " & userId & " at " & DateTime.Now.ToString())
        Catch ex As Exception
            ' Silent fail for logging
        End Try
    End Sub

#End Region

End Class
