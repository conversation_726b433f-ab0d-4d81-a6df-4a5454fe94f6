@echo off
echo ============================================================
echo SPMJ Email Microservice API Key Integration Test
echo ============================================================
echo.

echo [TEST 1] Testing Health Endpoint (No API Key Required)...
curl -s -o nul -w "Status: %%{http_code}\n" http://localhost:5000/api/admin/password/health
echo.

echo [TEST 2] Testing API Without Key (Should Return 401)...
curl -s -w "Status: %%{http_code}\n" -H "Content-Type: application/json" ^
     -d "{\"to\":\"<EMAIL>\",\"subject\":\"Test\",\"templateType\":\"test\",\"data\":{\"userName\":\"Test\",\"userId\":\"TEST001\",\"password\":\"test123\",\"isTemporary\":true,\"systemUrl\":\"http://localhost:8080\",\"timestamp\":\"2025-06-15 14:30:45\",\"adminId\":\"ADMIN001\",\"adminName\":\"Test Admin\"}}" ^
     http://localhost:5000/api/admin/password/send-notification
echo.

echo [TEST 3] Testing API With Correct Key (Should Return 200 or 500)...
curl -s -w "Status: %%{http_code}\n" -H "Content-Type: application/json" ^
     -H "X-API-Key: SPMJ-EmailService-2024-SecureKey-MOH-Malaysia" ^
     -d "{\"to\":\"<EMAIL>\",\"subject\":\"Test\",\"templateType\":\"test\",\"data\":{\"userName\":\"Test\",\"userId\":\"TEST001\",\"password\":\"test123\",\"isTemporary\":true,\"systemUrl\":\"http://localhost:8080\",\"timestamp\":\"2025-06-15 14:30:45\",\"adminId\":\"ADMIN001\",\"adminName\":\"Test Admin\"}}" ^
     http://localhost:5000/api/admin/password/send-notification
echo.

echo [TEST 4] Testing Email Validation With Key...
curl -s -w "Status: %%{http_code}\n" -H "Content-Type: application/json" ^
     -H "X-API-Key: SPMJ-EmailService-2024-SecureKey-MOH-Malaysia" ^
     -d "\"<EMAIL>\"" ^
     http://localhost:5000/api/admin/password/validate-email
echo.

echo ============================================================
echo API Key Integration Test Complete
echo.
echo Expected Results:
echo   Test 1: Status 200 (Health check - no auth required)
echo   Test 2: Status 401 (Unauthorized - no API key)
echo   Test 3: Status 200/500 (Authorized - may fail on email config)
echo   Test 4: Status 200 (Email validation with key)
echo.
echo If all tests show expected results, integration is working!
echo ============================================================
pause
