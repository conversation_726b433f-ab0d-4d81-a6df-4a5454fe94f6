<%@ Page Title="Admin Password Manager" Language="vb" MasterPageFile="~/Main.Master" AutoEventWireup="false" CodeBehind="PN_AdminPasswordManager.aspx.vb" Inherits="SPMJ.PN_AdminPasswordManager" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <title>Admin Password Manager - SPMJ System</title>
    <style type="text/css">
        .admin-container {
            background-color: #ffffff;
            padding: 20px;
            border: 2px solid #0066cc;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .admin-header {
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            padding: 15px 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 6px 6px 0 0;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .security-badge {
            background-color: #ff6b35;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin-left: 10px;
        }
        .form-section {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .section-title {
            color: #0066cc;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 5px;
        }
        .form-grid {
            display: table;
            width: 100%;
        }
        .form-row {
            display: table-row;
            margin-bottom: 10px;
        }
        .form-label {
            display: table-cell;
            width: 150px;
            padding: 8px 10px 8px 0;
            font-weight: bold;
            color: #333;
            vertical-align: top;
            font-size: 11px;
        }
        .form-control {
            display: table-cell;
            padding: 5px 0;
        }
        .input-field {
            width: 250px;
            padding: 6px 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 11px;
        }
        .input-field:focus {
            border-color: #0066cc;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,102,204,0.25);
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
            margin-top: 5px;
        }
        .btn-primary {
            background-color: #0066cc;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0052a3;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .message-panel {
            padding: 12px 15px;
            margin: 15px 0;
            border: 1px solid;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .msg-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .msg-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .msg-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .msg-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .user-profile {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .user-grid {
            width: 100%;
            font-size: 11px;
        }
        .user-grid td {
            padding: 6px 8px;
            vertical-align: top;
        }
        .user-label {
            font-weight: bold;
            color: #495057;
            width: 120px;
        }
        .user-value {
            color: #212529;
        }
        .password-result {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 15px 0;
        }
        .password-display {
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            color: #856404;
            background-color: white;
            padding: 10px;
            border: 1px solid #ffc107;
            border-radius: 4px;
            margin: 10px 0;
            letter-spacing: 2px;
        }
        .email-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
        }
        .email-sent {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .email-failed {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .help-text {
            font-size: 10px;
            color: #6c757d;
            margin-top: 4px;
            line-height: 1.3;
        }
        .security-note {
            background-color: #e7f3ff;
            border-left: 4px solid #0066cc;
            padding: 10px;
            margin: 10px 0;
            font-size: 11px;
            color: #004085;
        }
        .checkbox-container {
            margin: 10px 0;
        }
        .checkbox-container input[type="checkbox"] {
            margin-right: 8px;
        }
        .action-buttons {
            text-align: center;
            padding: 15px 0;
            border-top: 1px solid #dee2e6;
            margin-top: 15px;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="admin-container">
        <div class="admin-header">
            🔐 Admin Password Manager
            <span class="security-badge">SECURE</span>
        </div>

        <!-- Message Panel -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false">
            <div class="message-panel" id="divMessage" runat="server">
                <asp:Label ID="lblMessage" runat="server"></asp:Label>
            </div>
        </asp:Panel>

        <!-- User Search Section -->
        <div class="form-section">
            <div class="section-title">🔍 Search User</div>
            <div class="form-grid">
                <div class="form-row">
                    <div class="form-label">User ID:</div>
                    <div class="form-control">
                        <asp:TextBox ID="txtSearchUser" runat="server" CssClass="input-field" MaxLength="50" placeholder="Enter user ID to search"></asp:TextBox>
                        <asp:Button ID="btnSearchUser" runat="server" Text="Search User" CssClass="btn btn-primary" />
                    </div>
                </div>
            </div>
            <div class="help-text">Enter the User ID to search for account information and manage password.</div>
        </div>

        <!-- User Information Panel -->
        <asp:Panel ID="pnlUserInfo" runat="server" Visible="false">
            <div class="form-section">
                <div class="section-title">👤 User Information</div>
                <div class="user-profile">
                    <table class="user-grid" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="user-label">User ID:</td>
                            <td class="user-value"><asp:Label ID="lblUserId" runat="server"></asp:Label></td>
                            <td class="user-label">Status:</td>
                            <td class="user-value"><asp:Label ID="lblUserStatus" runat="server"></asp:Label></td>
                        </tr>
                        <tr>
                            <td class="user-label">Full Name:</td>
                            <td class="user-value"><asp:Label ID="lblUserName" runat="server"></asp:Label></td>
                            <td class="user-label">Module:</td>
                            <td class="user-value"><asp:Label ID="lblUserModule" runat="server"></asp:Label></td>
                        </tr>
                        <tr>
                            <td class="user-label">Email Address:</td>
                            <td class="user-value"><asp:Label ID="lblUserEmail" runat="server"></asp:Label></td>
                            <td class="user-label">Access Level:</td>
                            <td class="user-value"><asp:Label ID="lblUserAccess" runat="server"></asp:Label></td>
                        </tr>
                        <tr>
                            <td class="user-label">Last Login:</td>
                            <td class="user-value"><asp:Label ID="lblLastLogin" runat="server"></asp:Label></td>
                            <td class="user-label">Password Set:</td>
                            <td class="user-value"><asp:Label ID="lblPasswordDate" runat="server"></asp:Label></td>
                        </tr>
                    </table>
                </div>
            </div>
        </asp:Panel>

        <!-- Password Management Panel -->
        <asp:Panel ID="pnlPasswordActions" runat="server" Visible="false">
            <div class="form-section">
                <div class="section-title">🔑 Password Management</div>
                
                <div class="form-grid">
                    <div class="form-row">
                        <div class="form-label">Email Address:</div>
                        <div class="form-control">
                            <asp:TextBox ID="txtUserEmail" runat="server" CssClass="input-field" MaxLength="100" placeholder="<EMAIL>"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-label">Password Type:</div>
                        <div class="form-control">
                            <asp:RadioButton ID="rbTempPassword" runat="server" GroupName="PasswordType" Checked="true" Text="Temporary Password (User must change on first login)" />
                            <br />
                            <asp:RadioButton ID="rbPermanentPassword" runat="server" GroupName="PasswordType" Text="Permanent Password (Optional custom password)" />
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-label">Custom Password:</div>
                        <div class="form-control">
                            <asp:TextBox ID="txtCustomPassword" runat="server" CssClass="input-field" MaxLength="50" placeholder="Leave blank for auto-generated"></asp:TextBox>
                        </div>
                    </div>
                </div>

                <div class="checkbox-container">
                    <asp:CheckBox ID="chkSendEmail" runat="server" Checked="true" Text="Send password via email notification" />
                    <div class="help-text">Uncheck if you want to communicate password manually</div>
                </div>

                <div class="security-note">
                    <strong>Security Notice:</strong> Passwords are encrypted using SHA256 with unique salt. 
                    Temporary passwords require user to change on first login for enhanced security.
                </div>

                <div class="action-buttons">
                    <asp:Button ID="btnSetPassword" runat="server" Text="🔐 Set Password" CssClass="btn btn-success" />
                    <asp:Button ID="btnForceReset" runat="server" Text="🔄 Force Reset" CssClass="btn btn-danger" 
                        OnClientClick="return confirm('This will force user to change password on next login. Continue?');" />
                    <asp:Button ID="btnGenerateTemp" runat="server" Text="⚡ Generate Temporary" CssClass="btn btn-primary" />
                </div>
            </div>
        </asp:Panel>

        <!-- Password Result Panel -->
        <asp:Panel ID="pnlPasswordResult" runat="server" Visible="false">
            <div class="form-section">
                <div class="section-title">✅ Password Generation Result</div>
                <div class="password-result">
                    <div style="font-size: 14px; font-weight: bold; color: #856404;">New Password Generated:</div>
                    <div class="password-display">
                        <asp:Label ID="lblGeneratedPassword" runat="server"></asp:Label>
                    </div>
                    <div style="font-size: 12px; color: #856404;">
                        Password Type: <asp:Label ID="lblPasswordType" runat="server"></asp:Label>
                    </div>
                    
                    <!-- Email Status -->
                    <asp:Panel ID="pnlEmailStatus" runat="server" Visible="false">
                        <div id="divEmailStatus" runat="server" class="email-status">
                            <asp:Label ID="lblEmailStatus" runat="server"></asp:Label>
                        </div>
                    </asp:Panel>
                </div>
                
                <div class="help-text" style="text-align: center; margin-top: 15px;">
                    Please record this password securely. For temporary passwords, user will be required to change it on next login.
                </div>
            </div>
        </asp:Panel>
    </div>
</asp:Content>
