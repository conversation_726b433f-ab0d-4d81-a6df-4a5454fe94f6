Imports System.Security.Cryptography
Imports System.Text

''' <summary>
''' Password Helper for secure password hashing using SHA256 + Salt
''' Compatible with .NET Framework 3.5.1
''' Integrated with SPMJ Email Service Microservice
''' </summary>
Public Class PasswordHelper
    
    ' Salt length in bytes
    Private Const SALT_LENGTH As Integer = 32
    
    ''' <summary>
    ''' Generate a random salt
    ''' </summary>
    ''' <returns>Base64 encoded salt string</returns>
    Public Shared Function GenerateSalt() As String
        Dim saltBytes(SALT_LENGTH - 1) As Byte
        Dim rng As New RNGCryptoServiceProvider()
        rng.GetBytes(saltBytes)
        Return Convert.ToBase64String(saltBytes)
    End Function
    
    ''' <summary>
    ''' Hash password with salt using SHA256
    ''' </summary>
    ''' <param name="password">Plain text password</param>
    ''' <param name="salt">Salt string</param>
    ''' <returns>Base64 encoded hash</returns>
    Public Shared Function HashPassword(password As String, salt As String) As String
        If String.IsNullOrEmpty(password) Then
            Throw New ArgumentException("Password cannot be null or empty")
        End If
        If String.IsNullOrEmpty(salt) Then
            Throw New ArgumentException("Salt cannot be null or empty")
        End If
        
        Dim saltBytes As Byte() = Convert.FromBase64String(salt)
        Dim passwordBytes As Byte() = Encoding.UTF8.GetBytes(password)
        
        ' Combine password and salt
        Dim combinedBytes(passwordBytes.Length + saltBytes.Length - 1) As Byte
        Array.Copy(passwordBytes, 0, combinedBytes, 0, passwordBytes.Length)
        Array.Copy(saltBytes, 0, combinedBytes, passwordBytes.Length, saltBytes.Length)
        
        ' Hash the combined bytes
        Using sha256 As New SHA256CryptoServiceProvider()
            Dim hashBytes As Byte() = sha256.ComputeHash(combinedBytes)
            Return Convert.ToBase64String(hashBytes)
        End Using
    End Function
    
    ''' <summary>
    ''' Verify password against stored hash and salt
    ''' </summary>
    ''' <param name="password">Plain text password to verify</param>
    ''' <param name="hash">Stored password hash</param>
    ''' <param name="salt">Stored salt</param>
    ''' <returns>True if password matches</returns>
    Public Shared Function VerifyPassword(password As String, hash As String, salt As String) As Boolean
        If String.IsNullOrEmpty(password) Or String.IsNullOrEmpty(hash) Or String.IsNullOrEmpty(salt) Then
            Return False
        End If
        
        Try
            Dim computedHash As String = PasswordHelper.HashPassword(password, salt)
            Return computedHash.Equals(hash)
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Create a complete password entry (hash + salt)
    ''' </summary>
    ''' <param name="password">Plain text password</param>
    ''' <returns>Array with [0] = hash, [1] = salt</returns>
    Public Shared Function CreatePasswordEntry(password As String) As String()
        Dim salt As String = GenerateSalt()
        Dim hash As String = HashPassword(password, salt)
        Return New String() {hash, salt}
    End Function
    
    ''' <summary>
    ''' Check if password is encrypted (contains Base64 characters)
    ''' </summary>
    ''' <param name="password">Password to check</param>
    ''' <returns>True if appears to be encrypted</returns>
    Public Shared Function IsPasswordEncrypted(password As String) As Boolean
        If String.IsNullOrEmpty(password) Then Return False
        If password.Length < 40 Then Return False ' SHA256 Base64 is typically 44 chars
        
        Try
            ' Try to decode as Base64 - if successful, likely encrypted
            Convert.FromBase64String(password)
            Return True
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Migrate plain text password to secure hash for KOLEJ-PDSA
    ''' </summary>
    ''' <param name="userId">User ID</param>
    ''' <param name="plainPassword">Plain text password</param>
    ''' <returns>True if migration successful</returns>
    Public Shared Function MigrateUserPassword(userId As String, plainPassword As String, connection As System.Data.OleDb.OleDbConnection) As Boolean
        Try
            ' Generate salt and hash
            Dim passwordEntry() As String = CreatePasswordEntry(plainPassword)
            Dim hashedPassword As String = passwordEntry(0)
            Dim salt As String = passwordEntry(1)
            
            ' Update database with hashed password
            Using command As New System.Data.OleDb.OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET pwd = ?, salt = ?, pwd_encrypted = 1, pwd_migrated = 1, migration_date = ? WHERE id_pg = ?"
                command.Parameters.AddWithValue("@pwd", hashedPassword)
                command.Parameters.AddWithValue("@salt", salt)
                command.Parameters.AddWithValue("@migration_date", DateTime.Now)
                command.Parameters.AddWithValue("@id_pg", userId)
                
                Return command.ExecuteNonQuery() > 0
            End Using
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Check if database schema supports enhanced security
    ''' </summary>
    ''' <param name="connection">Database connection</param>
    ''' <returns>True if enhanced schema available</returns>
    Public Shared Function SupportsEnhancedSecurity(connection As System.Data.OleDb.OleDbConnection) As Boolean
        Try
            Using command As New System.Data.OleDb.OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT salt FROM kj_pengguna WHERE 1=0"
                command.ExecuteNonQuery()
                Return True
            End Using
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Enhanced password verification that handles varchar(15) PWD column workaround
    ''' </summary>
    Public Shared Function VerifyPasswordWorkaround(password As String, storedPwd As String, storedSalt As String) As Boolean
        If String.IsNullOrEmpty(password) Then
            Return False
        End If
        
        Try
            ' Check if this is the new workaround format (full_hash|salt in salt field)
            If Not String.IsNullOrEmpty(storedSalt) AndAlso storedSalt.Contains("|") Then
                ' New workaround format: salt field contains "full_hash|salt"
                Dim parts() As String = storedSalt.Split("|"c)
                If parts.Length = 2 Then
                    Dim fullHash As String = parts(0)
                    Dim actualSalt As String = parts(1)
                    
                    ' Verify against the full hash and salt
                    Dim computedHash As String = HashPassword(password, actualSalt)
                    Return computedHash.Equals(fullHash)
                End If
            End If
            
            ' Fall back to normal verification if not workaround format
            If Not String.IsNullOrEmpty(storedSalt) Then
                Return VerifyPassword(password, storedPwd, storedSalt)
            End If
            
            ' Plain text comparison as last resort
            Return password.Equals(storedPwd)
            
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Standard password verification (post-refactoring - no workarounds needed)
    ''' Use this after PWD column has been updated to varchar(255)
    ''' </summary>
    Public Shared Function VerifyPasswordStandard(password As String, hash As String, salt As String) As Boolean
        If String.IsNullOrEmpty(password) Or String.IsNullOrEmpty(hash) Or String.IsNullOrEmpty(salt) Then
            Return False
        End If
        
        Try
            ' Standard SHA256 verification - full hash comparison
            Dim computedHash As String = HashPassword(password, salt)
            Return computedHash.Equals(hash)
        Catch
            Return False
        End Try
    End Function

End Class
