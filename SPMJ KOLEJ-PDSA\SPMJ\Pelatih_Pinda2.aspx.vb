﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class Pelatih_Pinda2
    Inherits System.Web.UI.Page

    Public Sub Isi_Kelayakan()
        On Error Resume Next
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim strMark As String

        Cmd.CommandText = "select id_jenis, id_subjek, keputusan from pelatih_kelayakan where nokp = '" & Session("nokp") & "' order by id_subjek asc "
        'Comment Original 30042021  - OSH 
        'Cmd.CommandText = "select id_jenis, id_subjek, keputusan from pelatih_kelayakan where nokp = '" & Session("nokp") & "' "
        Rdr = Cmd.ExecuteReader()

        While Rdr.Read
            If Rdr(0) = "IEL" Then
                Cb_Luar.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsLuar.Text = Rdr(2)
            ElseIf Rdr(0) = "FL" Then
                Cb_SbjM.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsM.Text = Rdr(2)
            ElseIf Rdr(0) = "STPM" Then

                If Cb_SbjT1.Visible = False Then
                    Cb_SbjT1.Visible = True : Cb_KptsT1.Visible = True
                    Cb_SbjT1.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT1.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT2.Visible = False Then
                    Cb_SbjT2.Visible = True : Cb_KptsT2.Visible = True
                    Cb_SbjT2.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT2.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT3.Visible = False Then
                    Cb_SbjT3.Visible = True : Cb_KptsT3.Visible = True
                    Cb_SbjT3.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT3.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT4.Visible = False Then
                    Cb_SbjT4.Visible = True : Cb_KptsT4.Visible = True
                    Cb_SbjT4.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT4.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT5.Visible = False Then
                    Cb_SbjT5.Visible = True : Cb_KptsT5.Visible = True
                    Cb_SbjT5.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT5.Items.FindByValue(Rdr(2)).Selected = True
                End If

            ElseIf Rdr(0) = "A-L" Then
                'Selected values 19082020 - OSH 
                Cb_Aliran.SelectedValue = "2"

                If Cb_SbjT1.Visible = False Then
                    Cb_SbjT1.Visible = True : Cb_KptsT1.Visible = True
                    Cb_SbjT1.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT1.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT2.Visible = False Then
                    Cb_SbjT2.Visible = True : Cb_KptsT2.Visible = True
                    Cb_SbjT2.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT2.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT3.Visible = False Then
                    Cb_SbjT3.Visible = True : Cb_KptsT3.Visible = True
                    Cb_SbjT3.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT3.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT4.Visible = False Then
                    Cb_SbjT4.Visible = True : Cb_KptsT4.Visible = True
                    Cb_SbjT4.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT4.Items.FindByValue(Rdr(2)).Selected = True
                ElseIf Cb_SbjT5.Visible = False Then
                    Cb_SbjT5.Visible = True : Cb_KptsT5.Visible = True
                    Cb_SbjT5.Items.FindByValue(Rdr(1)).Selected = True : Cb_KptsT5.Items.FindByValue(Rdr(2)).Selected = True
                End If
            ElseIf Rdr(0) = "SPM" Then
                'Add Malay Language Subjects and Score 11052022 - OSH 
                If Rdr(1) = "-1" Then
                    Cb_Kpts1.Visible = True : Textbox1.Visible = True
                    Cb_Kpts1.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                ElseIf Rdr(1) = "-2" Then
                    Cb_Kpts2.Visible = True : Textbox2.Visible = True
                    Cb_Kpts2.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                ElseIf Rdr(1) = "-3" Then
                    Cb_Kpts3.Visible = True : Textbox3.Visible = True
                    Cb_Kpts3.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                ElseIf Rdr(1) = "-4" Then
                    Cb_Kpts4.Visible = True : Textbox4.Visible = True
                    Cb_Kpts4.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                    'Comment Original 26042022 - OSH 
                    'End If
                    'If 
                    'Fix Borken Condition  26042022 - OSH 
                ElseIf Cb_Sbj5.Visible = False Then
                    Cb_Sbj5.Visible = True : Cb_Kpts5.Visible = True : Textbox8.Visible = True
                    Cb_Sbj5.Items.FindByValue(Rdr(1)).Selected = True
                    Cb_Kpts5.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                ElseIf Cb_Sbj6.Visible = False Then
                    Cb_Sbj6.Visible = True : Cb_Kpts6.Visible = True : Textbox9.Visible = True
                    Cb_Sbj6.Items.FindByValue(Rdr(1)).Selected = True
                    Cb_Kpts6.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                End If
            ElseIf Rdr(0) = "O-L" Then 'O-Level 21082020 - OSH 

                'Dim CnO As New OleDbConnection : Dim CmdO As New OleDbCommand : Dim RdrO As OleDbDataReader
                'CnO.ConnectionString = ServerId : CnO.Open() : CmdO.Connection = Cn

                'CmdO.CommandText = "select count(id_subjek) from pelatih_kelayakan where nokp = '" & Session("nokp") & "' and id_jenis = 'O-L' "
                'RdrO = CmdO.ExecuteReader()

                'Comment Original 11052022 -OSH 
                'If Rdr(1) = "-2" Then
                'Add Disable Malay Language Subject and Score 11052022 - OSH  
                If Rdr(1) = "-1" Then
                    Cb_Kpts1.Visible = False : Textbox1.Visible = False
                ElseIf Rdr(1) = "-2" Then
                    Cb_Kpts2.Visible = True : Textbox2.Visible = True
                    'Comment Original 27042021 - OSH
                    'Cb_Kpts2.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                    'Fix Load Data O-Level subjects 27042021 - OSH 
                    Cb_Kpts2.Items.FindByText(Rdr(2)).Selected = True
                ElseIf Rdr(1) = "-3" Then
                    Cb_Kpts3.Visible = True : Textbox6.Visible = True
                    'Comment Original 27042021 - OSH
                    'Cb_Kpts3.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                    'Fix Populate O-Level subjects 27042021 - OSH 
                    Cb_Kpts3.Items.FindByText(Rdr(2)).Selected = True
                ElseIf Rdr(1) = "-4" Then
                    Cb_Kpts4.Visible = True : Textbox7.Visible = True
                    'Comment Original 27042021 - OSH
                    'Cb_Kpts4.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                    'Fix Populate O-Level subjects 27042021 - OSH 
                    Cb_Kpts4.Items.FindByText(Rdr(2)).Selected = True

                    'Fix Populate Issue 30042021 - OSH  
                ElseIf Cb_Sbj5.Visible = False Then
                    Cb_Sbj5.Visible = True : Cb_Kpts5.Visible = True : Textbox8.Visible = True
                    Cb_Sbj5.SelectedValue = Rdr(1)
                    'Cb_Sbj5.Items.FindByValue(Rdr(1)).Selected = True
                    'Comment Original 28042021 - OSH
                    'Cb_Kpts5.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                    'Fix Populate O-Level Subjects 28042021 - OSH 
                    Cb_Kpts5.Items.FindByText(Rdr(2)).Selected = True
                    Rdr.NextResult()
                ElseIf Cb_Sbj6.Visible = False Then
                    Cb_Sbj6.Visible = True : Cb_Kpts6.Visible = True : Textbox9.Visible = True
                    Cb_Sbj6.SelectedValue = Rdr(1)
                    'Cb_Sbj6.Items.FindByValue(Rdr(1)).Selected = True
                    'Comment Original 28042021 - OSH
                    'Cb_Kpts6.Items.FindByText(Check_Kelayakan(Rdr(2))).Selected = True
                    'Fix Populate O-Level Subjects 28042021 - OSH 
                    Cb_Kpts6.Items.FindByText(Rdr(2)).Selected = True
                    Rdr.NextResult()
                    'Add Subject and Result Rows  30042021 - OSH 
                ElseIf Cb_Sbj7.Visible = False Then
                    Cb_Sbj7.Visible = True : Cb_Kpts7.Visible = True : Textbox10.Visible = True
                    Cb_Sbj7.SelectedValue = Rdr(1)
                    'Cb_Sbj7.Items.FindByValue(Rdr(1)).Selected = True
                    Cb_Kpts7.Items.FindByText(Rdr(2)).Selected = True
                    Rdr.NextResult()
                ElseIf Cb_Sbj8.Visible = False Then
                    Cb_Sbj8.Visible = True : Cb_Kpts8.Visible = True : Textbox11.Visible = True
                    Cb_Sbj8.SelectedValue = Rdr(1)
                    'Cb_Sbj8.Items.FindByValue(Rdr(1)).Selected = True
                    Cb_Kpts8.Items.FindByText(Rdr(2)).Selected = True
                    Rdr.NextResult()
                ElseIf Cb_Sbj9.Visible = False Then
                    Cb_Sbj9.Visible = True : Cb_Kpts9.Visible = True : Textbox12.Visible = True
                    Cb_Sbj9.SelectedValue = Rdr(1)
                    'Cb_Sbj9.Items.FindByValue(Rdr(1)).Selected = True
                    Cb_Kpts9.Items.FindByText(Rdr(2)).Selected = True
                    Rdr.NextResult()
                End If
            End If

        End While

    End Sub

    Function Check_Kelayakan(ByVal X As String)
        Dim mark As Integer
        If X = "A+" Or X = "A" Or X = "B" Or X = "C" Or X = "D" Or X = "E" Then  Else mark = CInt(Mid(X, 1, 1))
        If IsNumeric(mark) And X.Length = 2 Then
            Select Case mark
                Case "1" : X = X + "\A"
                Case "2" : X = X + "\A-"
                Case "3" : X = X + "\B+"
                Case "4" : X = X + "\B"
                Case "5" : X = X + "\C+"
                Case "6" : X = X + "\C"
                Case "7" : X = X + "\D"
                Case "8" : X = X + "\E"
            End Select
        End If

        Return X
    End Function

    'Public Sub Isi_Subjek(ByVal X As String)
    '    'Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
    '    'Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
    '    Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
    '    Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
    '    Bt4.Visible = False : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
    '    Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

    '    Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
    '    Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
    '    Cb_SbjT1.Visible = False : Cb_SbjT2.Visible = False : Cb_SbjT3.Visible = False : Cb_SbjT4.Visible = False : Cb_SbjT5.Visible = False
    '    Cb_KptsT1.Visible = False : Cb_KptsT2.Visible = False : Cb_KptsT3.Visible = False : Cb_KptsT4.Visible = False : Cb_KptsT5.Visible = False


    '    Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
    '    Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
    'If X = "SPM" Or X = "O-L" Then
    '    Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = '" & X & "' order by id_subjek"
    '    Rdr = Cmd.ExecuteReader()
    '    While Rdr.Read
    '        Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
    '        Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
    '        'Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
    '        'Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
    '        'Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
    '    End While
    '    Rdr.Close()
    'End If

    'If X = "STPM" Or X = "A-L" Then
    '    Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = '" & X & "' order by id_subjek"
    '    Rdr = Cmd.ExecuteReader()
    '    While Rdr.Read
    '        Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
    '        Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
    '        Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
    '        Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
    '        Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
    '    End While
    '    Rdr.Close()
    'End If
    'Cn.Close()

    'If X = "SPM" Then


    '    Cb_Kpts2.Items.Clear()
    '    Cb_Kpts2.Items.Add("")
    '    Cb_Kpts2.Items.Add("A+")
    '    Cb_Kpts2.Items.Add("1A\A")
    '    Cb_Kpts2.Items.Add("2A\A-")
    '    Cb_Kpts2.Items.Add("3B\B+")
    '    Cb_Kpts2.Items.Add("4B\B")
    '    Cb_Kpts2.Items.Add("5C\C+")
    '    Cb_Kpts2.Items.Add("6C\C")
    '    Cb_Kpts2.Items.Add("7D\D")
    '    Cb_Kpts2.Items.Add("8E\E")

    '    Cb_Kpts3.Items.Clear()
    '    Cb_Kpts3.Items.Add("")
    '    Cb_Kpts3.Items.Add("A+")
    '    Cb_Kpts3.Items.Add("1A\A")
    '    Cb_Kpts3.Items.Add("2A\A-")
    '    Cb_Kpts3.Items.Add("3B\B+")
    '    Cb_Kpts3.Items.Add("4B\B")
    '    Cb_Kpts3.Items.Add("5C\C+")
    '    Cb_Kpts3.Items.Add("6C\C")
    '    Cb_Kpts3.Items.Add("7D\D")
    '    Cb_Kpts3.Items.Add("8E\E")

    '    Cb_Kpts4.Items.Clear()
    '    Cb_Kpts4.Items.Add("")
    '    Cb_Kpts4.Items.Add("A+")
    '    Cb_Kpts4.Items.Add("1A\A")
    '    Cb_Kpts4.Items.Add("2A\A-")
    '    Cb_Kpts4.Items.Add("3B\B+")
    '    Cb_Kpts4.Items.Add("4B\B")
    '    Cb_Kpts4.Items.Add("5C\C+")
    '    Cb_Kpts4.Items.Add("6C\C")
    '    Cb_Kpts4.Items.Add("7D\D")
    '    Cb_Kpts4.Items.Add("8E\E")

    '    Cb_Kpts5.Items.Clear()
    '    Cb_Kpts5.Items.Add("")
    '    Cb_Kpts5.Items.Add("A+")
    '    Cb_Kpts5.Items.Add("1A\A")
    '    Cb_Kpts5.Items.Add("2A\A-")
    '    Cb_Kpts5.Items.Add("3B\B+")
    '    Cb_Kpts5.Items.Add("4B\B")
    '    Cb_Kpts5.Items.Add("5C\C+")
    '    Cb_Kpts5.Items.Add("6C\C")
    '    Cb_Kpts5.Items.Add("7D\D")
    '    Cb_Kpts5.Items.Add("8E\E")

    '    Cb_Kpts6.Items.Clear()
    '    Cb_Kpts6.Items.Add("")
    '    Cb_Kpts6.Items.Add("A+")
    '    Cb_Kpts6.Items.Add("1A\A")
    '    Cb_Kpts6.Items.Add("2A\A-")
    '    Cb_Kpts6.Items.Add("3B\B+")
    '    Cb_Kpts6.Items.Add("4B\B")
    '    Cb_Kpts6.Items.Add("5C\C+")
    '    Cb_Kpts6.Items.Add("6C\C")
    '    Cb_Kpts6.Items.Add("7D\D")
    '    Cb_Kpts6.Items.Add("8E\E")

    'ElseIf X = "O-L" Then
    '    Cb_Kpts2.Items.Clear()
    '    Cb_Kpts2.Items.Add("A*")
    '    Cb_Kpts2.Items.Add("A")
    '    Cb_Kpts2.Items.Add("B")
    '    Cb_Kpts2.Items.Add("C")
    '    Cb_Kpts2.Items.Add("D")
    '    Cb_Kpts2.Items.Add("E")

    '    Cb_Kpts3.Items.Clear()
    '    Cb_Kpts3.Items.Add("A*")
    '    Cb_Kpts3.Items.Add("A")
    '    Cb_Kpts3.Items.Add("B")
    '    Cb_Kpts3.Items.Add("C")
    '    Cb_Kpts3.Items.Add("D")
    '    Cb_Kpts3.Items.Add("E")

    '    Cb_Kpts4.Items.Clear()
    '    Cb_Kpts4.Items.Add("A*")
    '    Cb_Kpts4.Items.Add("A")
    '    Cb_Kpts4.Items.Add("B")
    '    Cb_Kpts4.Items.Add("C")
    '    Cb_Kpts4.Items.Add("D")
    '    Cb_Kpts4.Items.Add("E")

    '    Cb_Kpts5.Items.Clear()
    '    Cb_Kpts5.Items.Add("A*")
    '    Cb_Kpts5.Items.Add("A")
    '    Cb_Kpts5.Items.Add("B")
    '    Cb_Kpts5.Items.Add("C")
    '    Cb_Kpts5.Items.Add("D")
    '    Cb_Kpts5.Items.Add("E")

    '    Cb_Kpts6.Items.Clear()
    '    Cb_Kpts6.Items.Add("A*")
    '    Cb_Kpts6.Items.Add("A")
    '    Cb_Kpts6.Items.Add("B")
    '    Cb_Kpts6.Items.Add("C")
    '    Cb_Kpts6.Items.Add("D")
    '    Cb_Kpts6.Items.Add("E")

    'ElseIf X = "STPM" Then
    '    Cb_KptsT1.Items.Clear()
    '    Cb_KptsT1.Items.Add("")
    '    Cb_KptsT1.Items.Add("A")
    '    Cb_KptsT1.Items.Add("A-")
    '    Cb_KptsT1.Items.Add("B+")
    '    Cb_KptsT1.Items.Add("B")
    '    Cb_KptsT1.Items.Add("B-")
    '    Cb_KptsT1.Items.Add("C+")
    '    Cb_KptsT1.Items.Add("C")
    '    Cb_KptsT1.Items.Add("C-")
    '    Cb_KptsT1.Items.Add("D+")
    '    Cb_KptsT1.Items.Add("D")
    '    Cb_KptsT1.Items.Add("D-")

    '    Cb_KptsT2.Items.Clear()
    '    Cb_KptsT2.Items.Add("")
    '    Cb_KptsT2.Items.Add("A")
    '    Cb_KptsT2.Items.Add("A-")
    '    Cb_KptsT2.Items.Add("B+")
    '    Cb_KptsT2.Items.Add("B")
    '    Cb_KptsT2.Items.Add("B-")
    '    Cb_KptsT2.Items.Add("C+")
    '    Cb_KptsT2.Items.Add("C")
    '    Cb_KptsT2.Items.Add("C-")
    '    Cb_KptsT2.Items.Add("D+")
    '    Cb_KptsT2.Items.Add("D")
    '    Cb_KptsT2.Items.Add("D-")

    '    Cb_KptsT3.Items.Clear()
    '    Cb_KptsT3.Items.Add("")
    '    Cb_KptsT3.Items.Add("A")
    '    Cb_KptsT3.Items.Add("A-")
    '    Cb_KptsT3.Items.Add("B+")
    '    Cb_KptsT3.Items.Add("B")
    '    Cb_KptsT3.Items.Add("B-")
    '    Cb_KptsT3.Items.Add("C+")
    '    Cb_KptsT3.Items.Add("C")
    '    Cb_KptsT3.Items.Add("C-")
    '    Cb_KptsT3.Items.Add("D+")
    '    Cb_KptsT3.Items.Add("D")
    '    Cb_KptsT3.Items.Add("D-")

    '    Cb_KptsT4.Items.Clear()
    '    Cb_KptsT4.Items.Add("")
    '    Cb_KptsT4.Items.Add("A")
    '    Cb_KptsT4.Items.Add("A-")
    '    Cb_KptsT4.Items.Add("B+")
    '    Cb_KptsT4.Items.Add("B")
    '    Cb_KptsT4.Items.Add("B-")
    '    Cb_KptsT4.Items.Add("C+")
    '    Cb_KptsT4.Items.Add("C")
    '    Cb_KptsT4.Items.Add("C-")
    '    Cb_KptsT4.Items.Add("D+")
    '    Cb_KptsT4.Items.Add("D")
    '    Cb_KptsT4.Items.Add("D-")

    '    Cb_KptsT5.Items.Clear()
    '    Cb_KptsT5.Items.Add("")
    '    Cb_KptsT5.Items.Add("A")
    '    Cb_KptsT5.Items.Add("A-")
    '    Cb_KptsT5.Items.Add("B+")
    '    Cb_KptsT5.Items.Add("B")
    '    Cb_KptsT5.Items.Add("B-")
    '    Cb_KptsT5.Items.Add("C+")
    '    Cb_KptsT5.Items.Add("C")
    '    Cb_KptsT5.Items.Add("C-")
    '    Cb_KptsT5.Items.Add("D+")
    '    Cb_KptsT5.Items.Add("D")
    '    Cb_KptsT5.Items.Add("D-")

    'ElseIf X = "A-L" Then
    '    Cb_KptsT1.Items.Clear()
    '    Cb_KptsT1.Items.Add("A*")
    '    Cb_KptsT1.Items.Add("A")
    '    Cb_KptsT1.Items.Add("B")
    '    Cb_KptsT1.Items.Add("C")
    '    Cb_KptsT1.Items.Add("D")
    '    Cb_KptsT1.Items.Add("E")

    '    Cb_KptsT2.Items.Clear()
    '    Cb_KptsT2.Items.Add("A*")
    '    Cb_KptsT2.Items.Add("A")
    '    Cb_KptsT2.Items.Add("B")
    '    Cb_KptsT2.Items.Add("C")
    '    Cb_KptsT2.Items.Add("D")
    '    Cb_KptsT2.Items.Add("E")

    '    Cb_KptsT3.Items.Clear()
    '    Cb_KptsT3.Items.Add("A*")
    '    Cb_KptsT3.Items.Add("A")
    '    Cb_KptsT3.Items.Add("B")
    '    Cb_KptsT3.Items.Add("C")
    '    Cb_KptsT3.Items.Add("D")
    '    Cb_KptsT3.Items.Add("E")

    '    Cb_KptsT4.Items.Clear()
    '    Cb_KptsT4.Items.Add("A*")
    '    Cb_KptsT4.Items.Add("A")
    '    Cb_KptsT4.Items.Add("B")
    '    Cb_KptsT4.Items.Add("C")
    '    Cb_KptsT4.Items.Add("D")
    '    Cb_KptsT4.Items.Add("E")

    '    Cb_KptsT5.Items.Clear()
    '    Cb_KptsT5.Items.Add("A*")
    '    Cb_KptsT5.Items.Add("A")
    '    Cb_KptsT5.Items.Add("B")
    '    Cb_KptsT5.Items.Add("C")
    '    Cb_KptsT5.Items.Add("D")
    '    Cb_KptsT5.Items.Add("E")

    'End If
    'End Sub

    'Public Sub Isi_Subjek(ByVal X As Int16)
    '    Cb_Luar.Visible = True ': Cb_Luar.SelectedIndex = 0 : Cb_Luarx.Visible = False
    '    'Cb_SbjM.Visible = True : Cb_SbjM.SelectedIndex = 0 : Cb_SbjMx.Visible = False
    '    Cb_SbjM.Visible = True : Cb_SbjMx.Visible = False

    '    Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
    '    Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
    '    'Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
    '    'Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
    '    'Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
    '    'Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
    '    'Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

    '    Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
    '    Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
    '    Cb_SbjT1.Visible = False : Cb_SbjT2.Visible = False : Cb_SbjT3.Visible = False : Cb_SbjT4.Visible = False : Cb_SbjT5.Visible = False
    '    Cb_KptsT1.Visible = False : Cb_KptsT2.Visible = False : Cb_KptsT3.Visible = False : Cb_KptsT4.Visible = False : Cb_KptsT5.Visible = False

    '    Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
    '    Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
    '    If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'PMR' order by dc_subjek"
    '    If X = 2 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
    '    lAcademic.Text = "SPM" ' Add labeling type of academic 21082020 - OSH 
    '    If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
    '    'Add A-Level Subjects 29072019 -OSH 
    '    If X = 4 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'A-L' order by dc_subjek"
    '    'Add O-Level Subjects 02082019 -OSH 
    '    If X = 5 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'O-L' order by dc_subjek"
    '    lAcademic.Text = "O-Level" ' Add labeling type of academic 21082020 - OSH 
    '    If X = 0 Then Exit Sub
    '    Rdr = Cmd.ExecuteReader()
    '    While Rdr.Read
    '        If Cb_Kursus.SelectedIndex <> 4 Then
    '            If X = 3 Or X = 4 Then  ' STPM or A-Level
    '                Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
    '                Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
    '                Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
    '                Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
    '                Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
    '            ElseIf X = 2 Or X = 5 Then ' SPM
    '                Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
    '                Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
    '                Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
    '                Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
    '                Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
    '            End If
    '            'Else
    '            '    Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
    '            '    Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
    '            '    Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
    '            '    Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
    '            '    Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
    '        End If
    '    End While
    '    Rdr.Close()
    '    Cn.Close()


    '    If X = 2 Then

    '        Gred_SPM(Cb_Kpts2)
    '        Gred_SPM(Cb_Kpts3)
    '        Gred_SPM(Cb_Kpts4)
    '        Gred_SPM(Cb_Kpts5)
    '        Gred_SPM(Cb_Kpts6)


    '    ElseIf X = 3 Then
    '        Gred_STPM(Cb_KptsT1)
    '        Gred_STPM(Cb_KptsT2)
    '        Gred_STPM(Cb_KptsT3)
    '        Gred_STPM(Cb_KptsT4)
    '        Gred_STPM(Cb_KptsT5)

    '        'Populate A-Level Grades 29072019 -OSH 
    '    ElseIf X = 4 Then
    '        Gred_O_A_Level(Cb_KptsT1)
    '        Gred_O_A_Level(Cb_KptsT2)
    '        Gred_O_A_Level(Cb_KptsT3)
    '        Gred_O_A_Level(Cb_KptsT4)
    '        Gred_O_A_Level(Cb_KptsT5)


    '        'Populate O-Level Grades 01082019 -OSH 
    '    ElseIf X = 5 Then

    '        Gred_O_A_Level(Cb_Kpts2)
    '        Gred_O_A_Level(Cb_Kpts3)
    '        Gred_O_A_Level(Cb_Kpts4)
    '        Gred_O_A_Level(Cb_Kpts5)
    '        Gred_O_A_Level(Cb_Kpts6)
    '    End If

    'End Sub

    ''Public Sub Isi_Subjek_2(ByVal X As Int16)
    ''    Cb_Luar.Visible = True ': Cb_Luar.SelectedIndex = 0 : Cb_Luarx.Visible = False
    ''    'Cb_SbjM.Visible = True : Cb_SbjM.SelectedIndex = 0 : Cb_SbjMx.Visible = False
    ''    Cb_SbjM.Visible = True : Cb_SbjMx.Visible = False

    ''    'Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
    ''    'Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
    ''    'Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
    ''    'Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
    ''    'Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
    ''    'Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
    ''    'Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

    ''    Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
    ''    Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
    ''    Cb_SbjT1.Visible = False : Cb_SbjT2.Visible = False : Cb_SbjT3.Visible = False : Cb_SbjT4.Visible = False : Cb_SbjT5.Visible = False
    ''    Cb_KptsT1.Visible = False : Cb_KptsT2.Visible = False : Cb_KptsT3.Visible = False : Cb_KptsT4.Visible = False : Cb_KptsT5.Visible = False

    ''    Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
    ''    Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

    ''    If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
    ''    'Add A-Level Subjects 29072019 -OSH 
    ''    If X = 4 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'A-L' order by dc_subjek"
    ''    If X = 0 Then Exit Sub
    ''    Rdr = Cmd.ExecuteReader()
    ''    While Rdr.Read
    ''        If Cb_Kursus.SelectedIndex <> 4 Then
    ''            If X = 3 Or X = 4 Then  ' STPM or A-Level
    ''                Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
    ''                Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
    ''                Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
    ''                Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
    ''                Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
    ''            End If
    ''        End If
    ''    End While
    ''    Rdr.Close()
    ''    Cn.Close()


    ''    If X = 3 Then
    ''        Gred_STPM(Cb_KptsT1)
    ''        Gred_STPM(Cb_KptsT2)
    ''        Gred_STPM(Cb_KptsT3)
    ''        Gred_STPM(Cb_KptsT4)
    ''        Gred_STPM(Cb_KptsT5)

    ''        'Populate A-Level Grades 29072019 -OSH 
    ''    ElseIf X = 4 Then
    ''        Gred_O_A_Level(Cb_KptsT1)
    ''        Gred_O_A_Level(Cb_KptsT2)
    ''        Gred_O_A_Level(Cb_KptsT3)
    ''        Gred_O_A_Level(Cb_KptsT4)
    ''        Gred_O_A_Level(Cb_KptsT5)
    ''    End If

    'End Sub

    Public Sub Isi_Subjek(ByVal X As String)

        'Reset and Disable International Language, SPM and O-Level Visibility
        Cb_Luar.Visible = True : Cb_SbjM.Visible = True : Cb_SbjMx.Visible = False
        Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
        Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False

        'Disable STPM and A-Level Visibility
        Cb_SbjT1.Visible = False : Cb_SbjT2.Visible = False : Cb_SbjT3.Visible = False : Cb_SbjT4.Visible = False : Cb_SbjT5.Visible = False
        Cb_KptsT1.Visible = False : Cb_KptsT2.Visible = False : Cb_KptsT3.Visible = False : Cb_KptsT4.Visible = False : Cb_KptsT5.Visible = False



        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = '" & X & "' order by dc_subjek"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Cb_Kursus.SelectedIndex <> 4 Then

                If X = "SPM" Or X = "O-L" Then ' SPM
                    Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)

                ElseIf X = "STPM" Or X = "A-L" Then  ' STPM or A-Level
                    Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
                End If
            End If
        End While
        Rdr.Close()
        Cn.Close()

        If X = "SPM" Then
            lAcademic.Text = "SPM" ' Add labeling type of academic 21082020 - OSH 
            Gred_SPM(Cb_Kpts1)
            Gred_SPM(Cb_Kpts2)
            Gred_SPM(Cb_Kpts3)
            Gred_SPM(Cb_Kpts4)
            Gred_SPM(Cb_Kpts5)
            Gred_SPM(Cb_Kpts6)

        ElseIf X = "O-L" Then
            lAcademic.Text = "O-Level" ' Add labeling type of academic 21082020 - OSH
            Gred_O_A_Level(Cb_Kpts2)
            Gred_O_A_Level(Cb_Kpts3)
            Gred_O_A_Level(Cb_Kpts4)
            Gred_O_A_Level(Cb_Kpts5)
            Gred_O_A_Level(Cb_Kpts6)
            Gred_O_A_Level(Cb_Kpts7)
            Gred_O_A_Level(Cb_Kpts8)
            Gred_O_A_Level(Cb_Kpts9)

        ElseIf X = "STPM" Then
            Gred_STPM(Cb_KptsT1)
            Gred_STPM(Cb_KptsT2)
            Gred_STPM(Cb_KptsT3)
            Gred_STPM(Cb_KptsT4)
            Gred_STPM(Cb_KptsT5)

            'Populate A-Level Grades 29072019 -OSH 
        ElseIf X = "A-L" Then
            Gred_O_A_Level(Cb_KptsT1)
            Gred_O_A_Level(Cb_KptsT2)
            Gred_O_A_Level(Cb_KptsT3)
            Gred_O_A_Level(Cb_KptsT4)
            Gred_O_A_Level(Cb_KptsT5)

        ElseIf X = "" Then
            Exit Sub
        End If

    End Sub




    'Public Sub Isi_Subjek_1(ByVal X As Int16)
    '       Cb_Luar.Visible = True ': Cb_Luar.SelectedIndex = 0 : Cb_Luarx.Visible = False
    '       'Cb_SbjM.Visible = True : Cb_SbjM.SelectedIndex = 0 : Cb_SbjMx.Visible = False
    '       Cb_SbjM.Visible = True : Cb_SbjMx.Visible = False

    '       Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
    '       Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
    '       Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
    '       'Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
    '       Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
    '       'Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
    '       'Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

    '       'Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
    '       'Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
    '       'Cb_SbjT1.Visible = False : Cb_SbjT2.Visible = False : Cb_SbjT3.Visible = False : Cb_SbjT4.Visible = False : Cb_SbjT5.Visible = False
    '       'Cb_KptsT1.Visible = False : Cb_KptsT2.Visible = False : Cb_KptsT3.Visible = False : Cb_KptsT4.Visible = False : Cb_KptsT5.Visible = False

    '       Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
    '       Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
    '       If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'PMR' order by dc_subjek"
    '       If X = 2 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
    '       lAcademic.Text = "SPM" ' Add labeling type of academic 21082020 - OSH 
    '       'Add O-Level Subjects 02082019 -OSH 
    '       If X = 5 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'O-L' order by dc_subjek"
    '       lAcademic.Text = "O-Level" ' Add labeling type of academic 21082020 - OSH

    '       If X = 0 Then Exit Sub
    '       Rdr = Cmd.ExecuteReader()
    '       While Rdr.Read
    '           If Cb_Kursus.SelectedIndex <> 4 Then

    '               If X = 2 Or X = 5 Then ' SPM
    '                   Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
    '                   Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
    '                   Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
    '                   Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
    '                   Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
    '               End If
    '           End If
    '       End While
    '       Rdr.Close()
    '       Cn.Close()


    '       If X = 2 Then

    '           Gred_SPM(Cb_Kpts2)
    '           Gred_SPM(Cb_Kpts3)
    '           Gred_SPM(Cb_Kpts4)
    '           Gred_SPM(Cb_Kpts5)
    '           Gred_SPM(Cb_Kpts6)

    '           'Populate O-Level Grades 01082019 -OSH 
    '       ElseIf X = 5 Then

    '           Gred_O_A_Level(Cb_Kpts2)
    '           Gred_O_A_Level(Cb_Kpts3)
    '           Gred_O_A_Level(Cb_Kpts4)
    '           Gred_O_A_Level(Cb_Kpts5)
    '           Gred_O_A_Level(Cb_Kpts6)
    '           Gred_O_A_Level(Cb_Kpts7)
    '           Gred_O_A_Level(Cb_Kpts8)
    '           Gred_O_A_Level(Cb_Kpts9)
    '       End If

    '   End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Private Sub Isi()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Cmd.CommandText = "select p.*, kr.* from pelatih p inner join pn_kursus kr on p.j_kursus = kr.id_kursus where p.nokp = '" & Session("nokp") & "'"
        'Cmd.CommandText = "select p.*, kj.*, tj.*, kr.*, wg.*, ng.*, ng2.dc_negeri as 'w_negeri',wg2.Dc_NEGARA as 'w_negara2'  from pelatih p " & _
        '                    "left outer join pn_kursus kr on p.j_kursus = kr.id_kursus " & _
        '                    "left outer join pn_kolej kj on p.id_kolej = kj.id_kolej " & _
        '                    "left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan " & _
        '                    "left outer join pn_negara wg on p.warganegara = wg.id_negara " & _
        '                    "left outer join pn_negeri ng on p.tp_negeri = ng.id_negeri " & _
        '                    "left outer join pn_negeri ng2 on p.w_negeri = ng2.id_negeri " & _
        '                    "left outer join pn_negara wg2 on p.w_negara=wg2.id_negara " & _
        '                    "where p.nokp = '" & Session("nokp") & "'"



        'FIXING MULTI-CATEGOERY SELECTION -20122012 OSH 
        'Comment Original 261112020 - OSH 
        'Cmd.CommandText = "select p.*, kj.dc_kolej, tj.*, kr.*, wg.*, ng.*, ng2.dc_negeri as 'w_negeri',wg2.Dc_NEGARA as 'w_negara2' from pelatih p " & _
        'Fixing query db field allias name Ticket #803722 26112020 - OSH 
        Cmd.CommandText = "select p.*, kj.dc_kolej, tj.*, kr.*, wg.*, ng.*, ng2.dc_negeri as 'w_negeri1',wg2.Dc_NEGARA as 'w_negara2' from pelatih p " & _
                            "left outer join pn_kursus kr on p.j_kursus = kr.id_kursus " & _
                            "left outer join pn_kolej kj on p.id_kolej = kj.id_kolej " & _
                            "left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan " & _
                            "left outer join pn_negara wg on p.warganegara = wg.id_negara " & _
                            "left outer join pn_negeri ng on p.tp_negeri = ng.id_negeri " & _
                            "left outer join pn_negeri ng2 on p.w_negeri = ng2.id_negeri " & _
                            "left outer join pn_negara wg2 on p.w_negara=wg2.id_negara " & _
                            "where p.nokp = '" & Session("nokp") & "' and p.j_kursus = '" & Session("j_kursus") & "'"



        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Cb_Kursus.Items.FindByText(Rdr("dc_kursus")).Selected = True
            If Not IsDBNull(Rdr("kelayakan")) Then Cb_Kelayakan.Items.Add(Rdr("kelayakan")) : Cb_Kelayakan.SelectedIndex = 0
            If Not IsDBNull(Rdr("setaraf")) Then Cb_Kelayakan_Taraf.SelectedIndex = Rdr("setaraf")
            'Improve security months and years read-only 17092019 - OSH 
            If Not IsDBNull(Rdr("sesi_bulan")) Then Cb_Sesi_Bln.SelectedValue = Rdr("sesi_bulan") : Cb_Sesi_Bln.Enabled = False
            If Not IsDBNull(Rdr("sesi_tahun")) Then Cb_Sesi_Thn.Items.FindByText(Rdr("sesi_tahun")).Selected = True : Cb_Sesi_Thn.Enabled = False
            Tx_Nama.Text = Rdr("nama") : Tx_Nama.Enabled = False
            Tx_NoKP.Text = Rdr("nokp") : Tx_NoKP.Enabled = False
            'Add Disable Army Field 24102013-OSH
            If Not IsDBNull(Rdr("NoTentera")) Then Tx_Tentera.Text = Rdr("NoTentera") : Tx_Tentera.Enabled = False Else Tx_Tentera.Enabled = False
            'Fixing select value 19082013 -OSH
            'If Not IsDBNull(Rdr("jenis_kp")) Then Cb_NoKP.SelectedItem.Value = Rdr("jenis_kp")
            If Not IsDBNull(Rdr("jenis_kp")) Then Cb_NoKP.SelectedIndex = Rdr("jenis_kp") : Cb_NoKP.Enabled = False

            'Comment Ori 19082013- OSH
            'If Not IsDBNull(Rdr("jenis_kp")) Then Cb_NoKP.SelectedIndex = Rdr("jenis_kp")
            'If Not IsDBNull(Rdr("dc_negara")) Then Cb_Warga.Items.FindByText(Rdr("dc_negara")).Selected = True
            If Not IsDBNull(Rdr("dc_negara")) Then
                If Rdr("dc_negara") = "MALAYSIA" Then Fn_Negara(1) Else Fn_Negara(0) '27042022
                Cb_Warga.Items.FindByText(Rdr("dc_negara")).Selected = True
            End If

            'Add Brith Date & Brith Place 1609202013 - OSH
            If Not IsDBNull(Rdr("tkh_lahir")) Then Tx_Tkh_Lahir.Text = Rdr("tkh_lahir")
            If Not IsDBNull(Rdr("tpt_lahir")) Then Tx_Tp_Lahir.Text = Rdr("tpt_lahir")
            If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") = "" Then Cb_Jantina.SelectedIndex = 0 Else Cb_Jantina.SelectedIndex = Rdr("jantina")
            'Comment Original 19082020 - OSH 
            'Cb_Bangsa.SelectedIndex = Rdr("bangsa")
            'Cb_Agama.SelectedIndex = Rdr("agama")
            'Fix selected values 19082020 - OSH 
            Cb_Bangsa.SelectedValue = Rdr("bangsa")
            Cb_Agama.SelectedValue = Rdr("agama")
            Cb_Kahwin.SelectedIndex = Rdr("t_kahwin")
            Tx_TP_Alamat.Text = Rdr("tp_alamat")
            Tx_TP_Poskod.Text = Rdr("tp_poskod")
            Tx_TP_Bandar.Text = Rdr("tp_bandar")
            If Not IsDBNull(Rdr("dc_negeri")) Then Cb_TP_Negeri.Items.FindByText(Rdr("dc_negeri")).Selected = True
            Tx_Tel.Text = Rdr("tel")
            Tx_Emel.Text = Rdr("emel")
            Tx_W_Nama.Text = Rdr("w_nama")
            'Fixing null field error #803722 26112020 - OSH  
            If Not IsDBNull(Rdr("w_alamat")) Then Tx_W_Alamat.Text = Rdr("w_alamat") Else Tx_W_Alamat.Text = " "
            If Not IsDBNull(Rdr("w_poskod")) Then Tx_W_Poskod.Text = Rdr("w_poskod") Else Tx_W_Poskod.Text = " "
            If Not IsDBNull(Rdr("w_bandar")) Then Tx_W_Bandar.Text = Rdr("w_bandar") Else Tx_W_Bandar.Text = " "

            'Comment Original 26112020 -OSH 
            'Tx_W_Alamat.Text = Rdr("w_alamat")
            'Tx_W_Poskod.Text = Rdr("w_poskod")
            'Tx_W_Bandar.Text = Rdr("w_bandar")
            ''Comment Original 21082020 -OSH 
            'If Not IsDBNull(Rdr("w_negeri")) Then Cb_W_Negeri.Items.FindByText(Rdr("w_negeri")).Selected = True
            If Cb_NoKP.SelectedItem.Text = "PASPORT" Then
                Cb_W_Negeri.Items.FindByText("LUAR NEGARA").Selected = True : Cb_W_Negeri.Enabled = False
            Else
                If Not IsDBNull(Rdr("w_negeri")) And Rdr("w_negeri") > 0 Then
                    'Comment Original 26112020 - OSH 
                    'Cb_W_Negeri.Items.FindByText(Rdr("w_negeri")).Selected = True : Cb_W_Negeri.Enabled = False
                    'Fixing values selected Ticket #803722 26112020 - OSH  
                    Cb_W_Negeri.Items.FindByValue(Rdr("w_negeri")).Selected = True : Cb_W_Negeri.Enabled = False
                Else
                    Cb_W_Negeri.Enabled = True
                End If
            End If
            'Comment Original 21082020 - OSH 
            'If Not IsDBNull(Rdr("w_negara2")) Then
            '    If Rdr("w_negara2") = "MALAYSIA" Then Fn_Negara0(1) Else Fn_Negara0(0)
            '    Cb_W_Negara.Items.FindByText(Rdr("w_negara2")).Selected = True
            'Else
            '    If Rdr("dc_negara") = "MALAYSIA" Then Fn_Negara0(1) Else Fn_Negara0(0)
            'End If
            'Fix bugs db field name 21082020 - OSH

            'If Not IsDBNull(Rdr("w_negara")) Then
            '    If Rdr("w_negara") = "1" Then Fn_Negara0(1) Else Fn_Negara0(0)
            '    Cb_W_Negara.Items.FindByValue(Rdr("w_negara")).Selected = True
            'Else
            '    If Rdr("dc_negara") = "1" Then Fn_Negara0(1) Else Fn_Negara0(0)
            'End If

            'Comment Original 27042022 - OSH 
            'Fixing load values set Ticket #803722 26112020 - OSH  
            If Not IsDBNull(Rdr("warganegara")) Then
                If Rdr("warganegara") = "1" Then
                    Fn_Negara0(1)
                Else
                    Fn_Negara0(0)
                End If
            End If
            'Comment Original 03122020 - OSH 
            'If Not IsDBNull(Rdr("w_negara")) And IsNumeric(Rdr("w_negara")) = True And Rdr("w_negara") > 0 Then Cb_W_Negara.Items.FindByValue(Rdr("w_negara")).Selected = True
            'Fix null value 03122020 - OSH 
            If Not IsDBNull(Rdr("w_negara")) And Not String.IsNullOrEmpty(Rdr("w_negara").ToString) Then
                If IsNumeric(Rdr("w_negara")) And Rdr("w_negara") > 0 Then
                    Cb_W_Negara.Items.FindByValue(Rdr("w_negara")).Selected = True
                End If
            End If
            Tx_W_Tel.Text = Rdr("w_tel")
            If Not IsDBNull(Rdr("dc_kolej")) Then Cb_Kolej.Items.FindByText(Rdr("dc_kolej")).Selected = True
            If Not IsDBNull(Rdr("dc_tajaan")) Then Cb_Tajaan.Items.FindByText(Rdr("dc_tajaan")).Selected = True
            'Improve security months and years read-only 17092019 - OSH 
            If Not IsDBNull(Rdr("sesi_bulan")) Then Cb_Sesi_Bulan.SelectedIndex = Rdr("sesi_bulan") : Cb_Sesi_Bulan.Enabled = False
            If Not IsDBNull(Rdr("sesi_tahun")) Then Cb_Sesi_Tahun.Items.FindByText(Rdr("sesi_tahun")).Selected = True : Cb_Sesi_Tahun.Enabled = False
            'Comment Original 17092019 - OSH 
            'If Not IsDBNull(Rdr("sesi_bulan")) Then Cb_Sesi_Bulan.SelectedIndex = Rdr("sesi_bulan")
            'If Not IsDBNull(Rdr("sesi_tahun")) Then Cb_Sesi_Tahun.Items.FindByText(Rdr("sesi_tahun")).Selected = True
            'Improve security read-only training date 17092019 - OSH 
            If Not IsDBNull(Rdr("tkh_latihan_mula")) Then Tx_M_Latihan.Text = Rdr("tkh_latihan_mula") : Tx_M_Latihan.Enabled = False
            'Comment Original 17092019 - OSH 
            'If Not IsDBNull(Rdr("tkh_latihan_mula")) Then Tx_M_Latihan.Text = Rdr("tkh_latihan_mula") 
            If Not IsDBNull(Rdr("tkh_latihan_tamat")) Then Tx_T_Latihan.Text = Rdr("tkh_latihan_tamat")

            If Not IsDBNull(Rdr("markah")) Then Tx_Markah.Text = Rdr("markah")
            If Not IsDBNull(Rdr("cuti")) Then Tx_Cuti.Text = Rdr("cuti")
            If Not IsDBNull(Rdr("cuti_ganti")) Then Tx_Cuti_Ganti.Text = Rdr("cuti_ganti")
            If Not IsDBNull(Rdr("cuti_sebab")) Then Tx_Cuti_Sebab.Text = Rdr("cuti_sebab")
            If Not IsDBNull(Rdr("tatatertib")) Then Cb_Tatatertib.SelectedIndex = Rdr("tatatertib")

            If Not IsDBNull(Rdr("ss1")) Then If Rdr("ss1") = 1 Then chk_Semak.Items(0).Selected = True
            If Not IsDBNull(Rdr("ss2")) Then If Rdr("ss2") = 1 Then chk_Semak.Items(1).Selected = True
            If Not IsDBNull(Rdr("ss3")) Then If Rdr("ss3") = 1 Then chk_Semak.Items(2).Selected = True
            If Not IsDBNull(Rdr("ss4")) Then If Rdr("ss4") = 1 Then chk_Semak.Items(3).Selected = True
            If Not IsDBNull(Rdr("ss5")) Then If Rdr("ss5") = 1 Then chk_Semak.Items(4).Selected = True
            If Not IsDBNull(Rdr("ss6")) Then If Rdr("ss6") = 1 Then chk_Semak.Items(5).Selected = True
            If Not IsDBNull(Rdr("ss7")) Then If Rdr("ss7") = 1 Then chk_Semak.Items(6).Selected = True
            If Not IsDBNull(Rdr("ss8")) Then If Rdr("ss8") = 1 Then chk_Semak.Items(7).Selected = True

            'Data Confirmation Checkbox 21102022 - OSH 
            'If Not IsDBNull(Rdr("Id_Sah_Data_Tata_Periksa_Akhir")) And Not IsDBNull(Rdr("Tkh_Sah_Data_Tata_Periksa_Akhir")) Then ChkSah.Checked = True
        End If
            Rdr.Close()

        'Reset All Subject Boxes and Pre-populate blanks 23082022 - OSH 
        'SPM , O-LEVEL 
        Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
        Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")

        'SPTM, A-LEVEL
        Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
        Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")

        If Cb_Kursus.SelectedIndex = 4 Then
            Cb_Kelayakan.Enabled = False
        Else
            'Fix Load Academic Results Populate 22082022 - OSH 
            Cmd.CommandText = "select distinct id_jenis from pelatih_kelayakan where nokp = '" & Session("nokp") & "' "
            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                Isi_Subjek(Rdr(0).ToString)
            End While
            Rdr.Close()
            Isi_Kelayakan()

            'Comment Original 18082020 - OSH 
            'Isi_Subjek(Cb_Kelayakan.SelectedItem.Text)
            'If Cb_Kelayakan.SelectedItem.Text = "STPM" Then Isi_Kelayakan(1) Else Isi_Kelayakan(0)
        End If

        'Comment Original 26042022 -OSH  
        Panel2.Enabled = False : Panel3.Enabled = False : Panel4.Enabled = False : Panel5.Enabled = False : Panel6.Enabled = False : PanelSemak.Enabled = False
        'If Cb_Kelayakan.SelectedItem.Text = "SRP" Or Cb_Kelayakan.SelectedItem.Text = "PMR" Then
        'If Cb_Kelayakan.SelectedItem.Text = "SRP" Or Cb_Kelayakan.SelectedItem.Text = "PMR" Then
        '        Panel6.Visible = False
        '        Panel5.Visible = False
        '        Panel4.Visible = False
        '        Panel3.Visible = False
        '    End If
        '    If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then
        '        Panel6.Visible = False
        '        Panel5.Visible = False
        '        Panel4.Visible = False
        '        Panel3.Visible = False
        '    End If
        '    If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
        '        Panel6.Visible = False
        '        Panel4.Visible = False
        '        Panel3.Visible = False
        '    End If
        '    If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then
        '        Panel5.Visible = False
        '        Panel4.Visible = False
        '        Panel2.Visible = False
        '    End If
        '    If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
        '        Panel6.Visible = False
        '        Panel4.Visible = False
        '        Panel2.Visible = False
        '    End If

        ''LOAD DEGREE LEVEL -INTERNATIONAL STUDENT EQUAL ACADEMIC PROFILE 19082020 - OSH 
        '    If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedItem.Text = "SETARAF" Then
        '        'Isi_Subjek(4) : Isi_Kelayakan 
        '        'Isi_Subjek(5): Isi_Kelayakan
        '        Isi_Subjek_1(5) : Isi_Kelayakan() 'SPM AND O-LEVEL
        '        Isi_Subjek_2(4) : Isi_Kelayakan() ' STPM AND A-LEVEL
        '    End If

        '    If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
        '    Panel5.Visible = False
        '    Panel3.Visible = False
        '    Panel2.Visible = False
        'End If
        'If Cb_Kelayakan.SelectedItem.Text = "" Then
        '    Panel6.Visible = False
        '    Panel5.Visible = False
        '    Panel4.Visible = False
        '    Panel3.Visible = False
        '    Cb_Kelayakan.Items.Add("SPM")
        '    Cb_Kelayakan.SelectedIndex = 1
        'End If
        'Bt4.Enabled = False
        'Bt5.Enabled = False
        'Bt6.Enabled = False
        'Bt7.Enabled = False
        'Bt8.Enabled = False

        Cb_Kursus.Enabled = False
        Cb_Kelayakan.Enabled = False : Cb_Kelayakan_Taraf.Enabled = False

        'Cb_Kpts1.Enabled = False
        'Cb_Kpts2.Enabled = False
        'Cb_Kpts3.Enabled = False
        'Cb_Kpts4.Enabled = False
        'Cb_Kpts5.Enabled = False
        'Cb_Kpts6.Enabled = False
        'Cb_Kpts7.Enabled = False
        'Cb_Kpts8.Enabled = False
        'Cb_Kpts9.Enabled = False 
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'If Not Akses_Pg("P1", "Pelatih_Pinda", Session("AKSES"), Session("MODUL")) Then
        '    Session("Msg_Tajuk") = "PEPERIKSAAN"
        '    Session("Msg_Isi") = "Akses Terhad"
        '    Response.Redirect("p0_Mesej.aspx")
        '    Exit Sub
        'End If

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        Cb_W_Negeri.Items.Clear()
        Cb_W_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negeri.Items.Add(Rdr(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KURSUS
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cb_Sesi_Bulan.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI")
        Cb_Sesi_Bulan.Items.Add("FEBRUARI")
        Cb_Sesi_Bulan.Items.Add("MAC")
        Cb_Sesi_Bulan.Items.Add("APRIL")
        Cb_Sesi_Bulan.Items.Add("MEI")
        Cb_Sesi_Bulan.Items.Add("JUN")
        Cb_Sesi_Bulan.Items.Add("JULAI")
        Cb_Sesi_Bulan.Items.Add("OGOS")
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER")
        Cb_Sesi_Bulan.Items.Add("OKTOBER")
        Cb_Sesi_Bulan.Items.Add("NOVEMBER")
        Cb_Sesi_Bulan.Items.Add("DISEMBER")

        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        For i = 0 To 5
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i)
        Next

        'KOLEJ
        Cb_Kolej.Items.Add(Session("Dc_KOLEJ"))
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Session("Id_KOLEJ")

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'BANGSA-07092013-OSH
        Cmd.CommandText = "SELECT DC_RACE,ID_RACE FROM SPMJ_REF_RACE ORDER BY ID_RACE"
        Rdr = Cmd.ExecuteReader()
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("")
        While Rdr.Read
            Cb_Bangsa.Items.Add(Rdr(0))
            Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()


        'KELAYAKAN BAHASA INGGERIS 
        Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NATIVE = 1"

        Rdr = Cmd.ExecuteReader()
        Cb_Luar.Items.Clear()
        Cb_Luar.Items.Add("")
        While Rdr.Read
            Cb_Luar.Items.Add(Rdr(0))
            Cb_Luar.Items.Item(Cb_Luar.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()


        'KELAYAKAN MATRIKULASI
        Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NATIVE = 1"

        Rdr = Cmd.ExecuteReader()
        Cb_SbjM.Items.Clear()
        Cb_SbjM.Items.Add("")
        While Rdr.Read
            Cb_SbjM.Items.Add(Rdr(0))
            Cb_SbjM.Items.Item(Cb_SbjM.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cn.Close()

        Cb_Sesi_Bln.Items.Add("(BULAN)")
        Cb_Sesi_Bln.Items.Item(0).Value = "0"
        Cb_Sesi_Bln.Items.Add("JANUARI")
        Cb_Sesi_Bln.Items.Item(1).Value = "1"
        Cb_Sesi_Bln.Items.Add("FEBRUARI")
        Cb_Sesi_Bln.Items.Item(2).Value = "2"
        Cb_Sesi_Bln.Items.Add("MAC")
        Cb_Sesi_Bln.Items.Item(3).Value = "3"
        Cb_Sesi_Bln.Items.Add("APRIL")
        Cb_Sesi_Bln.Items.Item(4).Value = "4"
        Cb_Sesi_Bln.Items.Add("MEI")
        Cb_Sesi_Bln.Items.Item(5).Value = "5"
        Cb_Sesi_Bln.Items.Add("JUN")
        Cb_Sesi_Bln.Items.Item(6).Value = "6"
        Cb_Sesi_Bln.Items.Add("JULAI")
        Cb_Sesi_Bln.Items.Item(7).Value = "7"
        Cb_Sesi_Bln.Items.Add("OGOS")
        Cb_Sesi_Bln.Items.Item(8).Value = "8"
        Cb_Sesi_Bln.Items.Add("SEPTEMBER")
        Cb_Sesi_Bln.Items.Item(9).Value = "9"
        Cb_Sesi_Bln.Items.Add("OKTOBER")
        Cb_Sesi_Bln.Items.Item(10).Value = "10"
        Cb_Sesi_Bln.Items.Add("NOVEMBER")
        Cb_Sesi_Bln.Items.Item(11).Value = "11"
        Cb_Sesi_Bln.Items.Add("DISEMBER")
        Cb_Sesi_Bln.Items.Item(0).Value = "12"

        Cb_Sesi_Thn.Items.Add("(TAHUN)")
        For i = 0 To 5
            Cb_Sesi_Thn.Items.Add(Year(Now) - i)
        Next


        Cb_Tatatertib.Items.Add("YA")
        Cb_Tatatertib.Items.Add("TIDAK")
        'test 24102013
        'Tx_Tkh_Lahir.Text = "07/10/1988"
        'Tx_T_Latihan.Text = "22/08/2013"

        'Add Equal Level Populate 18082020 - OSH 
        'Tempatan/ antarabangsa
        Cb_Kelayakan_Taraf.Items.Clear()
        Cb_Kelayakan_Taraf.Items.Add("(PILIHAN)")
        Cb_Kelayakan_Taraf.Items(0).Value = 0
        Cb_Kelayakan_Taraf.Items.Add("-")
        Cb_Kelayakan_Taraf.Items(1).Value = 1
        Cb_Kelayakan_Taraf.Items.Add("SETARAF")
        Cb_Kelayakan_Taraf.Items(2).Value = 2

        'Add STPM and A Level populate 19082020 - OSH 
        'STPM OR A-LEVEL
        Cb_Aliran.Items.Clear()
        Cb_Aliran.Items.Add("(PILIHAN)")
        Cb_Aliran.Items(0).Value = 0
        Cb_Aliran.Items.Add("STPM")
        Cb_Aliran.Items(1).Value = 1
        Cb_Aliran.Items.Add("A-LEVEL")
        Cb_Aliran.Items(2).Value = 2

        'Isi_Subjek("SPM")
        Isi()
    End Sub

    Public Sub Fn_Negara(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"
        Else
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Public Sub Fn_Negara0(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"
        Else
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_W_Negara.Items.Clear()
        Cb_W_Negara.Items.Add("")
        While Rdr.Read
            Cb_W_Negara.Items.Add(Rdr(0))
            Cb_W_Negara.Items.Item(Cb_W_Negara.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        'Mandatori...
        Dim X As String = ""
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.Text.Trim = "-" Then X += "Warganegara, "
        'Add Mandatory Field Brith Date & Brith Place 24102013 - OSH
        If Tx_Tkh_Lahir.Text.Trim = "" Then X += "Tarikh Lahir, "
        If Tx_Tp_Lahir.Text.Trim = "" Then X += "Tempat Lahir, "
        If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
        If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
        If Cb_Agama.Text.Trim = "" Then X += "Agama, "
        If Cb_Kahwin.Text.Trim = "" Then X += "Taraf Perkahwinan, "
        If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
        If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
        If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
        If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
        If Tx_Tel.Text.Trim = "" Then X += "No. Telefon, "
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "

        If Tx_Markah.Text = "" Then Tx_Markah.Text = "0"
        If Tx_Markah.Text = "" Then X += "Markah Berterusan(Jumlah Berterusan), "
        If Tx_Cuti.Text = "" Then Tx_Cuti.Text = "0"
        If Not IsNumeric(Tx_Cuti.Text) Then X += "Jumlah Cuti Sakit, "
        If Tx_Cuti_Ganti.Text = "" Then Tx_Cuti_Ganti.Text = "0"
        If Not IsNumeric(Tx_Cuti_Ganti.Text) Then X += "Jumlah Cuti Sakit Yang Diganti, "
        'Add Calculate For Leave 16082013- OSH
        If (Tx_Cuti.Text - Tx_Cuti_Ganti.Text) < 0 Then X += "Jumlah Cuti Sakit Yang sebenar, "

        'Comment Original 17082022 - OSH
        'If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila lengkapkan maklumat berikut: " & X) : Exit Sub

        'Improve student data verification 17082022 -OSH 
        'If X.Trim = "" Then
        '    If ChkSah.Checked = False Then
        '        Msg(Me, "Sila sahkan maklumat pelatih ini adalah benar ! ") : ChkSah.Focus() : Exit Sub
        '    End If
        'Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila lengkapkan maklumat berikut: " & X) : Exit Sub
        'End If

        'Discontiue not CGPA score 17082022 - OSH
        ''markah berterusan (status)
        'If Not IsNumeric(Tx_Markah.Text) Then
        '    If Tx_Markah.Text.ToUpper = "GAGAL" Or Tx_Markah.Text.ToUpper = "LULUS" Then  Else Msg(Me, "GAGAL/LULUS") : Exit Sub
        'End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Ori 04082014 -OSH
        '  Cmd.CommandText = "select nokp, isnull(status,'0') from pelatih where nokp = '" & Tx_NoKP.Text & "'"

        'Comment Ori 11082015 -OSH
        'Fixing Select parameter record 04082014 -OSH
        'Cmd.CommandText = "select nokp, isnull(status,'0') from pelatih where  status is null and  nokp = '" & Tx_NoKP.Text & "'"
        ' Fixing query to generic  resut for check open record status 12082015 -OSH
        Cmd.CommandText = "select nokp, status from pelatih where nokp = '" & Tx_NoKP.Text & "' and j_kursus = '" & Session("j_kursus") & "'"

        'Check status seal record after pass exam  
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            'If Rdr(1) = 0 Then
            ' Check status null for update open record by category 12082015-OSH
            If IsDBNull(Rdr("status")) = True Then

                Dim SQL As String
                Dim y, y1, y2 As DateTime
                Dim z, z1, z2 As String

                'Tarikh Lahir
                z = Tx_Tkh_Lahir.Text.Trim
                If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
                    y = DateTime.ParseExact(z, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                    z = y.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                    z = "'" & z & "'"
                Else
                    z = "NULL"
                End If


                'Tarikh Mula Latihan
                z1 = Tx_M_Latihan.Text.Trim
                If Tx_M_Latihan.Text.Trim <> String.Empty Then
                    y1 = DateTime.ParseExact(z1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                    z1 = y1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                    z1 = "'" & z1 & "'"
                Else
                    z1 = "NULL"
                End If

                'Tarikh Tamat Latihan
                z2 = Tx_T_Latihan.Text.Trim
                If Tx_T_Latihan.Text.Trim <> String.Empty Then
                    y2 = DateTime.ParseExact(z2, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                    z2 = y2.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                    z2 = "'" & z2 & "'"
                Else
                    z = "NULL"
                End If
                SQL = "update pelatih set " &
                                   "warganegara = '" & Cb_Warga.SelectedItem.Value & "', " &
                                   "tkh_lahir = " & z & "," &
                                   "tpt_lahir = '" & Apo(Tx_Tp_Lahir.Text.Trim) & "'," &
                                   "jantina = '" & Cb_Jantina.SelectedIndex & "', " &
                                   "bangsa = '" & Cb_Bangsa.SelectedIndex & "', " &
                                   "agama = '" & Cb_Agama.SelectedIndex & "', " &
                                   "t_kahwin = '" & Cb_Kahwin.SelectedIndex & "', " &
                                   "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim) & "', " &
                                   "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "', " &
                                   "tp_bandar = '" & Apo(Tx_TP_Bandar.Text.Trim) & "', " &
                                   "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "', " &
                                   "tel = '" & Tx_Tel.Text.Trim & "', " &
                                   "emel = '" & Tx_Emel.Text.Trim & "', " &
                                   "w_nama = '" & Apo(Tx_W_Nama.Text.Trim) & "', " &
                                   "w_alamat = '" & Apo(Tx_W_Alamat.Text.Trim) & "', " &
                                   "w_poskod = '" & Tx_W_Poskod.Text.Trim & "'," &
                                   "w_bandar = '" & Apo(Tx_W_Bandar.Text.Trim) & "'," &
                                   "w_negeri = '" & Cb_W_Negeri.SelectedItem.Value & "'," &
                                   "w_negara = '" & Cb_W_Negara.SelectedItem.Value & "'," &
                                   "w_tel = '" & Tx_W_Tel.Text.Trim & "'," &
                                   "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," &
                                   "sesi_bulan = '" & Cb_Sesi_Bulan.SelectedIndex & "'," &
                                   "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedItem.Text & "'," &
                                   "tkh_latihan_mula = " & z1 & "," &
                                   "tkh_latihan_tamat = " & z2 & "," &
                                   "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "', " &
                                   "markah = '" & Tx_Markah.Text.ToUpper & "', " &
                                   "cuti = '" & Tx_Cuti.Text & "', " &
                                   "cuti_ganti = '" & Tx_Cuti_Ganti.Text & "', " &
                                   "cuti_sebab = '" & Apo(Tx_Cuti_Sebab.Text.ToUpper) & "', " &
                                   "tatatertib = '" & Cb_Tatatertib.SelectedIndex & "', " &
                                   "ss1 = " & SSemak(0) & ", " &
                                   "ss2 = " & SSemak(1) & ", " &
                                   "ss3 = " & SSemak(2) & ", " &
                                   "ss4 = " & SSemak(3) & ", " &
                                   "ss5 = " & SSemak(4) & ", " &
                                   "ss6 = " & SSemak(5) & ", " &
                                   "ss7 = " & SSemak(6) & ", " &
                                   "ss8 = " & SSemak(7) & ", " &
                                   "mod_id = '" & Session("Id_PG") & "', " &
                                   "mod_tkh = getdate()  " &
                                   "where nokp ='" & Rdr("nokp") & "' and j_kursus = '" & Session("j_kursus") & "'"

                'Final verification and confirmation student final college exam details 21102022 - OSH  
                If ChkSah.Checked = True Then
                    SQL += "update pelatih set " &
                    "Id_Sah_Data_Tata_Periksa_Akhir = '" & Session("Id_PG") & "', " &
                    "Tkh_Sah_Data_Tata_Periksa_Akhir = getdate() " &
                    "where nokp ='" & Rdr("nokp") & "' and j_kursus = '" & Session("j_kursus") & "'"
                End If

                Rdr.Close()
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Session("Msg_Tajuk") = "Pinda Rekod Pelatih"
                Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
                Response.Redirect("Mesej.aspx")

            Else
                Session("Msg_Tajuk") = "Pinda Rekod Pelatih"
                Session("Msg_Isi") = "Proses Kemaskini Tidak Dibenarkan"
                Response.Redirect("Mesej.aspx")
                Exit Sub
            End If

        Else
            Rdr.Close()
        End If
        Cn.Close()
    End Sub

    Private Sub ChkSah_CheckedChanged(sender As Object, e As EventArgs) Handles ChkSah.CheckedChanged
        'Enable Submit Button When Declaration Is Confirm  20042023 - OSH
        If ChkSah.Checked = True Then
            cmdHantar.Enabled = True
        ElseIf ChkSah.Checked = False Then
            cmdHantar.Enabled = False
        End If
    End Sub
End Class