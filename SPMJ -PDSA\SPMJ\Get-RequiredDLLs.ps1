# PowerShell Script to Download Required DLLs for SPMJ Project
# Run this script to download the missing AjaxControlToolkit and iTextSharp libraries

param(
    [string]$BinPath = ".\bin"
)

Write-Host "Downloading required DLLs for SPMJ .NET 3.5 project..." -ForegroundColor Green

# Create bin directory if it doesn't exist
if (!(Test-Path $BinPath)) {
    New-Item -ItemType Directory -Path $BinPath -Force | Out-Null
    Write-Host "Created bin directory: $BinPath" -ForegroundColor Yellow
}

# Function to download file with retry
function Download-File {
    param($Url, $OutputPath, $Description)
    
    Write-Host "Downloading $Description..." -ForegroundColor Cyan
    try {
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -UseBasicParsing
        Write-Host "✓ Downloaded: $Description" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Failed to download: $Description" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# URLs for .NET 3.5 compatible libraries
$downloads = @(
    @{
        Url = "https://github.com/DevExpress/AjaxControlToolkit/releases/download/v3.0.30930/AjaxControlToolkit.Binary.NET35.zip"
        File = "AjaxControlToolkit.zip"
        Description = "AjaxControlToolkit for .NET 3.5"
    },
    @{
        Url = "https://github.com/itext/itextsharp/releases/download/5.5.10/itextsharp-dll-core.zip"
        File = "itextsharp.zip"
        Description = "iTextSharp 5.5.10 Core DLLs"
    }
)

# Alternative: Manual download instructions
Write-Host "`n" + "="*60 -ForegroundColor Yellow
Write-Host "MANUAL DOWNLOAD INSTRUCTIONS" -ForegroundColor Yellow
Write-Host "="*60 -ForegroundColor Yellow

Write-Host "`n1. AjaxControlToolkit for .NET 3.5:" -ForegroundColor Cyan
Write-Host "   - Download from: https://www.nuget.org/packages/AjaxControlToolkit/3.0.30930"
Write-Host "   - Or from: https://ajaxcontroltoolkit.codeplex.com/"
Write-Host "   - Extract AjaxControlToolkit.dll to: $BinPath"

Write-Host "`n2. iTextSharp 5.5.10 for .NET 3.5:" -ForegroundColor Cyan
Write-Host "   - Download from: https://www.nuget.org/packages/iTextSharp/5.5.10"
Write-Host "   - Or from: https://github.com/itext/itextsharp/releases/tag/5.5.10"
Write-Host "   - Extract itextsharp.dll to: $BinPath"
Write-Host "   - Extract itextsharp.xmlworker.dll to: $BinPath"

Write-Host "`n3. Alternative: Use NuGet Package Manager in Visual Studio:" -ForegroundColor Cyan
Write-Host "   - Right-click project → Manage NuGet Packages"
Write-Host "   - Install: AjaxControlToolkit (version 3.x for .NET 3.5)"
Write-Host "   - Install: iTextSharp (version 5.5.10)"

Write-Host "`nAfter downloading, place these DLLs in: $BinPath" -ForegroundColor Green
Write-Host "Required files:" -ForegroundColor Green
Write-Host "  - AjaxControlToolkit.dll" -ForegroundColor White
Write-Host "  - itextsharp.dll" -ForegroundColor White
Write-Host "  - itextsharp.xmlworker.dll" -ForegroundColor White

# Try alternative approach: Create temporary stub DLLs
Write-Host "`n" + "="*60 -ForegroundColor Yellow
Write-Host "CREATING TEMPORARY STUB ASSEMBLIES" -ForegroundColor Yellow
Write-Host "="*60 -ForegroundColor Yellow
Write-Host "Note: These are for compilation only. Replace with real DLLs for full functionality." -ForegroundColor Red

# Check if we have the real DLLs already
$requiredDlls = @("AjaxControlToolkit.dll", "itextsharp.dll", "itextsharp.xmlworker.dll")
$missingDlls = @()

foreach ($dll in $requiredDlls) {
    $dllPath = Join-Path $BinPath $dll
    if (!(Test-Path $dllPath)) {
        $missingDlls += $dll
    } else {
        Write-Host "✓ Found: $dll" -ForegroundColor Green
    }
}

if ($missingDlls.Count -gt 0) {
    Write-Host "`nMissing DLLs: $($missingDlls -join ', ')" -ForegroundColor Red
    Write-Host "The project will compile with stubs, but may not have full functionality." -ForegroundColor Yellow
} else {
    Write-Host "`n✓ All required DLLs are present!" -ForegroundColor Green
}

Write-Host "`nScript completed. Check the bin directory for DLLs." -ForegroundColor Green
