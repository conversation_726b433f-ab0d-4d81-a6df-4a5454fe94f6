﻿Imports System.Data.OleDb
Imports System.Data.SqlClient
Public Class P3_APC_Mesej_J
    Inherits System.Web.UI.Page
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        SQL = ""
        Dim Cn As New SqlConnection(ServerId_SQL)

        'Comment Original 21082024 - OSH 
        '        'ADD CHECK PREVIOUS OR FUTURE APC LIST 04012019 - OSH 
        '        If Session("Flag_APC") = "P" Then 'Previous APC 
        '            SQL = " select * from ( select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN TERDAHULU',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.xm_jenis <> 'kpsl' and jp.nokp in " & Session("NoKP_APC") & "
        'union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN TERDAHULU',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) inner join pn_kpsl_lantikan_jb klj on jp.nokp= klj.nokp where jp.nokp in " & Session("NoKP_APC") & "
        'union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN TERDAHULU',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh_old jp inner join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.j_daftar = 2  and jp.nokp in " & Session("NoKP_APC") & " and jp.nokp not in (select nokp from pn_kpsl_lantikan_jb)) l
        'order by l.Nama"

        '        ElseIf Session("Flag_APC") = "F" Then  ' Future APC 
        '            SQL = " select * from ( select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.xm_jenis <> 'kpsl' and jp.nokp in " & Session("NoKP_APC") & "
        'union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) inner join pn_kpsl_lantikan_jb klj on jp.nokp= klj.nokp where jp.nokp in " & Session("NoKP_APC") & "
        'union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh_old jp inner join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.j_daftar = 2 and  jp.nokp in " & Session("NoKP_APC") & " and jp.nokp not in (select nokp from pn_kpsl_lantikan_jb)) l
        'order by l.Nama"
        '        End If


        'Fix Private Sector KPSL Listing 21082024 -OSH 
        If Session("Flag_APC") = "P" Then 'Previous APC 
            SQL = " select * from ( select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN TERDAHULU',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.xm_jenis <> 'kpsl' and jp.nokp in " & Session("NoKP_APC") & "
        union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN TERDAHULU',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) inner join pn_kpsl_lantikan_jb klj on jp.nokp= klj.nokp where jp.nokp in " & Session("NoKP_APC") & "
        union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN TERDAHULU',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh_old jp inner join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.j_daftar = 2  and jp.nokp in " & Session("NoKP_APC") & " and jp.nokp not in (select nokp from pn_kpsl_lantikan_jb)
        union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN TERDAHULU',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.j_daftar = 1  and jp.nokp in " & Session("NoKP_APC") & " and jp.nokp not in (select nokp from pn_kpsl_lantikan_jb)) l
        order by l.Nama"

        ElseIf Session("Flag_APC") = "F" Then  ' Future APC 
            SQL = " select * from ( select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.xm_jenis <> 'kpsl' and jp.nokp in " & Session("NoKP_APC") & "
        union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) inner join pn_kpsl_lantikan_jb klj on jp.nokp= klj.nokp where jp.nokp in " & Session("NoKP_APC") & "
        union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh_old jp inner join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.j_daftar = 2 and  jp.nokp in " & Session("NoKP_APC") & " and jp.nokp not in (select nokp from pn_kpsl_lantikan_jb)
        union select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', RIGHT(CONVERT(CHAR(7),1000000 + jpa.apc_no ),6) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA',RIGHT(CONVERT(CHAR(7),1000000 + jpa2.apc_no ),6)+ '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_batal = 0 and jpa3.apc_tahun = year(getdate()) where jp.j_daftar = 1 and  jp.nokp in " & Session("NoKP_APC") & " and jp.nokp not in (select nokp from pn_kpsl_lantikan_jb)) l
        order by l.Nama"
        End If

        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        'ADD LOADING GRID TABLE INFINITINE 15082013
        List_Adp.SelectCommand.CommandTimeout = "0"
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        GridViewExportUtil.Export("Laporan.xls", Gd)
    End Sub


    Public Sub CariAPC(ByVal X As Integer, ByVal K As Integer, ByVal I As String, ByVal P As Char)

        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Data As New DataSet
        Dim List_Adp As SqlDataAdapter

        List_Adp = New SqlDataAdapter("GetAPCList", Cn)

        List_Adp.SelectCommand.CommandType = CommandType.StoredProcedure
        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@t_reg",
                                                                    SqlDbType.Int))
        List_Adp.SelectCommand.Parameters("@t_reg").Value = X

        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@work_id",
                                                               SqlDbType.Int))
        List_Adp.SelectCommand.Parameters("@work_id").Value = K

        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@id_pg",
                                                              SqlDbType.VarChar, 14))
        List_Adp.SelectCommand.Parameters("@id_pg").Value = I

        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@t_process",
                                                             SqlDbType.VarChar, 14))
        List_Adp.SelectCommand.Parameters("@t_process").Value = P

        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@RowCount",
                                                                   SqlDbType.Int, 4))
        List_Adp.SelectCommand.Parameters("@RowCount").Direction = ParameterDirection.Output

        List_Data = New DataSet()
        List_Adp.Fill(List_Data, "APClist")

        Gd.DataSource = List_Data.Tables("APClist")
        Gd.DataBind()
        Gd.Visible = True

        List_Adp.Dispose()
        Cn.Close()

    End Sub


End Class