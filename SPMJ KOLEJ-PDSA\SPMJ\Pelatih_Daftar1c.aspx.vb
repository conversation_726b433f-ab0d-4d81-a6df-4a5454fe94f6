﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.IO
Imports System.Data
Public Class Pelatih_Daftar1c
    Inherits System.Web.UI.Page

    Dim CLP, LP As Int16
    Dim Tx_Id_Subjek As String = ""
    Dim id_session As String
    Dim Ax_Id_Subjek As String = ""

    Public Sub Isi_Subjek(ByVal X As Int16)
        'Add conditions for correct clear up 070Cb_Kelayakan2023 - OSH
        'IGCSE
        If X = 1 Or X = 2 Then
            Cb_Sbj1.Items.Clear() : Cb_Sbj2.Items.Clear() : Cb_Sbj3.Items.Clear() : Cb_Sbj4.Items.Clear() : Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
            Cb_Sbj1.Items.Add("") : Cb_Sbj2.Items.Add("") : Cb_Sbj3.Items.Add("") : Cb_Sbj4.Items.Add("") : Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
            Cb_Sbj2.Visible = False : Cb_Sbj3.Visible = False : Cb_Sbj4.Visible = False : Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
            Cb_Kpts2.Visible = False : Cb_Kpts3.Visible = False : Cb_Kpts4.Visible = False : Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        ElseIf X = 3 Or X = 4 Then
            Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
            Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
            Cb_SbjT1.Visible = True ': Cb_SbjT2.Visible = True : Cb_SbjT3.Visible = True : Cb_SbjT4.Visible = True : Cb_SbjT5.Visible = True
            Cb_KptsT1.Visible = True ': Cb_KptsT2.Visible = True : Cb_KptsT3.Visible = True : Cb_KptsT4.Visible = True : Cb_KptsT5.Visible = True
            'Enable list numbers and add button 22112023 -OSH 
            Text_No_S1.Visible = True : Bt_STPM1.Visible = True
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Add Load SPM / O-Level 26072023 - OSH 
        If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
        If X = 2 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'O-L' order by dc_subjek"
        If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
        If X = 4 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'A-L' order by dc_subjek"
        If X = 0 Then Exit Sub

        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If X = 1 Or X = 2 Then 'SPM / O-Level
                Cb_Sbj1.Items.Add(Rdr(0)) : Cb_Sbj1.Items.Item(Cb_Sbj1.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj2.Items.Add(Rdr(0)) : Cb_Sbj2.Items.Item(Cb_Sbj2.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj3.Items.Add(Rdr(0)) : Cb_Sbj3.Items.Item(Cb_Sbj3.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj4.Items.Add(Rdr(0)) : Cb_Sbj4.Items.Item(Cb_Sbj4.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
            ElseIf X = 3 Or X = 4 Then  ' STPM or A-Level
                Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
                Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
            End If
        End While
        Rdr.Close()
        Cn.Close()

        'Add SPM grade loaded 27072023 - OSH 
        If X = 1 Then
            Gred_SPM(Cb_Kpts1)
            Gred_SPM(Cb_Kpts2)
            Gred_SPM(Cb_Kpts3)
            Gred_SPM(Cb_Kpts4)
            Gred_SPM(Cb_Kpts5)
            Gred_SPM(Cb_Kpts6)
            Gred_SPM(Cb_Kpts7)
            Gred_SPM(Cb_Kpts8)
            Gred_SPM(Cb_Kpts9)

            'Add O-Level grade loaded 27072023 - OSH 
        ElseIf X = 2 Then
            Gred_O_A_Level(Cb_Kpts1)
            Gred_O_A_Level(Cb_Kpts2)
            Gred_O_A_Level(Cb_Kpts3)
            Gred_O_A_Level(Cb_Kpts4)
            Gred_O_A_Level(Cb_Kpts5)
            Gred_O_A_Level(Cb_Kpts6)
            Gred_O_A_Level(Cb_Kpts7)
            Gred_O_A_Level(Cb_Kpts8)
            Gred_O_A_Level(Cb_Kpts9)

        ElseIf X = 3 Then
            Gred_STPM(Cb_KptsT1)
            Gred_STPM(Cb_KptsT2)
            Gred_STPM(Cb_KptsT3)
            Gred_STPM(Cb_KptsT4)
            Gred_STPM(Cb_KptsT5)

            'Populate A-Level Grades 29072019 -OSH 
        ElseIf X = 4 Then
            Gred_O_A_Level(Cb_KptsT1)
            Gred_O_A_Level(Cb_KptsT2)
            Gred_O_A_Level(Cb_KptsT3)
            Gred_O_A_Level(Cb_KptsT4)
            Gred_O_A_Level(Cb_KptsT5)
        End If
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Comment Original 05042022 - OSH 
        'If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login.aspx")
        'New Landing Page05042022-OSH      
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("Login_j.aspx")
        'If Not Akses_Pg("P1", "Pelatih_Daftar", Session("AKSES"), Session("MODUL")) Then
        '    Session("Msg_Tajuk") = "PEPERIKSAAN"
        '    Session("Msg_Isi") = "Akses Terhad"
        '    Response.Redirect("p0_Mesej.aspx")
        '    Exit Sub
        'End If

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'BULAN
        Cb_Sesi_Bulan.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI") : Cb_Sesi_Bulan.Items.Item(1).Value = "1"
        Cb_Sesi_Bulan.Items.Add("FEBRUARI") : Cb_Sesi_Bulan.Items.Item(2).Value = "2"
        Cb_Sesi_Bulan.Items.Add("MAC") : Cb_Sesi_Bulan.Items.Item(3).Value = "3"
        Cb_Sesi_Bulan.Items.Add("APRIL") : Cb_Sesi_Bulan.Items.Item(4).Value = "4"
        Cb_Sesi_Bulan.Items.Add("MEI") : Cb_Sesi_Bulan.Items.Item(5).Value = "5"
        Cb_Sesi_Bulan.Items.Add("JUN") : Cb_Sesi_Bulan.Items.Item(6).Value = "6"
        Cb_Sesi_Bulan.Items.Add("JULAI") : Cb_Sesi_Bulan.Items.Item(7).Value = "7"
        Cb_Sesi_Bulan.Items.Add("OGOS") : Cb_Sesi_Bulan.Items.Item(8).Value = "8"
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER") : Cb_Sesi_Bulan.Items.Item(9).Value = "9"
        Cb_Sesi_Bulan.Items.Add("OKTOBER") : Cb_Sesi_Bulan.Items.Item(10).Value = "10"
        Cb_Sesi_Bulan.Items.Add("NOVEMBER") : Cb_Sesi_Bulan.Items.Item(11).Value = "11"
        Cb_Sesi_Bulan.Items.Add("DISEMBER") : Cb_Sesi_Bulan.Items.Item(12).Value = "12"

        'TAHUN
        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        For i = 0 To 1 '5
            Cb_Sesi_Tahun.Items.Add(Year(Now) + i)
        Next

        'BANGSA-07092013-OSH
        Cmd.CommandText = "SELECT DC_RACE,ID_RACE FROM SPMJ_REF_RACE ORDER BY ID_RACE"
        Rdr = Cmd.ExecuteReader()
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("")
        While Rdr.Read
            Cb_Bangsa.Items.Add(Rdr(0))
            Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KOLEJ
        Cb_Kolej.Items.Add(Session("Dc_KOLEJ"))
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Session("Id_KOLEJ")
        Cb_Kolej.SelectedIndex = 0

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()





        ' Disable SPTM / A-LEVEL downdrop 01092023 - OSH
        Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
        Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
        Cb_SbjT1.Visible = False : Cb_SbjT2.Visible = False : Cb_SbjT3.Visible = False : Cb_SbjT4.Visible = False : Cb_SbjT5.Visible = False
        Cb_KptsT1.Visible = False : Cb_KptsT2.Visible = False : Cb_KptsT3.Visible = False : Cb_KptsT4.Visible = False : Cb_KptsT5.Visible = False


        'hiden panels by default 29082023 - OSH
        Panel1.Visible = False : Panel2.Visible = False : Panel5.Visible = False : Panel6.Visible = False : Panel7.Visible = False : Panel8.Visible = False : PanelSemak.Visible = False

        'Unable dropdown sesi pengambilan 03042023 - OSH
        Cb_Sesi_Bulan.Enabled = False : Cb_Sesi_Tahun.Enabled = False
    End Sub
    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Load_Kelayakan()
        If Cb_Kerakyatan.SelectedValue = "0" Or Cb_Kerakyatan.SelectedValue = "1" Then
            If Not Tx_M_Latihan.Text = "" Then
                Chk_Date_Policy()
            End If
        End If
    End Sub

    Public Sub Load_Panel()

        'Disable ComboBoxs if date, citizen aand courses correctly selected 29052023 -OSH  
        Cb_Kerakyatan.Enabled = False : Cb_Kursus.Enabled = False : Cb_Kelayakan.Enabled = False : Tx_M_Latihan.Enabled = False

        If Cb_Kursus.SelectedValue = 1 Then 'Diploma

            'Add SPM or O-Level 27072023 - OSH 
            If Cb_Kelayakan.SelectedValue = "0" Then
                LblSubjek.Text = "SPM" : LblSubjek.Font.Bold = True : LblSubjek.Enabled = False : Isi_Subjek(1) 'SPM
            ElseIf Cb_Kelayakan.SelectedValue = "1" Then
                LblSubjek.Text = "O-LEVEL" : LblSubjek.Font.Bold = True : LblSubjek.Enabled = False : Isi_Subjek(2) 'O-LEVEL
            End If

            If Cb_Kerakyatan.SelectedValue = "0" Then 'Warganegara
                Panel5.Visible = True : Panel7.Visible = False : Panel8.Visible = False ': Panel2.Visible = True : Panel1.Visible = True
            ElseIf Cb_Kerakyatan.SelectedValue = "1" Then 'Bukan Warganegara
                Panel5.Visible = True : Panel7.Visible = False : Panel8.Visible = True ': Panel2.Visible = True : Panel1.Visible = True
                Fn_Inggeris(0)
            End If

            PanelSemak.Visible = True
            Cb_Sbj1.Visible = True : Cb_Kpts1.Visible = True : Bt1.Visible = True
            Cb_Sbj1.Enabled = True : Cb_Kpts1.Enabled = True : Bt1.Enabled = True
            Text1.Visible = True

        ElseIf Cb_Kursus.SelectedValue = 3 Then 'Certificate

            If Cb_Kerakyatan.SelectedValue = "0" Then 'Warganegara
                Panel5.Visible = True : Panel7.Visible = False : Panel8.Visible = False : Panel2.Visible = False : Panel1.Visible = False
                PanelSemak.Visible = True
                LblSubjek.Text = "SPM" : LblSubjek.Font.Bold = True : LblSubjek.Enabled = False : Isi_Subjek(1)
                Cb_Sbj1.Visible = True : Cb_Kpts1.Visible = True : Bt1.Visible = True
                Cb_Sbj1.Enabled = True : Cb_Kpts1.Enabled = True : Bt1.Enabled = True
                Text1.Visible = True
            ElseIf Cb_Kerakyatan.SelectedValue = "1" Then 'Bukan Warganegara
                Panel5.Visible = False : Panel7.Visible = False : Panel8.Visible = False : Panel2.Visible = False : Panel1.Visible = False
            End If

        ElseIf Cb_Kursus.SelectedValue = "4" Or Cb_Kursus.SelectedValue = "8" Then 'Convertion Diploma and  Midwife: Panel2.Visible = True : Panel1.Visible = True

            If Cb_Kerakyatan.SelectedValue = "0" Then 'Citizen 
                Panel5.Visible = False : PanelSemak.Visible = False : Panel3.Visible = True : Panel2.Visible = True : Panel1.Visible = True
                'Call JQuery remove td 30082023 - OSH 
                'ClientScript.RegisterClientScriptBlock(Me.[GetType](), "", "Hidden();", True)
                'ElseIf Cb_Kerakyatan.SelectedValue = "1" Then 'Non-Citizen  
            End If

        ElseIf Cb_Kursus.SelectedValue = "5" Then 'Degree

            If Cb_Kerakyatan.SelectedValue = "0" Then 'Warganegara
                Panel5.Visible = True : Panel6.Visible = True : Panel7.Visible = True : Panel8.Visible = True : Panel2.Visible = False : Panel1.Visible = False
                PanelSemak.Visible = True
                LblSubjek.Text = "SPM" : LblSubjek.Font.Bold = True : LblSubjek.Enabled = False : Isi_Subjek(1)
                Cb_Sbj1.Visible = True : Cb_Kpts1.Visible = True : Bt1.Visible = True
                Cb_Sbj1.Enabled = True : Cb_Kpts1.Enabled = True : Bt1.Enabled = True
                Text1.Visible = True
                Fn_Inggeris(1) : Fn_Lanjutan(1)

                'Add STPM -degree -local 27112023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                    Panel7.Visible = False
                    Cb_Aliran.SelectedValue = 1 : Cb_Aliran.Enabled = False
                    Isi_Subjek(3) 'SPTM
                End If

                'Add STPM -degree -local 27112023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "A-LEVEL" Then
                    Panel7.Visible = False
                    Cb_Aliran.SelectedValue = 2 : Cb_Aliran.Enabled = False
                    Isi_Subjek(4) 'A-LEVEL
                End If

                'Add foundation - Degree - Local 27112023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "ASASI" Then
                    Panel6.Visible = False 'Hidden STPM / A-LEVEL 23112023 - OSH 
                    Cb_SbjM.SelectedValue = 2 : Cb_SbjM.Enabled = False
                End If


                'Add matriculation - Degree - Local 22112023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI" Then
                    Panel6.Visible = False 'Hidden STPM / A-LEVEL 23112023 - OSH 
                    Cb_SbjM.SelectedValue = 1 : Cb_SbjM.Enabled = False
                End If

                'Add Nursing Diploma- Degree - Local 22112023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "DIPLOMA (KEJURURAWATAN)" Then
                    Panel5.Visible = False : Panel6.Visible = False
                    Panel7.Visible = False : Panel8.Visible = False
                    PanelSemak.Visible = False
                End If


                'Add Non-Nursing Diploma- Degree - Local 05122023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "DIP. BUKAN KEJURURAWATAN" Then
                    Panel6.Visible = False
                    Cb_SbjM.SelectedValue = 3 : Cb_SbjM.Enabled = False
                End If


            ElseIf Cb_Kerakyatan.SelectedValue = "1" Then 'Bukan Warganegara
                Panel5.Visible = True : Panel6.Visible = True : Panel7.Visible = True : Panel8.Visible = True : Panel2.Visible = False : Panel1.Visible = False
                PanelSemak.Visible = True
                LblSubjek.Text = "O-LEVEL" : LblSubjek.Font.Bold = True : LblSubjek.Enabled = False : Isi_Subjek(2)
                Cb_Sbj1.Visible = True : Cb_Kpts1.Visible = True : Bt1.Visible = True
                Cb_Sbj1.Enabled = True : Cb_Kpts1.Enabled = True : Bt1.Enabled = True
                Text1.Visible = True
                Fn_Inggeris(0) : Fn_Lanjutan(0)

                'Add STPM -degree -local 27112023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "A-LEVEL" Then
                    Panel7.Visible = False
                    Cb_Aliran.SelectedValue = 2 : Cb_Aliran.Enabled = False
                    Isi_Subjek(4) 'A-LEVEL

                End If

                'Add foundation - Degree - Non- Citizen 1812023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "ASASI" Then
                    Panel6.Visible = False 'Hidden STPM / A-LEVEL 23112023 - OSH 
                    Cb_SbjM.SelectedValue = 2 : Cb_SbjM.Enabled = False
                End If

                'Add Nursing Diploma- Degree - Non- Citizen 1812023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "DIPLOMA (KEJURURAWATAN)" Then
                    Panel5.Visible = False : Panel6.Visible = False
                    Panel7.Visible = False : Panel8.Visible = True
                    PanelSemak.Visible = False
                End If

                'Add Non-Nursing Diploma- Degree - Non- Citizen 1812023 - OSH 
                If Cb_Kelayakan.SelectedItem.Text = "DIP. BUKAN KEJURURAWATAN" Then
                    Panel6.Visible = False
                    Cb_SbjM.SelectedValue = 3 : Cb_SbjM.Enabled = False
                End If

            End If
        End If
    End Sub
    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click

        'Check Diploma Convertion and Advanced Midwife 21082023 - OSH


        'Medan Mandatori...
        Dim X As String = ""
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.Text.Trim = "-" Then X += "Warganegara, "
        'Add Mandatory for brith date & brith place 17102013-OSH 
        If Tx_Tkh_Lahir.Text.Trim = "" Then X += "Tarikh Lahir, "
        If Tx_Tp_Lahir.Text.Trim = "" Then X += "Tempat Lahir , "
        If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
        If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
        If Cb_Agama.Text.Trim = "" Then X += "Agama, "
        If Cb_Kahwin.Text.Trim = "" Then X += "Taraf Perkahwinan, "
        If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
        If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
        If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
        If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
        If Tx_Tel.Text.Trim = "" Then X += "No. Telefon, "
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        'Original Comment 22122020 - OSH
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        'Fix loophole date 00000/00/00 pass through 24122020 - OSH   
        'If Tx_M_Latihan.Text.Trim = "" Or Not IsDate(Tx_M_Latihan.Text.Trim) Or Chk_Tkh(Tx_M_Latihan.Text) = "NULL" Then X += "Tarikh Mula Latihan, "
        If X.Trim = "" Then Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        'Improve student data verification 17082022 -OSH 
        'If X.Trim = "" Then
        '    If ChkSah.Checked = False Then
        '        Msg(Me, "Sila sahkan maklumat pelatih ini adalah benar ! ") : ChkSah.Focus() : Exit Sub
        '    End If
        'Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila lengkapkan maklumat berikut: " & X) : Exit Sub
        'End If
        'Check No KP length
        X = ""
        With Cb_NoKP
            If .SelectedValue = "0" Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
            If .SelectedValue = "0" Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"
        End With
        If X.Trim = "" Then Else Msg(Me, "Maklumat No. Kad Pengenalan tidak " & X) : Tx_NoKP.Focus() : Exit Sub

        'Check Active User Session  08092023 -  OSH 
        id_session = Session("id_pg")
        If String.IsNullOrEmpty(id_session) Then
            Session.Abandon() : Response.Redirect("Login_J.aspx") : Exit Sub
        Else

            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

            'Add 03012012/ 08012013- query secure by categoery -OSH
            Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text.Trim & "' AND j_kursus='" & Cb_Kursus.SelectedValue & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Msg(Me, "Rekod Pelatih Ini Telah Ada!")
                Rdr.Close()
                Exit Sub
            End If
            Rdr.Close()


            'Update personal old records 01102019
            'Dim h1 As Integer
            Dim SQL_U As String
            SQL_U = ""

            If Cb_Kursus.SelectedValue = "1" Or Cb_Kursus.SelectedValue = "8" Then ' DIPLOMA
                Cmd.CommandText = "select j_kursus from  pelatih where status is null and j_kursus in ('2','3') and nokp  = '" & Tx_NoKP.Text & "'  "
                Rdr = Cmd.ExecuteReader()

                While Rdr.Read
                    SQL_U += "update pelatih set status = 1 where status is null and j_kursus = '" & Rdr(0) & "' and nokp  = '" & Tx_NoKP.Text.Trim & "'"
                    SQL_U += "insert into kj_pelatih_kemaskini_log (nokp, lama,  baru, log_id, log_tkh) values ( '" & Tx_NoKP.Text.Trim & "', '" & Rdr(0) & "' ,'" & Cb_Kursus.SelectedValue & "','" & Session("Id_PG") & "',getdate()) "
                End While
                Rdr.Close()
            End If

            'SQL_U += "update pelatih set status = 1 where status is null and j_kursus = '" & h1 & "'  and nokp  = '" & Tx_NoKP.Text & "'"
            If SQL_U <> "" Then
                Cmd.CommandText = SQL_U
                Cmd.ExecuteNonQuery()
            End If

            Dim SQL As String
            'add SQL variable asign nothing 22102013-OSH
            SQL = Nothing


            Try
                'Add Choose type id 11081013-OSH
                'Fixing select value 19082013 -OSH
                'If Cb_NoKP.SelectedValue = "0" Or Cb_NoKP.SelectedValue = "2" Then
                'Comment Ori 19082013- OSH
                If Cb_NoKP.SelectedValue = "0" Or Cb_NoKP.SelectedValue = "2" Then
                    'Add insert Brith Date & Brith Place query 26032014 -OSH 
                    SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                    'Comment Ori 26032014- OSH
                    'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                    SQL += Cb_Kursus.SelectedItem.Value & ","
                    If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Text & "',"
                    SQL += "" & Cb_Kerakyatan.SelectedValue & ","
                    SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                    SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "'," ' Add ARMY Number 26032014 -OSH
                    'Fixing select value 19082013 -OSH
                    'SQL += Cb_NoKP.SelectedValue & ","
                    'Comment Ori 19082013- OSH
                    SQL += Cb_NoKP.SelectedValue & ","
                    SQL += Cb_Warga.SelectedItem.Value & ","
                    'Add Brith Date 16092013 - OSH
                    SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & ","
                    'Add Brith Place 16092013 - OSH
                    SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                    SQL += Cb_Jantina.SelectedIndex & ","
                    'Fix Id Value 15122023 - OSH 
                    SQL += Cb_Bangsa.SelectedValue & ","
                    SQL += Cb_Agama.SelectedValue & ","
                    'Comment Original 15122023 - OSH
                    'SQL += Cb_Bangsa.SelectedIndex & ","
                    'SQL += Cb_Agama.SelectedIndex & ","
                    SQL += Cb_Kahwin.SelectedIndex & ","
                    SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                    SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                    SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                    SQL += "'" & Tx_Tel.Text.Trim & "',"
                    'Comment Original 04072023 -OSH
                    'SQL += "'" & Tx_Emel.Text.Trim & "',"
                    'Casting email to lower case 04072023 -OSH
                    SQL += "'" & Tx_Emel.Text.Trim.ToLower & "',"
                    SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                    SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                    SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                    SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                    SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                    SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
                    SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                    SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                    SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                    SQL += Chk_Tkh(Tx_M_Latihan.Text) & "," 'Comment Original 22122020 - OSH 
                    'Fix loophole date 00000/00/00 pass through 22122020 - OSH  
                    'If Not String.IsNullOrEmpty(Chk_Tkh(Tx_M_Latihan.Text)) Then
                    '    SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
                    'Else
                    '    Msg(Me, "Sila isikan tarikh mula latihan")
                    'End If
                    SQL += SSemak(0) & ","
                    SQL += SSemak(1) & ","
                    SQL += SSemak(2) & ","
                    SQL += SSemak(3) & ","
                    SQL += SSemak(4) & ","
                    SQL += SSemak(5) & ","
                    SQL += SSemak(6) & ","
                    SQL += SSemak(7) & ","
                    SQL += "'" & Session("Id_PG") & "',"
                    SQL += "getdate()"
                    SQL += ")"
                    'Add AMRY ID SQL INSERT 11082013-OSH
                    'Fixing select value 19082013 -OSH
                    'ElseIf Cb_NoKP.SelectedValue = "1" Then
                    'Comment Ori 19082013- OSH
                ElseIf Cb_NoKP.SelectedValue = "1" Then
                    'Add insert Brith Date & Brith Place query 16092013 -OSH 
                    SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, tkh_lahir,tpt_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                    'Comment Ori 160920213 -OSH
                    'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, notentera, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
                    SQL += Cb_Kursus.SelectedItem.Value & ","
                    If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
                    ' SQL += "" & Cb_Kelayakan_Taraf.SelectedValue & ","
                    SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
                    SQL += "'" & Tx_Tentera.Text.Trim.ToUpper & "',"
                    'Fixing select value 19082013 -OSH
                    'SQL += Cb_NoKP.SelectedValue & ","
                    'Comment Ori 19082013- OSH
                    SQL += Cb_NoKP.SelectedValue & ","
                    SQL += Cb_Warga.SelectedItem.Value & ","
                    'Add Brith Date 16092013 - OSH
                    SQL += Chk_Tkh(Tx_Tkh_Lahir.Text) & ","
                    'Add Brith Place 16092013 - OSH
                    SQL += "'" & Tx_Tp_Lahir.Text.Trim & "',"
                    SQL += Cb_Jantina.SelectedIndex & ","
                    'Fix id value 15122023 - OSH 
                    SQL += Cb_Bangsa.SelectedValue & ","
                    SQL += Cb_Agama.SelectedValue & ","
                    'Comment Original 15122023 - OSH 
                    'SQL += Cb_Bangsa.SelectedIndex & ","
                    'SQL += Cb_Agama.SelectedIndex & ","
                    SQL += Cb_Kahwin.SelectedIndex & ","
                    SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
                    SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
                    SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
                    SQL += "'" & Tx_Tel.Text.Trim & "',"
                    SQL += "'" & Tx_Emel.Text.Trim & "',"
                    SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
                    SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
                    SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
                    SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
                    SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
                    SQL += "'" & Tx_W_Tel.Text.Trim & "',"
                    SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
                    SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
                    SQL += Cb_Sesi_Bulan.SelectedIndex & ","
                    SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
                    SQL += Chk_Tkh(Tx_M_Latihan.Text) & "," 'Comment Original 22122020 - OSH 
                    'Fix loophole date 00000/00/00 pass through 22122020 - OSH  
                    'If Not String.IsNullOrEmpty(Chk_Tkh(Tx_M_Latihan.Text)) Then
                    '    SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
                    'Else
                    '    Msg(Me, "Sila isikan tarikh mula latihan")
                    'End If
                    SQL += SSemak(0) & ","
                    SQL += SSemak(1) & ","
                    SQL += SSemak(2) & ","
                    SQL += SSemak(3) & ","
                    SQL += SSemak(4) & ","
                    SQL += SSemak(5) & ","
                    SQL += SSemak(6) & ","
                    SQL += SSemak(7) & ","
                    SQL += "'" & Session("Id_PG") & "',"
                    SQL += "getdate()"
                    SQL += ")"
                End If

                If Panel8.Visible = True Then 'BI luar/setaraf
                    If Cb_Luar.SelectedValue > 0 And Cb_KptsLuar.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text.Trim & "','" & Cb_Luar.SelectedValue & "','" & Cb_KptsLuar.Text.ToUpper & "', 'IEL',getdate())" & vbCrLf
                End If

                If Panel7.Visible = True Then 'matrik
                    If Cb_SbjM.SelectedValue > 0 And Cb_KptsM.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan, id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjM.SelectedValue & "','" & Cb_KptsM.Text.ToUpper & "', 'FL', getdate())" & vbCrLf
                End If

                'Add STPM and A-Level Query 27112023 - OSH 
                If Panel6.Visible = True Then 'STPM / A-LEVEL

                    If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                        Ax_Id_Subjek = "STPM"
                    ElseIf Cb_Kelayakan.SelectedItem.Text = "A-LEVEL" Then
                        Ax_Id_Subjek = "A-L"
                    End If

                    If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "','" & Ax_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "','" & Ax_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "','" & Ax_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "','" & Ax_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "','" & Ax_Id_Subjek.ToString & "',getdate())" & vbCrLf
                End If

                If Panel5.Visible = True Then 'SPM/O-LEVEL

                    'Improve academics qualification 24112023 - OSH 
                    If (Cb_Kelayakan.SelectedItem.Text = "STPM" Or Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI" Or Cb_Kelayakan.SelectedItem.Text = "ASASI" Or Cb_Kelayakan.SelectedItem.Text = "DIP. BUKAN KEJURURAWATAN" Or Cb_Kelayakan.SelectedItem.Text = "SPM") And Cb_Kerakyatan.SelectedValue = "0" Then 'local
                        Tx_Id_Subjek = "SPM"
                    ElseIf (Cb_Kelayakan.SelectedItem.Text = "A-LEVEL" Or Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI" Or Cb_Kelayakan.SelectedItem.Text = "ASASI" Or Cb_Kelayakan.SelectedItem.Text = "DIP. BUKAN KEJURURAWATAN" Or Cb_Kelayakan.SelectedItem.Text = "O-LEVEL") And Cb_Kerakyatan.SelectedValue = "1" Then 'international
                        Tx_Id_Subjek = "O-L"
                    End If

                    'Subjects And scores 15092022 - OSH 
                    If Cb_Kpts1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj1.SelectedItem.Value & "','" & Cb_Kpts1.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj2.SelectedItem.Value & "','" & Cb_Kpts2.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj3.SelectedItem.Value & "','" & Cb_Kpts3.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj4.SelectedItem.Value & "','" & Cb_Kpts4.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj7.SelectedItem.Value & "','" & Cb_Kpts7.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts8.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj8.SelectedItem.Value & "','" & Cb_Kpts8.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                    If Cb_Kpts9.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan,  id_jenis, tkh_sbj) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj9.SelectedItem.Value & "','" & Cb_Kpts9.SelectedItem.Text & "','" & Tx_Id_Subjek.ToString & "',getdate())" & vbCrLf
                End If

                'Final verification and confirmation student final college exam details 21102022 - OSH  
                If ChkSah.Checked = True Then
                    SQL += "update pelatih set " &
                           "Id_Sah_Data_Peribadi_Akademik = '" & Session("Id_PG") & "', " &
                           "Tkh_Sah_Data_Peribadi_Akademik = getdate() " &
                           "where nokp = '" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "' and j_kursus = " & Cb_Kursus.SelectedItem.Value & ""
                End If

                ''Enabled limit cap 21052024 -OSH 
                ''Disable 30 days limited 05122023 - OSH 
                ''Check Limit Date Range 05072023 - OSH
                'Comment Out Bug on calculation 14062024 - OSH
                'Dim today As Date
                'Dim limit As DateTime
                'Dim valid As Integer

                'Cmd.CommandText = "select getdate();"

                'Rdr = Cmd.ExecuteReader()
                'While Rdr.Read
                '    today = Rdr(0)
                'End While
                'Rdr.Close()

                'limit = today.AddDays(30)
                ''valid = Date.Compare(Tx_M_Latihan.Text.Trim, limit)
                'valid = Date.Compare(limit, Tx_M_Latihan.Text.Trim)


                'If valid = "1" Then
                'Check 30 days limit 14062024 - OSH
                'Dim Today As DateTime
                'Dim Pickday As Date
                'Dim Daysleft As Long

                'Cmd.CommandText = "select getdate();"

                'Rdr = Cmd.ExecuteReader()
                'While Rdr.Read
                '    Today = Rdr(0)
                'End While
                'Rdr.Close()

                'Pickday = CDate(Tx_M_Latihan.Text.Trim)
                'Daysleft = DateDiff(DateInterval.Day, Today, Pickday)

                'Daysleft = (Today - Pickday).Days

                'If Daysleft > 30 Then ' check 30 days limit  
                'If Daysleft > 700 Then ' check 30 days limit  

                'Msg(Me, "Tarikh Daftar Melebihi Tempoh Yang Dibenarkan") : Exit Sub
                'Else
                '    If String.IsNullOrEmpty(SQL) Then
                '        Msg(Me, "Sila Kemukakan Aduan Ke Unit Kurikulum, Bahagian Kejururawatan Menerusi Emel Dengan Butiran Pendaftaran Pelatih Lengkap")
                '    Else
                '        'Anti-duplicate quit execute sql insert command 28052024 - OSH    
                '        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text.Trim & "' AND j_kursus='" & Cb_Kursus.SelectedValue & "'"
                '        Rdr = Cmd.ExecuteReader()
                '        If Rdr.Read Then
                '            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
                '            Rdr.Close() : Exit Sub
                '        Else
                '            Rdr.Close()
                '            Cmd.CommandText = SQL
                '            Cmd.ExecuteNonQuery()
                '            Cn.Close()
                '            Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
                '            Session("Msg_Isi") = "Rekod Telah Dihantar..."
                '            Response.Redirect("Mesej.aspx")
                '        End If
                '    End If
                'End If


                'Enabled limit cap 21052024 -OSH 
                'Disable 30 days limited 05122023 - OSH 
                'Check Limit Date Range 05072023 - OSH
                ''        Dim today As DateTime
                ''        Dim limit As DateTime
                ''        Dim valid As Integer

                ''        Cmd.CommandText = "select getdate();"

                ''        Rdr = Cmd.ExecuteReader()
                ''        While Rdr.Read
                ''            today = Rdr(0)
                ''        End While
                ''        Rdr.Close()

                ''        limit = today.AddDays(30)
                ''        valid = Date.Compare(Tx_M_Latihan.Text.Trim, limit)

                ''        If valid = "1" Then
                ''            Msg(Me, "Tarikh Daftar Melebihi Tempoh Yang Dibenarkan") : Exit Sub
                ''        Else
                ''            If SQL = "" Then
                ''            Else
                ''                Cmd.CommandText = SQL
                ''                Cmd.ExecuteNonQuery()
                ''                Cn.Close()
                ''                Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
                ''                Session("Msg_Isi") = "Rekod Telah Dihantar..."
                ''                Response.Redirect("Mesej.aspx")
                ''            End If
                ''        End If

                'Comment Original 14112024 -OSH 

                'Dim pickday As DateTime
                'If DateTime.TryParseExact(Tx_M_Latihan.Text.Trim, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, Pickday) Then
                '    ' Check if the date is in the range 2023-01-01 to 2024-12-31
                '    Dim startDate As DateTime = New DateTime(today.Year - 1, 1, 1)
                '    Dim endDate As DateTime = New DateTime(today.Year, 12, 31)

                '    If pickday >= startDate AndAlso pickday <= endDate Then
                If String.IsNullOrEmpty(SQL) Then
                    Msg(Me, "Sila Kemukakan Aduan Ke Unit Kurikulum, Bahagian Kejururawatan Menerusi Emel Dengan Butiran Pendaftaran Pelatih Lengkap")
                Else
                    'Anti-duplicate quit execute sql insert command 28052024 - OSH    
                    Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text.Trim & "' AND j_kursus='" & Cb_Kursus.SelectedValue & "'"
                    Rdr = Cmd.ExecuteReader()
                    If Rdr.Read Then
                        Msg(Me, "Rekod Pelatih Ini Telah Ada!")
                        Rdr.Close() : Exit Sub
                    Else
                        Rdr.Close()
                        Cmd.CommandText = SQL
                        Cmd.ExecuteNonQuery()
                        Cn.Close()
                        Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
                        Session("Msg_Isi") = "Rekod Telah Dihantar..."
                        Response.Redirect("Mesej.aspx")
                    End If
                End If
                'Else
                '    Msg(Me, "Tarikh Daftar Melebihi Tempoh Yang Dibenarkan") : Exit Sub
                'End If
                'End If


            Catch ex As Exception
                Msg(Me, ex.Message)
            End Try
        End If
    End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Function Chk_Tkh(ByVal X As String)
        If IsDate(X) Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
        Return X
    End Function

    Function Chk_Staff()
        Dim A, J As Integer
        Dim t As Boolean = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'PENDAFTARAN PENUH
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where ret=0 and apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        'Comment Original 17072019 - OSH
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        'Add SOP 2018 rules, active apc only and reg types 17092019 - OSH 
        'Cmd.CommandText = "SELECT nokp, apc_tahun, j_daftar FROM jt_penuh_apc where apc_tahun <= year(getdate()) and ret = 0 and nokp='" & Tx_NoKP.Text.Trim & "' ORDER BY apc_tahun desc"
        'Fix Bug 
        Cmd.CommandText = "SELECT top 3 nokp, apc_tahun, j_daftar FROM jt_penuh_apc where apc_tahun <= year(getdate()) and ret = 0 and nokp='" & Tx_NoKP.Text.Trim & "' ORDER BY apc_tahun desc"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A += 1 : J = Rdr(2) 'Type of registration
            'Comment Original 12062024 - OSH
            'T = Rdr(1)
            'Fix Currect Year Checker 12062024  - OSH 
            If Rdr(1) = CInt(Year(Now)) Then
                t = True
            End If
        End While
        Rdr.Close()
        Cn.Close()
        'ADD REG TYPES CHECK 17072019 - OSH 

        If Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "8" Then 'KPSL
            'Comment Original 12062024 - OSH 
            'If A > 2 And J > 1 And T = CInt(Year(Now)) Then
            'Fix Currect Year Checker 12062024  - OSH 
            If A > 2 And J > 1 And t = True Then
                Chk_Staff = False
            Else
                Msg(Me, "Pengalaman bekerja tidak melepasi syarat minimum untuk pendaftaran Diploma KPSL! Sila semak maklumat kelayakan.")
                Chk_Staff = True
            End If
        ElseIf Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "4" Then 'midwife

            'Comment Original 12062024 - OSH 
            'If A > 1 And J = 1 And T = CInt(Year(Now)) Then

            'Fix Currect Year Checker 12062024  - OSH 
            If A > 1 And J = 1 And t = True Then
                Chk_Staff = False
            Else
                Msg(Me, "Pengalaman bekerja tidak melepasi syarat minimum untuk pendaftaran Diploma Lanjutan Kebidanan! Sila semak maklumat kelayakan.")
                Chk_Staff = True
            End If
            Return Chk_Staff
        End If
    End Function
    Public Sub Fn_Lanjutan(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NATIVE = 1" 'Local
        Else
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_LANJUTAN WHERE NNATIVE = 1" 'International
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_SbjM.Items.Clear()
        Cb_SbjM.Items.Add("(PILIHAN)")
        Cb_SbjM.Items.Item(0).Value = "0"
        While Rdr.Read
            Cb_SbjM.Items.Add(Rdr(0))
            Cb_SbjM.Items.Item(Cb_SbjM.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        Dim X As String = ""
        Session("POLISI") = "OMEGA"
        Chk_Subjek()

        If Chk_Subjek() = True Then
            ' Enable Student Details, Agreement and Submition Panel 01092023 - OSH
            Panel3.Visible = True : Panel2.Visible = True : Panel1.Visible = True

            'limit MYKAD to 12 digit only 20082023 - OSH 
            If Cb_NoKP.SelectedValue = "0" Then
                Tx_NoKP.MaxLength = 12
            End If

            'Disable SPM / O-Level and International Language Panel 17082023 - OSH 
            If Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "1" Then  'Citizen -Diploma
                Panel5.Enabled = False
            End If

            'Add Diasble Course Selection After Qualification Check FOR IGCSE 19082022- OSH
            Cb_Kursus.Enabled = False : Cb_Kelayakan.Enabled = False : Tx_M_Latihan.Enabled = False : Cb_Kerakyatan.Enabled = False
            'Disable 'SEMAK' button after qualification check 15092022 - OSH
            cmd_Semak.Enabled = False
        End If

    End Sub

    Protected Sub Cb_NoKP_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_NoKP.SelectedIndexChanged
        Tx_NoKP.Text = ""

        With Cb_NoKP
            If .SelectedValue = "0" Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
            If .SelectedValue = "1" Then Tx_Tentera.Enabled = True : Tx_Tentera.Visible = True : Tx_NoKP.MaxLength = 12 : Tx_Tentera.MaxLength = 10 : Fn_Negara(1) : Fn_Negeri(1) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan" : Tx_Tentera_TextBoxWatermarkExtender.Enabled = True : Tx_Tentera_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_Tentera_TextBoxWatermarkExtender.WatermarkText = "Nombor Tentera"
            If .SelectedValue = "2" Then Tx_Tentera.Enabled = False : Tx_Tentera.Visible = False : Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0) : Tx_NoKP_TextBoxWatermarkExtender.Enabled = True : Tx_NoKP_TextBoxWatermarkExtender.WatermarkCssClass = "watermarked" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Passport"
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
    End Sub

    Public Sub Fn_Negara(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"
        Else
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        Cb_W_Negara.Items.Clear()
        Cb_W_Negara.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negara.Items.Add(Rdr(0))
            Cb_W_Negara.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub
    Function Chk_Record()
        Dim A As Integer

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Add SOP 2018 rules, exist registion record 21102019 - OSH  
        Cmd.CommandText = "SELECT nokp from jt_penuh where j_daftar = 1 and nokp='" & Tx_NoKP.Text & "' "
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A = A + 1
        End While
        Rdr.Close()
        Cn.Close()
        'ADD REG TYPES CHECK 17072019 - OSH 
        If A > 0 Then
            Chk_Record = False
        Else
            Msg(Me, "Pelatih tidak berdaftar denagan Lembaga Jururawat Malaysia ! Sila semak maklumat kelayakan.")
            Chk_Record = True
        End If
        Return Chk_Record
    End Function

    'Add states switch to country for internations student 20122019 - OSH  
    Public Sub Fn_Negeri(ByVal X As Int16)
        Dim Cn5 As New OleDbConnection : Dim Cmd5 As New OleDbCommand : Dim Rdr5 As OleDbDataReader
        Cn5.ConnectionString = ServerId : Cn5.Open() : Cmd5.Connection = Cn5

        'WARGANEGARA
        If X = 1 Then
            'Comment Original 25082020 - OSH 
            'Lb_States.Text = "NEGERI" : Lb_States.Enabled = False : Cb_W_Negeri.Enabled = False

            'Fix disable states dropdownlist issue 25082020 - OSH  
            Lb_States.Text = "NEGERI" : Lb_States.Enabled = False : Cb_W_Negeri.Enabled = True
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("")
            'Comment Original 21082020 - OSH 
            'Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri order by dc_negeri"

            'Improve query exclude 'luar negeri' from states populate 21082020 - OSH
            Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri where dc_negeri <> 'LUAR NEGERA' order by dc_negeri"
        Else
            Lb_States.Text = "NEGARA" : Lb_States.Enabled = False
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False
            Cmd5.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr5 = Cmd5.ExecuteReader()
        Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
        'Comment Original 25082020 - OSH 
        'Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False

        While Rdr5.Read
            Cb_TP_Negeri.Items.Add(Rdr5(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr5(1)
            Cb_W_Negeri.Items.Add(Rdr5(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr5(1)
        End While
        Rdr5.Close()
        Cn5.Close()
    End Sub

    Protected Sub Bt1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt1.Click
        If Cb_Sbj1.SelectedIndex < 1 Or Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        Text2.Visible = True
        Cb_Sbj2.Visible = True
        Cb_Kpts2.Visible = True
        Bt2.Visible = True
        Bt1.Visible = False
    End Sub

    Protected Sub Bt2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt2.Click
        If Cb_Sbj2.SelectedIndex < 1 Or Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
        Text3.Visible = True
        Cb_Sbj3.Visible = True
        Cb_Kpts3.Visible = True
        Bt3.Visible = True
        Bt2.Visible = False
    End Sub

    Protected Sub Bt3_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt3.Click
        If Cb_Sbj3.SelectedIndex < 1 Or Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub
        Text4.Visible = True
        Cb_Sbj4.Visible = True
        Cb_Kpts4.Visible = True
        Bt4.Visible = True
        Bt3.Visible = False
    End Sub
    Protected Sub Bt4_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt4.Click
        If Cb_Sbj4.SelectedIndex < 1 Or Cb_Kpts4.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #4!") : Exit Sub
        Text5.Visible = True
        Cb_Sbj5.Visible = True
        Cb_Kpts5.Visible = True
        Bt5.Visible = True
        Bt4.Visible = False
    End Sub
    Protected Sub Bt5_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt5.Click
        If Cb_Sbj5.SelectedIndex < 1 Or Cb_Kpts5.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #5!") : Exit Sub
        Text6.Visible = True
        Cb_Sbj6.Visible = True
        Cb_Kpts6.Visible = True
        Bt6.Visible = True
        Bt5.Visible = False
    End Sub
    Protected Sub Bt6_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt6.Click
        If Cb_Sbj6.SelectedIndex < 1 Or Cb_Kpts6.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #6!") : Exit Sub
        Text7.Visible = True
        Cb_Sbj7.Visible = True
        Cb_Kpts7.Visible = True
        Bt7.Visible = True
        Bt6.Visible = False
    End Sub
    Protected Sub Bt7_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt7.Click
        If Cb_Sbj7.SelectedIndex < 1 Or Cb_Kpts7.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #7!") : Exit Sub
        Text8.Visible = True
        Cb_Sbj8.Visible = True
        Cb_Kpts8.Visible = True
        Bt8.Visible = True
        Bt7.Visible = False
    End Sub
    Protected Sub Bt8_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt8.Click
        If Cb_Sbj8.SelectedIndex < 1 Or Cb_Kpts8.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #8!") : Exit Sub
        Text9.Visible = True
        Cb_Sbj9.Visible = True
        Cb_Kpts9.Visible = True
        Bt8.Visible = False
    End Sub
    Protected Sub Tx_M_Latihan_TextChanged(sender As Object, e As EventArgs) Handles Tx_M_Latihan.TextChanged
        'Comment Original 19042023 - OSH
        'Chk_Date_Policy()

        'Improvement check training date available and disable date picking box 19042023 - OSH 
        If Not Tx_M_Latihan.Text = "" Then
            Chk_Date_Policy()
        End If
        'Comment Original 29052023 - OSH
        'Tx_M_Latihan.Enabled = False
    End Sub

    Public Sub Fn_Inggeris(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NATIVE = 1" 'Local
        Else
            Cmd.CommandText = "SELECT DC_SUBJEK, ID FROM PN_INGGERIS WHERE NNATIVE = 1" 'International
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_Luar.Items.Clear()
        Cb_Luar.Items.Add("(PILIHAN)")
        Cb_Luar.Items.Item(0).Value = "0"
        While Rdr.Read
            Cb_Luar.Items.Add(Rdr(0))
            Cb_Luar.Items.Item(Cb_Luar.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Cb_Kerakyatan_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cb_Kerakyatan.SelectedIndexChanged
        If Cb_Kerakyatan.SelectedValue = "0" Then
            Load_Kursus(True) : Cb_Kelayakan.Items.Clear()
        Else
            Load_Kursus(False) : Cb_Kelayakan.Items.Clear()
        End If

        If Cb_Kursus.SelectedValue > "0" Then
            If Not Tx_M_Latihan.Text = "" Then
                Chk_Date_Policy()
            End If
        End If
    End Sub

    Function Chk_Subjek() As Boolean
        If Cb_Kelayakan.Items.Count = 0 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        If Cb_Kursus.SelectedIndex = 8 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        'Chk_Subjek = True
        'Assign Default Value for Semak Variable 08092022 -OSH 
        Dim PL, ML, CL, CLP, LP, CP, L, PSL As Int16, semak As String = "G", Total As Double = 0, Tot As Double = 0, FC As Boolean
        PL = 0 : ML = 0 : CL = 0 : CLP = 0 : LP = 0 : CP = 0 : L = 0 : FC = False

        Dim Y As String = ""
        'Comment Original 08092022 -OSH 
        'Dim PL, CL, CLP, LP, CP As Int16, semak As String = "", Total As Double = 0, Tot As Double = 0
        Dim SC As Boolean


        'STANDARD CRITERIA ADDMISSION POLICY JULY 2018 - IGCSE -19082022 - OSH
        If Session("POLISI") = "OMEGA" Then

            SC = False 'Flag Science Subjects 


            'Diploma
            If Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "1" Then ' Warganegara
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj1) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj1) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj1) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj1) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts1.SelectedIndex > 7 And Cb_Kpts1.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj1) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj1) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj1) = True Then PSL = PSL + 1 'Other pass

                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj2) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj2) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj2) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj2) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts2.SelectedIndex > 7 And Cb_Kpts2.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj2) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj2) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj2) = True Then PSL = PSL + 1 'Other pass


                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj3) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj3) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj3) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj3) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj3) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj3) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj3) = True Then PSL = PSL + 1 'Other pass

                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj4) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj4) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj4) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj4) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj4) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj4) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj4) = True Then PSL = PSL + 1 'Other pass


                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj5) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj5) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj5) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj5) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts5.SelectedIndex > 7 And Cb_Kpts5.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj5) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj5) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj5) = True Then PSL = PSL + 1 'Other pass


                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj6) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj6) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj6) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj6) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts6.SelectedIndex > 7 And Cb_Kpts6.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj6) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj6) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj6) = True Then PSL = PSL + 1 'Other pass


                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj7) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj7) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj7) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj7) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts7.SelectedIndex > 7 And Cb_Kpts7.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj7) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj7) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj7) = True Then PSL = PSL + 1 'Other pass


                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj8) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj8) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj8) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj8) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts8.SelectedIndex > 7 And Cb_Kpts8.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj8) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj8) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj8) = True Then PSL = PSL + 1 'Other pass


                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj_Primary_Lang(Cb_Sbj9) = True Then PL = PL + 1 Else If Chk_Sbj_Math(Cb_Sbj9) = True Then ML = ML + 1 Else If Chk_Sbj_Primary_STEM(Cb_Sbj9) = True Then CL = CL + 1 : SC = True ' Malay, , English, Math and Science
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj9) = True Then CLP = CLP + 1 'English Credit
                If CLP = 0 Then
                    If Cb_Kpts9.SelectedIndex > 7 And Cb_Kpts9.SelectedIndex < 10 Then If Chk_Sbj_Primary_Sec_Lang(Cb_Sbj9) = True Then LP = LP + 1 'English Pass
                End If
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 10 Then If Not Chk_Sbj_All(Cb_Sbj9) Then PSL = PSL + 1 Else If SC = True Then If Chk_Sbj_Primary_STEM(Cb_Sbj9) = True Then PSL = PSL + 1 'Other pass
            End If


            'Diploma
            If Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "1" Then ' Warganegara
                'Check Credit 3+2 without english separate - SPM 
                If PL < 1 Then Y += "Bahasa Melayu," 'CHECK MALAY - CREDIT
                If ML < 1 Then Y += "Matematik," 'CHECK MATH - CREDIT
                If CL < 1 Then Y += "Sains," 'CHECK SCIENCE  - CREDIT
                If (LP + CLP) < 1 Then Y += "Bahasa Inggeris" 'CHECK ENGLISH - PASS OR CREDIT
                If Y.Trim = "" Then Else Y = Mid(Y, 1, Len(Y) - 2) : Msg(Me, "Kelayakan Pelatih Tidak Melepasi Syarat Minimum Kemasukkan Diploma (2025-2026) - Warganegara ! Sila isikan maklumat subjek berikut:  " & Y) : Exit Function


                If (PL + ML + CL) > 2 Then '3 CREDIT 
                    If (LP + CLP + PSL) > 1 Then  ' 2 PASS
                        semak = "L"
                    Else
                        semak = "G"
                        Msg(Me, "kelayakan pelatih tidak melepasi syarat minimum kemasukan Diploma (2025-2026)  - 3 Kredit dengan  Bahasa Inggeris Lulus dan 1 Subjek Lain   - peringkat diploma ! Sila semak maklumat kelayakan.") : Exit Function
                    End If
                Else
                    semak = "G"
                    Msg(Me, "kelayakan pelatih tidak melepasi syarat minimum kemasukan Diploma (2025-2026)  - 3 Kredit dengan Bahasa Melayu, Matematik, Subjek Sains  - peringkat diploma ! Sila semak maklumat kelayakan.") : Exit Function
                End If
            End If

            If semak = "L" Then
                Chk_Subjek = True
            End If

            Return Chk_Subjek
        End If
    End Function
    'CHECK DATE POLICY 14092022 - OSH 
    Public Sub Chk_Date_Policy()

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader

        Dim todayServer As DateTime
        Dim startAllow As DateTime = New DateTime(2025, 1, 1)
        Dim endAllow As DateTime = New DateTime(2026, 12, 31)

        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select getdate();"

        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            todayServer = Rdr(0)
        End While
        Rdr.Close()
        Cn.Close()

        Dim pickday As DateTime
        Dim startDate As DateTime
        Dim endDate As DateTime
        'check server date allow off cap 15112024 -OSH 
        'If todayServer >= startAllow AndAlso todayServer <= endAllow Then
        '    startDate = New DateTime(2018, 7, 1)
        '    endDate = New DateTime(Today.Year, 12, 31)

        'Else
        'Pre-check 14112024 - OSH
        startDate = New DateTime(Today.Year, 1, 1)
        endDate = New DateTime(Today.Year + 1, 12, 31)

        'End If

        If DateTime.TryParse(Tx_M_Latihan.Text.Trim, pickday) Then
            If pickday >= startDate AndAlso pickday <= endDate Then
                'Lock Dropdown 
                Cb_Kerakyatan.Enabled = False : Cb_Kursus.Enabled = False : Cb_Kelayakan.Enabled = False : Tx_M_Latihan.Enabled = False
                Session("POLISI") = "OMEGA" : Load_Panel()
                Cb_Sesi_Bulan.SelectedValue = pickday.Month
                Cb_Sesi_Tahun.SelectedValue = pickday.Year

                'Comment Original 14112024 -OSH 
                'Check Policy 
                '    Dim Tkh_Pengajian As Date = DateTime.ParseExact(Tx_M_Latihan.Text, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                '    Dim Tkh_SOP2018 As Date = CDate("2018-07-01")
                '    Dim Valid As Integer

                '    'Comment Original 04072023 - OSH 
                '    'Dim today As System.DateTime
                '    'Dim limit As System.DateTime

                '    Dim today As DateTime
                '    Dim limit As DateTime

                '    Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
                '    Cmd.CommandText = "select getdate();"

                '    Rdr = Cmd.ExecuteReader()
                '    While Rdr.Read
                '        today = Rdr(0)
                '    End While
                '    Rdr.Close()
                '    Cn.Close()

                '    Valid = Date.Compare(Tkh_Pengajian, Tkh_SOP2018)
                '    If Valid > 0 Then
                '        Cb_Sesi_Bulan.SelectedValue = Tkh_Pengajian.Month
                '        Cb_Sesi_Tahun.SelectedValue = Tkh_Pengajian.Year

                '        Session("POLISI") = "OMEGA" : Load_Panel()

                '        ' Reable 29 May 2023 12042023 - OSH 
                '        If CInt(Date.Compare(Tkh_Pengajian, today)) > 0 Then
                '            Session("POLISI") = "OMEGA" : Load_Panel()
                '        ElseIf CInt(Date.Compare(Tkh_Pengajian, limit)) > 0 And CInt(Date.Compare(Tkh_Pengajian, today)) < 0 Then
                '            Session("POLISI") = "OMEGA" : Load_Panel()
                '        Else
                '            'Comment Original 29052023 -OSH 
                '            'Msg(Me, "Melebihi  Had Tempoh 30 Hari Wajib Daftar") : Exit Sub
                '            'Comment Original 29102024 -OSH 
                '            'Msg(Me, "Melebihi  Had Tempoh 30 Hari Wajib Daftar") : Tx_M_Latihan.Enabled = True : Panel8.Visible = False : Panel7.Visible = False : Panel6.Visible = False : Panel5.Visible = False : PanelSemak.Visible = False : Exit Sub
                '            'Fix 29102024 -OSH 
                '            Msg(Me, "Melebihi  Had Tempoh Daftar Yang Dibenarkan") : Tx_M_Latihan.Enabled = True : Panel8.Visible = False : Panel7.Visible = False : Panel6.Visible = False : Panel5.Visible = False : PanelSemak.Visible = False : Exit Sub

                '        End If
                '    Else
                '        'Load hiden panels by default 07092022 - OSH
                '        Panel5.Visible = False : Panel7.Visible = False : Panel8.Visible = False : PanelSemak.Visible = False
                '        Msg(Me, "Sila Pilih Tarikh Yang Betul")
                '    End If

            Else
                'Msg(Me, "Melebihi Had Tempoh Daftar Yang Dibenarkan")
                Cb_Kerakyatan.Enabled = True : Cb_Kursus.Enabled = True : Cb_Kelayakan.Enabled = True : Tx_M_Latihan.Enabled = True
                Msg(Me, "Sila Pilih Tarikh Yang Betul") : Exit Sub
            End If
        End If

    End Sub

    Protected Sub ChkSah_CheckedChanged(sender As Object, e As EventArgs) Handles ChkSah.CheckedChanged
        'Enable Submit Button When Declaration Is Confirm  20042023 - OSH
        If ChkSah.Checked = True Then
            Panel3.Enabled = False : cmdHantar.Enabled = True
        ElseIf ChkSah.Checked = False Then
            Panel3.Enabled = True : cmdHantar.Enabled = False
        End If
    End Sub
    Function Chk_Sbj_Primary_Lang(ByVal cb As DropDownList)
        Chk_Sbj_Primary_Lang = False
        If cb.SelectedItem.Text.Trim = "BAHASA MELAYU" Then Chk_Sbj_Primary_Lang = True : Exit Function
        Return Chk_Sbj_Primary_Lang
    End Function
    Function Chk_Sbj_Primary_Sec_Lang(ByVal cb As DropDownList)
        Chk_Sbj_Primary_Sec_Lang = False
        If cb.SelectedItem.Text.Trim = "BAHASA INGGERIS" Then Chk_Sbj_Primary_Sec_Lang = True : Exit Function
        If cb.SelectedItem.Text.Trim = "ENGLISH" Then Chk_Sbj_Primary_Sec_Lang = True : Exit Function
        If cb.SelectedItem.Text.Trim = "ENGLISH LANGUAGE" Then Chk_Sbj_Primary_Sec_Lang = True : Exit Function
        Return Chk_Sbj_Primary_Sec_Lang
    End Function
    Function Chk_Sbj_Primary_STEM(ByVal cb As DropDownList)
        Chk_Sbj_Primary_STEM = False
        If cb.SelectedItem.Text.Trim = "SAINS" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "BIOLOGI" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "KIMIA" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "FIZIK" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "APPLIED SCIENCE" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "ADDITIONAL SCIENCE" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "COMBINED SCIENCE" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "PHYSICS" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "BIOLOGY" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "CHEMISTRY" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "SCIENCE - COMBINED" Then Chk_Sbj_Primary_STEM = True : Exit Function
        If cb.SelectedItem.Text.Trim = "SCIENCE" Then Chk_Sbj_Primary_STEM = True : Exit Function 'Add Subject 18122023 - OSH 
        Return Chk_Sbj_Primary_STEM
    End Function
    Function Chk_Sbj_Math(ByVal cb As DropDownList)
        Chk_Sbj_Math = False

        If cb.SelectedItem.Text.Trim = "MATEMATIK" Then Chk_Sbj_Math = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Math = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATHEMATICS" Then Chk_Sbj_Math = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATHEMATICS - ADDITIONAL" Then Chk_Sbj_Math = True : Exit Function
        Return Chk_Sbj_Math
    End Function
    Function Chk_Sbj_All(ByVal cb As DropDownList)
        Chk_Sbj_All = False
        If cb.SelectedItem.Text.Trim = "BAHASA MELAYU" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "BAHASA INGGERIS" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "ENGLISH" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "ENGLISH LANGUAGE" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "SAINS" Then Chk_Sbj_All = True : Exit Function 'Add Subject 13112024 - OSH 
        If cb.SelectedItem.Text.Trim = "SCIENCE" Then Chk_Sbj_All = True : Exit Function 'Add Subject 13112024 - OSH 
        If cb.SelectedItem.Text.Trim = "BIOLOGI" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "KIMIA" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "FIZIK" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "APPLIED SCIENCE" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "ADDITIONAL SCIENCE" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATEMATIK" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATEMATIK TAMBAHAN" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATHEMATICS" Then Chk_Sbj_All = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATHEMATICS - ADDITIONAL" Then Chk_Sbj_All = True : Exit Function
        Return Chk_Sbj_All
    End Function

    Function Chk_Sbj_All_I(ByVal cb As DropDownList)
        Chk_Sbj_All_I = False
        If cb.SelectedItem.Text.Trim = "BIOLOGI" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "KIMIA" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "FIZIK" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "APPLIED SCIENCE" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "ADDITIONAL SCIENCE" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATEMATIK" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATEMATIK TAMBAHAN" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATHEMATICS" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "MATHEMATICS - ADDITIONAL" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "COMBINED SCIENCE" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "PHYSICS" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "BIOLOGY" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "CHEMISTRY" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "SCIENCE - COMBINED" Then Chk_Sbj_All_I = True : Exit Function
        If cb.SelectedItem.Text.Trim = "SCIENCE" Then Chk_Sbj_All_I = True : Exit Function 'Add Subject 18122023 - OSH 
        Return Chk_Sbj_All_I
    End Function

    Protected Sub Cb_Aliran_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cb_Aliran.SelectedIndexChanged
        'If Cb_Aliran.SelectedValue = 1 Then
        '    Isi_Subjek(3) 'SPTM
        'ElseIf Cb_Aliran.SelectedValue = 2 Then
        '    Isi_Subjek(4) 'A-LEVEL
        'End If
    End Sub

    Public Sub Load_Kursus(ByVal X As Boolean)

        Dim Cn_K As New OleDbConnection : Dim Cmd_K As New OleDbCommand : Dim Rdr_K As OleDbDataReader
        Cn_K.ConnectionString = ServerId : Cn_K.Open() : Cmd_K.Connection = Cn_K

        If X = True Then 'CITIZEN
            Cmd_K.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS WHERE XM = 1 and id_kursus  in (1) ORDER BY ID_KURSUS "
        End If

        Rdr_K = Cmd_K.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr_K.Read
            Cb_Kursus.Items.Add(Rdr_K(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr_K(1)
        End While
        Rdr_K.Close()
        Cn_K.Close()
    End Sub

    Protected Sub Bt_STPM1_Click(sender As Object, e As EventArgs) Handles Bt_STPM1.Click
        If Cb_SbjT1.SelectedIndex < 1 Or Cb_KptsT1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        Text_No_S2.Visible = True
        Cb_SbjT2.Visible = True
        Cb_KptsT2.Visible = True
        Bt_STPM2.Visible = True
        Bt_STPM1.Visible = False
    End Sub

    Protected Sub Bt_STPM2_Click(sender As Object, e As EventArgs) Handles Bt_STPM2.Click
        If Cb_SbjT2.SelectedIndex < 1 Or Cb_KptsT2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        Text_No_S3.Visible = True
        Cb_SbjT3.Visible = True
        Cb_KptsT3.Visible = True
        Bt_STPM3.Visible = True
        Bt_STPM2.Visible = False
    End Sub

    Protected Sub Bt_STPM3_Click(sender As Object, e As EventArgs) Handles Bt_STPM3.Click
        If Cb_SbjT3.SelectedIndex < 1 Or Cb_KptsT3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        Text_No_S4.Visible = True
        Cb_SbjT4.Visible = True
        Cb_KptsT4.Visible = True
        Bt_STPM4.Visible = True
        Bt_STPM3.Visible = False
    End Sub

    Protected Sub Bt_STPM4_Click(sender As Object, e As EventArgs) Handles Bt_STPM4.Click
        If Cb_SbjT4.SelectedIndex < 1 Or Cb_KptsT4.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        Text_No_S5.Visible = True
        Cb_SbjT5.Visible = True
        Cb_KptsT5.Visible = True
        Bt_STPM4.Visible = False
    End Sub
    Public Sub Load_Kelayakan()
        'Load Academics Certs Based On Nationalty 14081023 - OSH 
        Cb_Kelayakan.Items.Clear()
        If Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue <> "5" Then 'Citizen
            Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Item(0).Value = "0"
            Tx_Tentera.Visible = False : Tx_Tentera.Enabled = False 'Hidden Army Textbox
            Fn_Negara(1) : Fn_Negeri(1) 'Populate Countries and States
            Cb_NoKP.Items.Clear()
            Cb_NoKP.Items.Add("MYKAD") : Cb_NoKP.Items.Item(0).Value = "0" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
            Cb_NoKP.Items.Add("TENTERA") : Cb_NoKP.Items.Item(1).Value = "1"

        ElseIf Cb_Kerakyatan.SelectedValue = "1" And Cb_Kursus.SelectedValue <> "5" Then ' Non-Citien
            Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Item(0).Value = "0"
            Cb_Kelayakan.Items.Add("O-LEVEL") : Cb_Kelayakan.Items.Item(1).Value = "1"
            Tx_Tentera.Visible = False : Tx_Tentera.Enabled = False 'Hidden Army Textbox
            Fn_Negara(0) : Fn_Negeri(0) 'Populate Countries and States
            Cb_NoKP.Items.Clear()
            Cb_NoKP.Items.Add("PASSPORT") : Cb_NoKP.Items.Item(0).Value = "2" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Passport"

        ElseIf Cb_Kerakyatan.SelectedValue = "0" And Cb_Kursus.SelectedValue = "5" Then 'Citizen - Degrees
            Cb_Kelayakan.Items.Add("STPM") : Cb_Kelayakan.Items.Add("A-LEVEL") : Cb_Kelayakan.Items.Add("MATRIKULASI") : Cb_Kelayakan.Items.Add("ASASI") : Cb_Kelayakan.Items.Add("DIPLOMA (KEJURURAWATAN)") : Cb_Kelayakan.Items.Add("DIP. BUKAN KEJURURAWATAN")
            Tx_Tentera.Visible = False : Tx_Tentera.Enabled = False 'Hidden Army Textbox
            Fn_Negara(1) : Fn_Negeri(1) 'Populate Countries and States
            Cb_NoKP.Items.Clear()
            Cb_NoKP.Items.Add("MYKAD") : Cb_NoKP.Items.Item(0).Value = "0" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Nombor Kad Pengenalan"
            Cb_NoKP.Items.Add("TENTERA") : Cb_NoKP.Items.Item(1).Value = "1"
            'Add 

        ElseIf Cb_Kerakyatan.SelectedValue = "1" And Cb_Kursus.SelectedValue = "5" Then ' Non-Citien - Degrees
            Cb_Kelayakan.Items.Add("A-LEVEL") : Cb_Kelayakan.Items.Add("ASASI") : Cb_Kelayakan.Items.Add("DIP. BUKAN KEJURURAWATAN")
            Tx_Tentera.Visible = False : Tx_Tentera.Enabled = False 'Hidden Army Textbox
            Fn_Negara(0) : Fn_Negeri(0) 'Populate Countries and States
            Cb_NoKP.Items.Clear()
            Cb_NoKP.Items.Add("PASSPORT") : Cb_NoKP.Items.Item(0).Value = "2" : Tx_NoKP_TextBoxWatermarkExtender.WatermarkText = "Passport"
        End If
    End Sub
End Class
