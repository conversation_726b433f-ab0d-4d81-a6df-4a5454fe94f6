#!/usr/bin/env powershell

Write-Host "=== SPMJ KOLEJ Email Service Reconnection Script ===" -ForegroundColor Cyan
Write-Host ""

# Step 1: Verify microservice is running
Write-Host "Step 1: Checking Email Microservice Status..." -ForegroundColor Yellow
$microserviceRunning = $false
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Email microservice is running and healthy" -ForegroundColor Green
        $microserviceRunning = $true
    }
} catch {
    Write-Host "❌ Email microservice is not running on port 5000" -ForegroundColor Red
    Write-Host "Please start the email microservice first!" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Step 2: Rebuild the web application
Write-Host "Step 2: Rebuilding Web Application Components..." -ForegroundColor Yellow
try {
    Push-Location "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ"
    
    # Compile EmailServiceClient
    Write-Host "Compiling EmailServiceClient..." -ForegroundColor Gray
    & 'C:\Windows\Microsoft.NET\Framework\v3.5\vbc.exe' /target:library /r:System.dll,System.Web.dll,System.Web.Extensions.dll /out:bin\EmailServiceClient.dll EmailServiceClient.vb 2>&1 | Out-String | Write-Host
    
    if (Test-Path "bin\EmailServiceClient.dll") {
        Write-Host "✅ EmailServiceClient.dll compiled successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠️ EmailServiceClient.dll compilation may have issues" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Compilation failed: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Pop-Location
}

Write-Host ""

# Step 3: Check IIS and suggest restart
Write-Host "Step 3: Web Server Status..." -ForegroundColor Yellow

# Check if IIS is available
$iisAvailable = Test-Path "C:\Windows\System32\inetsrv\iisreset.exe"
if ($iisAvailable) {
    Write-Host "IIS is available. To restart IIS, run as Administrator:" -ForegroundColor Green
    Write-Host "  C:\Windows\System32\inetsrv\iisreset.exe" -ForegroundColor White
} else {
    Write-Host "IIS not found. Application may be using IIS Express or Visual Studio Dev Server" -ForegroundColor Yellow
}

Write-Host ""

# Step 4: Test fixed email service integration
Write-Host "Step 4: Testing Email Service Integration..." -ForegroundColor Yellow
try {
    Push-Location "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ"
    
    # Create a quick test script
    $testScript = @'
Imports System
Imports System.Configuration

Module EmailServiceTest
    Sub Main()
        Try
            Console.WriteLine("Testing EmailServiceClient integration...")
            
            Dim client As New EmailServiceClient("http://localhost:5000")
            client.SetApiKey("SPMJ-EmailService-2024-SecureKey-MOH-Malaysia")
            
            Dim health As String = client.CheckHealth()
            Console.WriteLine("Health check result: " + health)
            
            If health.Contains("healthy") Then
                Console.WriteLine("✅ Email service integration working!")
            Else
                Console.WriteLine("⚠️ Email service health check returned: " + health)
            End If
            
        Catch ex As Exception
            Console.WriteLine("❌ Error: " + ex.Message)
        End Try
    End Sub
End Module
'@
    
    $testScript | Out-File -FilePath "EmailServiceTest.vb" -Encoding UTF8
    
    # Compile and run test
    & 'C:\Windows\Microsoft.NET\Framework\v3.5\vbc.exe' /r:System.dll,System.Web.dll,System.Web.Extensions.dll,EmailServiceClient.dll EmailServiceTest.vb EmailServiceClient.vb 2>$null
    
    if (Test-Path "EmailServiceTest.exe") {
        Write-Host "Running integration test..." -ForegroundColor Gray
        & ".\EmailServiceTest.exe"
        Remove-Item "EmailServiceTest.exe" -ErrorAction SilentlyContinue
        Remove-Item "EmailServiceTest.vb" -ErrorAction SilentlyContinue
    }
    
} catch {
    Write-Host "❌ Integration test failed: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Pop-Location
}

Write-Host ""

# Step 5: Instructions for restarting the web application
Write-Host "Step 5: Next Steps to Reconnect Web Application..." -ForegroundColor Yellow
Write-Host ""
Write-Host "The email microservice is running and the code has been fixed." -ForegroundColor Green
Write-Host "To reconnect the web application:" -ForegroundColor White
Write-Host ""
Write-Host "Option 1 - If using Visual Studio:" -ForegroundColor Cyan
Write-Host "  1. Open the SPMJ project in Visual Studio" -ForegroundColor White
Write-Host "  2. Press F5 or Ctrl+F5 to start debugging/run without debugging" -ForegroundColor White
Write-Host "  3. The application should start on a new port (e.g., localhost:xxxxx)" -ForegroundColor White
Write-Host ""
Write-Host "Option 2 - If using IIS Express:" -ForegroundColor Cyan
Write-Host "  1. Open Developer Command Prompt" -ForegroundColor White
Write-Host "  2. Navigate to the SPMJ folder" -ForegroundColor White
Write-Host "  3. Run: iisexpress /path:`"$((Get-Location).Path)`" /port:55054" -ForegroundColor White
Write-Host ""
Write-Host "Option 3 - If using IIS (as Administrator):" -ForegroundColor Cyan
Write-Host "  1. Run: C:\Windows\System32\inetsrv\iisreset.exe" -ForegroundColor White
Write-Host "  2. Browse to http://localhost/SPMJ or configured IIS path" -ForegroundColor White
Write-Host ""

Write-Host "=== Email Service Reconnection Complete ===" -ForegroundColor Cyan
Write-Host "✅ Microservice: Running" -ForegroundColor Green
Write-Host "✅ Code: Fixed" -ForegroundColor Green
Write-Host "⏳ Web App: Ready to restart" -ForegroundColor Yellow
