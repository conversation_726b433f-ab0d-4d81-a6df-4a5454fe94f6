-- Database Migration Script for Secure Password Implementation
-- Execute this script on your SPMJ database to add the salt column

-- Add salt column to pn_pengguna table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'pn_pengguna') AND name = 'salt')
BEGIN
    ALTER TABLE pn_pengguna ADD salt VARCHAR(100) NULL
    PRINT 'Added salt column to pn_pengguna table'
END
ELSE
BEGIN
    PRINT 'Salt column already exists in pn_pengguna table'
END

-- Add password_migrated flag to track which passwords have been converted
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'pn_pengguna') AND name = 'password_migrated')
BEGIN
    ALTER TABLE pn_pengguna ADD password_migrated BIT DEFAULT 0
    PRINT 'Added password_migrated column to pn_pengguna table'
END
ELSE
BEGIN
    PRINT 'Password_migrated column already exists in pn_pengguna table'
END

-- Initialize all existing records as not migrated
UPDATE pn_pengguna SET password_migrated = 0 WHERE password_migrated IS NULL

PRINT 'Database migration completed successfully!'
PRINT 'Note: All existing user passwords will need to be updated to encrypted versions on next login.'
