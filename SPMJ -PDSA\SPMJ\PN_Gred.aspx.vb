﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm63
    Inherits System.Web.UI.Page
    Public Sub Reset()
        Session("PN_Pinda") = False
        Tx_Id.ReadOnly = False
        Tx_Id.Text = ""
        Tx_gred.Text = ""
        cb_baru.Visible = False
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select dc_gred 'Gred Jawatan', id_gred from pn_gred order by dc_gred"
        Tb = "pn_gred"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Gred", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Cari("")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Dim SQL As String

        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If Session("PN_Pinda") Then
            SQL = "update pn_gred set dc_gred = '" & Tx_gred.Text.Trim & "' where id_gred = '" & Tx_Id.Text & "'"
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Tx_Id.Text = ""
            Tx_gred.Text = ""
            Cari("")
            Reset()
            Msg(Me, "Rekod Telah Disimpan...")
            Exit Sub
        End If

        If Tx_Gred.Text = "" Then Msg(Me, "Sila isi Gred Jawatan!") : Exit Sub

        Cmd.CommandText = "select * from pn_gred where dc_gred = '" & Tx_gred.Text.Trim & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()


        SQL = "insert pn_gred (dc_gred) select '" & Tx_gred.Text.Trim & "'"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Tx_Id.Text = ""
            Tx_gred.Text = ""
            Msg(Me, "Rekod Telah Disimpan...")
            Cari("")
        Catch ex As Exception
            Msg(Me, "Rekod Telah Ada!")
        End Try
        Reset()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(3).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("PN_Padam") Then
            Session("PN_Padam") = False
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Dim SQL As String
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "delete from pn_gred where id_gred='" & Gd.SelectedRow.Cells(3).Text & "' and dc_gred='" & Gd.SelectedRow.Cells(2).Text & "'"
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Tx_Id.Text = ""
                Msg(Me, "Rekod Telah Dipadam...")
                Cari("")
                Tx_Id.Text = ""
                Tx_gred.Text = ""
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
            Cn.Close()
            Exit Sub
        End If
        Session("PN_Pinda") = True
        Tx_Id.ReadOnly = True
        cb_baru.Visible = True
        Tx_Id.Text = Gd.SelectedRow.Cells(3).Text
        Tx_gred.Text = Gd.SelectedRow.Cells(2).Text
    End Sub

    Protected Sub Cb_Padam_Click(ByVal sender As Object, ByVal e As EventArgs)
        Session("PN_Padam") = True
    End Sub

    Protected Sub cb_baru_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cb_baru.Click
        Reset()
        Tx_Id.Focus()

    End Sub
End Class