# Test the AJAX Health Check Web Method directly
# This bypasses the UI and tests the backend functionality directly

Write-Host "Testing Email Service Health Check Web Method" -ForegroundColor Cyan

# Test the web method directly
Write-Host "`nTesting CheckEmailServiceHealth web method..." -ForegroundColor Yellow

try {
    # Create the JSON payload for the web method
    $jsonPayload = "{}"
    $bytes = [System.Text.Encoding]::UTF8.GetBytes($jsonPayload)
    
    $request = [System.Net.WebRequest]::Create("http://localhost:8080/Pwd.aspx/CheckEmailServiceHealth")
    $request.Method = "POST"
    $request.ContentType = "application/json; charset=utf-8"
    $request.ContentLength = $bytes.Length
    
    $requestStream = $request.GetRequestStream()
    $requestStream.Write($bytes, 0, $bytes.Length)
    $requestStream.Close()
    
    $response = $request.GetResponse()
    $responseStream = $response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($responseStream)
    $responseText = $reader.ReadToEnd()
    
    Write-Host "SUCCESS: Health check web method responded" -ForegroundColor Green
    Write-Host "   Status: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   Response: $responseText" -ForegroundColor White
    
    # Parse the response to see the actual health status
    try {
        $jsonResponse = $responseText | ConvertFrom-Json
        $healthData = $jsonResponse.d | ConvertFrom-Json
        Write-Host "   Health Status: $($healthData.status)" -ForegroundColor $(if($healthData.status -eq 'online') { 'Green' } else { 'Red' })
        Write-Host "   Health Message: $($healthData.message)" -ForegroundColor White
    } catch {
        Write-Host "   Could not parse health response JSON" -ForegroundColor Yellow
    }
    
    $reader.Close()
    $responseStream.Close()
    $response.Close()
    
} catch {
    Write-Host "FAILED: Health check web method call" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Check if it's a compilation error
    if ($_.Exception.Message -like "*Parser Error*" -or $_.Exception.Message -like "*Compilation*") {
        Write-Host "   This appears to be a compilation issue with the ASP.NET application" -ForegroundColor Yellow
    }
}

Write-Host "`nHealth Check Web Method Test Complete" -ForegroundColor Cyan
