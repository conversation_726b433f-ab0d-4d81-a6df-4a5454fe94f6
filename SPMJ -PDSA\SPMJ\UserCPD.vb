﻿Public Class UserCPD
    Private dateEndValue As Date
    Private dateStartValue As Date
    Private hcpValue As Integer
    Private identityNoValue As String

    Public Property DateEnd() As Date
        Get
            ' Gets the property value.
            Return dateEndValue
        End Get
        Set(ByVal Value As Date)
            ' Sets the property value.
            dateEndValue = Value
        End Set
    End Property

    Public Property DateStart() As Date
        Get
            ' Gets the property value.
            Return dateStartValue
        End Get
        Set(ByVal Value As Date)
            ' Sets the property value.
            dateStartValue = Value
        End Set
    End Property

    Public Property Hcp() As Integer
        Get
            ' Gets the property value.
            Return hcpValue
        End Get
        Set(ByVal Value As Integer)
            ' Sets the property value.
            hcpValue = Value
        End Set
    End Property
    Public Property IdentityNo() As String
        Get
            ' Gets the property value.
            Return identityNoValue
        End Get
        Set(ByVal Value As String)
            ' Sets the property value.
            identityNoValue = Value
        End Set
    End Property
End Class
