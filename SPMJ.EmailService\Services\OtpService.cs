using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using SPMJ.EmailService.Data;
using SPMJ.EmailService.Models;
using System.Security.Cryptography;

namespace SPMJ.EmailService.Services;

public class OtpService : IOtpService
{
    private readonly SPMJContext _context;
    private readonly IEmailService _emailService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<OtpService> _logger;

    public OtpService(SPMJContext context, IEmailService emailService, IMemoryCache cache, ILogger<OtpService> logger)
    {
        _context = context;
        _emailService = emailService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<OtpResponse> GenerateOtpAsync(OtpRequest request)
    {
        try
        {            // Validate user exists
            var user = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == request.UserId && u.Status == 1);

            if (user == null)
            {
                return new OtpResponse
                {
                    Success = false,
                    Message = "Pengguna tidak dijumpai atau tidak aktif"
                };
            }

            // Use provided email or user's email
            var emailToUse = !string.IsNullOrEmpty(request.Email) ? request.Email : user.Email;
            if (string.IsNullOrEmpty(emailToUse))
            {
                return new OtpResponse
                {
                    Success = false,
                    Message = "Alamat email tidak dijumpai"
                };
            }

            // Check if there's a recent OTP (rate limiting)
            var recentOtp = await _context.OtpTokens
                .Where(o => o.UserId == request.UserId && 
                           o.Purpose == request.Purpose && 
                           !o.Used && 
                           o.CreatedAt > DateTime.UtcNow.AddMinutes(-1))
                .FirstOrDefaultAsync();

            if (recentOtp != null)
            {
                return new OtpResponse
                {
                    Success = false,
                    Message = "Sila tunggu sekurang-kurangnya 1 minit sebelum meminta OTP baru"
                };
            }

            // Generate OTP
            var otpCode = GenerateOtpCode();
            var expiresAt = DateTime.UtcNow.AddMinutes(5);

            // Invalidate existing OTPs for this user and purpose
            var existingOtps = await _context.OtpTokens
                .Where(o => o.UserId == request.UserId && 
                           o.Purpose == request.Purpose && 
                           !o.Used)
                .ToListAsync();

            foreach (var existingOtp in existingOtps)
            {
                existingOtp.Used = true;
                existingOtp.UsedAt = DateTime.UtcNow;
            }

            // Save new OTP
            var otpToken = new OtpToken
            {
                UserId = request.UserId,
                OtpCode = otpCode,
                Purpose = request.Purpose,
                ExpiresAt = expiresAt
            };

            _context.OtpTokens.Add(otpToken);
            await _context.SaveChangesAsync();

            // Send email
            var emailSent = await _emailService.SendOtpEmailAsync(
                emailToUse, user.Nama ?? user.IdPg, otpCode, request.Purpose);

            if (!emailSent)
            {
                return new OtpResponse
                {
                    Success = false,
                    Message = "Gagal menghantar kod OTP melalui email"
                };
            }

            // Cache OTP for quick validation (optional optimization)
            var cacheKey = $"otp_{request.UserId}_{request.Purpose}";
            _cache.Set(cacheKey, otpCode, TimeSpan.FromMinutes(5));

            return new OtpResponse
            {
                Success = true,
                Message = "Kod OTP telah dihantar ke email anda",
                OtpCode = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development" ? otpCode : null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating OTP for user {UserId}", request.UserId);
            return new OtpResponse
            {
                Success = false,
                Message = "Ralat sistem. Sila cuba lagi."
            };
        }
    }

    public async Task<ApiResponse<bool>> ValidateOtpAsync(OtpValidationRequest request)
    {        try
        {
            // First check cache for quick validation
            var cacheKey = $"otp_{request.UserId}_{request.Purpose}";
            if (_cache.TryGetValue(cacheKey, out string? cachedOtp) && cachedOtp == request.OtpCode)
            {
                // Remove from cache after successful validation
                _cache.Remove(cacheKey);
                
                // Mark OTP as used in database
                var cachedOtpToken = await _context.OtpTokens
                    .FirstOrDefaultAsync(o => o.UserId == request.UserId &&
                                             o.OtpCode == request.OtpCode &&
                                             o.Purpose == request.Purpose &&
                                             !o.Used);
                
                if (cachedOtpToken != null)
                {
                    cachedOtpToken.Used = true;
                    cachedOtpToken.UsedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
                
                return new ApiResponse<bool>
                {
                    Success = true,
                    Message = "Kod OTP sah",
                    Data = true
                };
            }

            // Validate against database if not in cache
            var otpToken = await _context.OtpTokens
                .FirstOrDefaultAsync(o => o.UserId == request.UserId &&
                                         o.OtpCode == request.OtpCode &&
                                         o.Purpose == request.Purpose &&
                                         !o.Used &&
                                         o.ExpiresAt > DateTime.UtcNow);

            if (otpToken == null)
            {
                return new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Kod OTP tidak sah atau telah luput",
                    Data = false
                };
            }

            // Mark OTP as used
            otpToken.Used = true;
            otpToken.UsedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            // Remove from cache if exists
            _cache.Remove(cacheKey);

            return new ApiResponse<bool>
            {
                Success = true,
                Message = "Kod OTP sah",
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating OTP for user {UserId}", request.UserId);
            return new ApiResponse<bool>
            {
                Success = false,
                Message = "Ralat sistem",
                Data = false
            };
        }
    }

    public async Task<bool> CleanupExpiredOtpsAsync()
    {
        try
        {
            var expiredOtps = await _context.OtpTokens
                .Where(o => o.ExpiresAt < DateTime.UtcNow && !o.Used)
                .ToListAsync();

            foreach (var expiredOtp in expiredOtps)
            {
                expiredOtp.Used = true;
                expiredOtp.UsedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Cleaned up {Count} expired OTP tokens", expiredOtps.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired OTPs");
            return false;
        }
    }

    private string GenerateOtpCode()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var code = BitConverter.ToUInt32(bytes, 0) % 1000000;
        return code.ToString("D6");
    }
}
