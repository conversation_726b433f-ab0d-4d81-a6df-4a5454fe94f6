#!/usr/bin/env powershell

Write-Host "=== Testing SPMJ KOLEJ Web Application Email Service Status ===" -ForegroundColor Cyan
Write-Host ""

# Test the web method that the JavaScript calls
Write-Host "Testing CheckEmailServiceHealth web method..." -ForegroundColor Yellow

try {
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-WebRequest -Uri "http://localhost:55054/Pwd.aspx/CheckEmailServiceHealth" -Method POST -Headers $headers -Body '{}' -UseBasicParsing -TimeoutSec 15
    
    Write-Host "✅ Web method responds with status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response Content: $($response.Content)" -ForegroundColor White
    
    # Parse JSON to check status
    try {
        $jsonResponse = $response.Content | ConvertFrom-Json
        if ($jsonResponse.status -eq "online") {
            Write-Host "✅ Email Service Status: ONLINE" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Email Service Status: OFFLINE" -ForegroundColor Yellow
            Write-Host "Message: $($jsonResponse.message)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Could not parse JSON response" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Web method call failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Check if it's a 404 error (app not running)
    if ($_.Exception.Message -like "*404*") {
        Write-Host "❌ Web application not running on port 55054" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Cyan
