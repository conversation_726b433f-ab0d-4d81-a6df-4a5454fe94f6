# SPMJ KOLEJ Production Deployment Script
# Deploys the rebuilt web forms with email microservice integration

param(
    [string]$IISPath = "C:\inetpub\wwwroot\SPMJ",
    [string]$MicroserviceUrl = "http://localhost:5000",
    [switch]$SkipMicroserviceCheck,
    [switch]$BackupExisting
)

Write-Host "=== SPMJ KOLEJ PRODUCTION DEPLOYMENT ===" -ForegroundColor Green
Write-Host "Deploying password management system with email microservice integration..." -ForegroundColor White

# Step 1: Verify prerequisites
Write-Host "`n1. PREREQUISITE VERIFICATION" -ForegroundColor Cyan

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "✗ This script requires Administrator privileges" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    exit 1
}
Write-Host "✓ Running with Administrator privileges" -ForegroundColor Green

# Check IIS availability
if (Get-WindowsFeature -Name IIS-WebServerRole -ErrorAction SilentlyContinue | Where-Object InstallState -eq "Installed") {
    Write-Host "✓ IIS is installed and available" -ForegroundColor Green
} else {
    Write-Host "⚠ IIS not detected - manual web server configuration required" -ForegroundColor Yellow
}

# Check .NET Framework 3.5
$dotnet35 = Get-WindowsFeature -Name NET-Framework-Features -ErrorAction SilentlyContinue
if ($dotnet35 -and $dotnet35.InstallState -eq "Installed") {
    Write-Host "✓ .NET Framework 3.5 is available" -ForegroundColor Green
} else {
    Write-Host "⚠ .NET Framework 3.5 may need to be enabled" -ForegroundColor Yellow
}

# Step 2: Check email microservice
if (-not $SkipMicroserviceCheck) {
    Write-Host "`n2. EMAIL MICROSERVICE VERIFICATION" -ForegroundColor Cyan
    try {
        $healthResponse = Invoke-WebRequest -Uri "$MicroserviceUrl/health" -UseBasicParsing -TimeoutSec 5
        if ($healthResponse.StatusCode -eq 200) {
            Write-Host "✓ Email microservice is running and healthy" -ForegroundColor Green
            Write-Host "  URL: $MicroserviceUrl" -ForegroundColor Gray
            Write-Host "  Response: $($healthResponse.Content)" -ForegroundColor Gray
        } else {
            Write-Host "⚠ Email microservice returned status: $($healthResponse.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "✗ Email microservice not accessible: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "  Please ensure the .NET 9 email microservice is running on $MicroserviceUrl" -ForegroundColor Yellow
        $continue = Read-Host "Continue deployment anyway? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            exit 1
        }
    }
} else {
    Write-Host "`n2. EMAIL MICROSERVICE CHECK SKIPPED" -ForegroundColor Yellow
}

# Step 3: Backup existing deployment
if ($BackupExisting -and (Test-Path $IISPath)) {
    Write-Host "`n3. BACKING UP EXISTING DEPLOYMENT" -ForegroundColor Cyan
    $backupPath = "$IISPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    try {
        Copy-Item -Path $IISPath -Destination $backupPath -Recurse -Force
        Write-Host "✓ Existing deployment backed up to: $backupPath" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Backup failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Step 4: Deploy compiled assemblies
Write-Host "`n4. DEPLOYING COMPILED ASSEMBLIES" -ForegroundColor Cyan

$sourceFiles = @{
    "SPMJ KOLEJ-PDSA\SPMJ\SPMJ_KOLEJ_PWD.dll" = "bin\SPMJ_KOLEJ_PWD.dll"
    "SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx" = "Pwd.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx" = "ForcePasswordChange.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\Web.config" = "Web.config"
    "SPMJ KOLEJ-PDSA\SPMJ\Kolej.Master" = "Kolej.Master"
    "SPMJ KOLEJ-PDSA\SPMJ\Kolej.css" = "Kolej.css"
}

# Create target directories
if (-not (Test-Path $IISPath)) {
    New-Item -ItemType Directory -Path $IISPath -Force | Out-Null
    Write-Host "✓ Created deployment directory: $IISPath" -ForegroundColor Green
}

if (-not (Test-Path "$IISPath\bin")) {
    New-Item -ItemType Directory -Path "$IISPath\bin" -Force | Out-Null
    Write-Host "✓ Created bin directory: $IISPath\bin" -ForegroundColor Green
}

# Copy files
foreach ($source in $sourceFiles.Keys) {
    $target = "$IISPath\$($sourceFiles[$source])"
    if (Test-Path $source) {
        try {
            Copy-Item -Path $source -Destination $target -Force
            Write-Host "✓ Deployed: $source → $($sourceFiles[$source])" -ForegroundColor Green
        } catch {
            Write-Host "✗ Failed to deploy $source`: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠ Source file not found: $source" -ForegroundColor Yellow
    }
}

# Step 5: Configure web.config for production
Write-Host "`n5. CONFIGURING WEB.CONFIG FOR PRODUCTION" -ForegroundColor Cyan

$webConfigPath = "$IISPath\Web.config"
if (Test-Path $webConfigPath) {
    try {
        $webConfig = [xml](Get-Content $webConfigPath)
        
        # Update email service URL
        $emailServiceSetting = $webConfig.configuration.appSettings.add | Where-Object { $_.key -eq "EmailServiceUrl" }
        if ($emailServiceSetting) {
            $emailServiceSetting.value = $MicroserviceUrl
            Write-Host "✓ Updated EmailServiceUrl to: $MicroserviceUrl" -ForegroundColor Green
        } else {
            Write-Host "⚠ EmailServiceUrl setting not found in web.config" -ForegroundColor Yellow
        }
        
        # Save updated web.config
        $webConfig.Save($webConfigPath)
        Write-Host "✓ Web.config updated for production" -ForegroundColor Green
        
    } catch {
        Write-Host "⚠ Failed to update web.config: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Web.config not found at: $webConfigPath" -ForegroundColor Yellow
}

# Step 6: Set up IIS application (if possible)
Write-Host "`n6. IIS APPLICATION CONFIGURATION" -ForegroundColor Cyan

try {
    Import-Module WebAdministration -ErrorAction Stop
    
    # Check if application already exists
    $appName = "SPMJ"
    if (Get-WebApplication -Name $appName -ErrorAction SilentlyContinue) {
        Write-Host "✓ IIS Application '$appName' already exists" -ForegroundColor Green
    } else {
        # Create new application
        New-WebApplication -Name $appName -Site "Default Web Site" -PhysicalPath $IISPath -ErrorAction Stop
        Write-Host "✓ Created IIS Application: $appName" -ForegroundColor Green
    }
    
    # Set application pool to .NET 4.0 (compatible with .NET 3.5)
    $appPool = Get-WebApplication -Name $appName | Select-Object -ExpandProperty ApplicationPool
    Set-WebConfigurationProperty -Filter "system.applicationHost/applicationPools/add[@name='$appPool']" -Name "managedRuntimeVersion" -Value "v4.0"
    Write-Host "✓ Application pool configured for .NET 4.0" -ForegroundColor Green
    
} catch {
    Write-Host "⚠ IIS configuration failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "  Manual IIS configuration may be required" -ForegroundColor Gray
}

# Step 7: Deployment verification
Write-Host "`n7. DEPLOYMENT VERIFICATION" -ForegroundColor Cyan

$verificationTests = @{
    "Main DLL" = "$IISPath\bin\SPMJ_KOLEJ_PWD.dll"
    "Password Page" = "$IISPath\Pwd.aspx"
    "Force Change Page" = "$IISPath\ForcePasswordChange.aspx"
    "Web Config" = "$IISPath\Web.config"
    "Master Page" = "$IISPath\Kolej.Master"
    "Stylesheet" = "$IISPath\Kolej.css"
}

$allFilesDeployed = $true
foreach ($test in $verificationTests.Keys) {
    $filePath = $verificationTests[$test]
    if (Test-Path $filePath) {
        $fileInfo = Get-ChildItem $filePath
        Write-Host "✓ $test`: $($fileInfo.Name) ($($fileInfo.Length) bytes)" -ForegroundColor Green
    } else {
        Write-Host "✗ $test`: Missing at $filePath" -ForegroundColor Red
        $allFilesDeployed = $false
    }
}

# Step 8: Test web application
Write-Host "`n8. WEB APPLICATION TESTING" -ForegroundColor Cyan

try {
    $testUrl = "http://localhost/SPMJ/Pwd.aspx"
    Write-Host "Testing web application at: $testUrl" -ForegroundColor Gray
      $webResponse = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
    if ($webResponse.StatusCode -eq 200) {
        Write-Host "✓ Web application is accessible" -ForegroundColor Green
        Write-Host "  Status: $($webResponse.StatusCode) $($webResponse.StatusDescription)" -ForegroundColor Gray
    } else {
        Write-Host "⚠ Web application returned status: $($webResponse.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Web application test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "  This may be normal if IIS is not fully configured" -ForegroundColor Gray
}

# Step 9: Final deployment summary
Write-Host "`n=== DEPLOYMENT SUMMARY ===" -ForegroundColor Green

if ($allFilesDeployed) {
    Write-Host "✅ ALL FILES DEPLOYED SUCCESSFULLY" -ForegroundColor Green
} else {
    Write-Host "⚠️ SOME FILES MISSING - CHECK ABOVE ERRORS" -ForegroundColor Yellow
}

Write-Host "`nDeployment Details:" -ForegroundColor White
Write-Host "  Target Path: $IISPath" -ForegroundColor Gray
Write-Host "  Email Service: $MicroserviceUrl" -ForegroundColor Gray
Write-Host "  Web Application: http://localhost/SPMJ/" -ForegroundColor Gray

Write-Host "`nNext Steps:" -ForegroundColor White
Write-Host "1. Verify IIS configuration and application pool settings" -ForegroundColor Gray
Write-Host "2. Test password change functionality in browser" -ForegroundColor Gray
Write-Host "3. Verify email notifications are working" -ForegroundColor Gray
Write-Host "4. Check application logs for any runtime errors" -ForegroundColor Gray

Write-Host "`n=== DEPLOYMENT COMPLETE ===" -ForegroundColor Green
