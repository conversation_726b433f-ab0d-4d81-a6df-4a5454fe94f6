using System;
using Microsoft.Data.SqlClient;

namespace DatabaseConnectionTest
{
    class Program
    {
        static void Main(string[] args)
        {
            // Test connection strings
            string[] connectionStrings = {
                "Server=10.24.132.32;Database=DUMP_PDSA;User ID=sa;Password=***********;TrustServerCertificate=True",
                "Server=10.24.132.32,1433;Database=DUMP_PDSA;User ID=sa;Password=***********;TrustServerCertificate=True",
                "Data Source=10.24.132.32;Initial Catalog=DUMP_PDSA;User ID=sa;Password=***********;TrustServerCertificate=True",
                "Server=10.24.132.32;Database=DUMP_PDSA;User ID=ro;Password=***********;TrustServerCertificate=True"
            };

            foreach (var connStr in connectionStrings)
            {
                Console.WriteLine($"Testing: {connStr.Substring(0, Math.Min(50, connStr.Length))}...");
                
                try
                {
                    using (var connection = new SqlConnection(connStr))
                    {
                        connection.Open();
                        using (var command = new SqlCommand("SELECT COUNT(*) FROM pn_pengguna", connection))
                        {
                            var result = command.ExecuteScalar();
                            Console.WriteLine($"✅ SUCCESS: Found {result} users");
                            return; // Success - exit
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ FAILED: {ex.Message}");
                }
                Console.WriteLine();
            }
        }
    }
}
