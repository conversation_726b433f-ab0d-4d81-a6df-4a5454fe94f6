﻿Imports System.Data.OleDb
Imports System.Globalization
Imports System.Threading
Imports System.Data.SqlClient

Partial Public Class WebForm5
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'Comment Original 12072023 - OSH 
        'Dim SQL As String = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', p.MARKAH as 'MARKAH BERTERUSAN', isnull(p.cuti,0)-isnull(p.cuti_ganti,0) as 'LEBIHAN CUTI SAKIT', " & _
        '                    "p.SARING, case p.TATATERTIB when 1 then 'YA' when 2 then 'TIDAK' else '' end 'TATATERTIB',cast(sesi_bulan as varchar(4)) + '/' + cast(sesi_tahun as varchar(4)) as 'SESI PENGAMBILAN' from pelatih p " & _
        '                    "left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej where p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by sesi_tahun, sesi_bulan,p.nama"

        'Exclude Pass Exam Cannidate 12072023 - OSH
        'Dim SQL As String = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', p.MARKAH as 'MARKAH BERTERUSAN', isnull(p.cuti,0)-isnull(p.cuti_ganti,0) as 'LEBIHAN CUTI SAKIT', " &
        '                    "p.SARING, case p.TATATERTIB when 1 then 'YA' when 2 then 'TIDAK' else '' end 'TATATERTIB',cast(sesi_bulan as varchar(4)) + '/' + cast(sesi_tahun as varchar(4)) as 'SESI PENGAMBILAN' from pelatih p " &
        '                    "left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej where p.saring <> '2' and p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by sesi_tahun, sesi_bulan,p.nama"

        'Fix saring flag bug 05072024- OSH 

        Dim SQL As String = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', p.MARKAH as 'MARKAH BERTERUSAN', isnull(p.cuti,0)-isnull(p.cuti_ganti,0) as 'LEBIHAN CUTI SAKIT', " &
                            "p.SARING, case p.TATATERTIB when 1 then 'YA' when 2 then 'TIDAK' else '' end 'TATATERTIB',cast(sesi_bulan as varchar(4)) + '/' + cast(sesi_tahun as varchar(4)) as 'SESI PENGAMBILAN' from pelatih p " &
                            "left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej where (p.saring is null or p.saring = '0' or p.saring = '1' ) and  p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by sesi_tahun, sesi_bulan,p.nama"
        Dim List_Adp As New SqlDataAdapter(Sql, Cn)
        List_Adp.Fill(List_Data, "pelatih")
        Gd.DataSource = List_Data.Tables("pelatih")
        DataBind()
        Gd.Visible = True
        cmd_Hantar.Visible = True
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Pelatih_Saring", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        'KOLEJ
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS<3 ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KURSUS
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmdHantar0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar0.Click
        If Tx_Nama.Text <> "" Then Cari("p.nama like '" & Tx_Nama.Text & "%'")
    End Sub

    Protected Sub cmdHantar1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar1.Click
        If Tx_NoKP.Text <> "" Then Cari("p.nokp like '" & Tx_NoKP.Text & "%'")
    End Sub

    Protected Sub cmd_Saring_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Saring.Click
        If Cb_Saring.SelectedIndex = 1 Then Cari("p.saring = 1") Else Cari("(p.saring < 1 or p.saring is null)")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then  Else e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
        e.Row.Cells(4).Visible = False 'kolej
        e.Row.Cells(7).Visible = False 'saring
        e.Row.Cells(5).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(6).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(8).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(9).HorizontalAlign = HorizontalAlign.Center
        e.Row.Cells(5).Width = Unit.Pixel(60)
        e.Row.Cells(6).Width = Unit.Pixel(60)
        e.Row.Cells(7).Width = Unit.Pixel(60)
    End Sub

    Protected Sub cmd_Hantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Hantar.Click
        Dim i As Int16, x As CheckBox, SQL As String = ""

        For i = 0 To Gd.Rows.Count - 1
            x = Gd.Rows.Item(i).Cells(1).FindControl("chk_pilih")
            If x.Checked Then
                'Comment enlist update incorrect 14112012 - OSH
                'SQL += "update pelatih set saring = 1 where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'" & vbCrLf

                'Add improve enlist update 14112012 - OSH
                SQL += "update pelatih set saring = 1 where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' AND J_Kursus ='" & Cb_Kursus.SelectedValue & "' " & vbCrLf

            Else
                'Comment unenlist update incorrect 14112012- OSH
                'SQL += "update pelatih set saring = 0 where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'" & vbCrLf

                'Add improve unenlist update 14112012- OSH
                SQL += "update pelatih set saring = 0 where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' AND J_Kursus ='" & Cb_Kursus.SelectedValue & "'" & vbCrLf
            End If
        Next
        'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal)

        Try
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Session("Msg_Tajuk") = "Saring Pelatih untuk Peperiksaan"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx")
            'Msg(Me, "Rekod Telah Dikemaskini...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex < 0 Then Exit Sub
        Dim x As CheckBox
        x = e.Row.FindControl("chk_pilih")
        If e.Row.Cells(7).Text = "1" Then
            x.Checked = True
        End If
        If e.Row.Cells(8).Text = "YA" Then
            x.Checked = False
            x.Enabled = False
            e.Row.Cells(0).ForeColor = Drawing.Color.Red
            e.Row.Cells(2).ForeColor = Drawing.Color.Red
            e.Row.Cells(3).ForeColor = Drawing.Color.Red
            e.Row.Cells(4).ForeColor = Drawing.Color.Red
            e.Row.Cells(5).ForeColor = Drawing.Color.Red
            e.Row.Cells(6).ForeColor = Drawing.Color.Red
            e.Row.Cells(8).ForeColor = Drawing.Color.Red
        End If
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        If Cb_Kolej.SelectedValue = "" Then Exit Sub
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select distinct sesi_bulan, sesi_tahun, case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end from pelatih where id_kolej = " & Cb_Kolej.SelectedValue & " order by sesi_tahun, sesi_bulan"
        Rdr = Cmd.ExecuteReader()
        Cb_Sesi.Items.Clear()
        Cb_Sesi.Items.Add("SEMUA")
        Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Sesi.Items.Add(Rdr(1) & " - " & Rdr(2))
            Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = " p.sesi_bulan = " & Rdr(0) & " and p.sesi_tahun = " & Rdr(1) & " and "
        End While
        Rdr.Close()
        Cn.Close()
    End Sub
End Class