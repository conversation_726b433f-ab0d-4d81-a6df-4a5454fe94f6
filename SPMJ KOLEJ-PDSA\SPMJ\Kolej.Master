﻿<%@ Master Language="VB" AutoEventWireup="false" CodeBehind="Kolej.master.vb" Inherits="SPMJ.Kolej" %>

<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

    <style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .menu_big
        {
             font-family: Arial;
             font-size: 7pt; 
             color: #ffffff;
        }
        .menu_small
        {
             font-family: Arial;
             font-size: 7pt; 
             color: #ffffff;
        }
        .shadow
        {
        	float:left;

        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        </style>
<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>SPMJ</title>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body style="margin: 0px;padding: 0; background-color: #FFFFFF; background-image: url('../Image/Bg_Sgt.gif'); background-attachment: fixed;">
    <form id="form1" runat="server">
    <div align="center">
        <table cellpadding="0" cellspacing="0" style="width:1024px;" align="center">
            <tr style="padding: -1px; margin: -1px; white-space: nowrap"><td width="1024" bgcolor="Black"><asp:Image ID="Image1" 
                runat="server" style="margin: 0px;" ImageUrl="~/Image/Hd_Top.gif"  Width="1024px"/>
                <asp:Menu ID="Menu1" runat="server" 
                BackColor="#5E8635" BorderColor="#003300" 
    BorderStyle="Solid" BorderWidth="1px" 
    DynamicPopOutImageTextFormatString="" 
    Font-Bold="True" Font-Names="Arial" Font-Size="7pt" 
    ForeColor="White" MaximumDynamicDisplayLevels="2" 
    Orientation="Horizontal" StaticPopOutImageTextFormatString="" 
    StaticSubMenuIndent="" 
    style="color: #FFFFFF;left: -1px; margin-left: 0px; margin-right: 0px; margin-top: 0px;" 
                    Width="1024px" Font-Strikeout="False" 
                DynamicHorizontalOffset="-1" StaticEnableDefaultPopOutImage="False" 
                >
                        <StaticSelectedStyle BackColor="#FFFFCC" ForeColor="#5E8635" />
                        <LevelSubMenuStyles>
                            <asp:SubMenuStyle Font-Underline="False" />
                            <asp:SubMenuStyle Font-Underline="False" />
                            <asp:SubMenuStyle Font-Underline="False" />
                            <asp:SubMenuStyle Font-Underline="False" />
                        </LevelSubMenuStyles>
                        <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" 
                                   Width="20px" />
                        <DynamicHoverStyle Font-Bold="True" Font-Italic="False" 
        Font-Names="Arial" Font-Size="7pt" ForeColor="White" BackColor="#669900" />
                        <DynamicMenuStyle BackColor="White" BorderColor="#003300" BorderStyle="Solid" 
        BorderWidth="1px" HorizontalPadding="0px" VerticalPadding="0px" CssClass="shadow" />
                        <DynamicItemTemplate>
                            <%#Eval("Text", "")%>
                        </DynamicItemTemplate>
                        <DynamicSelectedStyle BackColor="#FFCC00" Font-Bold="True" ForeColor="Black" />
                        <DynamicMenuItemStyle Font-Bold="True" 
        Font-Names="Arial" Font-Size="7pt" ForeColor="#003300" 
        HorizontalPadding="5px" ItemSpacing="0px" VerticalPadding="3px" />
                        <StaticHoverStyle ForeColor="#006699" BackColor="White" BorderStyle="None" />
                        <Items>
                            <asp:MenuItem Text="LAMAN UTAMA" Value="1"></asp:MenuItem>

                            <asp:MenuItem Text="DAFTAR PELATIH BARU" Value="a">
                                <%--<asp:MenuItem Text="SIJIL DAN DIPLOMA (BARU)" Value="a1"></asp:MenuItem>--%>
                              <%--  <asp:MenuItem Text="IJAZAH (BARU)" Value="a2"></asp:MenuItem>--%>
                                <%--<asp:MenuItem Text="SIJIL DAN DIPLOMA (BARU)" Value="a1"></asp:MenuItem>--%>
                                <asp:MenuItem Text="SIJIL, DIPLOMA, IJAZAH  (BAHARU)" Value="a1"></asp:MenuItem>
                               <asp:MenuItem Text="IGCSE" Value="a2"></asp:MenuItem>
                                <asp:MenuItem Text="DIPLOMA (2025-2026)" Value="a3"></asp:MenuItem>
                                 </asp:MenuItem>
                           <asp:MenuItem Text="PINDA/SEMAK REKOD PELATIH" Value="b">
                            </asp:MenuItem>
                            <asp:MenuItem Text="SARING CALON PEPERIKSAAN" Value="c">
                            </asp:MenuItem>
                          <%--  <asp:MenuItem Text="SEMAK CALON PEPERIKSAAN" Value="d">
                            </asp:MenuItem>--%>
                            <asp:MenuItem Text="SENARAI CALON PEPERIKSAAN" Value="e"> 
                            </asp:MenuItem>
                            <asp:MenuItem Text="TUKAR KATA LALUAN" Value="x">
                            </asp:MenuItem>
                            <asp:MenuItem Text="KELUAR" Value="z"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>
                            <asp:MenuItem Selectable="False"></asp:MenuItem>

                        </Items></asp:Menu></td></tr>
            <tr align="left"><td style="border: 1px solid #000000; background-color: #FFFFFF;" 
                        width="1024"><asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server"></asp:ContentPlaceHolder></td></tr>
            <tr>
                    <td style="border-style: none solid solid solid; border-width: 1px; border-color: #000000; background-color: #5E8635; font-family: Arial; font-size: 7pt; font-variant: small-caps; font-weight: bolder; color: #FFFFFF;" 
                        width="1024" align="center" height="18">©2009 BAHAGIAN KEJURURAWATAN, KEMENTERIAN 
                        KESIHATAN MALAYSIA</td></tr></table>
    </div>
                    </form>
</body>
</html>
