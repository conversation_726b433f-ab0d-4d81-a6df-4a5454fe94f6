﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm57
    Inherits System.Web.UI.Page

    Public Sub Reset()
        Session("PN_Pinda") = False
        Tx_Id.Text = ""
        Tx_Id0.Text = ""
        Tx_Etnik.Text = ""
        cb_baru.Visible = False
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select dc_etnik ETNIK, kod_etnik 'KOD ETNIK', id_etnik from pn_etnik order by dc_etnik"
        Tb = "pn_etnik"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Main", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Cari("")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Id.Text = "" Then Msg(Me, "Sila isi Kod Etnik!") : Exit Sub
        If Tx_Id.Text.Length < 4 Then Msg(Me, "Format Kod Etnik tidak tepat") : Exit Sub
        If Not IsNumeric(Tx_Id.Text) Then Msg(Me, "Format Kod Etnik tidak tepat") : Exit Sub
        If Tx_Etnik.Text = "" Then Msg(Me, "Sila isi Etnik!") : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Dim SQL As String

        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If Session("PN_Pinda") Then
            Tx_Id.Enabled = False
            SQL = "update pn_etnik set dc_etnik = '" & Tx_Etnik.Text.Trim.ToUpper & "', kod_etnik = '" & Tx_Id.Text & "', mod_id='" & Session("Id_PG") & "', mod_tkh=getdate() where id_etnik=" & Tx_Id0.Text
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cari("")
            Reset()
            Msg(Me, "Rekod Telah Dikemaskini...")
            Tx_Id.Enabled = True
            Exit Sub
        End If

        Cmd.CommandText = "select * from pn_etnik where dc_etnik = '" & Tx_Etnik.Text.Trim & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Telah Wujud!!!")
            Rdr.Close()
            Reset()
            Exit Sub
        End If
        Rdr.Close()

        Cmd.CommandText = "select * from pn_etnik where kod_etnik = '" & Tx_Id.Text.Trim & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Telah Wujud!!!")
            Rdr.Close()
            Reset()
            Exit Sub
        End If
        Rdr.Close()

        SQL = "insert pn_etnik (kod_etnik, dc_etnik, log_id, log_tkh) select '" & Tx_Id.Text.Trim & "','" & Tx_Etnik.Text.Trim.ToUpper & "','" & Session("Id_PG") & "',getdate()"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Telah Disimpan...")
            Cari("")
        Catch ex As Exception
            Msg(Me, "Ralat!!!")
        End Try
        Reset()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(4).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(150)
        e.Row.Cells(2).Width = Unit.Pixel(350)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("PN_Padam") Then
            Session("PN_Padam") = False
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Dim SQL As String
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "delete from pn_etnik where kod_etnik='" & Gd.SelectedRow.Cells(3).Text & "' and dc_etnik='" & Gd.SelectedRow.Cells(2).Text & "' and id_etnik=" & Gd.SelectedRow.Cells(4).Text
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Tx_Id.Text = ""
                Msg(Me, "Rekod Telah Dipadam...")
                Cari("")
                Tx_Id.Text = ""
                Tx_Etnik.Text = ""
            Catch ex As Exception
                Msg(Me, "Ralat...")
            End Try
            Cn.Close()
            Exit Sub
        End If
        Session("PN_Pinda") = True
        cb_baru.Visible = True
        Tx_Etnik.Text = Gd.SelectedRow.Cells(2).Text
        Tx_Id.Text = Gd.SelectedRow.Cells(3).Text
        Tx_Id0.Text = Gd.SelectedRow.Cells(4).Text
    End Sub

    Protected Sub Cb_Padam_Click(ByVal sender As Object, ByVal e As EventArgs)
        Session("PN_Padam") = True
    End Sub

    Protected Sub cb_baru_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cb_baru.Click
        Reset()
        Tx_Id.Focus()
    End Sub
End Class