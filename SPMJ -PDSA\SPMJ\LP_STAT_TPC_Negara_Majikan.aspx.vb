﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_TPC_Negara_Majikan
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 2 Step -1
            Cb_Tahun.Items.Add(i)
        Next
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        'Dim j_daftar As Integer = Cb_Jawatan.SelectedValue
        Dim K, S, X, T As Long
        Dim Tajuk, Tajuk2 As String
        Tajuk = "Laporan Statistik Kelulusan Pendaftaran TPC " 'Bagi " & Cb_Jawatan.SelectedItem.Text & ", " & tahun
        Tajuk2 = "mengikut <PERSON>ikan dan <PERSON>"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "laporan" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>TEMPAT AMALAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>LELAKI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>PEREMPUAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'NEGERI
        Cmd.CommandText = "SELECT Id_AMALAN, Dc_AMALAN FROM PN_TPT_AMALAN ORDER BY Dc_AMALAN"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "Amalan")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows

            K = 0 : S = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where jantina=1 and warganegara=" & dr.Item(0)
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then K = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from tmp_tpc where jantina=2 and warganegara=" & dr.Item(0)
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then S = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from tmp_tpc where warganegara=" & dr.Item(0)
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then T = Rdr(0) : X = Rdr(0) - K - S
            Rdr.Close()
            'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where sektor=2 and id_amalan>0 and negeri=" & dr.Item(0) & ")"
            'Rdr = Cmd.ExecuteReader()
            'If Rdr.Read Then S = Rdr(0)
            'Rdr.Close()
            'If dr.Item(1) = "-" Then
            '    Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan not in (select id_amalan from pn_tpt_amalan where id_amalan>0 )"
            '    Rdr = Cmd.ExecuteReader()
            '    If Rdr.Read Then X = Rdr(0)
            '    Rdr.Close()
            'End If
            T = K + S + X
            'If T > 0 Then
            Header += "<tr>"
            Header += "    <td>" + dr.Item(1) + "</td> "
            Header += "    <td>" & K & "</td><td>" & S & "</td><td>" & X & "</td><td>" & T & "</td>"
            Header += "</tr>"
            'End If
        Next
        Cn.Close()
        Header += "</table>"
        Response.Write(Header)
        Response.End()
        Response.Flush()
    End Sub

    Protected Sub Cb_Jawatan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Jawatan.SelectedIndexChanged

    End Sub
End Class