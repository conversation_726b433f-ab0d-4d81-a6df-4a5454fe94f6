-- =====================================================================================
-- SPMJ KOLEJ-PDSA Database Schema Refactoring Script
-- Purpose: Update PWD column from varchar(15) to varchar(255) - Industry Standard
-- Date: June 17, 2025
-- Author: System Modernization Team
-- =====================================================================================

-- IMPORTANT: Run this script during maintenance window
-- Estimated downtime: 5-10 minutes depending on table size

USE [YOUR_DATABASE_NAME]  -- Replace with actual database name
GO

PRINT '=========================================='
PRINT 'STARTING PWD COLUMN REFACTORING PROCESS'
PRINT 'Date: ' + CAST(GETDATE() AS VARCHAR(20))
PRINT '=========================================='

-- =====================================================================================
-- STEP 1: CREATE BACKUP TABLE
-- =====================================================================================
PRINT 'Step 1: Creating backup table...'

-- Create backup of current table
IF OBJECT_ID('kj_pengguna_backup_20250617', 'U') IS NOT NULL
    DROP TABLE kj_pengguna_backup_20250617;

SELECT * 
INTO kj_pengguna_backup_20250617 
FROM kj_pengguna;

-- Verify backup
DECLARE @original_count INT, @backup_count INT;
SELECT @original_count = COUNT(*) FROM kj_pengguna;
SELECT @backup_count = COUNT(*) FROM kj_pengguna_backup_20250617;

PRINT 'Original table records: ' + CAST(@original_count AS VARCHAR(10));
PRINT 'Backup table records: ' + CAST(@backup_count AS VARCHAR(10));

IF @original_count = @backup_count
    PRINT '✅ Backup created successfully'
ELSE
    THROW 50001, '❌ Backup verification failed - STOPPING SCRIPT', 1;

-- =====================================================================================
-- STEP 2: ADD NEW PWD COLUMN
-- =====================================================================================
PRINT 'Step 2: Adding new PWD column with industry standard size...'

-- Add new PWD column with varchar(255)
ALTER TABLE kj_pengguna 
ADD PWD_NEW varchar(255) NULL;

-- Add documentation
IF EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('kj_pengguna') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('kj_pengguna') AND name = 'PWD_NEW'))
    EXEC sp_dropextendedproperty 'MS_Description', 'SCHEMA', 'dbo', 'TABLE', 'kj_pengguna', 'COLUMN', 'PWD_NEW';

EXEC sp_addextendedproperty 
    'MS_Description', 
    'Password hash - Industry standard varchar(255) for SHA256/SHA512/Bcrypt compatibility. Updated: 2025-06-17',
    'SCHEMA', 'dbo', 'TABLE', 'kj_pengguna', 'COLUMN', 'PWD_NEW';

PRINT '✅ PWD_NEW column added successfully'

-- =====================================================================================
-- STEP 3: MIGRATE EXISTING PASSWORD DATA
-- =====================================================================================
PRINT 'Step 3: Migrating existing password data...'

-- Count different password types before migration
DECLARE @plain_text_count INT, @encrypted_count INT, @workaround_count INT;

SELECT @plain_text_count = COUNT(*) 
FROM kj_pengguna 
WHERE ISNULL(pwd_encrypted, 0) = 0;

SELECT @encrypted_count = COUNT(*) 
FROM kj_pengguna 
WHERE pwd_encrypted = 1 AND (salt IS NULL OR CHARINDEX('|', salt) = 0);

SELECT @workaround_count = COUNT(*) 
FROM kj_pengguna 
WHERE pwd_encrypted = 1 AND salt IS NOT NULL AND CHARINDEX('|', salt) > 0;

PRINT 'Password type distribution:'
PRINT '  Plain text passwords: ' + CAST(@plain_text_count AS VARCHAR(10));
PRINT '  Standard encrypted passwords: ' + CAST(@encrypted_count AS VARCHAR(10));
PRINT '  Workaround format passwords: ' + CAST(@workaround_count AS VARCHAR(10));

-- Migrate password data
UPDATE kj_pengguna 
SET PWD_NEW = CASE 
    -- Workaround format: extract full hash from salt field
    WHEN pwd_encrypted = 1 AND salt IS NOT NULL AND CHARINDEX('|', salt) > 0 THEN
        LEFT(salt, CHARINDEX('|', salt) - 1)
    -- Standard encrypted: use existing PWD (may be truncated)
    WHEN pwd_encrypted = 1 AND salt IS NOT NULL THEN
        PWD
    -- Plain text: keep as-is (will force password change)
    ELSE
        PWD
END;

-- Verify migration
DECLARE @migrated_count INT;
SELECT @migrated_count = COUNT(*) FROM kj_pengguna WHERE PWD_NEW IS NOT NULL;

PRINT 'Records migrated to PWD_NEW: ' + CAST(@migrated_count AS VARCHAR(10));

IF @migrated_count = @original_count
    PRINT '✅ Password data migration completed successfully'
ELSE
    THROW 50002, '❌ Password migration verification failed - STOPPING SCRIPT', 1;

-- =====================================================================================
-- STEP 4: CLEAN UP SALT COLUMN (REMOVE WORKAROUND DATA)
-- =====================================================================================
PRINT 'Step 4: Cleaning up salt column from workaround format...'

-- Clean salt column from workaround format
UPDATE kj_pengguna 
SET salt = CASE 
    -- Extract original salt from workaround format
    WHEN pwd_encrypted = 1 AND salt IS NOT NULL AND CHARINDEX('|', salt) > 0 THEN
        SUBSTRING(salt, CHARINDEX('|', salt) + 1, LEN(salt))
    -- Keep existing salt as-is
    ELSE
        salt
END
WHERE pwd_encrypted = 1 AND salt IS NOT NULL;

-- Verify salt cleanup
DECLARE @clean_salt_count INT, @dirty_salt_count INT;
SELECT @dirty_salt_count = COUNT(*) 
FROM kj_pengguna 
WHERE salt IS NOT NULL AND CHARINDEX('|', salt) > 0;

SELECT @clean_salt_count = COUNT(*) 
FROM kj_pengguna 
WHERE salt IS NOT NULL AND CHARINDEX('|', salt) = 0;

PRINT 'Salt cleanup results:'
PRINT '  Clean salt records: ' + CAST(@clean_salt_count AS VARCHAR(10));
PRINT '  Dirty salt records (still contain |): ' + CAST(@dirty_salt_count AS VARCHAR(10));

IF @dirty_salt_count = 0
    PRINT '✅ Salt column cleanup completed successfully'
ELSE
    PRINT '⚠️ WARNING: Some salt records still contain workaround format';

-- =====================================================================================
-- STEP 5: ANALYSIS AND VALIDATION
-- =====================================================================================
PRINT 'Step 5: Analyzing password data quality...'

-- Analyze password lengths
SELECT 
    'Password Length Analysis' as analysis_type,
    MIN(LEN(PWD_NEW)) as min_length,
    MAX(LEN(PWD_NEW)) as max_length,
    AVG(LEN(PWD_NEW)) as avg_length,
    COUNT(*) as total_records
FROM kj_pengguna 
WHERE PWD_NEW IS NOT NULL;

-- Check for potential issues
SELECT 
    CASE 
        WHEN LEN(PWD_NEW) = 15 AND pwd_encrypted = 1 THEN 'Truncated Hash (needs re-encryption)'
        WHEN LEN(PWD_NEW) = 44 AND pwd_encrypted = 1 THEN 'Full SHA256 Hash'
        WHEN LEN(PWD_NEW) < 15 AND pwd_encrypted = 0 THEN 'Plain Text Password'
        ELSE 'Other'
    END as password_category,
    COUNT(*) as record_count
FROM kj_pengguna 
WHERE PWD_NEW IS NOT NULL
GROUP BY 
    CASE 
        WHEN LEN(PWD_NEW) = 15 AND pwd_encrypted = 1 THEN 'Truncated Hash (needs re-encryption)'
        WHEN LEN(PWD_NEW) = 44 AND pwd_encrypted = 1 THEN 'Full SHA256 Hash'
        WHEN LEN(PWD_NEW) < 15 AND pwd_encrypted = 0 THEN 'Plain Text Password'
        ELSE 'Other'
    END
ORDER BY record_count DESC;

-- =====================================================================================
-- STEP 6: FINAL SCHEMA CHANGE (CRITICAL SECTION)
-- =====================================================================================
PRINT 'Step 6: Performing final schema changes (brief downtime)...'

BEGIN TRANSACTION;

BEGIN TRY
    -- Drop old PWD column
    ALTER TABLE kj_pengguna DROP COLUMN PWD;
    PRINT '✅ Old PWD column dropped'
    
    -- Rename new column to PWD
    EXEC sp_rename 'kj_pengguna.PWD_NEW', 'PWD', 'COLUMN';
    PRINT '✅ PWD_NEW renamed to PWD'
    
    -- Make PWD column NOT NULL
    UPDATE kj_pengguna SET PWD = '' WHERE PWD IS NULL;  -- Handle any NULLs
    ALTER TABLE kj_pengguna ALTER COLUMN PWD varchar(255) NOT NULL;
    PRINT '✅ PWD column set to NOT NULL'
    
    COMMIT TRANSACTION;
    PRINT '✅ Schema changes committed successfully'
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT '❌ Error during schema changes: ' + ERROR_MESSAGE();
    THROW;
END CATCH;

-- =====================================================================================
-- STEP 7: FINAL VERIFICATION
-- =====================================================================================
PRINT 'Step 7: Final verification of schema changes...'

-- Verify new column structure
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'kj_pengguna' 
AND COLUMN_NAME IN ('PWD', 'salt', 'pwd_encrypted')
ORDER BY COLUMN_NAME;

-- Final record count verification
DECLARE @final_count INT;
SELECT @final_count = COUNT(*) FROM kj_pengguna;

PRINT 'Final verification:'
PRINT '  Original records: ' + CAST(@original_count AS VARCHAR(10));
PRINT '  Final records: ' + CAST(@final_count AS VARCHAR(10));

IF @original_count = @final_count
    PRINT '✅ Record count verification passed'
ELSE
    THROW 50003, '❌ Final record count verification failed', 1;

-- Test a sample record
IF EXISTS (SELECT TOP 1 * FROM kj_pengguna WHERE LEN(PWD) > 15)
    PRINT '✅ PWD column can now store long hashes (>15 characters)'
ELSE
    PRINT '⚠️ No records found with PWD length > 15 characters';

-- =====================================================================================
-- STEP 8: CLEANUP AND DOCUMENTATION
-- =====================================================================================
PRINT 'Step 8: Cleanup and documentation...'

-- Update table documentation
IF EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('kj_pengguna') AND name = 'MS_Description' AND minor_id = 0)
    EXEC sp_dropextendedproperty 'MS_Description', 'SCHEMA', 'dbo', 'TABLE', 'kj_pengguna';

EXEC sp_addextendedproperty 
    'MS_Description', 
    'User authentication table for KOLEJ-PDSA system. PWD column updated to varchar(255) for industry standard hash storage. Last updated: 2025-06-17',
    'SCHEMA', 'dbo', 'TABLE', 'kj_pengguna';

-- Create migration log entry
IF OBJECT_ID('migration_log', 'U') IS NOT NULL
BEGIN
    INSERT INTO migration_log (migration_date, migration_type, description, status)
    VALUES (GETDATE(), 'SCHEMA_UPDATE', 'PWD column expanded from varchar(15) to varchar(255)', 'SUCCESS');
END

PRINT '✅ Documentation updated'

-- Summary report
PRINT ''
PRINT '=========================================='
PRINT 'MIGRATION SUMMARY REPORT'
PRINT '=========================================='
PRINT 'Migration Date: ' + CAST(GETDATE() AS VARCHAR(20))
PRINT 'Records Processed: ' + CAST(@original_count AS VARCHAR(10))
PRINT 'Plain Text Passwords: ' + CAST(@plain_text_count AS VARCHAR(10)) + ' (will force password change)'
PRINT 'Encrypted Passwords: ' + CAST(@encrypted_count AS VARCHAR(10))
PRINT 'Workaround Passwords: ' + CAST(@workaround_count AS VARCHAR(10)) + ' (migrated successfully)'
PRINT 'PWD Column: varchar(15) → varchar(255) ✅'
PRINT 'Salt Column: Cleaned from workaround format ✅'
PRINT 'Backup Table: kj_pengguna_backup_20250617 ✅'
PRINT ''
PRINT '🎉 MIGRATION COMPLETED SUCCESSFULLY!'
PRINT 'The system now supports industry standard password storage.'
PRINT 'Code deployment can proceed to remove workaround methods.'
PRINT '=========================================='

-- Optional: Show next steps
PRINT ''
PRINT 'NEXT STEPS:'
PRINT '1. Deploy updated application code (ForcePasswordChange.aspx.vb, Login_J.aspx.vb)'
PRINT '2. Remove workaround methods from PasswordHelper.vb'
PRINT '3. Test password change and authentication functionality'
PRINT '4. Force password change for any remaining plain text passwords'
PRINT '5. Monitor system for 24-48 hours'
PRINT '6. Remove backup table after validation period'
GO
