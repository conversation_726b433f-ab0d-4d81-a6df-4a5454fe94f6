# Complete AJAX Controls Fix - Removes all AJAX controls from .NET 3.5 project
# This script addresses parser errors for UpdatePanel, ScriptManager, and ScriptManagerProxy

Write-Host "=== COMPLETE AJAX CONTROLS FIX ===" -ForegroundColor Green
Write-Host "Removing all AJAX controls from SPMJ KOLEJ-PDSA project..." -ForegroundColor Yellow

$projectPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ"
$logFile = "d:\source_code\.NET 3.5. - Q\AJAX-Controls-Complete-Fix.log"

# Initialize log
"=== AJAX Controls Complete Fix Log ===" | Out-File $logFile
"Date: $(Get-Date)" | Out-File $logFile -Append
"" | Out-File $logFile -Append

$fixesApplied = 0
$filesProcessed = 0

# Function to remove AJAX controls from a file
function Remove-AjaxControls($filePath) {
    try {
        $content = Get-Content $filePath -Raw
        $originalContent = $content
        $localFixes = 0
        
        # Remove ScriptManager controls
        $content = $content -replace '(?s)<asp:ScriptManager[^>]*>.*?</asp:ScriptManager>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        # Remove ScriptManagerProxy controls
        $content = $content -replace '(?s)<asp:ScriptManagerProxy[^>]*>.*?</asp:ScriptManagerProxy>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        # Remove UpdatePanel controls but preserve inner content
        $content = $content -replace '(?s)<asp:UpdatePanel[^>]*>\s*<ContentTemplate>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        $content = $content -replace '(?s)</ContentTemplate>\s*</asp:UpdatePanel>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        # Remove standalone UpdatePanel tags
        $content = $content -replace '<asp:UpdatePanel[^>]*>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        $content = $content -replace '</asp:UpdatePanel>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        # Remove AsyncPostBackTrigger and other AJAX-related controls
        $content = $content -replace '(?s)<Triggers>.*?</Triggers>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        $content = $content -replace '(?s)<asp:AsyncPostBackTrigger[^>]*/?>', ''
        if ($content -ne $originalContent) { $localFixes++; $originalContent = $content }
        
        # Clean up extra whitespace
        $content = $content -replace '\r\n\s*\r\n\s*\r\n', "`r`n`r`n"
        
        if ($localFixes -gt 0) {
            Set-Content $filePath $content -Encoding UTF8
            Write-Host "✅ Fixed $localFixes AJAX controls in: $(Split-Path $filePath -Leaf)" -ForegroundColor Green
            "Fixed $localFixes AJAX controls in: $filePath" | Out-File $logFile -Append
            return $localFixes
        } else {
            Write-Host "⚪ No AJAX controls found in: $(Split-Path $filePath -Leaf)" -ForegroundColor Gray
            return 0
        }
    }
    catch {
        Write-Host "❌ Error processing: $(Split-Path $filePath -Leaf) - $($_.Exception.Message)" -ForegroundColor Red
        "ERROR processing $filePath`: $($_.Exception.Message)" | Out-File $logFile -Append
        return 0
    }
}

# Get all .aspx files in the project
$aspxFiles = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse

Write-Host "`nProcessing $($aspxFiles.Count) .aspx files..." -ForegroundColor Yellow

foreach ($file in $aspxFiles) {
    $filesProcessed++
    Write-Host "`n[$filesProcessed/$($aspxFiles.Count)] Processing: $($file.Name)" -ForegroundColor Cyan
    
    $fixes = Remove-AjaxControls $file.FullName
    $fixesApplied += $fixes
}

# Also check master pages
$masterFiles = Get-ChildItem $projectPath -Filter "*.master" -Recurse

if ($masterFiles.Count -gt 0) {
    Write-Host "`nProcessing $($masterFiles.Count) .master files..." -ForegroundColor Yellow
    
    foreach ($file in $masterFiles) {
        $filesProcessed++
        Write-Host "`nProcessing: $($file.Name)" -ForegroundColor Cyan
        
        $fixes = Remove-AjaxControls $file.FullName
        $fixesApplied += $fixes
    }
}

# Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Green
Write-Host "Files processed: $filesProcessed" -ForegroundColor White
Write-Host "Total AJAX controls removed: $fixesApplied" -ForegroundColor White

"" | Out-File $logFile -Append
"=== SUMMARY ===" | Out-File $logFile -Append
"Files processed: $filesProcessed" | Out-File $logFile -Append
"Total AJAX controls removed: $fixesApplied" | Out-File $logFile -Append
"Completed: $(Get-Date)" | Out-File $logFile -Append

if ($fixesApplied -gt 0) {
    Write-Host "`n✅ AJAX controls fix completed successfully!" -ForegroundColor Green
    Write-Host "⚠️  Please rebuild the project and test in browser." -ForegroundColor Yellow
} else {
    Write-Host "`n⚪ No AJAX controls found to fix." -ForegroundColor Gray
}

Write-Host "`nLog saved to: $logFile" -ForegroundColor Cyan
