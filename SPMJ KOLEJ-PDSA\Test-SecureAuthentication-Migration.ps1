# =============================================================================
# SPMJ KOLEJ-PDSA Secure Authentication Testing Script
# PowerShell script to validate the secure authentication migration
# =============================================================================

param(
    [string]$DatabaseServer = "************",
    [string]$DatabaseName = "SPMJ_PDSA",
    [string]$EmailServiceUrl = "http://localhost:5000",
    [string]$ApplicationUrl = "http://localhost/SPMJ_KOLEJ_PDSA"
)

Write-Host "=======================================================================" -ForegroundColor Green
Write-Host "SPMJ KOLEJ-PDSA Secure Authentication Migration Validation" -ForegroundColor Green
Write-Host "=======================================================================" -ForegroundColor Green
Write-Host ""

$totalTests = 0
$passedTests = 0
$failedTests = 0

function Test-DatabaseConnection {
    Write-Host "1. Testing Database Connection..." -ForegroundColor Yellow
    $global:totalTests++
    
    try {
        $connectionString = "Server=$DatabaseServer;Database=$DatabaseName;Integrated Security=true"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        # Test basic connectivity
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT COUNT(*) FROM kj_pengguna"
        $userCount = $command.ExecuteScalar()
        
        $connection.Close()
        
        Write-Host "   ✓ Database connection successful" -ForegroundColor Green
        Write-Host "   ✓ Found $userCount users in kj_pengguna table" -ForegroundColor Green
        $global:passedTests++
        return $true
    }
    catch {
        Write-Host "   ❌ Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-DatabaseSchema {
    Write-Host "2. Testing Enhanced Database Schema..." -ForegroundColor Yellow
    $global:totalTests++
    
    try {
        $connectionString = "Server=$DatabaseServer;Database=$DatabaseName;Integrated Security=true"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        # Check for enhanced columns
        $command = $connection.CreateCommand()
        $command.CommandText = @"
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'kj_pengguna' 
              AND COLUMN_NAME IN ('salt', 'email', 'pwd_encrypted', 'failed_login_attempts', 'account_locked')
"@
        $reader = $command.ExecuteReader()
        
        $foundColumns = @()
        while ($reader.Read()) {
            $foundColumns += $reader["COLUMN_NAME"]
        }
        $reader.Close()
        
        $requiredColumns = @('salt', 'email', 'pwd_encrypted', 'failed_login_attempts', 'account_locked')
        $missingColumns = $requiredColumns | Where-Object { $_ -notin $foundColumns }
        
        if ($missingColumns.Count -eq 0) {
            Write-Host "   ✓ All enhanced security columns found" -ForegroundColor Green
            $global:passedTests++
            $connection.Close()
            return $true
        } else {
            Write-Host "   ❌ Missing columns: $($missingColumns -join ', ')" -ForegroundColor Red
            $global:failedTests++
            $connection.Close()
            return $false
        }
    }
    catch {
        Write-Host "   ❌ Schema validation failed: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-EmailServiceTables {
    Write-Host "3. Testing Email Service Integration Tables..." -ForegroundColor Yellow
    $global:totalTests++
    
    try {
        $connectionString = "Server=$DatabaseServer;Database=$DatabaseName;Integrated Security=true"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = @"
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME IN ('otp_tokens', 'password_reset_tokens', 'email_audit_log')
"@
        $reader = $command.ExecuteReader()
        
        $foundTables = @()
        while ($reader.Read()) {
            $foundTables += $reader["TABLE_NAME"]
        }
        $reader.Close()
        $connection.Close()
        
        $requiredTables = @('otp_tokens', 'password_reset_tokens', 'email_audit_log')
        $missingTables = $requiredTables | Where-Object { $_ -notin $foundTables }
        
        if ($missingTables.Count -eq 0) {
            Write-Host "   ✓ All email service tables found" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ Missing tables: $($missingTables -join ', ')" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    }
    catch {
        Write-Host "   ❌ Email service table validation failed: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-EmailServiceConnection {
    Write-Host "4. Testing Email Service Microservice..." -ForegroundColor Yellow
    $global:totalTests++
    
    try {
        $response = Invoke-RestMethod -Uri "$EmailServiceUrl/health" -Method Get -TimeoutSec 10
        
        if ($response.status -eq "healthy") {
            Write-Host "   ✓ Email service is healthy and responding" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ Email service unhealthy: $($response.status)" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    }
    catch {
        Write-Host "   ⚠ Email service not available: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "   ℹ System will work without OTP, but email features disabled" -ForegroundColor Cyan
        $global:passedTests++  # Not critical for basic function
        return $false
    }
}

function Test-ApplicationFiles {
    Write-Host "5. Testing Application Files..." -ForegroundColor Yellow
    $global:totalTests++
    
    $requiredFiles = @(
        "PasswordHelper.vb",
        "EmailServiceClient.vb", 
        "OtpVerification.aspx",
        "OtpVerification.aspx.vb",
        "OtpVerification.aspx.designer.vb",
        "Login_J.aspx.vb"
    )
    
    $basePath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\"
    $missingFiles = @()
    
    foreach ($file in $requiredFiles) {
        $fullPath = Join-Path $basePath $file
        if (-not (Test-Path $fullPath)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -eq 0) {
        Write-Host "   ✓ All required application files found (including Login_J.aspx.vb)" -ForegroundColor Green
        $global:passedTests++
        return $true
    } else {
        Write-Host "   ❌ Missing files: $($missingFiles -join ', ')" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-ConfigurationSettings {
    Write-Host "6. Testing Configuration Settings..." -ForegroundColor Yellow
    $global:totalTests++
    
    try {
        $webConfigPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Web.config"
        
        if (-not (Test-Path $webConfigPath)) {
            Write-Host "   ❌ Web.config not found" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
        
        [xml]$webConfig = Get-Content $webConfigPath
        $appSettings = $webConfig.configuration.appSettings.add
        
        $requiredSettings = @('EmailServiceUrl', 'EmailServiceEnabled', 'OtpEnabled', 'SecureAuthenticationEnabled')
        $foundSettings = @()
        
        foreach ($setting in $appSettings) {
            if ($setting.key -in $requiredSettings) {
                $foundSettings += $setting.key
            }
        }
        
        $missingSettings = $requiredSettings | Where-Object { $_ -notin $foundSettings }
        
        if ($missingSettings.Count -eq 0) {
            Write-Host "   ✓ All required configuration settings found" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ Missing settings: $($missingSettings -join ', ')" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    }
    catch {
        Write-Host "   ❌ Configuration validation failed: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-PasswordMigrationFunction {
    Write-Host "7. Testing Password Migration Function..." -ForegroundColor Yellow
    $global:totalTests++
    
    try {
        # Check if test user exists and can be used for migration test
        $connectionString = "Server=$DatabaseServer;Database=$DatabaseName;Integrated Security=true"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        # Look for a test user or create one
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT COUNT(*) FROM kj_pengguna WHERE id_pg = 'test_kolej'"
        $testUserExists = $command.ExecuteScalar()
        
        if ($testUserExists -eq 0) {
            # Create test user
            $command.CommandText = @"
                INSERT INTO kj_pengguna (id_pg, pwd, nama, email, status, id_kolej, pwd_encrypted, pwd_migrated)
                VALUES ('test_kolej', 'test123', 'Test User Kolej', '<EMAIL>', 1, 1, 0, 0)
"@
            $command.ExecuteNonQuery()
            Write-Host "   ✓ Created test user for migration testing" -ForegroundColor Green
        }
        
        # Verify test user has plain text password
        $command.CommandText = "SELECT pwd, pwd_encrypted FROM kj_pengguna WHERE id_pg = 'test_kolej'"
        $reader = $command.ExecuteReader()
        
        if ($reader.Read()) {
            $pwd = $reader["pwd"]
            $isEncrypted = $reader["pwd_encrypted"]
            $reader.Close()
            
            if ($pwd -eq 'test123' -and $isEncrypted -eq $false) {
                Write-Host "   ✓ Test user ready for password migration" -ForegroundColor Green
                $global:passedTests++
                $connection.Close()
                return $true
            } else {
                Write-Host "   ✓ Test user already migrated (pwd_encrypted=$isEncrypted)" -ForegroundColor Green
                $global:passedTests++
                $connection.Close()
                return $true
            }
        } else {
            Write-Host "   ❌ Test user not found" -ForegroundColor Red
            $global:failedTests++
            $connection.Close()
            return $false
        }
    }
    catch {
        Write-Host "   ❌ Password migration test failed: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

# =============================================================================
# EXECUTE ALL TESTS
# =============================================================================

Write-Host "Starting validation tests..." -ForegroundColor Cyan
Write-Host ""

$dbConnected = Test-DatabaseConnection
if ($dbConnected) {
    Test-DatabaseSchema
    Test-EmailServiceTables
    Test-PasswordMigrationFunction
}

Test-EmailServiceConnection
Test-ApplicationFiles  
Test-ConfigurationSettings

# =============================================================================
# RESULTS SUMMARY
# =============================================================================

Write-Host ""
Write-Host "=======================================================================" -ForegroundColor Green
Write-Host "VALIDATION RESULTS SUMMARY" -ForegroundColor Green
Write-Host "=======================================================================" -ForegroundColor Green
Write-Host ""
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host ""

$successRate = [math]::Round(($passedTests / $totalTests) * 100, 2)
Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } else { "Yellow" })

if ($failedTests -eq 0) {
    Write-Host ""
    Write-Host "🎉 ALL TESTS PASSED! 🎉" -ForegroundColor Green
    Write-Host "SPMJ KOLEJ-PDSA Secure Authentication is ready for production!" -ForegroundColor Green
} elseif ($successRate -ge 80) {
    Write-Host ""
    Write-Host "⚠ MOSTLY READY" -ForegroundColor Yellow
    Write-Host "Minor issues detected. Review failed tests and resolve before production." -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "❌ CRITICAL ISSUES DETECTED" -ForegroundColor Red
    Write-Host "Significant problems found. Do not deploy to production until resolved." -ForegroundColor Red
}

Write-Host ""
Write-Host "=======================================================================" -ForegroundColor Green
Write-Host "END OF VALIDATION" -ForegroundColor Green
Write-Host "=======================================================================" -ForegroundColor Green
