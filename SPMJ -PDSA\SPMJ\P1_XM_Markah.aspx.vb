﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm8
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String, ByVal cb As DropDownList)
        Dim <PERSON>, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>3 As Integer
        If cb.SelectedValue = 1 Then Jawatan = 1 : Jawatan2 = 5 : Jawatan3 = 8
        If cb.SelectedValue = 2 Then Jawatan = 2 : Jawatan2 = 2 : Jawatan3 = 2
        If cb.SelectedValue = 3 Then Jawatan = 3 : Jawatan2 = 3 : Jawatan3 = 3
        If cb.SelectedValue = 4 Then Jawatan = 4 : Jawatan2 = 4 : Jawatan3 = 4

        Dim y As String, z, zz As Integer
        If cb.SelectedIndex < 3 Then z = 0 : y = cb.SelectedIndex Else z = 1 : zz = cb.SelectedIndex + 1 : y = zz

        Try
            cmd_Jana.Visible = False
            Dim List_Data As New DataSet
            Dim Cn As New SqlConnection(ServerId_SQL)

            'Fixing Query Multiple Result ACTIVE record status 04092015 - OSH 
            Dim SQL As String = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'A/G', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', " & _
                             "(select top 1 markah from xm_markah xmm where xmm.ag=xc.ag " & _
                             "and xmm.tahun in (select tahun from pn_xm where id_xm=xc.id_xm and status=1) " & _
                             "and xmm.siri in (select siri from pn_xm where id_xm=xc.id_xm and status=1) " & _
                             "and (left(xmm.subjek,1)-" & z & ") in (select j_xm from pn_xm where id_xm=xc.id_xm and status=1) " & _
                             "order by tahun, siri, subjek) as rt, " & _
                             "markah_sah from pelatih p left outer join " & _
                             "pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where " & _
                             "(p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & ") and xc.id_xm in (select id_xm from pn_xm where " & _
                             "j_xm = " & Cb_Kursus.SelectedValue & " and status=1) and p.status is null and " & X & " and xc.status_ulang=0 order by cast(xc.ag as integer), p.id_kolej"


            'Comment Ori 04092015 - OSH
            'Dim SQL As String = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'A/G', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xc.id_xm 'Id_XM', " & _
            '                    "(select top 1 markah from xm_markah xmm where xmm.ag=xc.ag " & _
            '                    "and xmm.tahun in (select tahun from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '                    "and xmm.siri in (select siri from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '                    "and (left(xmm.subjek,1)-" & z & ") in (select j_xm from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '                    "order by tahun, siri, subjek) as rt, " & _
            '                    "markah_sah from pelatih p left outer join " & _
            '                    "pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where " & _
            '                    "(p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & ") and xc.id_xm in (select id_xm from pn_xm where " & _
            '                    "j_xm = " & Cb_Kursus.SelectedValue & " and status=1) and " & X & " and xc.status_ulang=0 order by cast(xc.ag as integer), p.id_kolej"

            '"(select top 1 markah from xm_markah xmm where xmm.ag=xc.ag " & _
            '"and xmm.tahun in (select tahun from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '"and xmm.siri in (select siri from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '"and (left(xmm.subjek,1)-" & z & ") in (select j_xm from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '"and xmm.subjek=" & y + "1) " & _
            '"as k1_mcq, k1_meq, k1_meq2, k1_meq3, k1_meq4," & _
            '"(select top 1 markah from xm_markah xmm where xmm.ag=xc.ag " & _
            '"and xmm.tahun in (select tahun from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '"and xmm.siri in (select siri from pn_xm where id_xm=xc.id_xm and status=1) " & _
            '"and (left(xmm.subjek,1)-" & z & ") in (select j_xm from pn_xm where id_xm=xc.id_xm and status=1)" & _
            '" and xmm.subjek=" & y + "2) " & _
            '"as k2_mcq, k2_meq, k2_meq2, k2_meq3, k2_meq4, o, mb, p, " & _
            Dim List_Adp As New SqlDataAdapter(SQL, Cn)
            List_Adp.Fill(List_Data, "pelatih")
            Gd.DataSource = List_Data.Tables("pelatih")
            DataBind()
            Gd.Visible = True
            List_Data.Dispose()
            List_Adp.Dispose()
            Cn.Dispose()

        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Isi_Markah", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click        
        If Cb_Kursus.SelectedIndex < 1 Then Msg(Me, "Sila pilih Jenis Kursus!") : Cb_Kursus.Focus() : Exit Sub
        If Tx_AG1.Text = "" Then Tx_AG1.Focus() : Exit Sub
        If Tx_AG2.Text = "" Then Tx_AG2.Text = Tx_AG1.Text
        If Not IsNumeric(Tx_AG1.Text) Or Not IsNumeric(Tx_AG2.Text) Then Msg(Me, "Sila betulkan angka giliran...") : Exit Sub

        Cari("(xc.ag between " & Tx_AG1.Text & " and " & Tx_AG2.Text & ") ", Cb_Kursus)
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        Dim i As Int16
        For i = 1 To 8
            e.Row.Cells(i).HorizontalAlign = HorizontalAlign.Center
        Next
        e.Row.Cells(8).Visible = False
        For i = 6 To 8
            e.Row.Cells(i).Visible = False
        Next

        'e.Row.Cells(2).Visible = False
        'e.Row.Cells(3).Visible = False
        'e.Row.Cells(4).Visible = False
        'e.Row.Cells(5).Visible = False
        'e.Row.Cells(6).Visible = False
        'e.Row.Cells(7).Visible = False
        'e.Row.Cells(8).Visible = False           

    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        Dim i As Int16, SQL As String = ""
        Dim rt As TextBox ', k1_mcq,k1_meq, k1_meq2, k1_meq3, k1_meq4, k2_mcq, k2_meq, k2_meq2, k2_meq3, k2_meq4, o, mb, p

        Try
            For i = 0 To Gd.Rows.Count - 1
                rt = Gd.Rows.Item(i).FindControl("Tx_rt") : If rt.Text = "" Then rt.Text = "0"
                'k1_mcq = Gd.Rows.Item(i).FindControl("Tx_k1mcq") : If k1_mcq.Text = "" Then k1_mcq.Text = "0"
                'k1_meq = Gd.Rows.Item(i).FindControl("Tx_k1meq") : If k1_meq.Text = "" Then k1_meq.Text = "0"
                'k1_meq2 = Gd.Rows.Item(i).FindControl("Tx_k1meq2") : If k1_meq2.Text = "" Then k1_meq2.Text = "0"
                'k1_meq3 = Gd.Rows.Item(i).FindControl("Tx_k1meq3") : If k1_meq3.Text = "" Then k1_meq3.Text = "0"
                'k1_meq4 = Gd.Rows.Item(i).FindControl("Tx_k1meq4") : If k1_meq4.Text = "" Then k1_meq4.Text = "0"
                'k2_mcq = Gd.Rows.Item(i).FindControl("Tx_k2mcq") : If k2_mcq.Text = "" Then k2_mcq.Text = "0"
                'k2_meq = Gd.Rows.Item(i).FindControl("Tx_k2meq") : If k2_meq.Text = "" Then k2_meq.Text = "0"
                'k2_meq2 = Gd.Rows.Item(i).FindControl("Tx_k2meq2") : If k2_meq2.Text = "" Then k2_meq2.Text = "0"
                'k2_meq3 = Gd.Rows.Item(i).FindControl("Tx_k2meq3") : If k2_meq3.Text = "" Then k2_meq3.Text = "0"
                'k2_meq4 = Gd.Rows.Item(i).FindControl("Tx_k2meq4") : If k2_meq4.Text = "" Then k2_meq4.Text = "0"
                'o = Gd.Rows.Item(i).FindControl("Tx_o") : If o.Text = "" Then o.Text = "0"
                'mb = Gd.Rows.Item(i).FindControl("Tx_mb") : If mb.Text = "" Then mb.Text = "0"
                'p = Gd.Rows.Item(i).FindControl("Tx_p") : If p.Text = "" Then p.Text = "0"

                SQL += "update xm_calon set "
                If rt.Text.Trim = "T" Then

                    SQL += "Keputusan = 'T' "
                ElseIf rt.Text.Trim = "" Then
                    SQL += "rt = NULL"
                Else
                    SQL += "rt = " & CDbl(rt.Text.Trim) & " "
                End If
                ''SQL += "rt = " & CDbl(rt.Text) & ", "
                'SQL += "k1_mcq = " & CDbl(k1_mcq.Text) & ", "
                'SQL += "k1_meq = " & CDbl(k1_meq.Text) & ", "
                'SQL += "k1_meq2 = " & CDbl(k1_meq2.Text) & ", "
                'SQL += "k1_meq3 = " & CDbl(k1_meq3.Text) & ", "
                'SQL += "k1_meq4 = " & CDbl(k1_meq4.Text) & ", "
                'SQL += "k2_mcq = " & CDbl(k2_mcq.Text) & ", "
                'SQL += "k2_meq = " & CDbl(k2_meq.Text) & ", "
                'SQL += "k2_meq2 = " & CDbl(k2_meq2.Text) & ", "
                'SQL += "k2_meq3 = " & CDbl(k2_meq3.Text) & ", "
                'SQL += "k2_meq4 = " & CDbl(k2_meq4.Text) & ", "
                'SQL += "o = " & CDbl(o.Text) & ", "
                'SQL += "mb = " & CDbl(mb.Text) & ", "
                'SQL += "p = " & CDbl(p.Text) & " "
                SQL += " where nokp = '" & Gd.Rows.Item(i).Cells(4).Text & "' and id_xm = '" & Gd.Rows.Item(i).Cells(6).Text & "'; " + vbCrLf
            Next

            If SQL = "" Then Exit Sub
            'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal)

            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Session("Msg_Tajuk") = "Isi Markah Peperiksaan"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx")
            'Msg(Me, "Rekod Telah Dikemaskini...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex < 0 Then
            '    If Cb_Kursus.SelectedIndex = 2 Then
            '        e.Row.Cells(8).Text = "M/B PRAKTIKAL"
            '        Exit Sub
            '    ElseIf Cb_Kursus.SelectedIndex = 4 Then
            '        e.Row.Cells(7).Text = "M/B TEORI"
            '        e.Row.Cells(8).Text = "M/B PRAKTIKAL"
            '        Exit Sub
            '    Else
            '        e.Row.Cells(7).Text = "M/B PRAKTIKAL"
            '        e.Row.Cells(8).Text = "PRAKTIKAL"
            '        Exit Sub
            '    End If
            Exit Sub
        End If

        cmd_Jana.Visible = True
        e.Row.Cells(0).Text = e.Row.Cells(2).Text

        Dim rt As TextBox = e.Row.FindControl("Tx_rt")
        'Dim k1_mcq As TextBox = e.Row.FindControl("Tx_k1mcq")
        'Dim k1_meq As TextBox = e.Row.FindControl("Tx_k1meq")
        'Dim k1_meq2 As TextBox = e.Row.FindControl("Tx_k1meq2")
        'Dim k1_meq3 As TextBox = e.Row.FindControl("Tx_k1meq3")
        'Dim k1_meq4 As TextBox = e.Row.FindControl("Tx_k1meq4")
        'Dim k2_mcq As TextBox = e.Row.FindControl("Tx_k2mcq")
        'Dim k2_meq As TextBox = e.Row.FindControl("Tx_k2meq")
        'Dim k2_meq2 As TextBox = e.Row.FindControl("Tx_k2meq2")
        'Dim k2_meq3 As TextBox = e.Row.FindControl("Tx_k2meq3")
        'Dim k2_meq4 As TextBox = e.Row.FindControl("Tx_k2meq4")
        'Dim o As TextBox = e.Row.FindControl("Tx_o")
        'Dim mb As TextBox = e.Row.FindControl("Tx_mb")
        'Dim p As TextBox = e.Row.FindControl("Tx_p")

        If e.Row.Cells(7).Text = "&nbsp;" Then rt.ReadOnly = False Else rt.Text = e.Row.Cells(7).Text : rt.ReadOnly = True
        'If e.Row.Cells(15).Text = "&nbsp;" Then  Else k1_mcq.Text = e.Row.Cells(15).Text
        'If e.Row.Cells(16).Text = "&nbsp;" Then  Else k1_meq.Text = e.Row.Cells(16).Text
        'If e.Row.Cells(17).Text = "&nbsp;" Then  Else k1_meq2.Text = e.Row.Cells(17).Text
        'If e.Row.Cells(18).Text = "&nbsp;" Then  Else k1_meq3.Text = e.Row.Cells(18).Text
        'If e.Row.Cells(19).Text = "&nbsp;" Then  Else k1_meq4.Text = e.Row.Cells(19).Text
        'If e.Row.Cells(20).Text = "&nbsp;" Then  Else k2_mcq.Text = e.Row.Cells(20).Text
        'If e.Row.Cells(21).Text = "&nbsp;" Then  Else k2_meq.Text = e.Row.Cells(21).Text
        'If e.Row.Cells(22).Text = "&nbsp;" Then  Else k2_meq2.Text = e.Row.Cells(22).Text
        'If e.Row.Cells(23).Text = "&nbsp;" Then  Else k2_meq3.Text = e.Row.Cells(23).Text
        'If e.Row.Cells(24).Text = "&nbsp;" Then  Else k2_meq4.Text = e.Row.Cells(24).Text
        'If e.Row.Cells(25).Text = "&nbsp;" Then  Else o.Text = e.Row.Cells(25).Text
        'If e.Row.Cells(26).Text = "&nbsp;" Then  Else mb.Text = e.Row.Cells(26).Text
        'If e.Row.Cells(27).Text = "&nbsp;" Then  Else p.Text = e.Row.Cells(27).Text

        If e.Row.Cells(8).Text = "&nbsp;" Then
        ElseIf e.Row.Cells(8).Text = "1" Then
            rt.BackColor = Drawing.Color.LightYellow
            'k1_mcq.ReadOnly = True : k1_mcq.BackColor = Drawing.Color.LightYellow
            'k1_meq.ReadOnly = True : k1_meq.BackColor = Drawing.Color.LightYellow
            'k1_meq2.ReadOnly = True : k1_meq2.BackColor = Drawing.Color.LightYellow
            'k1_meq3.ReadOnly = True : k1_meq3.BackColor = Drawing.Color.LightYellow
            'k1_meq4.ReadOnly = True : k1_meq4.BackColor = Drawing.Color.LightYellow
            'k2_mcq.ReadOnly = True : k2_mcq.BackColor = Drawing.Color.LightYellow
            'k2_meq.ReadOnly = True : k2_meq.BackColor = Drawing.Color.LightYellow
            'k2_meq2.ReadOnly = True : k2_meq2.BackColor = Drawing.Color.LightYellow
            'k2_meq3.ReadOnly = True : k2_meq3.BackColor = Drawing.Color.LightYellow
            'k2_meq4.ReadOnly = True : k2_meq4.BackColor = Drawing.Color.LightYellow
            'o.ReadOnly = True : o.BackColor = Drawing.Color.LightYellow
            'mb.ReadOnly = True : mb.BackColor = Drawing.Color.LightYellow
            'p.ReadOnly = True : p.BackColor = Drawing.Color.LightYellow
        End If
    End Sub

End Class