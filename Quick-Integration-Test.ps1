# Quick Integration Test for SPMJ Email Service
param(
    [string]$EmailServiceUrl = "http://localhost:5000",
    [string]$SPMJPath = "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ"
)

Write-Host "🔍 SPMJ Email Service Integration Test" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Test 1: Email Service Health Check
Write-Host "`n1. Testing Email Service Health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$EmailServiceUrl/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✅ Email Service is running (Status: $($response.StatusCode))" -ForegroundColor Green
        $emailServiceOk = $true
    } else {
        Write-Host "   ❌ Email Service returned status: $($response.StatusCode)" -ForegroundColor Red
        $emailServiceOk = $false
    }
} catch {
    Write-Host "   ❌ Email Service is not accessible: $($_.Exception.Message)" -ForegroundColor Red
    $emailServiceOk = $false
}

# Test 2: SPMJ Build Status
Write-Host "`n2. Testing SPMJ Build Status..." -ForegroundColor Yellow
if (Test-Path "$SPMJPath\bin\SPMJ.dll") {
    Write-Host "   ✅ SPMJ.dll found - Build successful" -ForegroundColor Green
    $buildOk = $true
} else {
    Write-Host "   ❌ SPMJ.dll not found - Build may have failed" -ForegroundColor Red
    $buildOk = $false
}

# Test 3: Integration Files
Write-Host "`n3. Testing Integration Files..." -ForegroundColor Yellow
$requiredFiles = @(
    "EmailServiceClient.vb",
    "AdminPasswordManager.aspx.vb",
    "AdminPasswordManager.aspx.designer.vb",
    "OtpVerification.aspx.vb",
    "OtpVerification.aspx.designer.vb",
    "PasswordResetModern.aspx.vb",
    "PasswordResetModern.aspx.designer.vb"
)

$filesOk = $true
foreach ($file in $requiredFiles) {
    if (Test-Path "$SPMJPath\$file") {
        Write-Host "   ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file missing" -ForegroundColor Red
        $filesOk = $false
    }
}

# Test 4: Web.config Email Service Configuration
Write-Host "`n4. Testing Web.config Configuration..." -ForegroundColor Yellow
$webConfig = "$SPMJPath\Web.config"
if (Test-Path $webConfig) {
    $content = Get-Content $webConfig -Raw
    if ($content -match 'EmailServiceUrl') {
        Write-Host "   ✅ EmailServiceUrl configured in Web.config" -ForegroundColor Green
        $configOk = $true
    } else {
        Write-Host "   ❌ EmailServiceUrl not found in Web.config" -ForegroundColor Red
        $configOk = $false
    }
} else {
    Write-Host "   ❌ Web.config not found" -ForegroundColor Red
    $configOk = $false
}

# Test 5: API Endpoint Test
Write-Host "`n5. Testing Email Service API..." -ForegroundColor Yellow
try {
    $swaggerResponse = Invoke-WebRequest -Uri "$EmailServiceUrl/swagger" -UseBasicParsing -TimeoutSec 5
    if ($swaggerResponse.StatusCode -eq 200) {
        Write-Host "   ✅ Swagger documentation accessible" -ForegroundColor Green
        $apiOk = $true
    } else {
        Write-Host "   ❌ Swagger documentation not accessible" -ForegroundColor Red
        $apiOk = $false
    }
} catch {
    Write-Host "   ⚠️  Swagger documentation not accessible (service may still work)" -ForegroundColor Yellow
    $apiOk = $true # Swagger is optional
}

# Summary
Write-Host "`n📊 INTEGRATION TEST SUMMARY" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

$totalTests = 5
$passedTests = 0
if ($emailServiceOk) { $passedTests++ }
if ($buildOk) { $passedTests++ }
if ($filesOk) { $passedTests++ }
if ($configOk) { $passedTests++ }
if ($apiOk) { $passedTests++ }

Write-Host "Tests Passed: $passedTests/$totalTests" -ForegroundColor White

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 ALL TESTS PASSED! Integration is ready for production." -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor White
    Write-Host "  1. Configure SMTP settings in SPMJ.EmailService\appsettings.json" -ForegroundColor Gray
    Write-Host "  2. Run database migration script" -ForegroundColor Gray
    Write-Host "  3. Deploy to production server" -ForegroundColor Gray
} elseif ($passedTests -ge 4) {
    Write-Host "`n⚠️  MOSTLY READY - Minor issues to resolve" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ INTEGRATION ISSUES - Check failed tests above" -ForegroundColor Red
}

Write-Host "`nFor deployment guide: SPMJ.EmailService\DEPLOYMENT_GUIDE.md" -ForegroundColor Gray
