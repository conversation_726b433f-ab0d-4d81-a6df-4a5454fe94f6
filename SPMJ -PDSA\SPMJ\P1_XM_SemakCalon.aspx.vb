﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm7
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim Jawatan, Jawatan2, <PERSON>awatan3 As Integer
        Dim SQL As String = ""

        If Cb_Kursus.SelectedValue = 1 Then Jawatan = 1 : Jawatan2 = 5 : Jawatan3 = 8
        If Cb_Kursus.SelectedValue = 2 Then Jawatan = 2 : Jawatan2 = 2 : Jawatan3 = 2
        If Cb_Kursus.SelectedValue = 3 Then Jawatan = 3 : Jawatan2 = 3 : Jawatan3 = 3
        If Cb_Kursus.SelectedValue = 4 Then Jawatan = 4 : Jawatan2 = 4 : Jawatan3 = 4

        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        If Cb_Status.SelectedIndex = 0 Then
            SQL = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'PUSAT PEPERIKSAAN', stuff('0000',5-len(xm.ag),len(xm.ag),xm.ag) as 'ANGKA GILIRAN'," & _
                  "case isnull(xm.ulangan,'0') when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as 'ULANGAN' " & _
                  "from xm_calon xm left outer join pn_kolej pnk on xm.id_pusat = pnk.id_kolej inner join pelatih p on p.nokp = xm.nokp " & _
                  "where p.saring = 1 and (j_kursus = " & Jawatan & " or j_kursus=" & Jawatan2 & " or j_kursus=" & Jawatan3 & ") " & X & " and status_ulang=0 and " & _
                  "xm.id_xm = (select id_xm from pn_xm where j_xm = " & Jawatan & " and status=1) order by cast(xm.ag as integer)"
        ElseIf Cb_Status.SelectedIndex = 1 Then
            SQL = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', xmt.sebab as 'SEBAB DITOLAK' from pelatih p " & _
                  "left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon_tolak xmt on p.nokp = xmt.nokp " & _
                  "where (j_kursus = " & Jawatan & " or j_kursus=" & Jawatan2 & " or j_kursus=" & Jawatan3 & ") " & X & " order by p.nama"
        ElseIf Cb_Status.SelectedIndex = 2 Then
            SQL = "select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', case j_kursus when 1 then 'JB' when 2 then 'JM' when 3 then 'PJ' " & _
                  "when 4 then 'B1' when 5 then 'JB' end as 'KURSUS' from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej " & _
                  "where (j_kursus = " & Jawatan & " or j_kursus=" & Jawatan2 & " or j_kursus=" & Jawatan3 & ") " & X & " and p.saring = 1 and p.nokp not in (select nokp from xm_calon) " & _
                  "and p.nokp not in (select nokp from xm_calon_tolak) order by p.id_kolej, p.nama"
        End If

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)


        List_Adp.Fill(List_Data, "pelatih")
        Gd.DataSource = List_Data.Tables("pelatih")
        DataBind()
        Gd.Visible = True
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Not Akses_Pg("P1", "Jana_Calon", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Cmd.CommandText = "select id_kursus, dc_kursus from pn_kursus where xm = 1 order by id_kursus"
        'Rdr = Cmd.ExecuteReader()
        'Cb_Kursus.Items.Add("")
        'While Rdr.Read
        '    Cb_Kursus.Items.Add(Rdr(1))
        '    Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(0)
        'End While
        'Rdr.Close()

        Cmd.CommandText = "select id_kolej, dc_kolej from pn_kolej where jenis < 3 order by dc_kolej"
        'Cmd.CommandText = "select id_kolej, dc_kolej from pn_kolej where jenis < 3 order by jenis, dc_kolej"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(1))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(0)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(1).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmd_Cari.Click    
        Cari("and p.id_kolej = " & Cb_Kolej.SelectedValue)
    End Sub

    Protected Sub cmd_Cari0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari0.Click
        GridViewExportUtil.Export("Laporan.xls", Gd)
    End Sub
End Class