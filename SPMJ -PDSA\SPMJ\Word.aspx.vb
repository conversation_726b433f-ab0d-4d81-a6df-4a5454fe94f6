﻿Public Partial Class Word
    Inherits System.Web.UI.Page

    Public Sub Notis_Niat()
        'Put user code to initialize the page here
        Dim x As String
        Dim s As String = "http://localhost:4844/surat/"
        'Dim s As String = "http://************/spmj/surat/"

        x = "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word'>"
        x += "<head><style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
        x += "<style type='text/css'><!-- .indent_3x { padding-left: 400pt;padding-right: 50pt;} --></style>"
        x += "<style> "
        x += "@page Section1 {mso-footer:f1;}"
        x += "div.Section1{page:Section1;}"
        x += "p.<PERSON><PERSON><PERSON><PERSON>, li.<PERSON><PERSON><PERSON><PERSON>, div.<PERSON><PERSON><PERSON>ooter{"
        x += "mso-pagination:widow-orphan;"
        x += "tab-stops:center 216.0pt right 432.0pt;}"
        x += "</style>"
        x += "</head><body><div class='Section1'>"

        'Header

        'x += "<table width='100%' style='font-family: Arial; font-size: 9px;background-color:#cfcfcf;'><tr>"
        x += "<table width='100%' style='border: none;mso-border-bottom-alt:solid windowtext .5pt;margin-left: 0px; font-family: Arial; font-size: 8pt;'><tr>"
        x += "<td><img width=110 height=88 src='" & s & "jata.png'></img></td>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'>"
        x += "<b>LEMBAGA BIDAN MALAYSIA</b>"
        x += "<br/><i>MIDWIVES BOARD MALAYSIA</i>"
        x += "<br/><b>KEMENTERIAN KESIHATAN MALAYSIA</b>"
        x += "<br/><i>MINISTRY OF HEALTH MALAYSIA</i>"
        x += "<br/><b>Aras 3, Blok E1, Kompleks E, Presint 1</b>"
        x += "<br/><i>Level 3, Block E1, Parcel E, Precinct 1</i>"
        x += "<br/><b>Pusat Pentadbiran Kerajaan Persekutuan</b>"
        x += "<br/><i>Federal Government Administrative Centre</i>"
        x += "<br/><b>62590 Putrajaya</b></p></div></td>"
        x += "<td style='width:21.64%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'>"
        x += "<div align ='center'><img width=80 height=100 src='" & s & "ljm2.gif'></img>"
        'x += "<v:shape ><v:imagedata src='" & s & "ljm2.gif'/>"
        x += "<br/>TEL :603-88831339"
        x += "<br/>FAX :603-88831329 </div>"
        x += "</td>"
        x += "</tr></table>"
        ' <div align="center"></div>

        'Rujukan
        x += "<br/>"
        x += "<div style='margin-left:3.5in; font-family: Arial; font-size: 12px;'> Rujukan Tuan : "
        x += "<br/>Rujukan Kami : KKM 87/A3/1/158(4)"
        x += "<br/>Tarikh : " + Now.ToShortDateString
        x += "</div>"

        'Penerima
        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12px;'>(Penerima)"
        x += "</div>"

        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 12px;'>Tuan/Puan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12px;'><b>PERMOHONAN NOTIS MENGENAI NIAT UNTUK MENJALANKAN AMALAN ATAU TERUS MENJALANKAN AMALAN KEBIDANAN MENGIKUT AKTA BIDAN 1966 PINDAAN 1990"
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12px;'>Dengan segala hormatnya merujuk perkara di atas dan surat Tuan bertarikh (tarikh) adalah berkaitan."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12px;'>"
        x += "2. &nbsp; &nbsp; &nbsp; &nbsp; Sukacita dimaklumkan Lembaga Bidan Malaysia telah <b> meluluskan </b> Pengamalan Kebidanan bagi tempoh (tarikh mula) hingga (tarikh tamat) untuk Pegawai berikut :-"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12px;'>"
        x += "&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <b>Nama Jururawat&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp;   No. Pendaftaran &nbsp; &nbsp; &nbsp; &nbsp;   Kebidanan Bahagian :</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12px;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12px;'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12px;'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"


        x += "<br>"
        x += "<br>"
        x += "</br>"


        x += "<br/><div style='font-family: Arial; font-size: 12px;'>"
        x += "<b> (HJH. FATHILAH BINTI HJ.ABD. WAHAB) </b>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."

        x += "<br/><div style='font-family: Arial; font-size: 12px;'>"
        x += "<br> s.k Cawangan Kawalan Amalan Perubatan Swasta"
        x += "<br>Kementerian Kesihatan Malaysia"
        x += "<br>Putrajaya."
        x += "</div>"

        x += "<br clear=all style='page-break-before:always'>"
        x += "<br clear=all style='page-break-before:always'>"
        x += "<div style='mso-element:footer' id='f1'>"
        'x += "<p class='MsoFooter'>"
        'x += "<span><img src='" & s & "img1.png'><img src='" & s & "img2.png'><img src='" & s & "img3.jpg'><img src='" & s & "img4.png'></span></p>"
        x += "<p class=MsoFooter style='border:none;mso-border-top-alt:solid windowtext 0.5pt;"
        x += "padding:0in;mso-padding-alt:0in 0in 1.0pt 0in'><span style='mso-no-proof:yes'>"
        x += " <!--[if gte vml 1]>"
        x += "<v:shape style='position:absolute;left:0;text-align:left;margin-left:315pt;margin-top:3.75pt;width:48.6pt;"
        x += " height:44.2pt;z-index:-2' wrapcoords='-257 0 -257 21300 21600 21300 21600 0 -257 0'>"
        x += " <v:imagedata src='" & s & "imgd.png' o:title=''/>"
        x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2049' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:178.2pt;"
        x += " margin-top:5.75pt;width:55.2pt;height:44.2pt;z-index:-4' wrapcoords='-237 0 -237 21375 21600 21375 21600 0 -237 0'"
        x += " o:allowoverlap='f'>"
        x += " <v:imagedata src='" & s & "imgb.png' o:title=''/>"
        x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2050' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:251.4pt;"
        x += " margin-top:7.35pt;width:49.2pt;height:40.6pt;z-index:-3' wrapcoords='-304 0 -304 21375 21600 21375 21600 0 -304 0'>"
        x += " <v:imagedata src='" & s & "imgc.gif' o:title=''/>"
        x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2052' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:97.8pt;"
        x += " margin-top:7.35pt;width:60pt;height:44.2pt;z-index:-1' wrapcoords='-225 0 -225 21373 21600 21373 21600 0 -225 0'"
        x += " o:allowoverlap='f'>"
        x += " <v:imagedata src='" & s & "imga.png' o:title=''/>"
        x += "</v:shape><![endif]--></span></p>"
        x += "</div>"
        x += "</div>"
        x += "</body>"
        x += "</html>"

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Notis_Niat()
        'Exit Sub

        'Dim x As String

        'x = "<html><head>"
        ''x += "<style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
        'x += "<style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
        'x += "<style type='text/css'><!-- .indent_3x { padding-left: 400pt;padding-right: 50pt;} --></style>"
        'x += "</head><body>"

        ''x += "<div>Your name is: <b>" + txtName.Text + "</b></div>"

        ''Header

        ''x += "<table width='100%' style='font-family: Arial; font-size: 9px;background-color:#cfcfcf;'><tr>"
        'x += "<table width='100%' style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'><tr>"
        'x += "<td width='20%' style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'></td>"
        'x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'>LEMBAGA JURURAWAT MALAYSIA"
        'x += "<br/><i>NURSING BOARD MALAYSIA</i>"
        'x += "<br/>KEMENTERIAN KESIHATAN MALAYSIA"
        'x += "<br/><i>MINISTRY OF HEALTH MALAYSIA</i>"
        'x += "<br/>ARAS 3, BLOK E1, KOMPLEKS E, PRESINT 1"
        'x += "<br/><i>LEVEL 3, BLOCK E1, PARCEL E, PRECINCT 1</i>"
        'x += "<br/>PUSAT PENTADBIRAN KERAJAAN PERSEKUTUAN"
        'x += "<br/><i>FEDERAL GOVERNMENT ADMINISTRATIVE CENTRE</i>"
        'x += "<br/>62590 PUTRAJAYA</p></div>"
        'x += "</td>"
        'x += "</tr></table><hr/>"

        ''Rujukan
        'x += "<br/>"
        'x += "<div class='indent_3x' style='font-family: Arial; font-size: 10px;'>Rujukan Kami : KKM 87/A3/1/158(4)"
        'x += "<br/>Tarikh : " + Now
        'x += "</div>"

        ''Penerima
        'x += "<br/>"
        'x += "<div style='font-family: Arial; font-size: 10px;'>(Penerima)"
        'x += "</div>"

        'x += "<br/>"
        'x += "<div  style='font-family: Arial; font-size: 10px;'>Tuan/Puan,"
        'x += "</div>"

        'x += "<br/>"
        'x += "<div style='font-family: Arial; font-size: 10px;'><b>KEPUTUSAN MESYUARAT PENGAMBILAN PERKHIDMATAN JURURAWAT TERLATIH WARGANEGARA ASING"
        'x += "</b></div>"

        'x += "<br/>"
        'x += "<div style='font-family: Arial; font-size: 10px;'>Dengan segala hormatnya merujuk perkara di atas."
        'x += "</div>"

        'x += "<br/><div>"
        'x += "2. Dengan ini dimaklumkan bahawa mesyuarat jawatankuasa kelulusan pengambilan jururawat terlatih warganegara asing telah diadakan pada (tarikh mesyuarat) dan mesyuarat tersebut telah menetapkan keputusan seperti di lampiran 1."
        'x += "<b>Jururawat terlatih warganegara asing yang telah diluluskan perlu memohon Perakuan Pengamalan Sementara (TPC) dari Lembaga Jururawat Malaysia melalui majikan sebelum memulakan perkhidmatan.</b>"
        'x += "</div>"

        ''strBody += "Ms Word document generated successfully."

        'x += "</body></html>"

        ''Response.Write(x)
        ''Exit Sub

        'Dim fileName As String = "Surat.doc"
        '' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        'Response.AppendHeader("Content-Type", "application/msword")
        'Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        'Response.Write(x)

    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button1.Click
        Notis_Niat()
    End Sub
End Class