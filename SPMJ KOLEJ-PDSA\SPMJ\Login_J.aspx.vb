﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.Text
Imports System.Configuration
Imports System.Collections.Generic

Partial Public Class Login_J
    Inherits System.Web.UI.Page
    
    ' Email service integration for KOLEJ-PDSA
    Private ReadOnly emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl") 
    Private emailClient As EmailServiceClient
    Function Blank(ByVal Tx As TextBox)
        Blank = False
        If Tx.Text.Trim = "" Then Blank = True
    End Function
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Initialize email service client for KOLEJ-PDSA secure authentication (always, not just on first load)
        Try
            If Not String.IsNullOrEmpty(emailServiceUrl) Then
                emailClient = New EmailServiceClient(emailServiceUrl)
                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Email service client initialized successfully")
            End If
        Catch ex As Exception
            ' Email service not available - continue without it
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Email service not available: " & ex.Message)
        End Try

        If IsPostBack Then Exit Sub

        Dim x As Menu = Master.FindControl("Menu1")
        x.Visible = False

        Dim x1 As String = ""
        Dim Fmt_Tkh = "<a style='font-family: Arial; font-size: 8pt; color: #CC0000; font-weight: bolder'>"
        Dim Fmt_By = "</a><br/><a style='font-family: Arial; font-size: 8pt; color: #006699;'>"
        Dim Fmt_Note = "</a><br/><a style='font-family: Arial; font-size: 8pt; color: #000000;'>"
        Dim Fmt_End = "</a><br/><br/>"

        'x1 += Fmt_Tkh
        'x1 += "[ Wed 13/8/2009 5:39PM ]"
        'x1 += Fmt_By
        'x1 += "UNIT PEPERIKSAAN:"
        'x1 += Fmt_Note
        'x1 += "Sila pastikan anda telah mendaftar kesemua pelatih yang akan mengambil tempat dalam Peperiksaan Akhir Kebidanan Bahagian I. Tarikh tutup adalah pada 31/12/2010."
        'x1 += Fmt_End

        'x1 += Fmt_Tkh
        'x1 += "[ Fri 31/12/2009 12:45PM ]"
        'x1 += Fmt_By
        'x1 += "SYSTEM ADMIN:"
        'x1 += Fmt_Note
        'x1 += "Sebarang pertanyaan berhubung penggunaan sistem ini, sila hubungi:<br/>"
        'x1 += "BAHAGIAN KEJURURAWATAN<br/>"
        'x1 += "KEMENTERIAN KESIHATAN MALAYSIA<br/>"
        'x1 += "Tel : 03-8883 0000<br/>"
        'x1 += Fmt_End


        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select * from kj_mesej where aktif = '1' order by TKH_MESEJ desc"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            x1 += Fmt_Tkh
            x1 += "[ " & Format(Rdr(0)) & " ]"
            x1 += Fmt_By
            x1 += Rdr(1)
            x1 += Fmt_Note
            x1 += Rdr(2)
            x1 += Fmt_End
        End While
        Rdr.Close()
        Cn.Close()

        Label1.Text = x1

        ' Wire up the event handlers programmatically
        AddHandler lnk_ForgotPassword.Click, AddressOf lnk_ForgotPassword_Click
        AddHandler btn_SendRecovery.Click, AddressOf btn_SendRecovery_Click
        AddHandler btn_CancelRecovery.Click, AddressOf btn_CancelRecovery_Click
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button1.Click
        If Blank(Tx_Id) Then LbL.Text = "Sila Isi Kod Pengguna!" : Tx_Id.Focus() : Exit Sub
        If Blank(Tx_Pwd) Then LbL.Text = "Sila Isi Kata Laluan!" : Tx_Pwd.Focus() : Exit Sub

        If Chk_SQL(Tx_Id.Text) = True Then Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub
        If Chk_SQL(Tx_Pwd.Text) = True Then Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub

        ' KOLEJ-PDSA Secure authentication with password hashing and OTP integration
        Try
            If AuthenticateUserSecureKolej(Tx_Id.Text.Trim(), Tx_Pwd.Text) Then
                ' Authentication successful - redirect to main system
                Response.Redirect("blank.aspx")
            Else
                LbL.Text = "Kesalahan pada Kod Pengguna/Kata Laluan!"
                LogFailedLoginAttemptKolej(Tx_Id.Text.Trim())
            End If
        Catch ex As Exception
            LbL.Text = "Ralat sistem berlaku. Sila cuba lagi."
            LogErrorKolej("Login_J_Button1_Click", ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' KOLEJ-PDSA Secure user authentication with password hashing support
    ''' Supports both legacy plain text and new encrypted passwords
    ''' Includes automatic migration and OTP integration for college system
    ''' </summary>    

    Private Function AuthenticateUserSecureKolej(userId As String, password As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing
        
        ' Declare variables at function scope for proper accessibility
        Dim passwordMatch As Boolean = False
        Dim storedPassword As String = ""
        Dim salt As String = ""
        Dim email As String = ""
        Dim isEncrypted As Boolean = False
        Dim currentIdKolej As String = ""
        Dim currentDcKolej As String = ""

        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()

            ' KOLEJ-PDSA specific query with enhanced security check
            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT kjp.Id_PG, kjp.PWD, kjp.salt, kjp.email, kjp.STATUS, kjp.pwd_encrypted, kjp.failed_login_attempts, kjp.account_locked, pnk.Id_KOLEJ, pnk.dc_kolej " &
                                "FROM kj_pengguna kjp INNER JOIN pn_kolej pnk ON kjp.Id_KOLEJ = pnk.Id_KOLEJ " &
                                "WHERE kjp.Id_PG = ? AND kjp.STATUS = 1"
            command.Parameters.AddWithValue("@Id_PG", userId)

            reader = command.ExecuteReader()

            If reader.Read() Then
                ' Read all data from database
                storedPassword = GetSafeStringValueKolej(reader, "PWD")
                salt = GetSafeStringValueKolej(reader, "salt")
                email = GetSafeStringValueKolej(reader, "email")
                isEncrypted = GetSafeBoolValueKolej(reader, "pwd_encrypted")
                Dim failedAttempts As Integer = GetSafeIntValueKolej(reader, "failed_login_attempts")
                Dim isLocked As Boolean = GetSafeBoolValueKolej(reader, "account_locked")
                currentIdKolej = GetSafeStringValueKolej(reader, "Id_KOLEJ")
                currentDcKolej = GetSafeStringValueKolej(reader, "dc_kolej")

                ' Check if account is locked
                If isLocked Then
                    LbL.Text = "Akaun telah dikunci. Sila hubungi pentadbir sistem."
                    Return False
                End If

                ' Check failed login attempts (lock after 5 attempts for KOLEJ)
                If failedAttempts >= 5 Then
                    LockUserAccountKolej(userId, connection)
                    LbL.Text = "Akaun telah dikunci kerana terlalu banyak percubaan log masuk yang gagal."
                    Return False
                End If

                ' Close reader before further operations
                reader.Close()

                ' Check password based on encryption status
                If isEncrypted AndAlso Not String.IsNullOrEmpty(salt) Then
                    ' Use enhanced hash verification for KOLEJ-PDSA (handles varchar(15) PWD workaround)
                    passwordMatch = PasswordHelper.VerifyPasswordWorkaround(password, storedPassword, salt)
                Else
                    ' Legacy plain text check
                    passwordMatch = (storedPassword = password)
                End If

                ' Handle successful authentication
                If passwordMatch Then
                    ' Reset failed login attempts on successful login
                    ResetFailedLoginAttemptsKolej(userId, connection)

                    ' For encrypted passwords, check OTP verification requirement
                    If isEncrypted Then
                        ' For encrypted passwords, ALWAYS trigger OTP verification
                        If Not String.IsNullOrEmpty(email) AndAlso emailClient IsNot Nothing Then
                            Try
                                ' Check if email service is available
                                If emailClient.CheckHealth() Then
                                    ' Store login session temporarily and redirect to OTP verification
                                    Session("TEMP_USER_ID") = userId
                                    Session("TEMP_ID_KOLEJ") = currentIdKolej
                                    Session("TEMP_DC_KOLEJ") = currentDcKolej
                                    Session("TEMP_ORIGIN") = "kolej"
                                    Session("TEMP_SYSTEM_TYPE") = "KOLEJ-PDSA"
                                    Session("TEMP_EMAIL") = email

                                    ' Generate OTP for KOLEJ system with encrypted password
                                    Dim otpResponse = emailClient.GenerateOTP(userId, email, "LOGIN_KOLEJ_SECURE")
                                    If otpResponse.Success Then
                                        ' Redirect to OTP verification page
                                        Response.Redirect("OtpVerification.aspx")
                                        Return False ' Don't complete login yet - wait for OTP
                                    Else
                                        ' OTP generation failed - allow direct login as fallback
                                        LogErrorKolej("OTP_Generation_Failed_Encrypted", otpResponse.Message)
                                    End If
                                End If
                            Catch ex As Exception
                                ' Email service not available - allow direct login as fallback
                                LogErrorKolej("OTP_Service_Unavailable_Encrypted", ex.Message)
                            End Try
                        End If

                        ' If OTP not available or failed, complete login directly
                        ' Set permanent session variables for KOLEJ-PDSA                        
                        Session("Id_PG") = userId
                        Session("PWD") = "" ' Don't store password in session
                        Session("Id_KOLEJ") = currentIdKolej
                        Session("Dc_KOLEJ") = currentDcKolej
                        Session("ORIGIN") = "kolej"
                        Session("SYSTEM_TYPE") = "KOLEJ-PDSA"

                        ' Log successful login for KOLEJ-PDSA
                        LogSuccessfulLoginKolej(userId)
                        Return True
                    Else
                        ' Plain text password - FORCE PASSWORD CHANGE for KOLEJ
                        ' Store user data for force password change
                        Session("FORCE_PASSWORD_CHANGE") = True
                        Session("TEMP_USER_ID") = userId
                        Session("TEMP_ID_KOLEJ") = currentIdKolej
                        Session("TEMP_DC_KOLEJ") = currentDcKolej
                        Session("TEMP_ORIGIN") = "kolej"
                        Session("TEMP_SYSTEM_TYPE") = "KOLEJ-PDSA"
                        Session("TEMP_OLD_PASSWORD") = password

                        ' Redirect to force password change page
                        Response.Redirect("ForcePasswordChange.aspx")
                        Return False ' Don't complete login yet
                    End If
                Else
                    ' Password authentication failed
                    ' Increment failed login attempts
                    IncrementFailedLoginAttemptsKolej(userId, connection)
                    Return False
                End If
            Else
                ' User not found in database
                Return False
            End If

        Catch ex As Exception
            LogErrorKolej("AuthenticateUserSecureKolej", ex.Message)
            Return False
        Finally
            If reader IsNot Nothing AndAlso Not reader.IsClosed Then reader.Close()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
        End Try
    End Function

#Region "KOLEJ-PDSA Security Helper Methods"

    ''' <summary>
    ''' Migrate user password from plain text to encrypted hash for KOLEJ-PDSA
    ''' </summary>
    Private Sub MigrateUserPasswordSecureKolej(userId As String, plainPassword As String, connection As OleDbConnection)
        Try
            ' Check if database supports enhanced security columns
            If PasswordHelper.SupportsEnhancedSecurity(connection) Then
                PasswordHelper.MigrateUserPassword(userId, plainPassword, connection)
            Else
                ' Basic migration - just hash the password for KOLEJ
                Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(plainPassword)
                Dim hashedPassword As String = passwordEntry(0)
                
                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "UPDATE kj_pengguna SET PWD = ? WHERE Id_PG = ?"
                    command.Parameters.AddWithValue("@PWD", hashedPassword)
                    command.Parameters.AddWithValue("@Id_PG", userId)
                    command.ExecuteNonQuery()
                End Using
            End If
        Catch ex As Exception
            LogErrorKolej("MigrateUserPasswordSecureKolej", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Lock user account due to failed login attempts (KOLEJ-PDSA specific)
    ''' </summary>
    Private Sub LockUserAccountKolej(userId As String, connection As OleDbConnection)
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET account_locked = 1, locked_date = ? WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@locked_date", DateTime.Now)
                command.Parameters.AddWithValue("@Id_PG", userId)
                command.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            LogErrorKolej("LockUserAccountKolej", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Reset failed login attempts counter (KOLEJ-PDSA specific)
    ''' </summary>
    Private Sub ResetFailedLoginAttemptsKolej(userId As String, connection As OleDbConnection)
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET failed_login_attempts = 0, last_successful_login = ? WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@last_successful_login", DateTime.Now)
                command.Parameters.AddWithValue("@Id_PG", userId)
                command.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            LogErrorKolej("ResetFailedLoginAttemptsKolej", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Increment failed login attempts counter (KOLEJ-PDSA specific)
    ''' </summary>
    Private Sub IncrementFailedLoginAttemptsKolej(userId As String, connection As OleDbConnection)
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET failed_login_attempts = ISNULL(failed_login_attempts, 0) + 1, last_failed_login = ? WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@last_failed_login", DateTime.Now)
                command.Parameters.AddWithValue("@Id_PG", userId)
                command.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            LogErrorKolej("IncrementFailedLoginAttemptsKolej", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Log successful login for audit trail (KOLEJ-PDSA specific)
    ''' </summary>
    Private Sub LogSuccessfulLoginKolej(userId As String)
        Try
            System.Diagnostics.Debug.WriteLine($"KOLEJ-PDSA Successful login: {userId} at {DateTime.Now}")
            ' Additional logging can be implemented here for KOLEJ system
        Catch ex As Exception
            LogErrorKolej("LogSuccessfulLoginKolej", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Log failed login attempt for security monitoring (KOLEJ-PDSA specific)
    ''' </summary>
    Private Sub LogFailedLoginAttemptKolej(userId As String)
        Try
            System.Diagnostics.Debug.WriteLine($"KOLEJ-PDSA Failed login attempt: {userId} at {DateTime.Now}")
            ' Additional logging can be implemented here for KOLEJ system
        Catch ex As Exception
            LogErrorKolej("LogFailedLoginAttemptKolej", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Log error for debugging and monitoring (KOLEJ-PDSA specific)
    ''' </summary>
    Private Sub LogErrorKolej(functionName As String, errorMessage As String)
        Try
            System.Diagnostics.Debug.WriteLine($"KOLEJ-PDSA Error in {functionName}: {errorMessage} at {DateTime.Now}")
            ' Additional error logging can be implemented here for KOLEJ system
        Catch
            ' Suppress errors in logging to prevent infinite loops
        End Try
    End Sub

#End Region

#Region "KOLEJ-PDSA Database Helper Methods"

    ''' <summary>
    ''' Safely get string value from database reader (KOLEJ-PDSA specific)
    ''' </summary>
    Private Function GetSafeStringValueKolej(reader As OleDbDataReader, columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return ""
            Else
                Return reader(columnName).ToString()
            End If
        Catch
            Return ""
        End Try
    End Function
    
    ''' <summary>
    ''' Safely get boolean value from database reader (KOLEJ-PDSA specific)
    ''' </summary>
    Private Function GetSafeBoolValueKolej(reader As OleDbDataReader, columnName As String) As Boolean
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return False
            Else
                Return Convert.ToBoolean(reader(columnName))
            End If
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Safely get integer value from database reader (KOLEJ-PDSA specific)
    ''' </summary>
    Private Function GetSafeIntValueKolej(reader As OleDbDataReader, columnName As String) As Integer
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return 0
            Else
                Return Convert.ToInt32(reader(columnName))
            End If
        Catch
            Return 0
        End Try
    End Function

#End Region

#Region "KOLEJ-PDSA Password Recovery Methods"

    ''' <summary>
    ''' Handle forgot password link click - Toggle password recovery panel visibility
    ''' </summary>
    Protected Sub lnk_ForgotPassword_Click(sender As Object, e As EventArgs) Handles lnk_ForgotPassword.Click
        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Forgot password link clicked")

        ' Toggle the password recovery panel visibility
        If pnl_PasswordRecovery.Visible Then
            ' Hide the password recovery panel
            pnl_PasswordRecovery.Visible = False
            lbl_RecoveryMessage.Text = ""
            txt_RecoveryUserId.Text = ""
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Recovery panel hidden")
        Else
            ' Show the password recovery panel
            pnl_PasswordRecovery.Visible = True
            lbl_RecoveryMessage.Text = ""
            txt_RecoveryUserId.Text = ""
            txt_RecoveryUserId.Focus()
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Recovery panel displayed")
        End If
    End Sub

    ''' <summary>
    ''' Handle password recovery send button click
    ''' </summary>
    Protected Sub btn_SendRecovery_Click(sender As Object, e As EventArgs) Handles btn_SendRecovery.Click
        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Send recovery button clicked")

        If String.IsNullOrEmpty(txt_RecoveryUserId.Text.Trim()) Then
            lbl_RecoveryMessage.Text = "<span style='color: red;'>Sila masukkan ID Pengguna.</span>"
            txt_RecoveryUserId.Focus()
            Return
        End If

        ' Check for SQL injection
        If Chk_SQL(txt_RecoveryUserId.Text) Then
            lbl_RecoveryMessage.Text = "<span style='color: red;'>ID Pengguna tidak sah.</span>"
            Return
        End If

        Try
            ' Get user information from database
            Dim userEmail As String = GetUserEmailForRecovery(txt_RecoveryUserId.Text.Trim())

            If String.IsNullOrEmpty(userEmail) Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>ID Pengguna tidak dijumpai atau tiada alamat email.</span>"
                Return
            End If

            ' Check if email service is available
            If emailClient Is Nothing Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Perkhidmatan email tidak tersedia pada masa ini.</span>"
                Return
            End If

            ' Check email service health
            Dim healthStatus As String = emailClient.CheckHealth()
            If Not healthStatus.Equals("healthy") Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Perkhidmatan email tidak tersedia pada masa ini.</span>"
                Return
            End If

            ' Generate temporary password
            Dim temporaryPassword As String = emailClient.GenerateTemporaryPassword(8)

            ' Send password recovery email
            Dim response As EmailServiceResponse = emailClient.SendPasswordRecoveryEmail(txt_RecoveryUserId.Text.Trim(), userEmail, temporaryPassword)

            If response.Success Then
                ' Update database with temporary password (encrypted)
                Try
                    Dim encryptedTempPassword As String = EncryptPassword(temporaryPassword)
                    Dim updateQuery As String = "UPDATE kj_pengguna SET kata_laluan = @password, force_password_change = 1, temp_password_expiry = @expiry WHERE id_pengguna = @userId"

                    Using updateCmd As New SqlCommand(updateQuery, conn)
                        updateCmd.Parameters.AddWithValue("@password", encryptedTempPassword)
                        updateCmd.Parameters.AddWithValue("@expiry", DateTime.Now.AddHours(24)) ' Temporary password expires in 24 hours
                        updateCmd.Parameters.AddWithValue("@userId", txt_RecoveryUserId.Text.Trim())

                        Dim rowsAffected As Integer = updateCmd.ExecuteNonQuery()
                        If rowsAffected > 0 Then
                            ' Show success message with full email address
                            lbl_RecoveryMessage.Text = "<span style='color: green;'>Kata laluan sementara telah dihantar ke email anda: " & userEmail & "</span>"
                            txt_RecoveryUserId.Text = ""
                            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Email sent and database updated successfully for: " & userEmail)
                        Else
                            lbl_RecoveryMessage.Text = "<span style='color: red;'>Gagal mengemaskini kata laluan dalam pangkalan data.</span>"
                            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Database update failed for user: " & txt_RecoveryUserId.Text.Trim())
                        End If
                    End Using
                Catch dbEx As Exception
                    lbl_RecoveryMessage.Text = "<span style='color: red;'>Gagal mengemaskini kata laluan: " & dbEx.Message & "</span>"
                    System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Database error: " & dbEx.Message)
                End Try
            Else
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Gagal menghantar email: " & response.Message & "</span>"
                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Email sending failed: " & response.Message)
            End If

        Catch ex As Exception
            lbl_RecoveryMessage.Text = "<span style='color: red;'>Ralat sistem: " & ex.Message & "</span>"
            LogErrorKolej("btn_SendRecovery_Click", ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Handle password recovery cancel button click
    ''' </summary>
    Protected Sub btn_CancelRecovery_Click(sender As Object, e As EventArgs) Handles btn_CancelRecovery.Click
        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Cancel recovery button clicked")

        ' Hide the password recovery panel
        pnl_PasswordRecovery.Visible = False
        lbl_RecoveryMessage.Text = ""
        txt_RecoveryUserId.Text = ""

        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA PASSWORD RECOVERY: Recovery panel hidden")
    End Sub

    ''' <summary>
    ''' Get user email for password recovery (KOLEJ-PDSA specific)
    ''' </summary>
    Private Function GetUserEmailForRecovery(userId As String) As String
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing

        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()

            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT email FROM kj_pengguna WHERE Id_PG = ? AND STATUS = 1"
            command.Parameters.AddWithValue("@Id_PG", userId)

            reader = command.ExecuteReader()

            If reader.Read() AndAlso Not reader.IsDBNull(0) Then
                Dim email As String = reader("email").ToString().Trim()
                If Not String.IsNullOrEmpty(email) AndAlso email.Contains("@") Then
                    Return email
                End If
            End If

            Return ""

        Catch ex As Exception
            LogErrorKolej("GetUserEmailForRecovery", ex.Message)
            Return ""
        Finally
            If reader IsNot Nothing AndAlso Not reader.IsClosed Then reader.Close()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
        End Try
    End Function

#End Region

End Class
