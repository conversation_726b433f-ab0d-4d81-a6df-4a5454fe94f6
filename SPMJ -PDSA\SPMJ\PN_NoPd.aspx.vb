﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm54
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Fn_Table()

        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)        
        'SQL = "select 'JURURAWAT BERDAFTAR' as 'JENIS PENDAFTARAN', jb as 'NO. PENDAFTARAN TERAKHIR' from pn_nopd union "
        'SQL += "select 'JURURAWAT MASYARAKAT', jm from pn_nopd union "
        'SQL += "select 'PENOLONG JURURAWAT', pj from pn_nopd union "
        'SQL += "select 'KEBIDANAN I', b1 from pn_nopd union "
        'SQL += "select 'KESIHATAN JIWA', kj from pn_nopd union "
        'SQL += "select 'KESIHATAN UMUM', ku from pn_nopd union "
        'SQL += "select 'TPC', tpc from pn_nopd"
        SQL = "select j_daftar as 'JENIS PENDAFTARAN', nopd as 'NO. PENDAFTARAN TERAKHIR' from tmp_nopd "
        Tb = "pn_nopd"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged

    End Sub

    Public Sub Fn_Table()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        SQL = "delete from tmp_nopd; "
        SQL += "declare @id1 integer "
        SQL += "set @id1 = (select top 1 nopd from JT_PENUH  where J_Daftar=1 order by NoPd desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('JURURAWAT BERDAFTAR', @id1 ); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd from JT_PENUH  where J_Daftar=2 order by NoPd desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('JURURAWAT MASYARAKAT', @id1 ); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd from JT_PENUH  where J_Daftar=3 order by NoPd desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('PENOLONG JURURAWAT', @id1 ); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd_b1 from JT_PENUH  where J_Daftar=1 order by nopd_b1 desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('KEBIDANAN 1', @id1); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd_kj from JT_PENUH  where J_Daftar=1 order by nopd_kj desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('KESIHATAN JIWA', @id1); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd_ku from JT_PENUH  where J_Daftar=1 order by nopd_ku desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('KESIHATAN UMUM', @id1); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd from JT_TPC  where J_Daftar=1 order by NoPd desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('JURURAWAT TERLATIH', @id1); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd from JT_TPC  where J_Daftar=2 order by NoPd desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('LATIHAN ELEKTIF', @id1); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd from JT_TPC  where J_Daftar=3 order by NoPd desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('PENGAJAR JURURAWAT', @id1); " + vbCrLf
        SQL += "set @id1 = (select top 1 nopd from JT_TPC  where J_Daftar=4 order by NoPd desc) "
        SQL += "insert into tmp_nopd (j_daftar, nopd) values ('INSTRUKTOR KLINIKAL', @id1); " + vbCrLf

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()
    End Sub
End Class