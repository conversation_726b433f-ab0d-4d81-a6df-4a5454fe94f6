﻿Imports System.IO
Imports System.Data
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Text
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports iTextSharp.text.html
Imports iTextSharp.text.html.simpleparser

Partial Public Class PNR_Calon_G
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub
    Private Function GetData(ByVal cmd As SqlCommand) As DataTable
        Dim dt As New DataTable()
        Dim con As New SqlConnection(ServerId_SQL)
        Dim sda As New SqlDataAdapter()
        cmd.CommandType = CommandType.Text
        cmd.Connection = con
        Try
            con.Open()
            sda.SelectCommand = cmd
            sda.Fill(dt)
            Return dt
        Catch ex As Exception
            Throw ex
        Finally
            con.Close()
            sda.Dispose()
            con.Dispose()
        End Try
    End Function
    Protected Sub cmdPDF_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdPDF.Click

        'Get the data from database into datatable 
        'Dim strQuery As String = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as ag, nama, p.nokp, p.j_kursus, pnk.dc_kolej, xc.keputusan, px.tahun from xm_calon xc inner join pn_xm px on xc.id_xm=px.id_xm inner join pelatih p  on xc.nokp=p.nokp  and (case when p.j_kursus in (select id_kursus  from pn_kursus where j_xm = '1' ) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM left outer join pn_kolej pnk on p.id_kolej=pnk.id_kolej where xc.id_xm=(select top 1 id_xm from pn_xm where j_xm= '1' order by tahun desc, siri desc, j_xm desc) and ulangan=1 and xc.keputusan='G' and status_ulang is null order by cast(xc.ag as int), p.id_kolej"
        Dim strQuery As String = "select * from jt_penuh where jantina = '1'"

        Dim cmd As New SqlCommand(strQuery)
        Dim dt As DataTable = GetData(cmd)

        'Create a dummy GridView 
        Dim GridView1 As New GridView()
        GridView1.AllowPaging = False
        GridView1.DataSource = dt
        GridView1.DataBind()

        Response.ContentType = "application/pdf"
        Response.AddHeader("content-disposition", "attachment;filename=GridViewExport.pdf")
        Response.Cache.SetCacheability(HttpCacheability.NoCache)
        Dim sw As New StringWriter()
        Dim hw As New HtmlTextWriter(sw)
        GridView1.RenderControl(hw)
        Dim sr As New StringReader(sw.ToString())
        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
        Dim htmlparser As New HTMLWorker(pdfDoc)
        PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        pdfDoc.Open()
        htmlparser.Parse(sr)
        pdfDoc.Close()
        Response.Write(pdfDoc)
        Response.End()
    End Sub

    Protected Sub cmdWord_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdWord.Click
        'Get the data from database into datatable
        'Dim strQuery As String = "select CustomerID, ContactName, City, " & _
        '                              "PostalCode from customers"
        Dim strQuery As String = "select * from jt_penuh where jantina = '1'"

        Dim cmd As New SqlCommand(strQuery)
        Dim dt As DataTable = GetData(cmd)

        'Create a dummy GridView
        Dim GridView1 As New GridView()
        GridView1.AllowPaging = False
        GridView1.DataSource = dt
        GridView1.DataBind()

        Response.Clear()
        Response.Buffer = True
        Response.AddHeader("content-disposition", _
             "attachment;filename=DataTable.doc")
        Response.Charset = ""
        Response.ContentType = "application/vnd.ms-word "
        Dim sw As New StringWriter()
        Dim hw As New HtmlTextWriter(sw)
        GridView1.RenderControl(hw)
        Response.Output.Write(sw.ToString())
        Response.Flush()
        Response.End()
    End Sub

    Protected Sub cmdExcel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdExcel.Click
        'Get the data from database into datatable
        'Dim strQuery As String = "select CustomerID, ContactName, City, " & _
        '                            "PostalCode from customers"

        'Dim strQuery As String = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as ag, nama, p.nokp, p.j_kursus, pnk.dc_kolej, xc.keputusan, px.tahun" & _
        '                         "from xm_calon xc inner join pn_xm px on xc.id_xm=px.id_xm inner join pelatih p on xc.nokp=p.nokp" & _
        '                         "and (case when p.j_kursus in (select id_kursus  from pn_kursus where j_xm = 1 ) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM" & _
        '                         "left outer join pn_kolej pnk on p.id_kolej=pnk.id_kolej" & _
        '                         "order by cast(xc.ag as int), p.id_kolej"

        Dim strQuery As String = "select 1 '#','Jururawat Berdaftar (JB)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp " & _
                                 "inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp " & _
                                 "and jpa.j_daftar = 1 and apc_tahun = '2016' and ret=0 and apc_tkh between '2016-01-01 00:00:00' and '2016-12-31 23:59:59'" & _
                                 "union select 2,'Jururawat Masyarakat (JM)' as 'JENIS APC', " & _
                                 "count(jpa.nokp) 'JUMLAH' from jt_penuh jp" & _
                                 "inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 2" & _
                                 "and apc_tahun = '2016' and ret=0 and apc_tkh between '2016-01-01 00:00:00' and '2016-12-31 23:59:59'" & _
                                 "union select 3, 'Penolong Jururawat (PJ)' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp " & _
                                 "inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 3 and apc_tahun = 2016 and ret=0 " & _
                                 "and apc_tkh between '2016-01-01 00:00:00' and '2016-12-31 23:59:59'"

        Dim cmd As New SqlCommand(strQuery)
        Dim dt As DataTable = GetData(cmd)

        'Create a dummy GridView
        Dim GridView1 As New GridView()
        GridView1.AllowPaging = False
        GridView1.DataSource = dt
        GridView1.DataBind()

        Response.Clear()
        Response.Buffer = True
        Response.AddHeader("content-disposition", _
             "attachment;filename=DataTable.xls")
        Response.Charset = ""
        Response.ContentType = "application/vnd.ms-excel"
        Dim sw As New StringWriter()
        Dim hw As New HtmlTextWriter(sw)

        For i As Integer = 0 To GridView1.Rows.Count - 1
            'Apply text style to each Row
            GridView1.Rows(i).Attributes.Add("class", "textmode")
        Next
        GridView1.RenderControl(hw)

        'style to format numbers to string
        Dim style As String = "<style> .textmode{mso-number-format:\@;}</style>"
        Response.Write(style)
        Response.Output.Write(sw.ToString())
        Response.Flush()
        Response.End()
    End Sub

    Protected Sub cmdStorePDF_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdStorePDF.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "stat_apc_output"
        cmd.Parameters.Add("@APC_Year", SqlDbType.Int, 4).Value = Tx_Tahun.Text
        cmd.Parameters.Add("@x_start_date", SqlDbType.Date, 4).Value = Tx_Tkh_Mula.Text
        cmd.Parameters.Add("@x_end_date", SqlDbType.Date, 4).Value = Tx_Tkh_Tamat.Text
        cmd.Connection = con
        Try
            con.Open()
            Dim GridView1 = New GridView()
            GridView1.EmptyDataText = "No Records Found"
            GridView1.DataSource = cmd.ExecuteReader()
            GridView1.DataBind()

            'Dump to PDF
            Response.ContentType = "application/pdf"
            Response.AddHeader("content-disposition", "attachment;filename=GridViewExport.pdf")
            Response.Cache.SetCacheability(HttpCacheability.NoCache)
            Dim sw As New StringWriter()
            Dim hw As New HtmlTextWriter(sw)
            GridView1.RenderControl(hw)
            Dim sr As New StringReader(sw.ToString())
            Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
            Dim htmlparser As New HTMLWorker(pdfDoc)
            'Add New 
            Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            Dim ev As New itsEvents
            pdfWrite.PageEvent = ev
            'PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            Dim phrase1 As New Phrase(Environment.NewLine)
            pdfDoc.Open()
            htmlparser.Parse(sr)
            pdfDoc.Close()
            Response.Write(pdfDoc)
            Response.End()
        Catch ex As Exception
            Throw ex
        Finally
            con.Close()
            con.Dispose()
        End Try
    End Sub
   
    Protected Sub cmdLow_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdLow.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "stat_low_score_cannidate"
        cmd.Connection = con
        Try
            con.Open()
            Dim GridView1 = New GridView()
            GridView1.EmptyDataText = "No Records Found"
            GridView1.DataSource = cmd.ExecuteReader()
            GridView1.DataBind()

            'Dump to PDF
            Response.ContentType = "application/pdf"
            Response.AddHeader("content-disposition", "attachment;filename=Low.pdf")
            Response.Cache.SetCacheability(HttpCacheability.NoCache)
            Dim sw As New StringWriter()
            Dim hw As New HtmlTextWriter(sw)
            GridView1.RenderControl(hw)
            Dim sr As New StringReader(sw.ToString())
            Dim pdfDoc As New Document(PageSize.A4.Rotate, 10.0F, 10.0F, 10.0F, 0.0F)
            Dim htmlparser As New HTMLWorker(pdfDoc)
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            htmlparser.Parse(sr)
            pdfDoc.Close()
            Response.Write(pdfDoc)
            Response.End()
        Catch ex As Exception
            Throw ex
        Finally
            con.Close()
            con.Dispose()
        End Try
    End Sub

    Protected Sub cmdFail_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdFail.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "stat_fail_cannidate"
        cmd.Connection = con
        Try
            con.Open()
            Dim GridView1 = New GridView()
            GridView1.EmptyDataText = "No Records Found"
            GridView1.DataSource = cmd.ExecuteReader()
            GridView1.DataBind()

            'Dump to PDF
            Response.ContentType = "application/pdf"
            Response.AddHeader("content-disposition", "attachment;filename=Low.pdf")
            Response.Cache.SetCacheability(HttpCacheability.NoCache)
            Dim sw As New StringWriter()
            Dim hw As New HtmlTextWriter(sw)
            GridView1.RenderControl(hw)
            Dim sr As New StringReader(sw.ToString())
            Dim pdfDoc As New Document(PageSize.A4.Rotate, 10.0F, 10.0F, 10.0F, 0.0F)
            Dim htmlparser As New HTMLWorker(pdfDoc)
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            htmlparser.Parse(sr)
            pdfDoc.Close()
            Response.Write(pdfDoc)
            Response.End()
        Catch ex As Exception
            Throw ex
        Finally
            con.Close()
            con.Dispose()
        End Try
    End Sub

    Protected Sub cmdReg_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdReg.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "report_full_registration"
        cmd.Connection = con
        Try
            con.Open()
            Dim GridView1 = New GridView()
            GridView1.EmptyDataText = "No Records Found"
            GridView1.DataSource = cmd.ExecuteReader()
            GridView1.DataBind()

            'Dump to PDF
            Response.ContentType = "application/pdf"
            Response.AddHeader("content-disposition", "attachment;filename=Registration.pdf")
            Response.Cache.SetCacheability(HttpCacheability.NoCache)
            Dim sw As New StringWriter()
            Dim hw As New HtmlTextWriter(sw)
            GridView1.RenderControl(hw)
            Dim sr As New StringReader(sw.ToString())
            Dim pdfDoc As New Document(PageSize.A4.Rotate, 10.0F, 10.0F, 10.0F, 0.0F)
            Dim htmlparser As New HTMLWorker(pdfDoc)
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            htmlparser.Parse(sr)
            pdfDoc.Close()
            Response.Write(pdfDoc)
            Response.End()
        Catch ex As Exception
            Throw ex
        Finally
            con.Close()
            con.Dispose()
        End Try
    End Sub

    Protected Sub cmdPDFheader_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdPDFheader.Click
        Dim pdfDoc As New Document()
        Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, New FileStream("d:\tryme2.pdf", FileMode.Create))
        Dim ev As New itsEvents
        pdfWrite.PageEvent = ev
        pdfDoc.Open()
        pdfDoc.Add(New Paragraph("Hello World"))
        pdfDoc.NewPage()
        pdfDoc.Add(New Paragraph("Hello World Again"))
        pdfDoc.Close()
    End Sub

    Protected Sub cmdTable_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdTable.Click

        'Dim con As New SqlConnection(ServerId_SQL)
        'Dim cmd As New SqlCommand()
        'cmd.CommandType = CommandType.StoredProcedure
        'cmd.CommandText = "stat_apc_output"
        'cmd.Parameters.Add("@APC_Year", SqlDbType.Int, 4).Value = Tx_Tahun.Text
        'cmd.Parameters.Add("@x_start_date", SqlDbType.Date, dddd4).Value = Tx_Tkh_Mula.Text
        'cmd.Parameters.Add("@x_end_date", SqlDbType.Date, 4).Value = Tx_Tkh_Tamat.Text
        'cmd.Connection = con

        'Dim document As New Document(PageSize.A4, 88.0F, 88.0F, 10.0F, 10.0F)
        'Dim NormalFont As Font = FontFactory.GetFont("Arial", 12, Font.NORMAL, Color.BLACK)
        'Using memoryStream As New System.IO.MemoryStream()
        '    Dim writer As PdfWriter = PdfWriter.GetInstance(document, memoryStream)
        '    Dim phrase As Phrase = Nothing
        '    Dim cell As PdfPCell = Nothing
        '    Dim table As PdfPTable = Nothing
        '    Dim color__1 As Color = Nothing

        '    document.Open()

        ''Header Table
        'Table = New PdfPTable(2)
        'Table.TotalWidth = 500.0F
        'Table.LockedWidth = True
        'Table.SetWidths(New Single() {0.3F, 0.7F})

        ''Company Logo
        'cell = ImageCell("~/images/northwindlogo.gif", 30.0F, PdfPCell.ALIGN_CENTER)
        'Table.AddCell(cell)

        ''Company Name and Address
        'Phrase = New Phrase()
        'Phrase.Add(New Chunk("Microsoft Northwind Traders Company" & vbLf & vbLf, FontFactory.GetFont("Arial", 16, Font.BOLD, Color.RED)))
        'Phrase.Add(New Chunk("107, Park site," & vbLf, FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)))
        'Phrase.Add(New Chunk("Salt Lake Road," & vbLf, FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)))
        'Phrase.Add(New Chunk("Seattle, USA", FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)))
        'cell = PhraseCell(Phrase, PdfPCell.ALIGN_LEFT)
        'cell.VerticalAlignment = PdfCell.ALIGN_TOP
        'Table.AddCell(cell)

        ''Separater Line
        'color__1 = New Color(System.Drawing.ColorTranslator.FromHtml("#A9A9A9"))
        'DrawLine(writer, 25.0F, Document.Top - 79.0F, Document.PageSize.Width - 25.0F, Document.Top - 79.0F, color__1)
        'DrawLine(writer, 25.0F, Document.Top - 80.0F, Document.PageSize.Width - 25.0F, Document.Top - 80.0F, color__1)
        'Document.Add(Table)

        'Table = New PdfPTable(2)
        'Table.HorizontalAlignment = Element.ALIGN_LEFT
        'Table.SetWidths(New Single() {0.3F, 1.0F})
        'Table.SpacingBefore = 20.0F

        ''Employee Details
        'cell = PhraseCell(New Phrase("Employee Record", FontFactory.GetFont("Arial", 12, Font.UNDERLINE, Color.BLACK)), PdfPCell.ALIGN_CENTER)
        'cell.Colspan = 2
        'Table.AddCell(cell)
        'cell = PhraseCell(New Phrase(), PdfPCell.ALIGN_CENTER)
        'cell.Colspan = 2
        'cell.PaddingBottom = 30.0F
        'Table.AddCell(cell)

        ''Photo
        'cell = ImageCell(String.Format("~/photos/{0}.jpg", dr("EmployeeId")), 25.0F, PdfPCell.ALIGN_CENTER)
        'Table.AddCell(cell)

        ''Name
        'Phrase = New Phrase()
        'Phrase.Add(New Chunk(dr("TitleOfCourtesy").ToString & " " + dr("FirstName").ToString & " " + dr("LastName").ToString, FontFactory.GetFont("Arial", 10, Font.BOLD, Color.BLACK)))
        'Phrase.Add(New Chunk("(" + dr("Title").ToString() + ")", FontFactory.GetFont("Arial", 8, Font.BOLD, Color.BLACK)))
        'cell = PhraseCell(Phrase, PdfPCell.ALIGN_LEFT)
        'cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE
        'Table.AddCell(cell)
        'Document.Add(Table)

        'DrawLine(writer, 160.0F, 80.0F, 160.0F, 690.0F, Color.BLACK)
        'DrawLine(writer, 115.0F, Document.Top - 200.0F, Document.PageSize.Width - 100.0F, Document.Top - 200.0F, Color.BLACK)

        'Table = New PdfPTable(2)
        'Table.SetWidths(New Single() {0.5F, 2.0F})
        'Table.TotalWidth = 340.0F
        'Table.LockedWidth = True
        'Table.SpacingBefore = 20.0F
        'Table.HorizontalAlignment = Element.ALIGN_RIGHT

        ''Employee Id
        'Table.AddCell(PhraseCell(New Phrase("Employee code:", FontFactory.GetFont("Arial", 8, Font.BOLD, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'Table.AddCell(PhraseCell(New Phrase("000" + dr("EmployeeId"), FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'cell = PhraseCell(New Phrase(), PdfPCell.ALIGN_CENTER)
        'cell.Colspan = 2
        'cell.PaddingBottom = 10.0F
        'Table.AddCell(cell)


        ''Address
        'Table.AddCell(PhraseCell(New Phrase("Address:", FontFactory.GetFont("Arial", 8, Font.BOLD, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'Phrase = New Phrase(New Chunk(dr("Address").ToString, FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)))
        'Phrase.Add(New Chunk(dr("City").ToString + vbLf, FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)))
        'Phrase.Add(New Chunk(dr("Region").ToString + " " + dr("Country").ToString + " " + dr("PostalCode").ToString, FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)))
        'Table.AddCell(PhraseCell(Phrase, PdfPCell.ALIGN_LEFT))
        'cell = PhraseCell(New Phrase(), PdfPCell.ALIGN_CENTER)
        'cell.Colspan = 2
        'cell.PaddingBottom = 10.0F
        'Table.AddCell(cell)

        ''Date of Birth
        'Table.AddCell(PhraseCell(New Phrase("Date of Birth:", FontFactory.GetFont("Arial", 8, Font.BOLD, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'Table.AddCell(PhraseCell(New Phrase(Convert.ToDateTime(dr("BirthDate")).ToString("dd MMMM, yyyy"), FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'cell = PhraseCell(New Phrase(), PdfPCell.ALIGN_CENTER)
        'cell.Colspan = 2
        'cell.PaddingBottom = 10.0F
        'Table.AddCell(cell)

        ''Phone
        'Table.AddCell(PhraseCell(New Phrase("Phone Number:", FontFactory.GetFont("Arial", 8, Font.BOLD, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'Table.AddCell(PhraseCell(New Phrase(dr("HomePhone") + " Ext: " + dr("Extension"), FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'cell = PhraseCell(New Phrase(), PdfPCell.ALIGN_CENTER)
        'cell.Colspan = 2
        'cell.PaddingBottom = 10.0F
        'Table.AddCell(cell)

        ''Addtional Information
        'Table.AddCell(PhraseCell(New Phrase("Addtional Information:", FontFactory.GetFont("Arial", 8, Font.BOLD, Color.BLACK)), PdfPCell.ALIGN_LEFT))
        'Table.AddCell(PhraseCell(New Phrase(dr("Notes").ToString(), FontFactory.GetFont("Arial", 8, Font.NORMAL, Color.BLACK)), PdfPCell.ALIGN_JUSTIFIED))
        'Document.Add(Table)
        'Document.Close()
        'Dim bytes As Byte() = MemoryStream.ToArray()
        'MemoryStream.Close()
        'Response.Clear()
        'Response.ContentType = "application/pdf"
        'Response.AddHeader("Content-Disposition", "attachment; filename=Employee.pdf")
        'Response.ContentType = "application/pdf"
        'Response.Buffer = True
        'Response.Cache.SetCacheability(HttpCacheability.NoCache)
        'Response.BinaryWrite(bytes)
        'Response.[End]()
        'Response.Close()
        'End Using
    End Sub

    'Private Shared Sub DrawLine(ByVal writer As PdfWriter, ByVal x1 As Single, ByVal y1 As Single, ByVal x2 As Single, ByVal y2 As Single, ByVal color As Color)
    '    Dim contentByte As PdfContentByte = writer.DirectContent
    '    contentByte.SetColorStroke(color)
    '    contentByte.MoveTo(x1, y1)
    '    contentByte.LineTo(x2, y2)
    '    contentByte.Stroke()
    'End Sub

    'Private Shared Function PhraseCell(ByVal phrase As Phrase, ByVal align As Integer) As PdfPCell
    '    Dim cell As New PdfPCell(phrase)
    '    cell.BorderColor = Color.WHITE
    '    cell.VerticalAlignment = PdfCell.ALIGN_TOP
    '    cell.HorizontalAlignment = align
    '    cell.PaddingBottom = 2.0F
    '    cell.PaddingTop = 0.0F
    '    Return cell
    'End Function

    'Private Shared Function ImageCell(ByVal path As String, ByVal scale As Single, ByVal align As Integer) As PdfPCell
    '    Dim image As iTextSharp.text.Image = iTextSharp.text.Image.GetInstance(HttpContext.Current.Server.MapPath(path))
    '    image.ScalePercent(scale)
    '    Dim cell As New PdfPCell(image)
    '    cell.BorderColor = Color.WHITE
    '    cell.VerticalAlignment = PdfCell.ALIGN_TOP
    '    cell.HorizontalAlignment = align
    '    cell.PaddingBottom = 0.0F
    '    cell.PaddingTop = 0.0F
    '    Return cell
    'End Function

    Protected Sub cmdRows_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdRows.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim dt As New DataTable()

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "stat_apc_output"
        cmd.Parameters.Add("@APC_Year", SqlDbType.Int, 4).Value = Tx_Tahun.Text
        cmd.Parameters.Add("@x_start_date", SqlDbType.Date, 4).Value = Tx_Tkh_Mula.Text
        cmd.Parameters.Add("@x_end_date", SqlDbType.Date, 4).Value = Tx_Tkh_Tamat.Text
        cmd.Connection = con
        Try
            con.Open()
            Dim GridView1 = New GridView()
            GridView1.EmptyDataText = "No Records Found"
            GridView1.DataSource = cmd.ExecuteReader()
            GridView1.DataBind()



            'dt.Columns.AddRange(New DataColumn(2) {New DataColumn("Id", GetType(Integer)), New DataColumn("Name", GetType(String)), New DataColumn("Country", GetType(String))})
            For Each row As GridViewRow In GridView1.Rows
                If TryCast(row.FindControl("CheckBox1"), CheckBox).Checked Then
                    Dim id As Integer = Integer.Parse(row.Cells(1).Text)
                    Dim name As String = row.Cells(2).Text
                    Dim country As String = row.Cells(3).Text
                    dt.Rows.Add(id, name, country)
                End If
            Next

            'Dump to PDF
            Response.ContentType = "application/pdf"
            Response.AddHeader("content-disposition", "attachment;filename=GridViewExport.pdf")
            Response.Cache.SetCacheability(HttpCacheability.NoCache)
            Dim sw As New StringWriter()
            Dim hw As New HtmlTextWriter(sw)
            GridView1.RenderControl(hw)
            Dim sr As New StringReader(sw.ToString())
            Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
            Dim htmlparser As New HTMLWorker(pdfDoc)
            'Add New 
            Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            Dim ev As New itsEvents
            pdfWrite.PageEvent = ev
            'PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            htmlparser.Parse(sr)
            pdfDoc.Close()
            Response.Write(pdfDoc)
            Response.End()
        Catch ex As Exception
            Throw ex
        Finally
            con.Close()
            con.Dispose()
        End Try
    End Sub

    Protected Sub cmdPDF_T_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdPDF_T.Click

        Dim table As New PdfPTable(3)
        Dim cell As New PdfPCell(New Phrase("Header spanning 3 columns"))

        cell.Colspan = 3
        cell.HorizontalAlignment = 1 '0=Left, 1=Centre, 2=Right
        table.AddCell(cell)
        table.AddCell("Col 1 Row 1")
        table.AddCell("Col 2 Row 1")
        table.AddCell("Col 3 Row 1")
        table.AddCell("Col 1 Row 2")
        table.AddCell("Col 2 Row 2")
        table.AddCell("Col 3 Row 2")

        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
        PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        pdfDoc.Open()
        pdfDoc.Add(table)
        pdfDoc.Close()
        Response.ContentType = "application/pdf"
        Response.AddHeader("content-disposition", _
       "attachment;filename=GridViewExport.pdf")
        Response.Cache.SetCacheability(HttpCacheability.NoCache)
        Response.Write(pdfDoc)
        Response.End()

    End Sub

    Protected Sub cmdStorePDF_T_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdStorePDF_T.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()

        Dim table As New PdfPTable(3)
        'relative col widths in proportions - 1/3 and 2/3
        'dim widths as  new float[] { 1f, 2f }
        'table.SetWidths(widths)
        table.HorizontalAlignment = 0

        'leave a gap before and after the table
        table.SpacingBefore = 20.0F
        table.SpacingAfter = 30.0F


        Dim cell As New PdfPCell(New Phrase("Products"))
        cell.Colspan = 3
        cell.Border = 0
        cell.HorizontalAlignment = 1
        table.AddCell(cell)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "stat_apc_output"
        cmd.Parameters.Add("@APC_Year", SqlDbType.Int, 4).Value = Tx_Tahun.Text
        cmd.Parameters.Add("@x_start_date", SqlDbType.Date, 4).Value = Tx_Tkh_Mula.Text
        cmd.Parameters.Add("@x_end_date", SqlDbType.Date, 4).Value = Tx_Tkh_Tamat.Text
        cmd.Connection = con

        Try
            con.Open()
            Dim rdr As SqlDataReader = cmd.ExecuteReader()

            While rdr.Read()
                table.AddCell(rdr(0).ToString)
                table.AddCell(rdr(1).ToString)
                table.AddCell(rdr(2).ToString)
            End While
        Catch ex As Exception
            Response.Write(ex.Message)
        End Try

        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
        PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        pdfDoc.Open()
        pdfDoc.Add(table)
        pdfDoc.Close()
        Response.ContentType = "application/pdf"
        Response.AddHeader("content-disposition", _
       "attachment;filename=StoreTable.pdf")
        Response.Cache.SetCacheability(HttpCacheability.NoCache)
        Response.Write(pdfDoc)
        Response.End()
        con.Close()
        con.Dispose()
    End Sub

    Protected Sub cmdNestTable_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdNestTable.Click

        Dim table As New PdfPTable(4)
        table.TotalWidth = 400.0F
        table.LockedWidth = True

        Dim header As New PdfPCell(New Phrase("Header"))
        header.Colspan = 4
        table.AddCell(header)
        table.AddCell("Cell 1")
        table.AddCell("Cell 2")
        table.AddCell("Cell 3")
        table.AddCell("Cell 4")

        Dim nested As New PdfPTable(1)
        nested.AddCell("Nested Row 1")
        nested.AddCell("Nested Row 2")
        nested.AddCell("Nested Row 3")

        Dim nesthousing As New PdfPCell(nested)
        nesthousing.Padding = 0.0F
        table.AddCell(nesthousing)

        Dim bottom As New PdfPCell(New Phrase("bottom"))
        bottom.Colspan = 3
        table.AddCell(bottom)

        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
        PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        pdfDoc.Open()
        pdfDoc.Add(table)
        pdfDoc.Close()
        Response.ContentType = "application/pdf"
        Response.AddHeader("content-disposition", _
       "attachment;filename=NestedTable.pdf")
        Response.Cache.SetCacheability(HttpCacheability.NoCache)
        Response.Write(pdfDoc)
        Response.End()
    End Sub

    Protected Sub cmdStoreNested_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdStoreNested.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()

        If Tx_Tahun.Text = "" Then
            MsgBox("Sila isi maklumat tahun", MsgBoxStyle.Information)
        ElseIf Tx_Tkh_Mula.Text = "" Then
            MsgBox("Sila isi maklumat tahun", MsgBoxStyle.Information)
        ElseIf Tx_Tkh_Tamat.Text = "" Then
            MsgBox("Sila isi maklumat tahun", MsgBoxStyle.Information)
        End If

        Dim table As New PdfPTable(3)
        table.TotalWidth = 400.0F
        table.LockedWidth = True

        Dim header As New PdfPCell(New Phrase("Header"))
        header.Colspan = 3
        table.AddCell(header)
        table.AddCell("Cell 1")
        table.AddCell("Cell 2")
        table.AddCell("Cell 3")


        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "stat_apc_output"
        cmd.Parameters.Add("@APC_Year", SqlDbType.Int, 4).Value = Tx_Tahun.Text
        cmd.Parameters.Add("@x_start_date", SqlDbType.Date, 4).Value = Tx_Tkh_Mula.Text
        cmd.Parameters.Add("@x_end_date", SqlDbType.Date, 4).Value = Tx_Tkh_Tamat.Text
        cmd.Connection = con

        Try
            con.Open()
            Dim rdr As SqlDataReader = cmd.ExecuteReader()


            While rdr.Read()
                table.AddCell(rdr(0).ToString)
                table.AddCell(rdr(1).ToString)
                table.AddCell(rdr(2).ToString)
            End While

            'Dump to pdf
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            pdfDoc.Add(table)
            pdfDoc.Close()
            Response.ContentType = "application/pdf"
            Response.AddHeader("content-disposition", _
           "attachment;filename=NestedTable.pdf")
            Response.Cache.SetCacheability(HttpCacheability.NoCache)
            Response.Write(pdfDoc)
            Response.End()
        Catch ex As Exception

        End Try
        'Try
        '    con.Open()
        '    Dim rdr As SqlDataReader = cmd.ExecuteReader()

        '    Dim cells As New PdfPTable(3)
        '    'Dim cellshousing As New PdfPCell(cells)

        '    While rdr.Read()
        '        cells.AddCell(rdr(0).ToString)
        '        cells.AddCell(rdr(1).ToString)
        '        cells.AddCell(rdr(2).ToString)
        '    End While

        '    'cellshousing.Padding = 0.0F
        '    ' table.AddCell(cellshousing)
        '    table.AddCell(cells)



        '    'Dim nested As New PdfPTable(1)
        '    'nested.AddCell("Nested Row 1")
        '    'nested.AddCell("Nested Row 2")
        '    'nested.AddCell("Nested Row 3")

        '    'Dim nesthousing As New PdfPCell(nested)
        '    'nesthousing.Padding = 0.0F
        '    'table.AddCell(nesthousing)

        '    'Dim bottom As New PdfPCell(New Phrase("bottom"))
        '    'bottom.Colspan = 3
        '    'table.AddCell(bottom)

        '    'Dump to pdf
        '    PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        '    pdfDoc.Open()
        '    pdfDoc.Add(table)
        '    pdfDoc.Close()
        '    Response.ContentType = "application/pdf"
        '    Response.AddHeader("content-disposition", _
        '   "attachment;filename=NestedTable.pdf")
        '    Response.Cache.SetCacheability(HttpCacheability.NoCache)
        '    Response.Write(pdfDoc)
        '    Response.End()

        'Catch ex As Exception
        '    Response.Write(ex.Message)
        'End Try

    End Sub

    Protected Sub cmdSizePDF_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdSizePDF.Click
        Dim table As New PdfPTable(4)

        table.TotalWidth = 285.0F
        table.HorizontalAlignment = 0
        table.SpacingAfter = 10

        Dim TWidths(4) As Single
        TWidths(0) = 20.0F
        TWidths(1) = 15.0F
        TWidths(2) = 100.0F
        TWidths(3) = 150.0F

        table.SetWidths(TWidths)
        table.LockedWidth = True



    End Sub
End Class


'Public Class itsEvents
'    Inherits PdfPageEventHelper

'    Public Overrides Sub OnStartPage(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)
'        'Dim ch As New Chunk("This is my Stack Overflow Header on page " & writer.PageNumber)
'        Dim ch As New Chunk("This is my Stack Overflow Header on page ")
'        document.Add(ch)
'    End Sub
'End Class