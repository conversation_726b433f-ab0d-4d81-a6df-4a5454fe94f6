Imports System.Data
Imports System.Text

Partial Public Class Test_RequestSize
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ' Create large hidden field data to simulate ViewState issues
            CreateLargeViewStateData()
            
            ' Populate test grid
            PopulateTestGrid()
        End If
    End Sub

    Private Sub CreateLargeViewStateData()
        ' Create large strings to simulate heavy ViewState
        Dim largeString As New StringBuilder()
        For i As Integer = 1 To 1000
            largeString.Append("This is test data for simulating large ViewState scenarios. ")
        Next
        
        hiddenData1.Value = largeString.ToString()
        hiddenData2.Value = largeString.ToString()
        hiddenData3.Value = largeString.ToString()
    End Sub

    Private Sub PopulateTestGrid()
        Dim dt As New DataTable()
        dt.Columns.Add("ID", GetType(Integer))
        dt.Columns.Add("Name", GetType(String))
        dt.Columns.Add("Description", GetType(String))
        
        ' Add test data
        For i As Integer = 1 To 50
            Dim row As DataRow = dt.NewRow()
            row("ID") = i
            row("Name") = "Test Item " & i.ToString()
            row("Description") = "This is a test description for item " & i.ToString() & ". It contains enough text to make the ViewState larger and test the request size limits."
            dt.Rows.Add(row)
        Next
        
        gvTestData.DataSource = dt
        gvTestData.DataBind()
    End Sub

    Protected Sub btnTestLargeRequest_Click(ByVal sender As Object, ByVal e As EventArgs)
        Try
            ' Simulate processing that might have caused the "request entity is too large" error
            Dim currentTime As DateTime = DateTime.Now
            Dim requestSize As Long = Request.ContentLength
            
            ' Create result message
            Dim result As New StringBuilder()
            result.AppendLine("✓ Large request test completed successfully!")
            result.AppendLine("Test Time: " & currentTime.ToString("yyyy-MM-dd HH:mm:ss"))
            result.AppendLine("Request Content Length: " & requestSize.ToString() & " bytes")
            result.AppendLine("Hidden Field 1 Size: " & hiddenData1.Value.Length.ToString() & " characters")
            result.AppendLine("Hidden Field 2 Size: " & hiddenData2.Value.Length.ToString() & " characters")
            result.AppendLine("Hidden Field 3 Size: " & hiddenData3.Value.Length.ToString() & " characters")
            result.AppendLine("Grid Rows: " & gvTestData.Rows.Count.ToString())
            
            If requestSize > 4194304 Then ' 4MB in bytes
                result.AppendLine("⚠ Request size (" & (requestSize / 1024 / 1024).ToString("F2") & " MB) exceeds old 4MB limit")
                result.AppendLine("✓ Fix is working - request was processed successfully!")
            Else
                result.AppendLine("ℹ Request size (" & (requestSize / 1024).ToString("F1") & " KB) is within normal range")
            End If
            
            lblResults.Text = result.ToString().Replace(vbCrLf, "<br />")
            lblResults.CssClass = "success"
            
        Catch ex As Exception
            lblResults.Text = "❌ Test failed: " & ex.Message
            lblResults.CssClass = "warning"
        End Try
    End Sub

    Protected Sub gvTestData_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)
        Try
            Dim selectedRow As GridViewRow = gvTestData.SelectedRow
            If selectedRow IsNot Nothing Then
                Dim selectedID As String = selectedRow.Cells(1).Text
                Dim selectedName As String = selectedRow.Cells(2).Text
                
                lblResults.Text = "✓ PILIH button test successful!<br />" & _
                                "Selected Item: " & selectedName & " (ID: " & selectedID & ")<br />" & _
                                "This simulates the maintenance page PILIH functionality."
                lblResults.CssClass = "success"
            End If
        Catch ex As Exception
            lblResults.Text = "❌ PILIH test failed: " & ex.Message
            lblResults.CssClass = "warning"
        End Try
    End Sub
End Class
