using Microsoft.EntityFrameworkCore;
using SPMJ.EmailService.Models;

namespace SPMJ.EmailService.Data;

public class SPMJContext : DbContext
{
    public SPMJContext(DbContextOptions<SPMJContext> options) : base(options)
    {
    }

    public DbSet<PnPengguna> PnPengguna { get; set; }
    public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }
    public DbSet<OtpToken> OtpTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure PnPengguna
        modelBuilder.Entity<PnPengguna>(entity =>
        {
            entity.HasKey(e => e.IdPg);
            entity.Property(e => e.IdPg).HasMaxLength(50);            entity.Property(e => e.Pwd).HasMaxLength(100);
            entity.Property(e => e.Salt).HasMaxLength(100);
            entity.Property(e => e.Nama).HasMaxLength(255);
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.Status).HasColumnType("tinyint");
            entity.Property(e => e.Modul).HasMaxLength(50);
            entity.Property(e => e.Akses).HasColumnType("tinyint");
        });

        // Configure PasswordResetToken
        modelBuilder.Entity<PasswordResetToken>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Token).HasMaxLength(255).IsRequired();
            entity.HasIndex(e => e.Token).IsUnique();
            entity.HasIndex(e => new { e.UserId, e.Used });
        });

        // Configure OtpToken
        modelBuilder.Entity<OtpToken>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(50).IsRequired();
            entity.Property(e => e.OtpCode).HasMaxLength(10).IsRequired();
            entity.Property(e => e.Purpose).HasMaxLength(50).IsRequired();
            entity.HasIndex(e => new { e.UserId, e.Purpose, e.Used });
        });
    }
}
