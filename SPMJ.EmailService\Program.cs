using Microsoft.EntityFrameworkCore;
using SPMJ.EmailService.Data;
using SPMJ.EmailService.Services;
using SPMJ.EmailService.Models;
using SPMJ.EmailService.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddDbContext<SPMJContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Register custom services
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IPasswordService, PasswordService>();
builder.Services.AddScoped<IOtpService, OtpService>();
builder.Services.AddScoped<IAdminPasswordEmailService, AdminPasswordEmailService>();
builder.Services.AddMemoryCache();

// Add CORS for .NET 3.5 integration - SECURED
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSPMJ", policy =>
    {
        policy.WithOrigins("http://localhost:8080", "http://************", "https://your-main-app-domain.com")
              .WithMethods("GET", "POST", "PUT", "DELETE")
              .WithHeaders("Content-Type", "X-API-Key", "Authorization")
              .AllowCredentials();
    });
});

// Add logging
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Remove HTTPS redirection for development - causes port issues
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

app.UseCors("AllowSPMJ");

// Add Rate Limiting
app.UseMiddleware<RateLimitingMiddleware>();

// Add API Key Authentication
app.UseMiddleware<ApiKeyAuthenticationMiddleware>();

app.MapControllers();

// Add health check endpoint
app.MapGet("/health", () => new { status = "healthy", timestamp = DateTime.UtcNow });

app.Run();
