Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Data
Imports System.Web.UI.WebControls

Partial Public Class WebForm55
    Inherits System.Web.UI.Page

    ' Email service client for microservice integration
    Private emailClient As EmailServiceClient

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        
        ' Enhanced security check
        If Session("Id_PG") Is Nothing OrElse Session("Id_PG").ToString() = "" Then
            Session.Abandon()
            Response.Redirect("p0_Login.aspx")
            Return
        End If
        
        ' Check access permissions with enhanced validation
        If Not Akses_Pg("PN", "Institut", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "COLLEGE MANAGEMENT ACCESS"
            Session("Msg_Isi") = "Access Restricted - Insufficient Privileges"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
          ' Initialize email service client
        Try
            emailClient = New EmailServiceClient("http://localhost:8080")
            System.Diagnostics.Debug.WriteLine("Email service client initialized for college management")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Email service client initialization failed: " & ex.Message)
            ' Continue without email service - graceful degradation
        End Try
        
        ' Configure enhanced security headers
        ConfigureSecurityHeaders()
        
        ' Load college data with enhanced error handling
        LoadCollegeData()
        
        ' Log page access for security auditing
        LogSecurityEvent("COLLEGE_MANAGEMENT_ACCESS", Session("Id_PG").ToString())
    End Sub

    Private Sub ConfigureSecurityHeaders()
        ' Enhanced security headers for college management
        Response.Headers.Add("X-Content-Type-Options", "nosniff")
        Response.Headers.Add("X-Frame-Options", "DENY")
        Response.Headers.Add("X-XSS-Protection", "1; mode=block")
        Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin")
    End Sub

    Private Sub LoadCollegeData()
        Try
            System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: Loading college data with enhanced security")
            Cari("")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("College data loading error: " & ex.Message)
            ShowMessage("Error loading college data. Please refresh the page.", "error")
        End Try
    End Sub

    Public Sub Reset()
        Try
            Session("PN_Pinda") = False
            Tx_Id.ReadOnly = False
            Tx_Id.Text = ""
            Tx_Kolej.Text = ""
            Cb_Jenis.SelectedIndex = 0
            cmd_baru.Visible = False
            
            ' Clear message panel
            HideMessage()
            
            System.Diagnostics.Debug.WriteLine("College form reset completed")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Form reset error: " & ex.Message)
        End Try
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim connection As SqlConnection = Nothing
        
        Try
            connection = New SqlConnection(ServerId_SQL)
            connection.Open()
            
            System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: Executing secure college data query")
              ' Enhanced query with parameterized approach for security
            Dim SQL As String = "SELECT dc_kolej AS 'INSTITUSI LATIHAN', id_kolej, " & _
                               "CASE jenis " & _
                               "WHEN 1 THEN 'K' " & _
                               "WHEN 2 THEN 'S' " & _
                               "WHEN 3 THEN 'LN' " & _
                               "ELSE '' END AS 'JENIS' " & _
                               "FROM pn_kolej " & _
                               "WHERE status = 1 " & _
                               "ORDER BY jenis, dc_kolej"
            
            Using adapter As New SqlDataAdapter(SQL, connection)
                Dim dataSet As New DataSet()
                adapter.Fill(dataSet, "pn_kolej")
                
                ' Enhanced GridView configuration
                ConfigureGridView()
                
                Gd.DataSource = dataSet.Tables("pn_kolej")
                Gd.DataBind()
                
                System.Diagnostics.Debug.WriteLine("Loaded " & dataSet.Tables("pn_kolej").Rows.Count.ToString() & " college records")
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("College data retrieval error: " & ex.Message)
            ShowMessage("Error retrieving college data: " & ex.Message, "error")
        Finally
            If connection IsNot Nothing AndAlso connection.State = Data.ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Sub

    Private Sub ConfigureGridView()
        ' Enhanced GridView configuration for modern interface
        Gd.AllowPaging = True
        Gd.PageSize = 10
        Gd.AllowSorting = True
        Gd.CssClass = "college-grid"
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Try
            System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: College save operation initiated")
            
            ' Clear previous messages
            HideMessage()
            
            ' Enhanced server-side validation
            If Not ValidateCollegeData() Then
                Return
            End If
            
            ' Determine operation type
            Dim isUpdateOperation As Boolean = CBool(Session("PN_Pinda"))
            
            If isUpdateOperation Then
                UpdateCollegeRecord()
            Else
                CreateNewCollegeRecord()
            End If
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("College save error: " & ex.Message)
            ShowMessage("System error occurred. Please try again.", "error")
            LogSecurityEvent("COLLEGE_SAVE_ERROR", Session("Id_PG").ToString(), ex.Message)
        End Try
    End Sub

    Protected Sub cmd_Reset_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Reset.Click
        Try
            Reset()
            ShowMessage("Form has been reset successfully.", "info")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Reset error: " & ex.Message)
        End Try
    End Sub

    Private Function ValidateCollegeData() As Boolean
        ' Enhanced validation with detailed error messages
        
        ' Check institution name
        If String.IsNullOrEmpty(Tx_Kolej.Text) OrElse Tx_Kolej.Text.Trim().Length < 3 Then
            ShowMessage("Institution name must be at least 3 characters long.", "error")
            Tx_Kolej.Focus()
            Return False
        End If
        
        ' Check institution type
        If Cb_Jenis.SelectedIndex < 1 Then
            ShowMessage("Please select an institution type.", "error")
            Cb_Jenis.Focus()
            Return False
        End If
        
        ' Additional business rule validations
        If Tx_Kolej.Text.Trim().Length > 200 Then
            ShowMessage("Institution name cannot exceed 200 characters.", "error")
            Tx_Kolej.Focus()
            Return False
        End If
        
        ' Check for potentially harmful input
        If ContainsPotentiallyHarmfulInput(Tx_Kolej.Text) Then
            ShowMessage("Institution name contains invalid characters.", "error")
            Tx_Kolej.Focus()
            Return False
        End If
        
        Return True
    End Function

    Private Function ContainsPotentiallyHarmfulInput(input As String) As Boolean        ' Basic XSS and SQL injection prevention
        Dim harmfulPatterns() As String = {"<script", "</script", "javascript:", "vbscript:", _
                                          "onload=", "onerror=", "exec(", "eval("}
        
        For Each pattern In harmfulPatterns
            If input.ToLower().Contains(pattern.ToLower()) Then
                Return True
            End If
        Next
        
        Return False
    End Function

    Private Sub CreateNewCollegeRecord()
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            ' Check for duplicate institution name
            If CheckDuplicateInstitution(connection) Then
                ShowMessage("An institution with this name already exists.", "error")
                Return
            End If
            
            ' Create new college record with parameterized query
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "INSERT INTO pn_kolej (dc_kolej, jenis, status, created_by, created_date) VALUES (?, ?, 1, ?, ?)"
                command.Parameters.AddWithValue("@dc_kolej", Tx_Kolej.Text.Trim())
                command.Parameters.AddWithValue("@jenis", Cb_Jenis.SelectedIndex)
                command.Parameters.AddWithValue("@created_by", Session("Id_PG").ToString())
                command.Parameters.AddWithValue("@created_date", DateTime.Now)
                
                Dim rowsAffected As Integer = command.ExecuteNonQuery()
                
                If rowsAffected > 0 Then
                    ' Send email notification if enabled
                    Dim emailSent As Boolean = False
                    If chkEmailNotification.Checked Then
                        emailSent = SendCollegeChangeNotification("CREATE", Tx_Kolej.Text.Trim())
                    End If
                    
                    ' Log successful creation
                    LogSecurityEvent("COLLEGE_CREATED", Session("Id_PG").ToString(), Tx_Kolej.Text.Trim())
                    
                    ' Show success message
                    Dim successMessage As String = "Institution has been successfully created!"
                    If chkEmailNotification.Checked Then
                        If emailSent Then
                            successMessage &= " Notification email has been sent to administrators."
                        Else
                            successMessage &= " Note: Email notification could not be sent due to service unavailability."
                        End If
                    End If
                    
                    ShowMessage(successMessage, "success")
                    
                    ' Refresh data and reset form
                    Reset()
                    Cari("")
                Else
                    ShowMessage("Failed to create institution record.", "error")
                End If
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("College creation error: " & ex.Message)
            ShowMessage("Error creating institution: " & ex.Message, "error")
            LogSecurityEvent("COLLEGE_CREATE_ERROR", Session("Id_PG").ToString(), ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = Data.ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Sub

    Private Sub UpdateCollegeRecord()
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE pn_kolej SET dc_kolej = ?, jenis = ?, modified_by = ?, modified_date = ? WHERE id_kolej = ?"
                command.Parameters.AddWithValue("@dc_kolej", Tx_Kolej.Text.Trim())
                command.Parameters.AddWithValue("@jenis", Cb_Jenis.SelectedIndex)
                command.Parameters.AddWithValue("@modified_by", Session("Id_PG").ToString())
                command.Parameters.AddWithValue("@modified_date", DateTime.Now)
                command.Parameters.AddWithValue("@id_kolej", Tx_Id.Text)
                
                Dim rowsAffected As Integer = command.ExecuteNonQuery()
                
                If rowsAffected > 0 Then
                    ' Send email notification if enabled
                    Dim emailSent As Boolean = False
                    If chkEmailNotification.Checked Then
                        emailSent = SendCollegeChangeNotification("UPDATE", Tx_Kolej.Text.Trim())
                    End If
                    
                    ' Log successful update
                    LogSecurityEvent("COLLEGE_UPDATED", Session("Id_PG").ToString(), Tx_Kolej.Text.Trim())
                    
                    ' Show success message
                    Dim successMessage As String = "Institution has been successfully updated!"
                    If chkEmailNotification.Checked Then
                        If emailSent Then
                            successMessage &= " Notification email has been sent to administrators."
                        Else
                            successMessage &= " Note: Email notification could not be sent due to service unavailability."
                        End If
                    End If
                    
                    ShowMessage(successMessage, "success")
                    
                    ' Refresh data and reset form
                    Reset()
                    Cari("")
                Else
                    ShowMessage("Failed to update institution record.", "error")
                End If
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("College update error: " & ex.Message)
            ShowMessage("Error updating institution: " & ex.Message, "error")
            LogSecurityEvent("COLLEGE_UPDATE_ERROR", Session("Id_PG").ToString(), ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = Data.ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Sub

    Private Function CheckDuplicateInstitution(connection As OleDbConnection) As Boolean
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT COUNT(*) FROM pn_kolej WHERE UPPER(dc_kolej) = ? AND status = 1"
                command.Parameters.AddWithValue("@dc_kolej", Tx_Kolej.Text.Trim().ToUpper())
                
                Dim count As Integer = Convert.ToInt32(command.ExecuteScalar())
                Return count > 0
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Duplicate check error: " & ex.Message)
            Return False
        End Try
    End Function

#Region "Microservice Integration"

    Private Function SendCollegeChangeNotification(operation As String, institutionName As String) As Boolean
        Try
            If emailClient Is Nothing Then
                System.Diagnostics.Debug.WriteLine("Email service client not available")
                Return False
            End If
            
            Dim userId As String = Session("Id_PG").ToString()
            Dim userEmail As String = GetAdminEmail(userId)
            
            If String.IsNullOrEmpty(userEmail) Then
                System.Diagnostics.Debug.WriteLine("Admin email not found for notifications")
                Return False
            End If

            System.Diagnostics.Debug.WriteLine("MICROSERVICE: Sending college " & operation & " notification for: " & institutionName)
              ' Create notification message based on operation
            Dim body As String = "Institution '" & institutionName & "' has been " & operation.ToLower() & "d in the SPMJ system by " & userId & ". " & _
                               "Operation completed at " & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") & "."
            
            ' Send notification via microservice
            Dim response = emailClient.SendAdminNotification("COLLEGE_MANAGEMENT", body, userId)
              If response.Success Then
                System.Diagnostics.Debug.WriteLine("? College change notification sent successfully")
                Return True
            Else
                System.Diagnostics.Debug.WriteLine("? College change notification failed: " & response.Message)
                Return False
            End If
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("? Microservice notification error: " & ex.Message)
            Return False
        End Try
    End Function

    Private Function GetAdminEmail(userId As String) As String
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT email FROM pn_pengguna WHERE id_pg = ?"
                command.Parameters.AddWithValue("@id_pg", userId)
                
                Dim result As Object = command.ExecuteScalar()
                Return If(result IsNot Nothing AndAlso result IsNot DBNull.Value, result.ToString(), "")
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Get admin email error: " & ex.Message)
            Return ""
        Finally
            If connection IsNot Nothing AndAlso connection.State = Data.ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

#End Region

#Region "Enhanced GridView Events"

    Protected Sub Gd_RowCommand(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewCommandEventArgs) Handles Gd.RowCommand
        Try
            If e.CommandName = "EditCollege" Then
                LoadCollegeForEdit(e.CommandArgument.ToString())
            ElseIf e.CommandName = "DeleteCollege" Then
                DeleteCollege(e.CommandArgument.ToString())
            End If
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("GridView command error: " & ex.Message)
            ShowMessage("Error processing request.", "error")
        End Try
    End Sub

    Private Sub LoadCollegeForEdit(collegeId As String)
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT id_kolej, dc_kolej, jenis FROM pn_kolej WHERE id_kolej = ?"
                command.Parameters.AddWithValue("@id_kolej", collegeId)
                
                Using reader As OleDbDataReader = command.ExecuteReader()
                    If reader.Read() Then
                        Tx_Id.Text = reader("id_kolej").ToString()
                        Tx_Kolej.Text = reader("dc_kolej").ToString()
                        Cb_Jenis.SelectedIndex = Convert.ToInt32(reader("jenis"))
                        
                        ' Set edit mode
                        Session("PN_Pinda") = True
                        Tx_Id.ReadOnly = True
                        cmd_baru.Visible = True
                        
                        ShowMessage("Institution loaded for editing.", "info")
                    End If
                End Using
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Load college for edit error: " & ex.Message)
            ShowMessage("Error loading institution for editing.", "error")
        Finally
            If connection IsNot Nothing AndAlso connection.State = Data.ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Sub

    Private Sub DeleteCollege(collegeId As String)
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            ' Soft delete - set status to 0 instead of physical delete
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE pn_kolej SET status = 0, deleted_by = ?, deleted_date = ? WHERE id_kolej = ?"
                command.Parameters.AddWithValue("@deleted_by", Session("Id_PG").ToString())
                command.Parameters.AddWithValue("@deleted_date", DateTime.Now)
                command.Parameters.AddWithValue("@id_kolej", collegeId)
                
                Dim rowsAffected As Integer = command.ExecuteNonQuery()
                
                If rowsAffected > 0 Then
                    ' Send email notification if enabled
                    Dim emailSent As Boolean = False
                    If chkEmailNotification.Checked Then
                        emailSent = SendCollegeChangeNotification("DELETE", "Institution ID: " & collegeId)
                    End If
                    
                    ' Log successful deletion
                    LogSecurityEvent("COLLEGE_DELETED", Session("Id_PG").ToString(), collegeId)
                    
                    ' Show success message
                    Dim successMessage As String = "Institution has been successfully deleted!"
                    If chkEmailNotification.Checked AndAlso emailSent Then
                        successMessage &= " Notification email has been sent to administrators."
                    End If
                    
                    ShowMessage(successMessage, "success")
                    
                    ' Refresh data
                    Cari("")
                Else
                    ShowMessage("Failed to delete institution.", "error")
                End If
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Delete college error: " & ex.Message)
            ShowMessage("Error deleting institution: " & ex.Message, "error")
            LogSecurityEvent("COLLEGE_DELETE_ERROR", Session("Id_PG").ToString(), ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = Data.ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Sub

    Protected Sub Gd_PageIndexChanging(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewPageEventArgs) Handles Gd.PageIndexChanging
        Try
            Gd.PageIndex = e.NewPageIndex
            Cari("")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("GridView paging error: " & ex.Message)
        End Try
    End Sub

    Protected Sub Gd_Sorting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewSortEventArgs) Handles Gd.Sorting
        Try
            ' Implement sorting logic here if needed
            System.Diagnostics.Debug.WriteLine("Sorting by: " & e.SortExpression)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("GridView sorting error: " & ex.Message)
        End Try
    End Sub

    ' Legacy compatibility for existing row creation logic
    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        Try
            If e.Row.RowType = System.Web.UI.WebControls.DataControlRowType.DataRow Then
                ' Hide ID column if it exists
                If e.Row.Cells.Count > 3 Then
                    e.Row.Cells(3).Visible = False
                End If
                
                ' Set row numbering if needed
                If e.Row.RowIndex >= 0 Then
                    ' Enhanced row styling can be applied here
                End If
            End If
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Row creation error: " & ex.Message)
        End Try
    End Sub

    ' Legacy compatibility for selection change
    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        ' This method maintained for backward compatibility
        ' New functionality handled through RowCommand events
    End Sub

#End Region

#Region "Helper Methods and Utilities"

    Private Sub ShowMessage(message As String, messageType As String)
        messagePanel.Visible = True
        messageLabel.Text = message
        
        Select Case messageType.ToLower()
            Case "success"
                messagePanel.CssClass = "message-container message-success"
            Case "error"
                messagePanel.CssClass = "message-container message-error"
            Case "info"
                messagePanel.CssClass = "message-container message-info"
            Case Else
                messagePanel.CssClass = "message-container message-info"
        End Select
    End Sub

    Private Sub HideMessage()
        messagePanel.Visible = False
        messageLabel.Text = ""
    End Sub

    Private Sub LogSecurityEvent(eventType As String, userId As String, Optional details As String = "")
        Try
            System.Diagnostics.Debug.WriteLine("SECURITY EVENT: " & eventType & " | User: " & userId & " | Details: " & details & " | Time: " & DateTime.Now.ToString())
            
            ' Optional: Log to database or external security system
            ' LogToSecurityDatabase(eventType, userId, details)
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Security logging error: " & ex.Message)
        End Try
    End Sub

    ' Legacy compatibility wrapper
    Private Sub Msg(page As System.Web.UI.Page, message As String)
        ' Convert legacy Msg calls to new ShowMessage method
        If message.Contains("Disimpan") Then
            ShowMessage("Record has been saved successfully.", "success")
        ElseIf message.Contains("Ralat") Then
            ShowMessage("An error occurred while processing your request.", "error")
        ElseIf message.Contains("Telah Ada") Then
            ShowMessage("A record with this information already exists.", "error")
        Else
            ShowMessage(message, "info")
        End If
    End Sub

    ''' <summary>
    ''' Convert string to title case (.NET 3.5 compatible)
    ''' </summary>
    Private Function ToTitleCase(input As String) As String
        If String.IsNullOrEmpty(input) Then Return input
        
        Dim words As String() = input.Split(" "c)
        For i As Integer = 0 To words.Length - 1
            If words(i).Length > 0 Then
                words(i) = Char.ToUpper(words(i)(0)) + words(i).Substring(1).ToLower()
            End If
        Next
        Return String.Join(" ", words)
    End Function

#End Region

#Region "Configuration Properties"

    ' Database connection string properties
    Private ReadOnly Property ServerId() As String
        Get
            If Session("ServerId") IsNot Nothing Then
                Return Session("ServerId").ToString()
            End If
            Return System.Configuration.ConfigurationManager.ConnectionStrings("DefaultConnection").ConnectionString
        End Get
    End Property

    Private ReadOnly Property ServerId_SQL() As String
        Get
            If Session("ServerId_SQL") IsNot Nothing Then
                Return Session("ServerId_SQL").ToString()
            End If
            Return System.Configuration.ConfigurationManager.ConnectionStrings("SQLConnection").ConnectionString
        End Get
    End Property

#End Region

End Class
