﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class LP_XM
    Inherits System.Web.UI.Page

    Public Sub Cari_Baru(ByVal X As String, ByVal X2 As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "pelatih"
        Dim SQL As String = X + X2 '+ " and xc.mb=0 and xc.o=0 and xc.p=0 " ' dialih pada 15/05/2012
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
        If Gd.Rows.Count > 0 Then cmd_Cari2.Visible = True Else cmd_Cari2.Visible = False
    End Sub

    Public Sub Cari_Lama(ByVal X As String, ByVal X2 As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "pelatih"
        Dim SQL As String = X + " and (xc.mb<>0 or xc.o<>0 or xc.p<>0)" + X2
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd1.DataSource = List_Data.Tables(Tb)
        DataBind()
        If Gd1.Rows.Count > 0 Then cmd_Cari3.Visible = True Else cmd_Cari3.Visible = False
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        cmd_Cari2.Visible = False : cmd_Cari3.Visible = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where (jenis < 3) order by dc_kolej"        
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'BANGSA
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("(SEMUA)")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = ""
        Cb_Bangsa.Items.Add("MELAYU")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "1"
        Cb_Bangsa.Items.Add("CINA")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "2"
        Cb_Bangsa.Items.Add("INDIA")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "3"
        Cb_Bangsa.Items.Add("LAIN-LAIN")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "4"

        'JANTINA
        Cb_Jantina.Items.Clear()
        Cb_Jantina.Items.Add("(SEMUA)")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = ""
        Cb_Jantina.Items.Add("LELAKI")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "1"
        Cb_Jantina.Items.Add("PEREMPUAN")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "2"

        'JAWATAN
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("(SEMUA)")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
        Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
        Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
        Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
        Cb_Kursus.Items.Add("KEBIDANAN I")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim SQL, SQL_pilih, SQL_order, SQL_Replace, X, Id_XM As String
        Dim Jwtn, Jwtn2, Jwtn3 As Integer

        cmd_Cari2.Visible = False : cmd_Cari3.Visible = False
        X = "" : Id_XM = ""
        If Cb_Kolej.SelectedIndex > 0 Then X += " where p.id_kolej=" & Cb_Kolej.SelectedValue
        If Cb_Kursus.SelectedIndex > 0 Then
            If X.Length = 0 Then X += " where " Else X += " and "
            X += " j_xm=" & Cb_Kursus.SelectedIndex 
        End If
        If Tx_Tahun.Text <> "" And Tx_Siri.Text <> "" Then
            If X.Length = 0 Then
                X += " where tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            Else
                X += " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            End If
        End If

        If X = "" Then
        Else
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "select top 1 px.id_xm from xm_calon xc inner join pn_xm px on xc.id_xm=px.id_xm inner join pelatih p on xc.nokp=p.nokp " & _
                  "left join pn_kolej pk on pk.id_kolej=p.id_kolej " & X & " order by tahun desc, siri desc, j_xm desc"
            Cmd.CommandText = SQL : Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then Id_XM = Rdr(0) : X = "and xc.id_xm=" & Id_XM Else X = "" : Exit Sub
            Rdr.Close() : Cn.Close()
        End If

        SQL = "select"
        SQL_pilih = "" : SQL_Replace = ""
        SQL_order = " order by "
        If Chk_Pilih.Items(0).Selected Then SQL_pilih += " stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'AG',"
        If Chk_Pilih.Items(1).Selected Then SQL_pilih += " NAMA,"
        If Chk_Pilih.Items(2).Selected Then SQL_pilih += " p.nokp as 'NO.KP',"
        If Chk_Pilih.Items(3).Selected Then SQL_pilih += " case jantina when 1 then 'L' when 2 then 'P' else '' end as 'JANTINA',"
        If Chk_Pilih.Items(4).Selected Then SQL_pilih += " case bangsa when 1 then 'M' when 2 then 'C' when 3 then 'I' else 'L' end as 'BANGSA' ,"
        If Chk_Pilih.Items(5).Selected Then SQL_pilih += " pw.dc_negara 'WARGANEGARA',"
        If Chk_Pilih.Items(6).Selected Then SQL_pilih += " pk1.dc_kolej as 'TEMPAT LATIHAN',"
        If Chk_Pilih.Items(7).Selected Then SQL_pilih += " dc_tajaan 'TAJAAN',"
        If Chk_Pilih.Items(8).Selected Then SQL_pilih += " case p.j_kursus when 1 then 'JB' when 2 then 'JM' when 3 then 'PJ' when 4 then 'KB1' when 5 then 'JB (PRE)' when 8 then 'JB (KPSL)' end as 'JENIS KURSUS',"
        If Chk_Pilih.Items(9).Selected Then SQL_pilih += " cast(sesi_bulan as varchar(2)) + '/' + cast(sesi_tahun as varchar(4)) 'SESI',"
        If Chk_Pilih.Items(10).Selected Then SQL_pilih += " CONVERT(char(12), tkh_latihan_mula, 103) 'TKH MULA LATIHAN',"
        If Chk_Pilih.Items(11).Selected Then SQL_pilih += "CONVERT(char(12), tkh_latihan_tamat, 103) 'TKH TAMAT LATIHAN',"
        If Chk_Pilih.Items(12).Selected Then
            If Cb_Kursus.SelectedIndex = 1 Then
                SQL_pilih += " CONVERT(char(12), px.t1_tkh, 103) 'TKH PEPERIKSAAN',"
            ElseIf Cb_Kursus.SelectedIndex = 2 Then
                SQL_pilih += " CONVERT(char(12), px.t1_tkh, 103) 'TKH PEPERIKSAAN',"
            ElseIf Cb_Kursus.SelectedIndex = 3 Then
                SQL_pilih += " CONVERT(char(12), px.t1_tkh, 103) 'TKH PEPERIKSAAN',"
            ElseIf Cb_Kursus.SelectedIndex = 4 Then
                If (Tx_Tahun.Text <> "" And Tx_Siri.Text <> "") Then

                    If ((Tx_Tahun.Text < 2012 Or (Tx_Tahun.Text = 2012 And (Tx_Siri.Text = 1 Or Tx_Siri.Text = 2)))) Then ' ditukar pada 15/05/2012
                        SQL_pilih += " CONVERT(char(12), px.t1_tkh, 103) 'TKH PEPERIKSAAN K1',CONVERT(char(12), px.o_tkh, 103) 'TKH PEPERIKSAAN OSCE',CONVERT(char(12), px.v_tkh, 103) 'TKH PEPERIKSAAN VIVA',"
                    Else
                        SQL_pilih += " CONVERT(char(12), px.t1_tkh, 103) 'TKH PEPERIKSAAN',"
                    End If
                Else
                    SQL_pilih += " CONVERT(char(12), px.t1_tkh, 103) 'TKH PEPERIKSAAN',"

                End If

            End If
        End If
        If Chk_Pilih.Items(13).Selected Then
            If Cb_Kursus.SelectedIndex = 1 Then
                'IMPROVENT SQL RESULT 'T' ASSIGN RECAST VALUE  RN 15112021-OSH
                SQL_pilih += "case xc.keputusan when '' then '0' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6)) end  as 'JUMLAH MARKAH',"
                'IMPROVENT SQL RESULT 'T' ASSIGN RECAST VALUE  RN 27092012-OSH
                'SQL_pilih += "case xc.keputusan when '' then '0.00' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6)) end  as 'JUMLAH MARKAH',"
                'Comment Ori 27092012 -OSH                
                'SQL_pilih += "case xc.keputusan when 'T' then '' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(xc.markah_jum as decimal(6,1))  end  as 'JUMLAH MARKAH',"
            ElseIf Cb_Kursus.SelectedIndex = 2 Then
                'IMPROVENT SQL RESULT 'T' ASSIGN 0.00 VALUE  CN 15112021-OSH
                SQL_pilih += "case xc.keputusan when 'T' then '0' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',"
                'IMPROVENT SQL RESULT 'T' ASSIGN 0.00 VALUE  CN 27092012-OSH
                'SQL_pilih += "case xc.keputusan when 'T' then '0.00' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',"
                'Comment Ori 27092012 -OSH                 
                'SQL_pilih += "case xc.keputusan when 'T' then '' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(xc.markah_jum as decimal(6,1))  end   as 'JUMLAH MARKAH',"
            ElseIf Cb_Kursus.SelectedIndex = 3 Then
                'IMPROVENT SQL RESULT 'T' ASSIGN 0.00 VALUE AN 15112021-OSH
                SQL_pilih += "case xc.keputusan when 'T' then '0' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',"
                'IMPROVENT SQL RESULT 'T' ASSIGN 0.00 VALUE AN 27092012-OSH
                'SQL_pilih += "case xc.keputusan when 'T' then '0.00' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',"
                'Comment Ori 27092012 -OSH 
                'SQL_pilih += "case xc.keputusan when 'T' then '' else xc.rt end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(xc.markah_jum as decimal(6,1))  end   as 'JUMLAH MARKAH',"
            ElseIf Cb_Kursus.SelectedIndex = 4 Then
                'IMPROVENT SQL RESULT 'T' ASSIGN 0.00 VALUE  MID 15112021-OSH
                SQL_pilih += "case xc.keputusan when 'T' then '0' else cast(xc.rt as varchar(3)) end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',"
                'IMPROVENT SQL RESULT 'T' ASSIGN 0.00 VALUE  MID 27092012-OSH
                'SQL_pilih += "case xc.keputusan when 'T' then '0.00' else cast(xc.rt as varchar(3)) end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',"
                'Comment Ori 27092012 -OSH 
                'SQL_pilih += "case xc.keputusan when 'T' then '' else cast(xc.rt as varchar(3)) end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',"
                If (Tx_Tahun.Text <> "" And Tx_Siri.Text <> "") Then
                    If ((Tx_Tahun.Text < 2012 Or (Tx_Tahun.Text = 2012 And (Tx_Siri.Text = 1 Or Tx_Siri.Text = 2)))) Then ' ditukar pada 15/05/2012
                        SQL_Replace = "cast((cast((xc.k1_mcq/30)*20 as decimal(6,1))+xc.k1_meq + xc.k1_meq2 + xc.k1_meq3 + xc.k1_meq4)*0.6 as decimal(6,1)) as 'P. TEORI(60%)', " & _
                                     "xc.mb as 'M/B TEORI(40%)', " & _
                                     "cast(((cast((xc.k1_mcq/30)*20 as decimal(6,1))+xc.k1_meq + xc.k1_meq2 + xc.k1_meq3 + xc.k1_meq4)*0.6) + xc.mb as decimal(6,1)) as 'JUMLAH TEORI(100%)'," & _
                                     "xc.o as 'OSCE(60%)', " & _
                                     "xc.p as 'M/B PRAKTIKAL(40%)', " & _
                                     "xc.o + xc.p as 'JUMLAH PRAKTIKAL(100%)'," & _
                                     "cast(((cast((xc.k1_mcq/30)*20 as decimal(6,1))+xc.k1_meq + xc.k1_meq2 + xc.k1_meq3 + xc.k1_meq4)*0.6) + xc.mb + xc.o + xc.p as decimal(6,1)) as 'JUMLAH BESAR'," & _
                                     "cast((cast((cast((xc.k1_mcq/30)*20 as decimal(6,1))+xc.k1_meq + xc.k1_meq2 + xc.k1_meq3 + xc.k1_meq4)*0.6 as decimal(6,1)) + xc.mb + xc.o + xc.p)/2 as decimal(6,1)) as 'JUMLAH MARKAH',"
                    End If
                End If
            End If
        End If
        If Chk_Pilih.Items(14).Selected Then SQL_pilih += " case xc.keputusan when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' else '' end as 'KEPUTUSAN',"
        If Chk_Pilih.Items(15).Selected Then SQL_pilih += " pk1.dc_kolej as 'PUSAT PEPERIKSAAN ASAL',"
        If Chk_Pilih.Items(16).Selected Then SQL_pilih += " pk2.dc_kolej as 'PUSAT PEPERIKSAAN TUMPANG',"
        If Chk_Pilih.Items(17).Selected Then SQL_pilih += " case when xc.markah_jum between 80 and 100 then 'A' when xc.markah_jum between 75 and 79.99 then 'A-' when xc.markah_jum between 70 and 74.99 then 'B+' when xc.markah_jum between 65 and 69.99 then 'B' when xc.markah_jum between 60 and 64.99 then 'B-' when xc.markah_jum between 55 and 59.99 then 'C+' when xc.markah_jum between 50 and 54.99 then 'C' when xc.markah_jum between 47 and 49.99 then 'C-' when xc.markah_jum between 44 and 46.99 then 'D+' else 'D' end as 'GRED',"
        If Len(SQL_pilih) > 0 Then SQL_pilih = Mid(SQL_pilih, 1, Len(SQL_pilih) - 1) : SQL += SQL_pilih

        SQL += " from xm_calon xc "
        SQL += " inner join pn_xm px on px.id_xm=xc.id_xm "
        SQL += " inner join pelatih p on p.nokp=xc.nokp  and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM"
        SQL += " left outer join pn_tajaan pt on p.tajaan=pt.id_tajaan "
        SQL += " left outer join pn_negara pw on p.warganegara=pw.id_negara "
        SQL += " left outer join pn_kolej pk1 on p.id_kolej=pk1.id_kolej "
        SQL += " left outer join pn_kolej pk2 on xc.id_pusat=pk2.id_kolej "
        SQL += " where p.nokp is not null " & X & ""

        If Cb_Kolej.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and p.id_kolej = " & Cb_Kolej.SelectedValue
        End If

        If Cb_Kursus.SelectedIndex < 1 Then
            SQL += ""
        Else
            If Cb_Kursus.SelectedIndex = 1 Then Jwtn = 1 : Jwtn2 = 5 : Jwtn3 = 8
            If Cb_Kursus.SelectedIndex = 2 Then Jwtn = 2 : Jwtn2 = 2 : Jwtn3 = 2
            If Cb_Kursus.SelectedIndex = 3 Then Jwtn = 3 : Jwtn2 = 3 : Jwtn3 = 3
            If Cb_Kursus.SelectedIndex = 4 Then Jwtn = 4 : Jwtn2 = 4 : Jwtn3 = 4
            SQL += " and (p.j_kursus = " & Jwtn & " or p.j_kursus = " & Jwtn2 & " or p.j_kursus = " & Jwtn3 & ")"
        End If

        If Cb_Jantina.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and p.jantina = " & Cb_Jantina.SelectedValue
        End If

        If Cb_Bangsa.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and p.bangsa = " & Cb_Bangsa.SelectedValue
        End If

        If Tx_AG1.Text.Trim = "" Then
        Else
            If Tx_AG2.Text.Trim = "" Then
            Else
                If Not IsNumeric(Tx_AG1.Text) Or Not IsNumeric(Tx_AG2.Text) Then Exit Sub
                SQL += " and xc.ag between " & Tx_AG1.Text & " and " & Tx_AG2.Text
            End If
        End If

        If Chk_Pilih.Items(0).Selected Then SQL_order += " cast(xc.ag as integer),"
        If Len(SQL_order) > 0 Then SQL_order = Mid(SQL_order, 1, Len(SQL_order) - 1) ' : SQL += SQL_order

        Try
            If SQL_Replace = "" Then
                Cari_Baru(SQL, SQL_order) ' dialih pada 15/05/2012
            Else
                SQL = Replace(SQL, "case xc.keputusan when 'T' then '' else cast(xc.rt as varchar(3)) end as 'RESPON TUNGGAL', case xc.keputusan when 'T' then '' else cast(cast(xc.markah_jum as decimal(6,1)) as varchar(6))  end   as 'JUMLAH MARKAH',", SQL_Replace)
                Cari_Lama(SQL, SQL_order)
            End If
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        GridViewExportUtil.Export("Laporan.xls", Gd)        
    End Sub

    Protected Sub cmd_Cari3_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari3.Click
        GridViewExportUtil.Export("Laporan.xls", Gd1)
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub Gd1_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd1.RowCreated
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub
End Class