﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.Text

Partial Public Class WebForm67
    Inherits System.Web.UI.Page
    
    ' Email service integration
    Private ReadOnly emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl") 
    Private emailClient As EmailServiceClient

    Function Blank(ByVal Tx As TextBox)
        Blank = False
        If Tx.Text.Trim = "" Then Blank = True
    End Function

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        ' Initialize email service client
        Try
            If Not String.IsNullOrEmpty(emailServiceUrl) Then
                emailClient = New EmailServiceClient(emailServiceUrl)
            End If
        Catch ex As Exception
            ' Email service not available - continue without it
            System.Diagnostics.Debug.WriteLine("Email service not available: " & ex.Message)
        End Try

        Dim x As Menu = Master.FindControl("Menu1")
        x.Visible = False

        Dim x1 As String = ""
        Dim Fmt_Tkh = "<a style='font-family: Arial; font-size: 8pt; color: #CC0000; font-weight: bolder'>"
        Dim Fmt_By = "</a><br/><a style='font-family: Arial; font-size: 8pt; color: #006699;'>"
        Dim Fmt_Note = "</a><br/><a style='font-family: Arial; font-size: 8pt; color: #000000;'>"
        Dim Fmt_End = "</a><br/><br/>"

        'x1 += Fmt_Tkh
        'x1 += "[ Wed 13/8/2009 5:39PM ]"
        'x1 += Fmt_By
        'x1 += "UNIT PEPERIKSAAN:"
        'x1 += Fmt_Note
        'x1 += "Sila pastikan anda telah mendaftar kesemua pelatih yang akan mengambil tempat dalam Peperiksaan Akhir Kebidanan Bahagian I. Tarikh tutup adalah pada 31/12/2010."
        'x1 += Fmt_End

        'x1 += Fmt_Tkh
        'x1 += "[ Fri 31/12/2009 12:45PM ]"
        'x1 += Fmt_By
        'x1 += "SYSTEM ADMIN:"
        'x1 += Fmt_Note
        'x1 += "Sebarang pertanyaan berhubung penggunaan sistem ini, sila hubungi:<br/>"
        'x1 += "BAHAGIAN KEJURURAWATAN<br/>"
        'x1 += "KEMENTERIAN KESIHATAN MALAYSIA<br/>"
        'x1 += "Tel : 03-8883 0000<br/>"
        'x1 += Fmt_End


        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select * from kj_mesej order by TKH_MESEJ desc"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            x1 += Fmt_Tkh
            x1 += "[ " & Format(Rdr(0)) & " ]"
            x1 += Fmt_By
            x1 += Rdr(1)
            x1 += Fmt_Note
            x1 += Rdr(2)
            x1 += Fmt_End
        End While
        Rdr.Close()
        Cn.Close()

        Label1.Text = x1
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button1.Click
        If Blank(Tx_Id) Then LbL.Text = "Sila Isi Kod Pengguna!" : Tx_Id.Focus() : Exit Sub
        If Blank(Tx_Pwd) Then LbL.Text = "Sila Isi Kata Laluan!" : Tx_Pwd.Focus() : Exit Sub

        If Chk_SQL(Tx_Id.Text) = True Then Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub
        If Chk_SQL(Tx_Pwd.Text) = True Then Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub

        ' Secure authentication with password hashing and OTP integration
        Try
            If AuthenticateUserSecure(Tx_Id.Text.Trim(), Tx_Pwd.Text) Then
                ' Authentication successful - redirect to main system
                Response.Redirect("blank.aspx")
            Else
                LbL.Text = "Kesalahan pada Kod Pengguna/Kata Laluan!"
                LogFailedLoginAttempt(Tx_Id.Text.Trim())
            End If
        Catch ex As Exception
            LbL.Text = "Ralat sistem berlaku. Sila cuba lagi."
            LogError("Login_Button1_Click", ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Secure user authentication with password hashing support
    ''' Supports both legacy plain text and new encrypted passwords
    ''' Includes automatic migration and OTP integration
    ''' </summary>
    Private Function AuthenticateUserSecure(userId As String, password As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim command As OleDbCommand = Nothing
        Dim reader As OleDbDataReader = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            ' First, get user information with enhanced security check
            command = New OleDbCommand()
            command.Connection = connection
            command.CommandText = "SELECT kjp.id_pg, kjp.pwd, kjp.salt, kjp.email, kjp.status, kjp.pwd_encrypted, kjp.failed_login_attempts, kjp.account_locked, pnk.id_kolej, pnk.dc_kolej " &
                                "FROM kj_pengguna kjp INNER JOIN pn_kolej pnk ON kjp.id_kolej = pnk.id_kolej " &
                                "WHERE kjp.id_pg = ? AND kjp.status = 1"
            command.Parameters.AddWithValue("@id_pg", userId)
            
            reader = command.ExecuteReader()
            
            If reader.Read() Then
                Dim storedPassword As String = GetSafeStringValue(reader, "pwd")
                Dim salt As String = GetSafeStringValue(reader, "salt")
                Dim email As String = GetSafeStringValue(reader, "email")
                Dim isEncrypted As Boolean = GetSafeBoolValue(reader, "pwd_encrypted")
                Dim failedAttempts As Integer = GetSafeIntValue(reader, "failed_login_attempts")
                Dim isLocked As Boolean = GetSafeBoolValue(reader, "account_locked")
                
                ' Check if account is locked
                If isLocked Then
                    LbL.Text = "Akaun telah dikunci. Sila hubungi pentadbir sistem."
                    Return False
                End If
                
                ' Check failed login attempts (lock after 5 attempts)
                If failedAttempts >= 5 Then
                    LockUserAccount(userId, connection)
                    LbL.Text = "Akaun telah dikunci kerana terlalu banyak percubaan log masuk yang gagal."
                    Return False
                End If
                
                Dim passwordMatch As Boolean = False
                
                ' Check password based on encryption status
                If isEncrypted AndAlso Not String.IsNullOrEmpty(salt) Then
                    ' Use secure hash verification
                    passwordMatch = PasswordHelper.VerifyPassword(password, storedPassword, salt)
                Else
                    ' Legacy plain text check with automatic migration
                    If storedPassword = password Then
                        passwordMatch = True
                        ' Migrate to encrypted password
                        MigrateUserPasswordSecure(userId, password, connection)
                    End If
                End If
                
                If passwordMatch Then
                    ' Reset failed login attempts on successful login
                    ResetFailedLoginAttempts(userId, connection)
                    
                    ' Set session variables
                    Session("Id_PG") = reader("id_pg").ToString()
                    Session("PWD") = "" ' Don't store password in session
                    Session("Id_KOLEJ") = reader("id_kolej").ToString()
                    Session("Dc_KOLEJ") = reader("dc_kolej").ToString()
                    Session("ORIGIN") = "yes"
                    
                    ' Check if OTP is enabled and user has email
                    If Not String.IsNullOrEmpty(email) AndAlso emailClient IsNot Nothing Then
                        Try
                            ' Check if email service is available
                            If emailClient.CheckHealth() Then
                                ' Store login session temporarily and redirect to OTP verification
                                Session("TEMP_USER_ID") = userId
                                Session("TEMP_ID_KOLEJ") = reader("id_kolej").ToString()
                                Session("TEMP_DC_KOLEJ") = reader("dc_kolej").ToString()
                                Session("TEMP_ORIGIN") = "yes"
                                
                                ' Generate OTP
                                Dim otpResponse = emailClient.GenerateOTP(userId, email, "LOGIN")
                                If otpResponse.Success Then
                                    ' Redirect to OTP verification page
                                    Response.Redirect("OtpVerification.aspx")
                                    Return False ' Don't complete login yet
                                End If
                            End If
                        Catch ex As Exception
                            ' Email service not available - continue with normal login
                            LogError("OTP_Generation_Failed", ex.Message)
                        End Try
                    End If
                    
                    ' Log successful login
                    LogSuccessfulLogin(userId)
                    Return True
                Else
                    ' Increment failed login attempts
                    IncrementFailedLoginAttempts(userId, connection)
                    Return False
                End If
            Else
                Return False
            End If
            
        Catch ex As Exception
            LogError("AuthenticateUserSecure", ex.Message)
            Return False
        Finally
            If reader IsNot Nothing Then reader.Close()
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then connection.Close()
        End Try
    End Function

#Region "Security Helper Methods"

    ''' <summary>
    ''' Migrate user password from plain text to encrypted hash
    ''' </summary>
    Private Sub MigrateUserPasswordSecure(userId As String, plainPassword As String, connection As OleDbConnection)
        Try
            ' Check if database supports enhanced security columns
            If PasswordHelper.SupportsEnhancedSecurity(connection) Then
                PasswordHelper.MigrateUserPassword(userId, plainPassword, connection)
            Else
                ' Basic migration - just hash the password
                Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(plainPassword)
                Dim hashedPassword As String = passwordEntry(0)
                
                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "UPDATE kj_pengguna SET pwd = ? WHERE id_pg = ?"
                    command.Parameters.AddWithValue("@pwd", hashedPassword)
                    command.Parameters.AddWithValue("@id_pg", userId)
                    command.ExecuteNonQuery()
                End Using
            End If
        Catch ex As Exception
            LogError("MigrateUserPasswordSecure", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Lock user account due to failed login attempts
    ''' </summary>
    Private Sub LockUserAccount(userId As String, connection As OleDbConnection)
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET account_locked = 1, locked_date = ? WHERE id_pg = ?"
                command.Parameters.AddWithValue("@locked_date", DateTime.Now)
                command.Parameters.AddWithValue("@id_pg", userId)
                command.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            LogError("LockUserAccount", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Reset failed login attempts counter
    ''' </summary>
    Private Sub ResetFailedLoginAttempts(userId As String, connection As OleDbConnection)
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET failed_login_attempts = 0, last_successful_login = ? WHERE id_pg = ?"
                command.Parameters.AddWithValue("@last_successful_login", DateTime.Now)
                command.Parameters.AddWithValue("@id_pg", userId)
                command.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            LogError("ResetFailedLoginAttempts", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Increment failed login attempts counter
    ''' </summary>
    Private Sub IncrementFailedLoginAttempts(userId As String, connection As OleDbConnection)
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET failed_login_attempts = ISNULL(failed_login_attempts, 0) + 1, last_failed_login = ? WHERE id_pg = ?"
                command.Parameters.AddWithValue("@last_failed_login", DateTime.Now)
                command.Parameters.AddWithValue("@id_pg", userId)
                command.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            LogError("IncrementFailedLoginAttempts", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Log successful login for audit trail
    ''' </summary>
    Private Sub LogSuccessfulLogin(userId As String)
        Try
            System.Diagnostics.Debug.WriteLine($"Successful login: {userId} at {DateTime.Now}")
            ' Additional logging can be implemented here
        Catch ex As Exception
            LogError("LogSuccessfulLogin", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Log failed login attempt for security monitoring
    ''' </summary>
    Private Sub LogFailedLoginAttempt(userId As String)
        Try
            System.Diagnostics.Debug.WriteLine($"Failed login attempt: {userId} at {DateTime.Now}")
            ' Additional logging can be implemented here
        Catch ex As Exception
            LogError("LogFailedLoginAttempt", ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' Log error for debugging and monitoring
    ''' </summary>
    Private Sub LogError(functionName As String, errorMessage As String)
        Try
            System.Diagnostics.Debug.WriteLine($"Error in {functionName}: {errorMessage} at {DateTime.Now}")
            ' Additional error logging can be implemented here
        Catch
            ' Suppress errors in logging to prevent infinite loops
        End Try
    End Sub

#End Region

#Region "Database Helper Methods"

    ''' <summary>
    ''' Safely get string value from database reader
    ''' </summary>
    Private Function GetSafeStringValue(reader As OleDbDataReader, columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return ""
            Else
                Return reader(columnName).ToString()
            End If
        Catch
            Return ""
        End Try
    End Function
    
    ''' <summary>
    ''' Safely get boolean value from database reader
    ''' </summary>
    Private Function GetSafeBoolValue(reader As OleDbDataReader, columnName As String) As Boolean
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return False
            Else
                Return Convert.ToBoolean(reader(columnName))
            End If
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Safely get integer value from database reader
    ''' </summary>
    Private Function GetSafeIntValue(reader As OleDbDataReader, columnName As String) As Integer
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return 0
            Else
                Return Convert.ToInt32(reader(columnName))
            End If
        Catch
            Return 0
        End Try
    End Function

#End Region

End Class