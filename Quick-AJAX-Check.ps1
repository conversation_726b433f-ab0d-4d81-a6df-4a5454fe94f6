# Final AJAX Controls Verification Script
Write-Host "=== FINAL AJAX CONTROLS VERIFICATION ===" -ForegroundColor Green

$projectPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ"
$issuesFound = 0

Write-Host "Checking for any remaining AJAX controls..." -ForegroundColor Yellow

# Check for ScriptManager
Write-Host "`n1. Checking for ScriptManager controls..." -ForegroundColor Cyan
$scriptManagers = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse | Select-String "asp:ScriptManager"
if ($scriptManagers) {
    Write-Host "❌ Found ScriptManager controls" -ForegroundColor Red
    $issuesFound++
} else {
    Write-Host "✅ No ScriptManager controls found" -ForegroundColor Green
}

# Check for UpdatePanel
Write-Host "`n2. Checking for UpdatePanel controls..." -ForegroundColor Cyan
$updatePanels = Get-ChildItem $projectPath -Filter "*.aspx" -Recurse | Select-String "asp:UpdatePanel"
if ($updatePanels) {
    Write-Host "❌ Found UpdatePanel controls" -ForegroundColor Red
    $issuesFound++
} else {
    Write-Host "✅ No UpdatePanel controls found" -ForegroundColor Green
}

# Check critical files
Write-Host "`n3. Verifying critical files..." -ForegroundColor Cyan
$criticalFiles = @("Login_J.aspx", "Login.aspx", "Pwd.aspx")

foreach ($file in $criticalFiles) {
    $filePath = Join-Path $projectPath $file
    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        if ($content -match "asp:(ScriptManager|UpdatePanel)") {
            Write-Host "❌ AJAX controls found in: $file" -ForegroundColor Red
            $issuesFound++
        } else {
            Write-Host "✅ $file is clean" -ForegroundColor Green
        }
    }
}

# Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Green
if ($issuesFound -eq 0) {
    Write-Host "✅ SUCCESS: All AJAX controls removed!" -ForegroundColor Green
    Write-Host "✅ Parser error should be resolved." -ForegroundColor Green
} else {
    Write-Host "❌ Issues found: $issuesFound" -ForegroundColor Red
}
