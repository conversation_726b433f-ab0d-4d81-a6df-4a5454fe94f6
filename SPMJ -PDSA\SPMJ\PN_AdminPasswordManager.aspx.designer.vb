'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:3.5.30729.9179
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

''' <summary>
''' PN_AdminPasswordManager Designer File
''' .NET Framework 3.5.1 Compatible Implementation
''' Auto-generated control declarations for Advanced Password Management System
''' Features: SHA256+Salt ONLY encryption, Email microservice integration
''' </summary>
''' <remarks>
''' This file contains auto-generated control declarations.
''' Manual modifications to this file will be lost during regeneration.
''' To customize controls, modify the code-behind file instead.
''' Compatible with .NET Framework 3.5.1 and VB.NET 9.0
''' </remarks>
Partial Public Class PN_AdminPasswordManager

#Region "Message and Status Controls"

    ''' <summary>
    ''' Main message panel for system notifications
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Used for displaying success, error, warning, and info messages to admin users.
    ''' </remarks>
    Protected WithEvents pnlMessage As Global.System.Web.UI.WebControls.Panel

    ''' <summary>
    ''' HTML container for message styling and CSS classes
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Provides CSS class assignment for different message types.
    ''' </remarks>
    Protected WithEvents divMessage As Global.System.Web.UI.HtmlControls.HtmlGenericControl

    ''' <summary>
    ''' Label control for displaying message text content
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Contains the actual message text for admin notifications.
    ''' </remarks>
    Protected WithEvents lblMessage As Global.System.Web.UI.WebControls.Label

#End Region

#Region "User Search Controls"

    ''' <summary>
    ''' Text input for user ID search functionality
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Allows admin to enter user ID for password management operations.
    ''' </remarks>
    Protected WithEvents txtSearchUser As Global.System.Web.UI.WebControls.TextBox

    ''' <summary>
    ''' Button to trigger user search operation
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Initiates database search for specified user ID.
    ''' </remarks>
    Protected WithEvents btnSearchUser As Global.System.Web.UI.WebControls.Button

#End Region

#Region "User Information Display Controls"

    ''' <summary>
    ''' Panel container for user information display
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Contains all user detail labels and information display.    ''' </remarks>
    Protected WithEvents pnlUserInfo As Global.System.Web.UI.WebControls.Panel

    ''' <summary>
    ''' Label to display the found user's ID
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows the user ID that was searched and found in the system.
    ''' </remarks>
    Protected WithEvents lblUserId As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display user account status (Active/Inactive)
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Indicates whether the user account is currently active or disabled.
    ''' </remarks>
    Protected WithEvents lblUserStatus As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display the user's full name
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows the full name associated with the user account.
    ''' </remarks>
    Protected WithEvents lblUserName As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display user's assigned module or department
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows which system module or department the user belongs to.
    ''' </remarks>
    Protected WithEvents lblUserModule As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display user's email address
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows the email address for password notification purposes.
    ''' </remarks>
    Protected WithEvents lblUserEmail As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display user's access level or permissions
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows the user's permission level within the system.
    ''' </remarks>
    Protected WithEvents lblUserAccess As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display user's last login date and time
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows when the user last successfully logged into the system.
    ''' </remarks>
    Protected WithEvents lblLastLogin As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display the date of last password change
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows when the user's password was last modified.
    ''' </remarks>
    Protected WithEvents lblPasswordDate As Global.System.Web.UI.WebControls.Label

#End Region

#Region "Password Management Controls"

    ''' <summary>
    ''' Panel container for password management actions
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Contains all password-related controls and action buttons.
    ''' </remarks>
    Protected WithEvents pnlPasswordActions As Global.System.Web.UI.WebControls.Panel

    ''' <summary>
    ''' Text input for user's email address for notifications
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.    ''' To modify move field declaration from designer file to code-behind file.
    ''' Allows admin to enter or modify email for password notifications.
    ''' </remarks>
    Protected WithEvents txtUserEmail As Global.System.Web.UI.WebControls.TextBox

    ''' <summary>
    ''' Radio button for selecting temporary password generation
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Allows admin to choose temporary password with forced change on login.
    ''' </remarks>
    Protected WithEvents rbTempPassword As Global.System.Web.UI.WebControls.RadioButton

    ''' <summary>
    ''' Radio button for selecting permanent password generation
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Allows admin to choose permanent password that doesn't require change.
    ''' </remarks>
    Protected WithEvents rbPermanentPassword As Global.System.Web.UI.WebControls.RadioButton

    ''' <summary>
    ''' Text input for custom password entry
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Allows admin to set a specific custom password for the user.
    ''' </remarks>
    Protected WithEvents txtCustomPassword As Global.System.Web.UI.WebControls.TextBox

    ''' <summary>
    ''' Checkbox to enable email notification sending
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Controls whether password notification emails are sent via microservice.
    ''' </remarks>
    Protected WithEvents chkSendEmail As Global.System.Web.UI.WebControls.CheckBox

    ''' <summary>
    ''' Button to execute password setting operation with SHA256+Salt encryption
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Triggers password update with SHA256+Salt ONLY encryption.
    ''' </remarks>
    Protected WithEvents btnSetPassword As Global.System.Web.UI.WebControls.Button

    ''' <summary>
    ''' Button to force immediate password reset with temporary password
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Forces password reset requiring user to change on next login.
    ''' </remarks>
    Protected WithEvents btnForceReset As Global.System.Web.UI.WebControls.Button

    ''' <summary>
    ''' Button to generate temporary password for new users
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Generates secure temporary password for first-time user setup.
    ''' </remarks>
    Protected WithEvents btnGenerateTemp As Global.System.Web.UI.WebControls.Button

#End Region

#Region "Password Result Display Controls"

    ''' <summary>
    ''' Panel container for displaying password generation results
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Contains the generated password display and type information.
    ''' </remarks>
    Protected WithEvents pnlPasswordResult As Global.System.Web.UI.WebControls.Panel

    ''' <summary>
    ''' Label to display the generated password (SHA256+Salt encrypted)
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows the plain-text password before SHA256+Salt encryption.
    ''' </remarks>
    Protected WithEvents lblGeneratedPassword As Global.System.Web.UI.WebControls.Label

    ''' <summary>
    ''' Label to display the type of password generated
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Indicates whether password is temporary or permanent.
    ''' </remarks>
    Protected WithEvents lblPasswordType As Global.System.Web.UI.WebControls.Label

#End Region

#Region "Email Status Display Controls"

    ''' <summary>
    ''' Panel container for email notification status display
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.    ''' Shows success or failure status of email microservice integration.
    ''' </remarks>
    Protected WithEvents pnlEmailStatus As Global.System.Web.UI.WebControls.Panel

    ''' <summary>
    ''' HTML container for email status styling and CSS classes
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Provides CSS class assignment for email success/failure styling.
    ''' </remarks>
    Protected WithEvents divEmailStatus As Global.System.Web.UI.HtmlControls.HtmlGenericControl

    ''' <summary>
    ''' Label to display email notification status message
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Shows detailed status of email microservice communication results.    ''' </remarks>
    Protected WithEvents lblEmailStatus As Global.System.Web.UI.WebControls.Label

#End Region

#Region "Diagnostic Controls"

    ''' <summary>
    ''' Diagnostic button for troubleshooting password update issues
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Runs comprehensive diagnostic tests to identify password update failures.
    ''' </remarks>
    Protected WithEvents btnRunDiagnostic As Global.System.Web.UI.WebControls.Button

    ''' <summary>
    ''' Debug information button for troubleshooting
    ''' .NET Framework 3.5.1 Compatible
    ''' </summary>
    ''' <remarks>
    ''' Auto-generated field.
    ''' To modify move field declaration from designer file to code-behind file.
    ''' Displays comprehensive debugging information for troubleshooting.
    ''' </remarks>
    Protected WithEvents btnShowDebugInfo As Global.System.Web.UI.WebControls.Button

#End Region

End Class
