﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class PN_Pengguna_Kolej
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'comment on 24112012 - sql ambiguous column name 'status' error
        'SQL = "select id_pg as 'ID PENGGUNA', '**********' as 'KATA LALUAN', NAMA, pk.dc_kolej 'KOLEJ', case status when 1 then 'A' else 'TA' end as 'STATUS' from kj_pengguna kp inner join pn_kolej pk on kp.id_kolej=pk.id_kolej order by id_pg"
        'Comment Original 10082018 -OSH
        'Add Improve sql revive sql query after alter of table PN_KOLEJ for BLESS PROJECT (update 23112012 root of cause) 24122012
        'SQL = "select kp.Id_PG as 'ID PENGGUNA','**********' as 'KATA LALUAN', kp.NAMA, pk.Dc_KOLEJ as 'KOLEJ', case kp.STATUS when 1 then 'A' else 'TA' end as 'STATUS' from KJ_PENGGUNA kp inner join PN_KOLEJ pk on kp.Id_KOLEJ=pk.Id_KOLEJ order by pk.Dc_KOLEJ ASC,cast(pk.JENIS as INT) asc,kp.NAMA ASC"
        'Filter Audit only 10082018 -OSH
        SQL = "select kp.Id_PG as 'ID PENGGUNA','**********' as 'KATA LALUAN', kp.NAMA, pk.Dc_KOLEJ as 'KOLEJ', case kp.STATUS when 1 then 'A' else 'TA' end as 'STATUS' from KJ_PENGGUNA kp inner join PN_KOLEJ pk on kp.Id_KOLEJ=pk.Id_KOLEJ WHERE KP.STATUS = 1 order by pk.Dc_KOLEJ ASC,cast(pk.JENIS as INT) asc,kp.NAMA ASC"
        Tb = "kj_pengguna"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Main", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS between 1 and 2 ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        Cari("")
        'Cb_Status.SelectedIndex = 1

    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        ''e.Row.Cells(1).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(30)
        'e.Row.Cells(6).Visible = False
        'e.Row.Cells(4).Visible = False
        'e.Row.Cells(5).Visible = False
        'e.Row.Cells(7).Visible = False
        ''e.Row.Cells(8).Visible = False
        'e.Row.Cells(9).Visible = False
        'e.Row.Cells(10).Visible = False
        'e.Row.Cells(11).Visible = False

        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("AKSES") = 2 Then cmd_Cari3.Visible = True
        Tx_IdPg.Text = Gd.SelectedRow.Cells(2).Text
        Tx_Nama.Text = Gd.SelectedRow.Cells(4).Text
        Tx_Pwd.Text = Gd.SelectedRow.Cells(3).Text
        Cb_Kolej.ClearSelection()
        'Comment original 28062018 -OSH
        'Cb_Kolej.Items.FindByText(Gd.SelectedRow.Cells(5).Text).Selected = True
        'Covert &amp; to & 26082018 -OSH
        Cb_Kolej.Items.FindByText(Server.HtmlDecode(Gd.SelectedRow.Cells(5).Text)).Selected = True
        If Gd.SelectedRow.Cells(6).Text = "A" Then Cb_Status.SelectedIndex = 1 Else Cb_Status.SelectedIndex = 0
        Session("PINDA") = True
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Cb_Kolej.SelectedIndex < 0 Then Cb_Kolej.Focus() : Exit Sub
        'If Cb_Sektor.SelectedIndex < 1 Then Exit Sub
        If Tx_Nama.Text = "" Then Tx_Nama.Focus() : Exit Sub
        If Tx_IdPg.Text = "" Then Tx_IdPg.Focus() : Exit Sub
        If Tx_Pwd.Text = "" Then Tx_Pwd.Focus() : Exit Sub

        Dim SQL As String

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        If Session("PINDA") = True Then
            SQL = "update kj_pengguna set nama='" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', tkh_daftar=getdate(), status = " & Cb_Status.SelectedIndex
            SQL += "where id_pg='" & Tx_IdPg.Text & "'"
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Msg(Me, "Rekod Pindaan Telah Disimpan...")
                Cari("")
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
            Cn.Close()
            Exit Sub
        End If

        'Comment Original 16082018 - OSH
        'Cmd.CommandText = "select id_pg from kj_pengguna where id_pg = '" & Tx_IdPg.Text & "'"

        'Improve check active user only 16082018 - OSH
        Cmd.CommandText = "select id_pg from kj_pengguna where id_pg = '" & Tx_IdPg.Text.Trim & "' and status = 1"

        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then Msg(Me, "Rekod dengan kod pengguna yang sama telah ada.") : Exit Sub
        Rdr.Close()

        'Commment Original 220082024 - OSH 
        'SQL = "insert kj_pengguna (id_pg, pwd, nama, id_kolej, status, tkh_daftar) "
        'SQL += "select '" & Tx_IdPg.Text.Trim & "', '" & Tx_Pwd.Text & "', '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', '" & Cb_Kolej.SelectedValue & "',1, getdate()"

        'Fix blank insert query 22082024 - OSH
        SQL = "insert kj_pengguna (id_pg, pwd, nama, id_kolej, status, tkh_daftar) " &
               "select '" & Tx_IdPg.Text.Trim & "', '" & Tx_Pwd.Text.Trim & "', '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', '" & Cb_Kolej.SelectedValue & "',1, getdate()"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Baharu Telah Disimpan...")
            Cari("")
        Catch ex As Exception
            Msg(Me, "Error...")
        End Try
        Cn.Close()
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        Cb_Kolej.SelectedIndex = 0
        Cb_Status.SelectedIndex = 1
        Tx_IdPg.Text = ""
        Tx_Nama.Text = ""
        Tx_Pwd.Text = ""
        Session("PINDA") = False
    End Sub

    Protected Sub cmd_Cari3_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari3.Click
        If Tx_Nama.Text = "" Then Exit Sub
        If Tx_IdPg.Text = "" Then Exit Sub
        If Tx_Pwd.Text = "" Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim SQL As String
        'Comment Original 10082018 - OSH
        'SQL = "update kj_pengguna set pwd=id_pg where id_pg = '" & Tx_IdPg.Text & "'"

        'ADD DATE OF RESET 10082018 -OSH
        SQL = "update kj_pengguna set pwd=id_pg, tkh_reset = getdate() where id_pg = '" & Tx_IdPg.Text & "'"

        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Kata Laluan telah dikemaskini..")
        Catch ex As Exception
            Msg(Me, "Error...")
        End Try
        Cn.Close()
        cmd_Cari3.Visible = False
    End Sub
End Class