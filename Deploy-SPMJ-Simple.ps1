# SPMJ KOLEJ Simple Production Deployment Script
param(
    [string]$IISPath = "C:\inetpub\wwwroot\SPMJ",
    [string]$MicroserviceUrl = "http://localhost:5000"
)

Write-Host "=== SPMJ KOLEJ DEPLOYMENT ===" -ForegroundColor Green

# Check admin privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges" -ForegroundColor Red
    exit 1
}

Write-Host "Running with Administrator privileges" -ForegroundColor Green

# Check email microservice
Write-Host "`nChecking email microservice..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$MicroserviceUrl/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "Email microservice is running" -ForegroundColor Green
} catch {
    Write-Host "Email microservice not accessible: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Deploy files
Write-Host "`nDeploying files..." -ForegroundColor Cyan

$sourceFiles = @{
    "SPMJ KOLEJ-PDSA\SPMJ\SPMJ_KOLEJ_PWD.dll" = "bin\SPMJ_KOLEJ_PWD.dll"
    "SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx" = "Pwd.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx" = "ForcePasswordChange.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\Web.config" = "Web.config"
}

# Create target directories
if (-not (Test-Path $IISPath)) {
    New-Item -ItemType Directory -Path $IISPath -Force | Out-Null
    Write-Host "Created deployment directory: $IISPath" -ForegroundColor Green
}

if (-not (Test-Path "$IISPath\bin")) {
    New-Item -ItemType Directory -Path "$IISPath\bin" -Force | Out-Null
    Write-Host "Created bin directory" -ForegroundColor Green
}

# Copy files
foreach ($source in $sourceFiles.Keys) {
    $target = "$IISPath\$($sourceFiles[$source])"
    if (Test-Path $source) {
        try {
            Copy-Item -Path $source -Destination $target -Force
            Write-Host "Deployed: $source" -ForegroundColor Green
        } catch {
            Write-Host "Failed to deploy $source : $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "Source file not found: $source" -ForegroundColor Yellow
    }
}

# Configure IIS application
Write-Host "`nConfiguring IIS..." -ForegroundColor Cyan
try {
    Import-Module WebAdministration -ErrorAction Stop
    
    $appName = "SPMJ"
    if (Get-WebApplication -Name $appName -ErrorAction SilentlyContinue) {
        Write-Host "IIS Application already exists" -ForegroundColor Green
    } else {
        New-WebApplication -Name $appName -Site "Default Web Site" -PhysicalPath $IISPath
        Write-Host "Created IIS Application: $appName" -ForegroundColor Green
    }
} catch {
    Write-Host "IIS configuration failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Verification
Write-Host "`nVerifying deployment..." -ForegroundColor Cyan
$verifyFiles = @(
    "$IISPath\bin\SPMJ_KOLEJ_PWD.dll",
    "$IISPath\Pwd.aspx",
    "$IISPath\ForcePasswordChange.aspx",
    "$IISPath\Web.config"
)

$allDeployed = $true
foreach ($file in $verifyFiles) {
    if (Test-Path $file) {
        Write-Host "Found: $(Split-Path $file -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "Missing: $(Split-Path $file -Leaf)" -ForegroundColor Red
        $allDeployed = $false
    }
}

if ($allDeployed) {
    Write-Host "`n=== DEPLOYMENT SUCCESSFUL ===" -ForegroundColor Green
    Write-Host "Web application deployed to: http://localhost/SPMJ/" -ForegroundColor White
} else {
    Write-Host "`n=== DEPLOYMENT INCOMPLETE ===" -ForegroundColor Yellow
}
