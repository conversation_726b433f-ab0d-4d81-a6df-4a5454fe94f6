@echo off
echo ============================================================
echo SPMJ Password Recovery Feature Test
echo ============================================================
echo.

echo [INFO] Testing the password recovery functionality...
echo.

echo [TEST 1] Checking Email Microservice Password Recovery Template...
echo.
echo   Testing password recovery email template:
curl -s -w "Status: %%{http_code}\n" ^
     -H "Content-Type: application/json" ^
     -H "X-API-Key: SPMJ-EmailService-2024-SecureKey-MOH-Malaysia" ^
     -d "{\"to\":\"<EMAIL>\",\"subject\":\"Pemulihan Kata Laluan - Sistem SPMJ\",\"templateType\":\"password_recovery\",\"data\":{\"userName\":\"Test User\",\"userId\":\"TEST001\",\"password\":\"TempRecovery123!\",\"isTemporary\":true,\"systemUrl\":\"http://localhost:8080\",\"timestamp\":\"2025-06-15 15:30:45\",\"adminName\":\"User initiated password recovery\"}}" ^
     http://localhost:5000/api/admin/password/send-notification
echo.

echo [TEST 2] Checking Force Reset Email Template...
echo.
echo   Testing force reset email template:
curl -s -w "Status: %%{http_code}\n" ^
     -H "Content-Type: application/json" ^
     -H "X-API-Key: SPMJ-EmailService-2024-SecureKey-MOH-Malaysia" ^
     -d "{\"to\":\"<EMAIL>\",\"subject\":\"Password Reset - SPMJ System (Action Required)\",\"templateType\":\"force_reset\",\"data\":{\"userName\":\"Test User\",\"userId\":\"TEST001\",\"password\":\"ForceReset123!\",\"isTemporary\":true,\"systemUrl\":\"http://localhost:8080\",\"timestamp\":\"2025-06-15 15:30:45\",\"adminId\":\"ADMIN001\",\"adminName\":\"System Administrator\"}}" ^
     http://localhost:5000/api/admin/password/send-force-reset
echo.

echo ============================================================
echo PASSWORD RECOVERY FEATURE OVERVIEW
echo ============================================================
echo.
echo [FEATURE] Password Recovery on Login Page
echo   ✓ "Lupa Kata Laluan?" link added to login form
echo   ✓ Recovery panel with user ID input
echo   ✓ Industry-standard security implementation
echo.
echo [SECURITY FEATURES] 
echo   ✓ Cryptographically secure temporary password generation
echo   ✓ 12-character passwords with mixed case, numbers, symbols
echo   ✓ SQL injection protection
echo   ✓ Email validation before sending
echo   ✓ Security audit logging
echo   ✓ Rate limiting protection (don't reveal if user exists)
echo   ✓ Temporary password expiration
echo   ✓ Force password change on first login
echo.
echo [EMAIL TEMPLATES]
echo   ✓ Professional password recovery template
echo   ✓ Force reset warning template  
echo   ✓ Bilingual support (Bahasa Malaysia)
echo   ✓ Mobile-responsive HTML design
echo   ✓ Security warnings and instructions
echo.
echo [INTEGRATION]
echo   ✓ Uses existing email microservice
echo   ✓ Compatible with AdminPasswordManager hashing
echo   ✓ Maintains existing login functionality
echo   ✓ Proper error handling and user feedback
echo.
echo ============================================================
echo MANUAL TESTING STEPS
echo ============================================================
echo.
echo 1. SETUP:
echo    - Ensure email microservice is running (dotnet run)
echo    - Ensure main SPMJ application is running
echo    - Have a test user with registered email in database
echo.
echo 2. TEST PASSWORD RECOVERY:
echo    a) Open login page (p0_Login.aspx)
echo    b) Click "Lupa Kata Laluan?" link
echo    c) Enter valid user ID in recovery panel
echo    d) Click "Hantar" button
echo    e) Check for success message
echo    f) Verify email received with temporary password
echo.
echo 3. TEST TEMPORARY PASSWORD LOGIN:
echo    a) Use temporary password from email
echo    b) Login should succeed
echo    c) Should be redirected to password change
echo    d) Complete password change process
echo.
echo 4. TEST SECURITY FEATURES:
echo    a) Try recovery with invalid user ID
echo    b) Try recovery with user without email
echo    c) Verify proper error messages
echo    d) Check audit logs for security events
echo.
echo ============================================================
echo Expected Results:
echo   - Email microservice tests: Status 200 (emails sent)
echo   - Recovery UI: Panel shows/hides correctly
echo   - Security: No sensitive information leaked
echo   - User Experience: Clear instructions and feedback
echo ============================================================
pause
