using Microsoft.EntityFrameworkCore;
using SPMJ.EmailService.Data;
using SPMJ.EmailService.Models;
using System.Security.Cryptography;
using System.Text;

namespace SPMJ.EmailService.Services;

public class PasswordService : IPasswordService
{
    private readonly SPMJContext _context;
    private readonly IEmailService _emailService;
    private readonly ILogger<PasswordService> _logger;
    private const int SALT_LENGTH = 32;

    public PasswordService(SPMJContext context, IEmailService emailService, ILogger<PasswordService> logger)
    {
        _context = context;
        _emailService = emailService;
        _logger = logger;
    }

    public async Task<PasswordResetResponse> RequestPasswordResetAsync(PasswordResetRequest request)
    {
        try
        {            // Find user
            var user = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == request.UserId && u.Status == 1);

            if (user == null)
            {
                return new PasswordResetResponse
                {
                    Success = false,
                    Message = "Pengguna tidak dijumpai atau tidak aktif"
                };
            }

            // Use provided email or user's email
            var emailToUse = !string.IsNullOrEmpty(request.Email) ? request.Email : user.Email;
            if (string.IsNullOrEmpty(emailToUse))
            {
                return new PasswordResetResponse
                {
                    Success = false,
                    Message = "Alamat email tidak dijumpai"
                };
            }

            // Generate reset token
            var token = GenerateSecureToken();
            var expiresAt = DateTime.UtcNow.AddHours(24);

            // Save reset token
            var resetToken = new PasswordResetToken
            {
                UserId = user.IdPg,
                Token = token,
                ExpiresAt = expiresAt
            };

            _context.PasswordResetTokens.Add(resetToken);
            await _context.SaveChangesAsync();

            // Send email
            var emailSent = await _emailService.SendPasswordResetEmailAsync(
                emailToUse, user.Nama ?? user.IdPg, token, request.BaseUrl);

            if (!emailSent)
            {
                return new PasswordResetResponse
                {
                    Success = false,
                    Message = "Gagal menghantar email reset kata laluan"
                };
            }

            return new PasswordResetResponse
            {
                Success = true,
                Message = "Link reset kata laluan telah dihantar ke email anda",
                Token = token
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting password reset for user {UserId}", request.UserId);
            return new PasswordResetResponse
            {
                Success = false,
                Message = "Ralat sistem. Sila cuba lagi."
            };
        }
    }

    public async Task<ApiResponse<string>> ValidateResetTokenAsync(string token)
    {
        try
        {
            var resetToken = await _context.PasswordResetTokens
                .FirstOrDefaultAsync(t => t.Token == token && !t.Used && t.ExpiresAt > DateTime.UtcNow);

            if (resetToken == null)
            {
                return new ApiResponse<string>
                {
                    Success = false,
                    Message = "Token tidak sah atau telah luput"
                };
            }

            var user = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == resetToken.UserId);

            if (user == null)
            {
                return new ApiResponse<string>
                {
                    Success = false,
                    Message = "Pengguna tidak dijumpai"
                };
            }

            return new ApiResponse<string>
            {
                Success = true,
                Message = "Token sah",
                Data = user.IdPg
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating reset token");
            return new ApiResponse<string>
            {
                Success = false,
                Message = "Ralat sistem"
            };
        }
    }

    public async Task<ApiResponse<bool>> CompletePasswordResetAsync(string token, string newPassword)
    {
        try
        {
            var resetToken = await _context.PasswordResetTokens
                .FirstOrDefaultAsync(t => t.Token == token && !t.Used && t.ExpiresAt > DateTime.UtcNow);

            if (resetToken == null)
            {
                return new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Token tidak sah atau telah luput"
                };
            }

            var user = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == resetToken.UserId);

            if (user == null)
            {
                return new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Pengguna tidak dijumpai"
                };
            }

            // Generate new salt and hash password
            var salt = GenerateSalt();
            var hashedPassword = HashPassword(newPassword, salt);

            // Update user password
            user.Pwd = hashedPassword;
            user.Salt = salt;
            user.PasswordMigrated = true;

            // Mark token as used
            resetToken.Used = true;
            resetToken.UsedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return new ApiResponse<bool>
            {
                Success = true,
                Message = "Kata laluan berjaya dikemaskini",
                Data = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing password reset");
            return new ApiResponse<bool>
            {
                Success = false,
                Message = "Ralat sistem"
            };
        }
    }

    public async Task<ApiResponse<string>> CreateAdminPasswordAsync(AdminPasswordCreateRequest request)
    {
        try
        {
            var user = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == request.UserId);

            if (user == null)
            {
                return new ApiResponse<string>
                {
                    Success = false,                Message = "Pengguna tidak dijumpai"
                };
            }

            var admin = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == request.AdminId && u.Status == 1);

            if (admin == null)
            {
                return new ApiResponse<string>
                {
                    Success = false,
                    Message = "Admin tidak dijumpai atau tidak aktif"
                };
            }

            // Generate new salt and hash password
            var salt = GenerateSalt();
            var hashedPassword = HashPassword(request.TempPassword, salt);

            // Update user password
            user.Pwd = hashedPassword;
            user.Salt = salt;
            user.PasswordMigrated = true;

            await _context.SaveChangesAsync();

            // Send email notification
            var emailSent = await _emailService.SendAdminPasswordEmailAsync(
                request.Email, user.Nama ?? user.IdPg, request.TempPassword, admin.Nama ?? admin.IdPg);

            return new ApiResponse<string>
            {
                Success = true,
                Message = emailSent ? "Kata laluan telah ditetapkan dan email dihantar" : "Kata laluan ditetapkan tetapi email gagal dihantar",
                Data = request.TempPassword
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating admin password for user {UserId}", request.UserId);
            return new ApiResponse<string>
            {
                Success = false,
                Message = "Ralat sistem"
            };
        }
    }    public async Task<ApiResponse<string>> ResetAdminPasswordAsync(AdminPasswordResetRequest request)
    {
        try
        {
            var user = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == request.UserId && u.Status == 1);

            if (user == null)
            {
                return new ApiResponse<string>
                {
                    Success = false,
                    Message = "Pengguna tidak dijumpai atau tidak aktif"
                };
            }            var admin = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == request.AdminId && u.Status == 1);

            if (admin == null)
            {
                return new ApiResponse<string>
                {
                    Success = false,
                    Message = "Admin tidak dijumpai atau tidak aktif"
                };
            }

            // Generate temporary password if not provided
            var tempPassword = request.TempPassword ?? GenerateTemporaryPassword();

            // Generate new salt and hash password
            var salt = GenerateSalt();
            var hashedPassword = HashPassword(tempPassword, salt);

            // Update user password
            user.Pwd = hashedPassword;
            user.Salt = salt;
            user.PasswordMigrated = true;

            await _context.SaveChangesAsync();

            // Send email notification
            var emailSent = await _emailService.SendAdminPasswordEmailAsync(
                request.Email, user.Nama ?? user.IdPg, tempPassword, admin.Nama ?? admin.IdPg);

            return new ApiResponse<string>
            {
                Success = true,
                Message = emailSent ? "Kata laluan telah direset dan email dihantar" : "Kata laluan direset tetapi email gagal dihantar",
                Data = tempPassword
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting admin password for user {UserId}", request.UserId);
            return new ApiResponse<string>
            {
                Success = false,
                Message = "Ralat sistem"
            };
        }
    }

    public async Task<PasswordResetResponse> SendPasswordResetEmailAsync(SendPasswordResetEmailRequest request)
    {
        try
        {
            _logger.LogInformation("Sending password reset email for user {UserId} to {Email}", request.UserId, request.Email);

            // Verify user exists
            var user = await _context.PnPengguna
                .FirstOrDefaultAsync(u => u.IdPg == request.UserId && u.Status == 1);

            if (user == null)
            {
                return new PasswordResetResponse
                {
                    Success = false,
                    Message = "Pengguna tidak dijumpai atau tidak aktif"
                };
            }

            // Send email with temporary password
            var emailSent = await _emailService.SendPasswordResetEmailAsync(
                request.Email, request.UserName, request.TempPassword);

            if (!emailSent)
            {
                return new PasswordResetResponse
                {
                    Success = false,
                    Message = "Gagal menghantar email kata laluan sementara"
                };
            }

            _logger.LogInformation("Password reset email sent successfully for user {UserId}", request.UserId);

            return new PasswordResetResponse
            {
                Success = true,
                Message = "Kata laluan sementara telah dihantar ke email anda"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password reset email for user {UserId}", request.UserId);
            return new PasswordResetResponse
            {
                Success = false,
                Message = "Ralat sistem semasa menghantar email"
            };
        }
    }

    public string HashPassword(string password, string salt)
    {
        if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(salt))
            throw new ArgumentException("Password and salt cannot be null or empty");

        var saltBytes = Convert.FromBase64String(salt);
        var passwordBytes = Encoding.UTF8.GetBytes(password);

        var combinedBytes = new byte[passwordBytes.Length + saltBytes.Length];
        Array.Copy(passwordBytes, 0, combinedBytes, 0, passwordBytes.Length);
        Array.Copy(saltBytes, 0, combinedBytes, passwordBytes.Length, saltBytes.Length);

        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(combinedBytes);
        return Convert.ToBase64String(hashBytes);
    }

    public string GenerateSalt()
    {
        var saltBytes = new byte[SALT_LENGTH];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(saltBytes);
        return Convert.ToBase64String(saltBytes);
    }

    public bool VerifyPassword(string password, string hash, string salt)
    {
        if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hash) || string.IsNullOrEmpty(salt))
            return false;

        try
        {
            var computedHash = HashPassword(password, salt);
            return computedHash.Equals(hash);
        }
        catch
        {
            return false;
        }
    }

    private string GenerateSecureToken()
    {
        var tokenBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(tokenBytes);
        return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    private string GenerateTemporaryPassword()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, 8)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }
}
