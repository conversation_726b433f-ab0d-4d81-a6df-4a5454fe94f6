<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="OtpVerification.aspx.vb" Inherits="SPMJ.OtpVerification" title="SPMJ - Pengesahan OTP" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .otp-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            font-family: Arial, sans-serif;
        }
        
        .otp-header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .otp-icon {
            font-size: 64px;
            color: #3498db;
            margin-bottom: 20px;
        }
        
        .otp-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .otp-subtitle {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.5;
        }
        
        .otp-form {
            margin: 30px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .otp-input {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            text-align: center;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            letter-spacing: 5px;
            font-weight: bold;
        }
        
        .otp-input:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }
        
        .btn-otp {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        
        .resend-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }
        
        .countdown {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .message-panel {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        
        .message-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="otp-container">
        <div class="otp-header">
            <div class="otp-icon">🔐</div>            <div class="otp-title">Pengesahan Keselamatan</div>
            <div class="otp-subtitle">
                Log masuk dengan kata laluan selamat memerlukan pengesahan tambahan.<br />
                Kod pengesahan telah dihantar ke alamat email anda.<br />
                Sila masukkan kod 6 digit untuk melengkapkan log masuk yang selamat.
            </div>
        </div>
        
        <!-- Message Panel -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false">
            <div class="message-panel" id="divMessage" runat="server">
                <asp:Label ID="lblMessage" runat="server"></asp:Label>
            </div>
        </asp:Panel>
        
        <div class="otp-form">
            <div class="form-group">
                <label class="form-label">Kod Pengesahan (OTP):</label>
                <asp:TextBox ID="txtOtpCode" runat="server" CssClass="otp-input" MaxLength="6" placeholder="000000"></asp:TextBox>
            </div>
            
            <div class="form-group">
                <asp:Button ID="btnVerifyOTP" runat="server" Text="Sahkan Kod" CssClass="btn-otp btn-primary" />
            </div>
        </div>
        
        <div class="resend-section">
            <asp:Panel ID="pnlCountdown" runat="server">
                <span>Hantar semula kod dalam <span class="countdown" id="countdown">60</span> saat</span>
            </asp:Panel>
            
            <asp:Panel ID="pnlResend" runat="server" Visible="false">
                <asp:Button ID="btnResendOTP" runat="server" Text="Hantar Semula Kod" CssClass="btn-otp btn-secondary" />
            </asp:Panel>
            
            <div style="margin-top: 20px;">
                <asp:Button ID="btnCancel" runat="server" Text="Batal Log Masuk" CssClass="btn-otp btn-secondary" />
            </div>
        </div>
    </div>
    
    <script type="text/javascript">
        // Countdown timer for resend OTP
        var countdown = 60;
        var timer = setInterval(function() {
            countdown--;
            document.getElementById('countdown').innerText = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                document.getElementById('<%= pnlCountdown.ClientID %>').style.display = 'none';
                document.getElementById('<%= pnlResend.ClientID %>').style.display = 'block';
            }
        }, 1000);
        
        // Auto-submit when 6 digits entered
        document.getElementById('<%= txtOtpCode.ClientID %>').addEventListener('input', function(e) {
            if (e.target.value.length === 6) {
                // Auto-submit after a short delay
                setTimeout(function() {
                    document.getElementById('<%= btnVerifyOTP.ClientID %>').click();
                }, 500);
            }
        });
        
        // Focus on OTP input when page loads
        window.onload = function() {
            document.getElementById('<%= txtOtpCode.ClientID %>').focus();
        };
    </script>
</asp:Content>
