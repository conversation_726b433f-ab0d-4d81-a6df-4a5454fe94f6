﻿Imports System.Data.OleDb
Imports System.Security.Cryptography
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Configuration
Imports System.Collections.Generic

Partial Public Class WebForm73
    Inherits System.Web.UI.Page

    ' Email service client for microservice integration
    Private emailClient As EmailServiceClient

#Region "Page Events"

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        
        ' Enhanced security check
        If Session("Id_PG") Is Nothing OrElse Session("Id_PG").ToString() = "" Then
            Session.Abandon()
            Response.Redirect("Login.aspx")
            Return
        End If
          ' Initialize email service client with baseUrl
        Try
            Dim baseUrl As String = System.Configuration.ConfigurationManager.AppSettings("EmailServiceBaseUrl")
            If baseUrl Is Nothing OrElse baseUrl.Trim() = "" Then
                ' Try multiple fallback URLs for the email microservice
                Dim fallbackUrls() As String = {
                    "http://localhost:5000",
                    "http://localhost:5001", 
                    "https://spmj-email-service.azurewebsites.net",
                    "http://spmj-email-service:5000"
                }
                
                For Each url In fallbackUrls
                    Try
                        Dim testClient As New EmailServiceClient(url)
                        If testClient.CheckHealth() Then
                            baseUrl = url
                            Exit For
                        End If
                    Catch
                        ' Continue to next URL
                    End Try
                Next
                
                If baseUrl Is Nothing OrElse baseUrl.Trim() = "" Then
                    baseUrl = "http://localhost:5000" ' Final fallback
                End If
            End If
            
            emailClient = New EmailServiceClient(baseUrl)
            System.Diagnostics.Debug.WriteLine("Email service client initialized for password management with URL: " & baseUrl)
            
            ' Test connection and log status
            If emailClient.CheckHealth() Then
                System.Diagnostics.Debug.WriteLine("✅ Email service health check: ONLINE")
            Else
                System.Diagnostics.Debug.WriteLine("⚠️ Email service health check: OFFLINE")
            End If
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Email service client initialization failed: " & ex.Message)
            ' Continue without email service - graceful degradation
        End Try
        
        ' Configure enhanced security headers
        ConfigureSecurityHeaders()
        
        ' Log page access for security auditing
        LogSecurityEvent("PASSWORD_CHANGE_ACCESS", Session("Id_PG").ToString())
        
        System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: Password change page loaded with enhanced security")
    End Sub

#End Region

#Region "Security Configuration"

    Private Sub ConfigureSecurityHeaders()
        ' Enhanced security headers for password management
        Response.Headers.Add("X-Content-Type-Options", "nosniff")
        Response.Headers.Add("X-Frame-Options", "DENY")
        Response.Headers.Add("X-XSS-Protection", "1; mode=block")
        Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin")
        Response.Headers.Add("Cache-Control", "no-cache, no-store, must-revalidate")
        Response.Headers.Add("Pragma", "no-cache")
        Response.Headers.Add("Expires", "0")
    End Sub

#End Region

#Region "Password Change Logic"

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Try
            System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: Password change operation initiated")
            
            ' Clear previous messages
            HideMessage()
            
            ' Enhanced server-side validation
            If Not ValidatePasswordChangeRequest() Then
                Return
            End If
            
            ' Verify current password with enhanced security
            If Not VerifyCurrentPassword() Then
                ShowMessage("Current password is incorrect. Please try again.", "error")
                LogSecurityEvent("PASSWORD_CHANGE_FAILED_INVALID_CURRENT", Session("Id_PG").ToString())
                Return
            End If
            
            ' Validate new password strength
            If Not ValidateNewPasswordStrength() Then
                Return
            End If
            
            ' Check password history (prevent reuse)
            If IsPasswordInHistory(Tx_Pwd2.Text.Trim()) Then
                ShowMessage("You cannot reuse a recent password. Please choose a different password.", "error")
                LogSecurityEvent("PASSWORD_CHANGE_FAILED_REUSE", Session("Id_PG").ToString())
                Return
            End If
            
            ' Update password with enhanced security
            If UpdatePasswordSecurely() Then
                ' Send email notification if enabled
                Dim emailSent As Boolean = False
                If chkEmailNotification.Checked Then
                    emailSent = SendPasswordChangeNotification()
                End If
                
                ' Log successful password change
                LogSecurityEvent("PASSWORD_CHANGED_SUCCESS", Session("Id_PG").ToString())
                
                ' Show success message
                Dim successMessage As String = "Your password has been changed successfully!"
                If chkEmailNotification.Checked Then
                    If emailSent Then
                        successMessage &= " A confirmation email has been sent to your registered email address."
                    Else
                        successMessage &= " Note: Email notification could not be sent due to service unavailability."
                    End If
                End If
                
                ShowMessage(successMessage, "success")
                
                ' Clear form for security
                ClearPasswordFields()
                
                ' Update session password for current session validation
                Session("pwd") = Tx_Pwd2.Text.Trim()
            Else
                ShowMessage("Failed to update password. Please try again.", "error")
                LogSecurityEvent("PASSWORD_CHANGE_FAILED_UPDATE", Session("Id_PG").ToString())
            End If
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Password change error: " & ex.Message)
            ShowMessage("A system error occurred. Please try again later.", "error")
            LogSecurityEvent("PASSWORD_CHANGE_ERROR", Session("Id_PG").ToString(), ex.Message)
        End Try
    End Sub

    Protected Sub btnCancel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnCancel.Click
        Try
            ClearPasswordFields()
            HideMessage()
            ' Optionally redirect to dashboard or previous page
            ' Response.Redirect("Dashboard.aspx")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Cancel error: " & ex.Message)
        End Try
    End Sub

#End Region

#Region "Validation Methods"
    Private Function ValidatePasswordChangeRequest() As Boolean
        ' Validate current password field
        If Tx_Pwd.Text Is Nothing OrElse Tx_Pwd.Text.Trim() = "" Then
            ShowMessage("Please enter your current password.", "error")
            Return False
        End If

        ' Validate new password field
        If Tx_Pwd2.Text Is Nothing OrElse Tx_Pwd2.Text.Trim() = "" Then
            ShowMessage("Please enter a new password.", "error")
            Return False
        End If

        ' Validate confirm password field
        If Tx_Pwd3.Text Is Nothing OrElse Tx_Pwd3.Text.Trim() = "" Then
            ShowMessage("Please confirm your new password.", "error")
            Return False
        End If

        ' Check if new passwords match
        If Tx_Pwd2.Text <> Tx_Pwd3.Text Then
            ShowMessage("New password and confirmation do not match.", "error")
            Return False
        End If

        ' Check if new password is different from current
        If Tx_Pwd.Text = Tx_Pwd2.Text Then
            ShowMessage("New password must be different from your current password.", "error")
            Return False
        End If

        Return True
    End Function

    Private Function VerifyCurrentPassword() As Boolean
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            Using command As New OleDbCommand()
                command.Connection = connection
                ' Use parameterized query for security
                command.CommandText = "SELECT pwd, salt FROM kj_pengguna WHERE id_pg = ?"
                command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())
                  Using reader As OleDbDataReader = command.ExecuteReader()
                    If reader.Read() Then
                        Dim storedPassword As String = reader("pwd").ToString()
                        Dim salt As String = ""
                        If reader("salt") IsNot DBNull.Value Then
                            salt = reader("salt").ToString()
                        End If
                          ' If salt exists, verify with SHA256+Salt, otherwise legacy comparison
                        If salt IsNot Nothing AndAlso salt.Trim() <> "" Then
                            Dim hashedInput As String = HashPasswordWithSalt(Tx_Pwd.Text.Trim(), salt)
                            Return hashedInput.Equals(storedPassword)
                        Else
                            ' Legacy comparison for backward compatibility
                            Return String.Compare(Tx_Pwd.Text.Trim(), storedPassword, True) = 0
                        End If
                    End If
                End Using
            End Using
            
            Return False
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Current password verification error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    Private Function ValidateNewPasswordStrength() As Boolean
        Dim password As String = Tx_Pwd2.Text.Trim()
        Dim errors As New List(Of String)
        
        ' Check minimum length
        If password.Length < 8 Then
            errors.Add("Password must be at least 8 characters long")
        End If
        
        ' Check maximum length
        If password.Length > 255 Then
            errors.Add("Password cannot exceed 255 characters")
        End If
        
        ' Check for uppercase letter
        If Not Regex.IsMatch(password, "[A-Z]") Then
            errors.Add("Password must contain at least one uppercase letter")
        End If
        
        ' Check for lowercase letter
        If Not Regex.IsMatch(password, "[a-z]") Then
            errors.Add("Password must contain at least one lowercase letter")
        End If
        
        ' Check for number
        If Not Regex.IsMatch(password, "\d") Then
            errors.Add("Password must contain at least one number")
        End If
        
        ' Check for special character
        If Not Regex.IsMatch(password, "[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]") Then
            errors.Add("Password must contain at least one special character")
        End If
        
        ' Check for sequential characters
        If HasSequentialCharacters(password) Then
            errors.Add("Password cannot contain sequential characters (abc, 123, etc.)")
        End If
        
        ' Check against common passwords
        If IsCommonPassword(password) Then
            errors.Add("Password is too common. Please choose a more unique password")
        End If
          If errors.Count > 0 Then
            Dim errorMessage As String = "Password requirements not met: "
            For i As Integer = 0 To errors.Count - 1
                errorMessage &= errors(i)
                If i < errors.Count - 1 Then
                    errorMessage &= ", "
                End If
            Next
            ShowMessage(errorMessage, "error")
            Return False
        End If
        
        Return True
    End Function

    Private Function HasSequentialCharacters(password As String) As Boolean
        Dim lowerPassword As String = password.ToLower()
        
        ' Check for sequential letters
        For i As Integer = 0 To lowerPassword.Length - 3
            Dim c1 As Char = lowerPassword(i)
            Dim c2 As Char = lowerPassword(i + 1)
            Dim c3 As Char = lowerPassword(i + 2)
            
            If Char.IsLetter(c1) AndAlso Char.IsLetter(c2) AndAlso Char.IsLetter(c3) Then
                If Asc(c2) = Asc(c1) + 1 AndAlso Asc(c3) = Asc(c2) + 1 Then
                    Return True
                End If
                If Asc(c2) = Asc(c1) - 1 AndAlso Asc(c3) = Asc(c2) - 1 Then
                    Return True
                End If
            End If
            
            If Char.IsDigit(c1) AndAlso Char.IsDigit(c2) AndAlso Char.IsDigit(c3) Then
                If Asc(c2) = Asc(c1) + 1 AndAlso Asc(c3) = Asc(c2) + 1 Then
                    Return True
                End If
                If Asc(c2) = Asc(c1) - 1 AndAlso Asc(c3) = Asc(c2) - 1 Then
                    Return True
                End If
            End If
        Next
        
        Return False
    End Function

    Private Function IsCommonPassword(password As String) As Boolean
        ' List of common passwords to reject        
        Dim commonPasswords() As String = {
            "password", "123456", "123456789", "qwerty", "abc123", "password123",
            "admin", "administrator", "root", "user", "guest", "test", "demo",
            "welcome", "login", "pass", "default", "changeme", "newpassword"
        }

        For Each commonPwd In commonPasswords
            If String.Compare(password, commonPwd, True) = 0 Then
                Return True
            End If
        Next
        
        Return False
    End Function

    Private Function IsPasswordInHistory(newPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            ' Check password history (last 5 passwords)
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT TOP 5 pwd_hash, salt FROM kj_password_history WHERE id_pg = ? ORDER BY created_date DESC"
                command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())
                  Using reader As OleDbDataReader = command.ExecuteReader()
                    While reader.Read()
                        Dim historicalHash As String = reader("pwd_hash").ToString()
                        Dim salt As String = ""
                        If reader("salt") IsNot DBNull.Value Then
                            salt = reader("salt").ToString()
                        End If
                          If salt IsNot Nothing AndAlso salt.Trim() <> "" Then
                            Dim newPasswordHash As String = HashPasswordWithSalt(newPassword, salt)
                            If newPasswordHash.Equals(historicalHash) Then
                                Return True
                            End If
                        End If
                    End While
                End Using
            End Using
            
            Return False
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Password history check error: " & ex.Message)
            ' If history check fails, allow password change for better user experience
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

#End Region

#Region "Password Security Methods"

    Private Function UpdatePasswordSecurely() As Boolean
        Dim connection As OleDbConnection = Nothing
        Dim transaction As OleDbTransaction = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            transaction = connection.BeginTransaction()
            
            ' Generate salt and hash new password
            Dim salt As String = GenerateSalt()
            Dim hashedPassword As String = HashPasswordWithSalt(Tx_Pwd2.Text.Trim(), salt)
            
            ' Update main password record
            Using command As New OleDbCommand()
                command.Connection = connection
                command.Transaction = transaction
                command.CommandText = "UPDATE kj_pengguna SET pwd = ?, salt = ?, pwd_last_changed = ?, pwd_changed_by = ? WHERE id_pg = ?"
                command.Parameters.AddWithValue("@pwd", hashedPassword)
                command.Parameters.AddWithValue("@salt", salt)
                command.Parameters.AddWithValue("@pwd_last_changed", DateTime.Now)
                command.Parameters.AddWithValue("@pwd_changed_by", Session("Id_PG").ToString())
                command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())
                
                Dim rowsAffected As Integer = command.ExecuteNonQuery()
                If rowsAffected = 0 Then
                    transaction.Rollback()
                    Return False
                End If
            End Using
            
            ' Add to password history
            Try
                Using historyCommand As New OleDbCommand()
                    historyCommand.Connection = connection
                    historyCommand.Transaction = transaction
                    historyCommand.CommandText = "INSERT INTO kj_password_history (id_pg, pwd_hash, salt, created_date, created_by) VALUES (?, ?, ?, ?, ?)"
                    historyCommand.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())
                    historyCommand.Parameters.AddWithValue("@pwd_hash", hashedPassword)
                    historyCommand.Parameters.AddWithValue("@salt", salt)
                    historyCommand.Parameters.AddWithValue("@created_date", DateTime.Now)
                    historyCommand.Parameters.AddWithValue("@created_by", Session("Id_PG").ToString())
                    
                    historyCommand.ExecuteNonQuery()
                End Using
            Catch ex As Exception
                ' Password history is optional - don't fail the entire operation
                System.Diagnostics.Debug.WriteLine("Password history insert failed: " & ex.Message)
            End Try
            
            transaction.Commit()
            System.Diagnostics.Debug.WriteLine("✅ Password updated successfully with enhanced security")
            Return True
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Password update error: " & ex.Message)
            If transaction IsNot Nothing Then
                Try
                    transaction.Rollback()
                Catch rollbackEx As Exception
                    System.Diagnostics.Debug.WriteLine("Transaction rollback error: " & rollbackEx.Message)
                End Try
            End If
            Return False
        Finally
            If transaction IsNot Nothing Then
                transaction.Dispose()
            End If
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    Private Function GenerateSalt() As String
        Dim rng As New System.Security.Cryptography.RNGCryptoServiceProvider()
        Dim saltBytes(31) As Byte ' 256 bits
        rng.GetBytes(saltBytes)
        Return Convert.ToBase64String(saltBytes)
    End Function

    Private Function HashPasswordWithSalt(password As String, salt As String) As String
        Try
            Using sha256 As SHA256 = SHA256.Create()
                Dim passwordBytes As Byte() = Encoding.UTF8.GetBytes(password & salt)
                Dim hashBytes As Byte() = sha256.ComputeHash(passwordBytes)
                Return Convert.ToBase64String(hashBytes)
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Password hashing error: " & ex.Message)
            Throw
        End Try
    End Function

#End Region

#Region "Microservice Integration"
    Private Function SendPasswordChangeNotification() As Boolean
        Try
            If emailClient Is Nothing Then
                System.Diagnostics.Debug.WriteLine("Email service client not available")
                Return False
            End If

            Dim userId As String = Session("Id_PG").ToString()
            Dim userEmail As String = GetUserEmail(userId)

            If userEmail Is Nothing OrElse userEmail.Trim() = "" Then
                System.Diagnostics.Debug.WriteLine("User email not found for notifications")
                Return False
            End If

            System.Diagnostics.Debug.WriteLine("MICROSERVICE: Sending password change notification for user: " & userId)

            ' Create notification message
            Dim subject As String = "SPMJ KOLEJ - Password Changed Successfully"
            Dim body As String = "Your password has been successfully changed in the SPMJ KOLEJ system. " &
                               "If you did not make this change, please contact your system administrator immediately. " &
                               "Change completed at " & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") & " from IP: " & GetClientIP() & "."            ' Send notification via microservice
            Dim success As Boolean = emailClient.SendPasswordChangeNotification(userId, userEmail)
            If success Then
                System.Diagnostics.Debug.WriteLine("Password change notification sent successfully")
                Return True
            Else
                System.Diagnostics.Debug.WriteLine("Password change notification failed")
                Return False
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Microservice notification error: " & ex.Message)
            Return False
        End Try
    End Function

    Private Function GetUserEmail(userId As String) As String
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()

            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT email FROM kj_pengguna WHERE id_pg = ?"
                command.Parameters.AddWithValue("@id_pg", userId)
                Dim result As Object = command.ExecuteScalar()
                If result IsNot Nothing AndAlso result IsNot DBNull.Value Then
                    Return result.ToString()
                Else
                    Return ""
                End If
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Get user email error: " & ex.Message)
            Return ""
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function
    Private Function GetClientIP() As String
        Try
            Dim ipAddress As String = Request.ServerVariables("HTTP_X_FORWARDED_FOR")
            If ipAddress Is Nothing OrElse ipAddress.Trim() = "" Then
                ipAddress = Request.ServerVariables("REMOTE_ADDR")
            End If
            If ipAddress Is Nothing OrElse ipAddress.Trim() = "" Then
                Return "Unknown"
            Else
                Return ipAddress
            End If
        Catch
            Return "Unknown"
        End Try
    End Function

#End Region

#Region "Helper Methods"

    Private Sub ShowMessage(message As String, messageType As String)
        messagePanel.Visible = True
        messageLabel.Text = message
        
        Select Case messageType.ToLower()
            Case "success"
                messagePanel.CssClass = "message-container message-success"
            Case "error"
                messagePanel.CssClass = "message-container message-error"
            Case "info"
                messagePanel.CssClass = "message-container message-info"
            Case Else
                messagePanel.CssClass = "message-container message-info"
        End Select
    End Sub

    Private Sub HideMessage()
        messagePanel.Visible = False
        messageLabel.Text = ""
    End Sub

    Private Sub ClearPasswordFields()
        Tx_Pwd.Text = ""
        Tx_Pwd2.Text = ""
        Tx_Pwd3.Text = ""
    End Sub
    Private Sub LogSecurityEvent(eventType As String, userId As String, Optional details As String = "")
        Try
            Dim clientIP As String = GetClientIP()
            Dim logMessage As String = "SECURITY EVENT: " & eventType & " | User: " & userId & " | IP: " & clientIP & " | Details: " & details & " | Time: " & DateTime.Now.ToString()
            System.Diagnostics.Debug.WriteLine(logMessage)

            ' Optional: Log to database security table
            ' LogToSecurityDatabase(eventType, userId, clientIP, details)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Security logging error: " & ex.Message)
        End Try
    End Sub

    ' Legacy compatibility wrapper
    Private Sub Msg(page As Page, message As String)
        ' Convert legacy Msg calls to new ShowMessage method
        If message.Contains("Dikemaskini") OrElse message.Contains("Disimpan") Then
            ShowMessage("Password has been changed successfully.", "success")
        ElseIf message.Contains("Tidak Sama") Then
            ShowMessage("New passwords do not match. Please try again.", "error")
        Else
            ShowMessage(message, "info")
        End If
    End Sub

#End Region

#Region "Configuration Properties"

    ' Database connection string property
    Private ReadOnly Property ServerId() As String
        Get
            If Session("ServerId") IsNot Nothing Then
                Return Session("ServerId").ToString()
            End If
            Return System.Configuration.ConfigurationManager.ConnectionStrings("DefaultConnection").ConnectionString
        End Get
    End Property

#End Region

#Region "AJAX Methods for Email Service Health"

    <System.Web.Services.WebMethod()>
    Public Shared Function CheckEmailServiceHealth() As String
        Try
            ' Get email service configuration
            Dim baseUrl As String = System.Configuration.ConfigurationManager.AppSettings("EmailServiceBaseUrl")
            If baseUrl Is Nothing OrElse baseUrl.Trim() = "" Then
                baseUrl = "http://localhost:5000"
            End If
            
            ' Test email service connection
            Dim testClient As New EmailServiceClient(baseUrl)
            Dim healthResult As String = testClient.CheckHealth()
            
            ' Return JSON response based on health check result
            If healthResult.Contains("healthy") Then
                Return "{""status"":""online"",""message"":""Email service is operational""}"
            Else
                Return "{""status"":""offline"",""message"":""Email service is not responding: " & healthResult.Replace("""", "'") & """}"
            End If
            
        Catch ex As Exception
            Return "{""status"":""offline"",""message"":""Email service connection failed: " & ex.Message.Replace("""", "'") & """}"
        End Try
    End Function

#End Region

End Class