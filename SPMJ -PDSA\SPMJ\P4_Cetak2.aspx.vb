﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm46
    Inherits System.Web.UI.Page
    Public x As String

    Public Sub Surat_Majikan_Baru()
        x = ""
        x += Header_Surat(1, 1)
        Dim i As Int16 = 2
        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Tuan : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Kami : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> KKM 87/A3/1/158(   )</td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Tarikh &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  :</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'>  " + Tarikh(1) + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br/>Pengurus Sumber Manusia"
        x += "<br/>" + StrConv(Session("mj_nama"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("mj_alamat"), VbStrConv.ProperCase)
        If Session("mj_alamat1") = "" Or Session("mj_alamat1") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("mj_alamat1"), VbStrConv.ProperCase)
        If Session("mj_alamat2") = "" Or Session("mj_alamat2") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("mj_alamat2"), VbStrConv.ProperCase)
        x += "<br/>" + Session("mj_poskod") + ", " + StrConv(Session("mj_bandar"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("mj_negeri"), VbStrConv.ProperCase) + "."
        x += "<br/>"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt;'>Tuan/Puan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>KEPUTUSAN MESYUARAT PENGAMBILAN PERKHIDMATAN JURURAWAT TERLATIH WARGANEGARA ASING"
        If Session("mj_jenis") = 1 Then x += " SEBAGAI INSTRUKTOR KLINIKAL DAN PENGAJAR JURURAWAT"
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'>Dengan segala hormatnya merujuk perkara di atas."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Dengan ini dimaklumkan bahawa Mesyuarat Jawatankuasa Kelulusan Pengambilan Perkhidmatan Jururawat Terlatih Warganegara Asing "
        If Session("mj_jenis") = 1 Then x += "sebagai Instruktor Klinikal dan Pengajar Jururawat "
        x += "telah diadakan pada ..................... Berikut adalah senarai keputusan "
        If Session("mj_jenis") = 0 Then x += "Jururawat Terlatih "
        If Session("mj_jenis") = 1 Then x += "Instruktor Klinikal dan Pengajar Jururawat "
        x += "Warganegara Asing mengikut "
        If Session("mj_jenis") = 0 Then x += "fasiliti kesihatan "
        If Session("mj_jenis") = 1 Then x += "institusi "
        x += "yang memohon perkhidmatan mereka seperti di lampiran 1. "
        x += "</div>"
        i += 1

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
        If Session("mj_jenis") = 0 Then x += "Jururawat Terlatih "
        If Session("mj_jenis") = 1 Then x += "Instruktor Klinikal dan Pengajar Jururawat "
        x += "Warganegara Asing yang telah diluluskan perlu memohon Perakuan Sementara (TPC) dari Lembaga Jururawat Malaysia (LJM) melalui majikan sebelum memulakan perkhidmatan. "
        x += "<b>Segala urusan pas penggajian hendaklah dibuat melalui majikan tuan/puan.</b>"
        x += "</div>"
        i += 1

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sehubungan dengan itu, Jururawat Terlatih Warganegara Asing yang diluluskan perlu melengkapkan perkara tersebut :-</div>"
        x += "<br/>"

        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'>"        
        x += "<tr><td style='width:9.5%;'></td><td style='vertical-align:top;' >4.1</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "Majikan perlu mengemukakan borang permohonan <i>Nurses Registration 1985 part II 10(1) Application For Temporary Practicing Certificate (TPC) </i>"
        x += "<b>berserta dokumen sokongan </b>yang telah <b>diakui sah </b>kepada Lembaga Jururawat Malaysia (LJM) untuk proses pengeluaran Perakuan Pengamalan Sementara (TPC) <b>sebelum</b> memohon "
        If Session("mj_jenis") = 1 Then x += "kelulusan pas pengajian dari Kementerian Pengajian Tinggi Malaysia. Selepas itu, majikan perlu memohon "
        x += "<b>kelulusan <i>Multiple Entry Visa </i></b> dari Jabatan Imigresen.</td></tr>"
        x += "<tr><td style='width:9.5%;'></td><td style='vertical-align:top;' >4.2</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "Setelah majikan <b>mendapat TPC "
        If Session("mj_jenis") = 1 Then x += ", kelulusan pas pengajian </b> dari Kementerian Pengajian Tinggi Malaysia <b>"
        x += "</b>dan<b> <i>Multiple Entry Visa</i></b> dari Jabatan Imigresen Malaysia, Jururawat Terlatih Warganegara Asing tersebut <b>dibenarkan berkhidmat</b> di "
        If Session("mj_jenis") = 0 Then x += "Fasiliti Kesihatan "
        If Session("mj_jenis") = 1 Then x += "Kolej / Universiti "
        x += "yang mengambil perkhidmatan mereka.</td></tr>"
        x += "</table>"
        i += 1

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tawaran kelulusan ini hanya sah laku dalam tempoh <b>enam (6) bulan</b> dari tarikh kelulusan dan <b>tamat pada --------------</b>. Permohonan Perakuan Pengamalan "
        x += "Sementara (TPC) perlu dibuat sebelum tamat tempoh tawaran kelulusan. <b>Kelewatan/kegagalan majikan berbuat demikian menyebabkan kelulusan TPC terbatal dan permohonan pendaftaran perakuan pengamalan "
        x += "sementara (TPC) perlu dibuat semula.</b></div>"
        i += 1

        If Session("mj_jenis") = 0 Then
            x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
            x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>Pihak tuan diingatkan</b> bahawa penggajian Jururawat Terlatih Warganegara Asing di Fasiliti Kesihatan <b>tidak dibenarkan bertugas di Obstetrik seperti yang "
            x += "tertera di perenggan 1 dalam Surat <i>Approval For Temporary Practicing Certificate (TPC)</i></b> tertakluk kepada syarat LJM. <b>Mereka hanya dibenarkan bertugas di lain-lain disiplin kecuali disiplin Obstetrik.</b>"
            x += "</div>"
            i += 1
        End If

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sila buat pembayaran<b> RM 70.00 fee</b> untuk setiap <b>TPC</b>. Borang permohonan TPC hendaklah "
        x += "dikembalikan ke Lembaga Jururawat Malaysia setelah dilengkapkan bersama <b> satu (1)</b> keping "
        x += "gambar berwarna berukuran pasport, salinan kontrak, salinan pasport (muka hadapan sahaja) yang diakui sah."
        x += "</div>"
        i += 1

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sehubungan dengan itu, Jururawat Terlatih Warganegara Asing yang telah diluluskan perlu <b>menjalani program <i>mentoring</i> yang telah disusun oleh majikan.</b> Sila hantar "
        x += "<b>laporan <i>mentoring</i></b> dan "
        If Session("mj_jenis") = 0 Then x += "melengkapkan borang <i><b>rekod proficiency procedure</b></i> dalam tempoh <b>tiga (3) bulan</b> "
        If Session("mj_jenis") = 1 Then x += "<i><b>proficiency</b></i> dalam tempoh <b>tiga (3) bulan</b> "
        x += "dari tarikh pemohon mula berkhidmat di organisasi tuan/puan ke Lembaga Jururawat Malaysia (LJM) bersama-sama laporan prestasi."
        x += "</div>"
        i += 1

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += i & ".&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Bersama-sama ini disertakan surat <i>Approval for Temporary  Practicing Certificate (TPC)</i> "
        x += " untuk majikan dan pemohon, <i>Nurses Registration 1985 Part II 10(1) Application For Temporary Practicing Certificate (TPC)</i> "
        If Session("mj_jenis") = 0 Then x += "serta borang rekod <i>proficiency procedure</i> "
        x += "untuk pemohon dan resit bayaran bernombor ----------------------- berjumlah RM ----------- (Ringgit Malaysia: ---------------- Sahaja) untuk makluman tuan/puan."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"
        x += "<br/>"
        x += "<br/>"
        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ. ABD. WAHAB) </b>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."
        x += "<br> s.k - Fail Program"
        x += "</div>"

        x += Footer_Surat()

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Public Sub Surat_Majikan_Gagal()
        'Dim s As String = "http://localhost:4844/surat/"
        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Tuan : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Kami : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> KKM 87/A3/1/158(   )</td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Tarikh &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  :</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'>  " + Tarikh(1) + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<div style='font-family: Arial; font-size: 12pt;'>Pengurus Sumber Manusia"
        x += "<br/>" + StrConv(Session("mj_nama"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("mj_alamat"), VbStrConv.ProperCase)
        If Session("mj_alamat1") = "" Or Session("mj_alamat1") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("mj_alamat1"), VbStrConv.ProperCase)
        If Session("mj_alamat2") = "" Or Session("mj_alamat2") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("mj_alamat2"), VbStrConv.ProperCase)
        x += "<br/>" + Session("mj_poskod") + ", " + StrConv(Session("mj_bandar"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("mj_negeri"), VbStrConv.ProperCase) + "."
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt;'>Tuan/Puan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>KEPUTUSAN MESYUARAT PENGAMBILAN PERKHIDMATAN JURURAWAT TERLATIH WARGANEGARA ASING"
        If Session("mj_jenis") = 0 Then x += " "
        If Session("mj_jenis") = 1 Then x += " SEBAGAI PENGAJAR JURURAWAT DAN INSTRUKTOR KLINIKAL"
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'>Dengan segala hormatnya merujuk perkara di atas."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "2. &nbsp; &nbsp; &nbsp;Dengan ini dimaklumkan bahawa Mesyuarat Jawatankuasa Kelulusan Pengambilan Perkhidmatan Jururawat Terlatih Warganegara Asing "
        If Session("mj_jenis") = 1 Then x += "sebagai Pengajar Jururawat dan Instruktor Klinikal "
        x += "telah diadakan pada ................ dan mesyuarat tersebut telah menetapkan keputusan seperti di lampiran 1."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "3. &nbsp; &nbsp; &nbsp; &nbsp; Bersama ini dilampirkan resit bayaran bernombor ----------------------- berjumlah RM ----------- (Ringgit Malaysia: ---------------- Sahaja) untuk makluman tuan/puan."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"

        x += "<br/>"
        x += "<br/>"
        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ. ABD. WAHAB) </b>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br> s.k Fail Program"
        x += "</div>"

        x += Footer_Surat()

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select dc_amalan 'MAJIKAN', alamat, alamat1, alamat2, poskod, bandar, dc_negeri 'NEGERI' from pn_tpt_amalan pta inner join pn_negeri pn on pta.negeri=pn.id_negeri where dc_amalan like '%" & Tx_Nama.Text & "%'  order by dc_amalan"
        Tb = "pn_tpt_amalan"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Nama.Text.Trim = "" Or Cb_Jenis.SelectedIndex < 0 Then Exit Sub
        Cari("")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(3).Visible = False
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(6).Visible = False
        e.Row.Cells(7).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("mj_nama") = Gd.SelectedRow.Cells(2).Text
        Session("mj_alamat") = Gd.SelectedRow.Cells(3).Text
        Session("mj_alamat1") = Gd.SelectedRow.Cells(4).Text
        Session("mj_alamat2") = Gd.SelectedRow.Cells(5).Text
        Session("mj_poskod") = Gd.SelectedRow.Cells(6).Text
        Session("mj_bandar") = Gd.SelectedRow.Cells(7).Text
        Session("mj_negeri") = Gd.SelectedRow.Cells(8).Text
        Session("mj_jenis") = Cb_Jenis.SelectedIndex
        If Chk_Gagal.Checked Then Surat_Majikan_Gagal() Else Surat_Majikan_Baru()
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
      

    End Sub
End Class