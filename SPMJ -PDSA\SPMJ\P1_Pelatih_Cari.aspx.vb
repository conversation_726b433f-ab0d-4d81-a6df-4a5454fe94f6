﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm3
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'Comment Original 26092019 - OSH
        'Dim List_Adp As New SqlDataAdapter("select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ',tj.<PERSON>c_Tajaan as 'TAJAAN' from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan where p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Se<PERSON>.SelectedValue & X & " order by p.nama", Cn)

        'Comment Original 25042022 - OSH
        'Add Month and year 26092019 - OSH 
        'Dim List_Adp As New SqlDataAdapter("select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ',tj.Dc_Tajaan as 'TAJAAN', p.sesi_bulan as 'BULAN', p.sesi_tahun as 'TAHUN' from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan where p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by p.nama", Cn)

        'Comment Original 12072023 - OSH
        'Add Months Labeling  25042022 - OSH 
        ' Dim List_Adp As New SqlDataAdapter("select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ',tj.Dc_Tajaan as 'TAJAAN', case p.sesi_bulan  when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end as 'BULAN', p.sesi_tahun as 'TAHUN', p.sesi_bulan from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan where p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & " order by p.nama", Cn)

        'add query for pass cannidate indicator 12072023 -OSH
        'Dim List_Adp As New SqlDataAdapter("select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ',tj.Dc_Tajaan as 'TAJAAN', case p.sesi_bulan  when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end as 'BULAN', p.sesi_tahun as 'TAHUN', p.sesi_bulan, p.status from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan where p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & X & "  order by p.nama", Cn)

        'Add month year 05072024 - OSH 
        Dim List_Adp As New SqlDataAdapter("select p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ',tj.Dc_Tajaan as 'TAJAAN', case p.sesi_bulan  when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end as 'BULAN', p.sesi_tahun as 'TAHUN', p.sesi_bulan, p.status from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej left outer join pn_tajaan tj on p.tajaan = tj.id_tajaan where p.id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " and " & Cb_Sesi.SelectedValue & X & "  order by p.nama", Cn)

        List_Adp.Fill(List_Data, "pelatih")
        Gd.DataSource = List_Data.Tables("pelatih")
        DataBind()
        Gd.Visible = True

    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then Else e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
        'Hidden Months Value Cell 25042022 - OSH
        e.Row.Cells(8).Visible = False
        'Hidden Status Value Cell 25042022 - OSH
        e.Row.Cells(9).Visible = False
    End Sub

    Private Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Gd.SelectedIndexChanged
        'If (Gd.SelectedRow.Cells(6).Text >= 7 And Gd.SelectedRow.Cells(7).Text > 2017) And Cb_Kursus.SelectedValue = 5 Then
        'Check_Gamma(Gd.SelectedRow.Cells(3).Text.Trim)
        'Comment Original 25042022 - OSH 
        'Add degree redirect for 2018 admission policy and certificate and diploma 29092019 - OSH
        'If (Gd.SelectedRow.Cells(6).Text >= 7 And Gd.SelectedRow.Cells(7).Text > 2017) And Cb_Kursus.SelectedValue = 5 Then
        'ElseIf (Gd.SelectedRow.Cells(6).Text >= 7 And Gd.SelectedRow.Cells(7).Text > 2017) Then
        'Comment Original 25042022 - OSH  
        'Change Months Value Cell 25042022 - OSH
        'If (Gd.SelectedRow.Cells(8).Text >= 7 And Gd.SelectedRow.Cells(7).Text > 2017) And Cb_Kursus.SelectedValue = 5 Then
        '    Session("nokp") = Gd.SelectedRow.Cells(3).Text
        '    Session("j_kursus") = Cb_Kursus.SelectedValue
        '    'Comment Original 25042022 -OSH
        '    'Session("bulan") = Gd.SelectedRow.Cells(6).Text
        '    'Change Months Value Cell 25042022 - OSH
        '    Session("bulan") = Gd.SelectedRow.Cells(8).Text
        '    Session("tahun") = Gd.SelectedRow.Cells(7).Text
        '    Response.Redirect("p1_pelatih_pinda5.aspx")

        '    'Add Rule for Check Delay Application 25042022 - OSH  
        'ElseIf Gd.SelectedRow.Cells(7).Text < 2019 And Cb_Kursus.SelectedValue = 5 Then
        '    Check_Delay(Gd.SelectedRow.Cells(3).Text)
        'Else
        '    Session("nokp") = Gd.SelectedRow.Cells(3).Text
        '    'add session type course 31052012 
        '    Session("j_kursus") = Cb_Kursus.SelectedValue
        '    'Response.Redirect("p1_pelatih_pinda.aspx")
        '    Response.Redirect("p1_pelatih_pinda4.aspx")
        'End If

        Session("nokp") = Gd.SelectedRow.Cells(3).Text
        Session("j_kursus") = Cb_Kursus.SelectedValue
        Session("bulan") = Gd.SelectedRow.Cells(8).Text
        Session("tahun") = Gd.SelectedRow.Cells(7).Text

        If Session("j_kursus") = "4" Or Session("j_kursus") = "8" Then
            'Comment Original 15082022 - OSH 
            'Response.Redirect("p1_pelatih_pinda6.aspx")
            'Response.Redirect("p1_pelatih_pinda7.aspx") 'Adjust subjeects position 15082022 - OSH 
            Response.Redirect("p1_pelatih_pinda8.aspx") ' Include APC details
        End If

        'Check Old Or New Form 
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Comment Original 13072023 - OSH  
        'Cmd.CommandText = "Select DISTINCT  P.KELAYAKAN, P.SESI_BULAN, P.SESI_TAHUN, A.ID_JENIS FROM PELATIH  P RIGHT JOIN PELATIH_KELAYAKAN  A On (P.NOKP = A.NOKP)  WHERE P.NOKP =  '" & Gd.SelectedRow.Cells(3).Text.Trim & "'"
        Cmd.CommandText = "SELECT DISTINCT  P.KELAYAKAN, P.SESI_BULAN, P.SESI_TAHUN,  A.ID_JENIS FROM PELATIH  P LEFT OUTER JOIN PELATIH_KELAYAKAN  A ON (P.NOKP = A.NOKP)  WHERE P.NOKP =  '" & Gd.SelectedRow.Cells(3).Text.Trim & "'"
        'Comment Original 26062023 -OSH 
        'Cmd.CommandText = "SELECT KELAYAKAN, SESI_BULAN, SESI_TAHUN FROM PELATIH WHERE NOKP = '" & Gd.SelectedRow.Cells(3).Text.Trim & "'"
        'Cmd.CommandText = "SELECT TOP 1 ID_JENIS FROM PELATIH_KELAYAKAN WHERE NOKP = '" & Gd.SelectedRow.Cells(3).Text.Trim & "'"
        Rdr = Cmd.ExecuteReader()
        Rdr.Read()


        'Check Squree reorder 19122022 - OSH
        'If Rdr(1) < 7 And Rdr(2) < 2018 Then
        If Rdr(2) < 2018 Then
            Response.Redirect("p1_pelatih_pinda8.aspx")
            'Comment Original 26062023 -OSH 
            'ElseIf Rdr(0) = "SPM" And Rdr(1) > 6 And Rdr(2) > 2017 Then
            '    Response.Redirect("p1_pelatih_pinda7.aspx")
            'improvement check  Old or New Format 260602023 - OSH
        ElseIf Rdr(0) = "SPM" And Rdr(1) > 6 And Rdr(2) > 2017 And Not IsDBNull(Rdr(3)) Then
            'Response.Redirect("p1_pelatih_pinda7.aspx")
            Response.Redirect("p1_pelatih_pinda9.aspx")
            'For 2019 and benyond 13092024 - OSH 
        ElseIf Rdr(1) > 0 And Rdr(2) > 2018 And Not IsDBNull(Rdr(3)) Then
            'Response.Redirect("p1_pelatih_pinda7.aspx")
            Response.Redirect("p1_pelatih_pinda9.aspx")
            'Comment Original 04082023 - OSH
            'ElseIf Rdr(0) = "SPM" And Rdr(1) > 6 And Rdr(2) > 2017 And IsDBNull(Rdr(3)) Then
            'Fixing Loading Error Due To Date Ranges 04082023 - OSH  
        ElseIf Rdr(0) = "SPM" And (Rdr(1) > 6 And Rdr(2) > 2017) Or (Rdr(1) > 0 And Rdr(2) > 2018) And IsDBNull(Rdr(3)) Then
            Response.Redirect("p1_pelatih_pinda4a.aspx")
            'Comment Original 13042023 - OSH  
            'ElseIf Rdr(0) = "IGCSE" And (Rdr(1) > 6 And Rdr(2) > 2017) Or (Rdr(1) > 0 And Rdr(2) > 2018) Then
            'Fix loophole tie igcse with 2018 and benyond 13042023 -OSH
        ElseIf Rdr(0) = "IGCSE" And ((Rdr(1) > 6 And Rdr(2) > 2017) Or (Rdr(1) > 0 And Rdr(2) > 2018)) Then
            Response.Redirect("p1_pelatih_pinda9.aspx")
            'Fix STPM load Incorrect 26082024 - OSH 
        ElseIf Rdr(0) = "STPM" And ((Rdr(1) > 6 And Rdr(2) > 2017) Or (Rdr(1) > 0 And Rdr(2) > 2018)) Then
            Response.Redirect("p1_pelatih_pinda9.aspx")
        Else
            'Add load default 12042023 - OSH
            Response.Redirect("p1_pelatih_pinda7.aspx")
        End If


        ''Check IGCSE 28102022 - OSH
        'If Not IsDBNull(Rdr(0)) And Rdr(0) = "IGCSE" Then
        '    Response.Redirect("p1_pelatih_pinda9.aspx")
        'ElseIf Not IsDBNull(Rdr(0)) Then
        '    'Session("nokp") = Gd.SelectedRow.Cells(3).Text
        '    'Session("j_kursus") = Cb_Kursus.SelectedValue
        '    'Session("bulan") = Gd.SelectedRow.Cells(8).Text
        '    'Session("tahun") = Gd.SelectedRow.Cells(7).Text
        '    'Response.Redirect("p1_pelatih_pinda5.aspx")'Comment original 10082022 - OSH
        '    Response.Redirect("p1_pelatih_pinda7.aspx") 'Adjust subjeects position 10082022 - OSH 
        'ElseIf IsDBNull(Rdr(0)) Then 'Check record id null 13122022 -OSH
        '    'Session("nokp") = Gd.SelectedRow.Cells(3).Text
        '    'Session("j_kursus") = Cb_Kursus.SelectedValue
        '    'Session("bulan") = Gd.SelectedRow.Cells(8).Text
        '    'Session("tahun") = Gd.SelectedRow.Cells(7).Text 'Comment original 10082022 - OSH
        '    Response.Redirect("p1_pelatih_pinda8.aspx") 'Adjust subjeects position 10082022 - OSH 
        '    'Response.Redirect("p1_pelatih_pinda6.aspx")
        '    'Response.Redirect("p1_pelatih_pinda4.aspx")
        'End If
        'Rdr.Close()
    End Sub

    Private Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'Fix security access 23072018 - OSH 
        If Session("ORIGIN") = "yes" Then
            If IsPostBack Then Exit Sub
            'KOLEJ
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ where jenis between 1 and 2 ORDER BY DC_KOLEJ"
            Rdr = Cmd.ExecuteReader()
            Cb_Kolej.Items.Clear()
            Cb_Kolej.Items.Add("")
            While Rdr.Read
                Cb_Kolej.Items.Add(Rdr(0))
                Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()

            'KURSUS
            Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
            Rdr = Cmd.ExecuteReader()
            Cb_Kursus.Items.Clear()
            Cb_Kursus.Items.Add("")
            While Rdr.Read
                Cb_Kursus.Items.Add(Rdr(0))
                Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
            Cn.Close()
        Else
            Response.Redirect("p0_Login.aspx")
        End If

        'Comment Ori 23072018 - OSH
        'If IsPostBack Then Exit Sub
        ''KOLEJ
        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ where jenis between 1 and 2 ORDER BY DC_KOLEJ"
        'Rdr = Cmd.ExecuteReader()
        'Cb_Kolej.Items.Clear()
        'Cb_Kolej.Items.Add("")
        'While Rdr.Read
        '    Cb_Kolej.Items.Add(Rdr(0))
        '    Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()

        ''KURSUS
        'Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        'Rdr = Cmd.ExecuteReader()
        'Cb_Kursus.Items.Clear()
        'Cb_Kursus.Items.Add("")
        'While Rdr.Read
        '    Cb_Kursus.Items.Add(Rdr(0))
        '    Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()
        'Cn.Close()
    End Sub

    Protected Sub cmd_Word_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Word.Click
        Response.Clear()
        Response.Charset = ""
        Response.ContentType = "application/msword"

        Dim strFileName As String = "GenerateDocument" + ".doc"
        Response.AddHeader("Content-Dispositon", "inline;filename=" + strFileName)

        Dim IsiHTML As New StringBuilder

        IsiHTML.Append("<table style='border: 1px solid #000000; font-variant: small-caps; font-family: Arial; font-size: 12pt; font-weight: bold;'>")

        Dim i As Int16
        IsiHTML.Append("<tr><td>" & Gd.HeaderRow.Cells(2).Text & "</td>")
        IsiHTML.Append("<td>" & Gd.HeaderRow.Cells(3).Text & "</td>")
        IsiHTML.Append("<td>" & Gd.HeaderRow.Cells(4).Text & "</td></tr>")

        For i = 0 To Gd.Rows.Count - 1
            IsiHTML.Append("<tr><td>" & Gd.Rows.Item(i).Cells(2).Text & "</td>")
            IsiHTML.Append("<td>" & Gd.Rows.Item(i).Cells(3).Text & "</td>")
            IsiHTML.Append("<td>" & Gd.Rows.Item(i).Cells(4).Text & "</td></tr>")
        Next
        IsiHTML.Append("</table>")

        Response.Write(IsiHTML)
        Response.End()
        Response.Flush()
    End Sub

    Protected Sub cmd_Excel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Excel.Click
        Response.Clear()
        Response.Charset = ""
        Response.ContentType = "application/msexcel"

        Dim strFileName As String = "GenerateDocument" + ".xls"
        Response.AddHeader("Content-Dispositon", "inline;filename=" + strFileName)

        Dim IsiHTML As New StringBuilder

        'IsiHTML.Append("<h1 title='Heading' align='Center' style='font-family: arial; font-size:8pt; color:black;'><u>Tajuk Sini...</u></h1>")
        IsiHTML.Append("<table'>")
        Dim i As Int16
        IsiHTML.Append("<tr><td>" & Gd.HeaderRow.Cells(2).Text & "</td>")
        IsiHTML.Append("<td>" & Gd.HeaderRow.Cells(3).Text & "</td>")
        IsiHTML.Append("<td>" & Gd.HeaderRow.Cells(4).Text & "</td></tr>")

        For i = 0 To Gd.Rows.Count - 1
            IsiHTML.Append("<tr><td>" & Gd.Rows.Item(i).Cells(2).Text & "</td>")
            IsiHTML.Append("<td>" & Gd.Rows.Item(i).Cells(3).Text & "</td>")
            IsiHTML.Append("<td>" & Gd.Rows.Item(i).Cells(4).Text & "</td></tr>")
        Next
        IsiHTML.Append("</table>")

        Response.Write(IsiHTML)
        Response.End()
        Response.Flush()
    End Sub

    Protected Sub cmdHantar1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar1.Click
        'Msg
        Dim X As String = ""
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi, "
        If Cb_Kursus.Text.Trim = "" Then X += "Jenis Kursus, "
        'If Cb_Sesi.Text.Trim = "" Then X += "Sesi Pengambilan, "
        'Comment Original 05072024 - OSH 
        'If Cb_Kolej.Text.Trim = "" Then X += "Sesi Pengambilan, "
        If X.Trim = "" Then Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila lengkapkan maklumat berikut: " & X) : Exit Sub

        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" Then Exit Sub
        'Comment Original 07112023 - OSH 
        Cari("p.nama like '" & Replace(Tx_Nama.Text, "'", "''") & "%' and p.nokp like '" & Tx_NoKP.Text & "%'")
        'Fix individual records incorrect output 07112023 - OSH 
        'Cari(" and p.nama like '" & Replace(Tx_Nama.Text, "'", "''") & "%' and p.nokp like '" & Tx_NoKP.Text & "%'")
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Comment Original 25042022 - OSH 
        'Cmd.CommandText = "select distinct sesi_bulan, sesi_tahun, case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end from pelatih where id_kolej = " & Cb_Kolej.SelectedValue & " order by sesi_tahun, sesi_bulan"

        'Add Filter By Academic Programme Types 25042022 - OSH  
        Cmd.CommandText = "select distinct sesi_bulan, sesi_tahun, case sesi_bulan when 1 then 'JANUARI' when 2 then 'FEBRUARI' when 3 then 'MAC' when 4 then 'APRIL' when 5 then 'MEI' when 6 then 'JUN' when 7 then 'JULAI' when 8 then 'OGOS' when 9 then 'SEPTEMBER' when 10 then 'OKTOBER' when 11 then 'NOVEMBER' when 12 then 'DISEMBER' end from pelatih where id_kolej = " & Cb_Kolej.SelectedValue & " and j_kursus = " & Cb_Kursus.SelectedValue & " order by sesi_tahun, sesi_bulan"
        Rdr = Cmd.ExecuteReader()
        Cb_Sesi.Items.Clear()
        Cb_Sesi.Items.Add("SEMUA")
        Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Sesi.Items.Add(Rdr(1) & " - " & Rdr(2))
            Cb_Sesi.Items.Item(Cb_Sesi.Items.Count - 1).Value = " p.sesi_bulan = " & Rdr(0) & " and p.sesi_tahun = " & Rdr(1) & " and "
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Private Sub Gd_RowDataBound(sender As Object, e As GridViewRowEventArgs) Handles Gd.RowDataBound
        'Add colour code on retation and non-application 10022017 - OSH
        Try
            If e.Row.RowType = DataControlRowType.DataRow Then
                If (e.Row.Cells(9).Text = "1") Then
                    e.Row.BackColor = Drawing.Color.LightGoldenrodYellow
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub
End Class