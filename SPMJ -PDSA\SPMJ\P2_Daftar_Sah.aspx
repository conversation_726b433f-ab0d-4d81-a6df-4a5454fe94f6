﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P2_Daftar_Sah.aspx.vb" Inherits="SPMJ.WebForm16" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
        .style2
        {
            width: 660px;
        }
        .style3
        {
            height: 23px;
            width: 660px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td class="style2">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td class="style2">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style3">pendaftaran penuh - pengesahan pendaftaran</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style2">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style3" valign="middle">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="16px" 
            tabIndex="36" Width="120px" Enabled="False" 
                            ReadOnly="True">JENIS PENDAFTARAN</asp:TextBox>
                <asp:DropDownList ID="Cb_Jenis" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" Width="190px" CssClass="std" 
                    AutoPostBack="True">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT BERDAFTAR</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT MASYARAKAT</asp:ListItem>
                    <asp:ListItem Value="3">PENOLONG JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">BIDAN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="center" class="style1"></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White" class="style2">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td class="style2">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td class="style2">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="712px" GridLines="Horizontal" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:TemplateField ShowHeader="False" HeaderText="JANA AUTO">
                                    <HeaderTemplate>
                                        Jana Auto
                                    </HeaderTemplate>
                                    <ItemTemplate>
                                        <asp:CheckBox ID="chk_pilih" runat="server" AutoPostBack="True" 
                                    oncheckedchanged="chk_pilih_CheckedChanged" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Jana Manual">
                                    <EditItemTemplate>
                                        <asp:CheckBox ID="CheckBox1" runat="server" />
                                    </EditItemTemplate>
                                    <ItemTemplate>
                                        <asp:CheckBox ID="CheckBox1" runat="server" AutoPostBack="True" 
                                    oncheckedchanged="Ch_CheckedChanged" 
                                    Enabled="False" />
                                        <asp:TextBox ID="Tx_NoPd" runat="server" BackColor="#FFFFDF" Font-Names="Arial" 
                                    Font-Size="8pt" ontextchanged="tx_nopd_TextChanged" Visible="False" 
                                    Width="60px"></asp:TextBox>
                                        <asp:TextBox ID="Tx_Tkh_Daftar" runat="server" CssClass="std" Width="75px" 
                                    Visible="False" BackColor="#FFFFDF"></asp:TextBox>
                                        <cc1:MaskedEditExtender ID="Tx_Tkh_Daftar_MaskedEditExtender" runat="server" 
                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                    CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                    CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                    Enabled="True" Mask="99/99/9999" MaskType="Date" 
                                    TargetControlID="Tx_Tkh_Daftar" UserDateFormat="DayMonthYear">
                                        </cc1:MaskedEditExtender>
                                        <cc1:CalendarExtender ID="Tx_Tkh_Daftar_CalendarExtender" runat="server" 
                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                    TargetControlID="Tx_Tkh_Daftar">
                                        </cc1:CalendarExtender>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="True" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td class="style2">
                &nbsp;</td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td class="style2">
                <br />
                <asp:Button ID="cmd_Jana" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="21px" tabIndex="3" Text="JANA" Width="100px" 
                    Visible="False" />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
