﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class PN_Nota_Kolej
    Inherits System.Web.UI.Page

    Public Sub Reset()
        Session("PN_Pinda") = False
        Tx_Mesej.Text = ""
        Tx_Tajuk.Text = ""
        cb_baru.Visible = False
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select * from kj_mesej"
        Tb = "kj_mesej"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Exam", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Cari("")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(1).Visible = False
        e.Row.Cells(0).Wrap = False
        'e.Row.Cells(2).Visible = False
        'e.Row.Cells(4).Visible = False
        'e.Row.Cells(5).Visible = False
        'e.Row.Cells(7).Visible = False
        ''e.Row.Cells(8).Visible = False
        'e.Row.Cells(9).Visible = False
        'e.Row.Cells(10).Visible = False
        'e.Row.Cells(11).Visible = False

        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex < 0 Then Exit Sub
        e.Row.Cells(4).Text = Replace(e.Row.Cells(4).Text, "&lt;br/&gt;", vbCrLf)
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("PN_Padam") Then
            Session("PN_Padam") = False
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Dim SQL As String
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "delete from kj_mesej where tajuk='" & Gd.SelectedRow.Cells(3).Text & "'"
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Reset()
                Cari("")
                'Msg(Me, "Rekod Telah Dipadam...")
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
            Cn.Close()
            Exit Sub
        End If
        Session("PN_Pinda") = True
        cb_baru.Visible = True
        If Gd.SelectedRow.Cells(3).Text <> "&nbsp;" Then Tx_Tajuk.Text = Gd.SelectedRow.Cells(3).Text
        If Gd.SelectedRow.Cells(4).Text <> "&nbsp;" Then Tx_Mesej.Text = Gd.SelectedRow.Cells(4).Text
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Tajuk.Text = "" Then Exit Sub
        If Tx_Mesej.Text = "" Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim SQL As String

        'If Tx_Id.Text = "" Then
        'Else
        '    SQL = "update pn_tpt_amalan set "
        '    SQL += "dc_amalan = '" & Tx_Tajuk.Text & "', "
        '    SQL += "alamat = '" & Tx_Mesej.Text & "', "
        '    SQL += "poskod = '" & Tx_Poskod.Text & "', "
        '    SQL += "bandar = '" & Tx_Bandar.Text & "', "
        '    SQL += "negeri = '" & Cb_Negeri.Text & "', "
        '    SQL += "sektor = " & Cb_Sektor.Text & ", "
        '    SQL += "tel = '" & Tx_Tel.Text & "', "
        '    SQL += "fax = '" & Tx_Fax.Text & "' "
        '    SQL += "where id_amalan = " & Tx_Id.Text
        '    Cmd.CommandText = SQL
        '    Cmd.ExecuteNonQuery()
        '    Cn.Close()
        '    Reset()
        '    Cari(Cb_Negeri.SelectedValue)
        '    Msg(Me, "Rekod Telah Disimpan...")
        '    Exit Sub
        'End If

        SQL = "insert kj_mesej (tkh_mesej, tajuk, mesej) values (getdate(),  '" & Tx_Tajuk.Text & "',  '" & Apo(Replace(HyperLink(ImageLink(Tx_Mesej.Text)), vbCrLf, "<br/>")) & "')"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cari("")
            Msg(Me, "Rekod Telah Disimpan...")
        Catch ex As Exception
            Msg(Me, "Error...")
        End Try
        Cn.Close()
        Reset()
    End Sub

    Protected Sub cb_baru_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cb_baru.Click
        'Reset()
        'cb_baru.Visible = False
        Tx_Mesej.Text = HyperLink(Tx_Tajuk.Text)
    End Sub

    Protected Sub Cb_Padam_Click(ByVal sender As Object, ByVal e As EventArgs)
        Session("Pn_Padam") = True
    End Sub
End Class