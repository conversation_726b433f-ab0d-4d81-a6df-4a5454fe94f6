# Login Flow Debug Test Guide

## Current Issue
Users may be experiencing issues where they are redirected back to the login page after successful password change, rather than progressing to OTP verification or the main application.

## Debug Points Added
We have added comprehensive debug logging throughout the authentication flow:

### p0_Login.aspx.vb Debug Points:
1. Login attempt start/end markers
2. User ID and password validation
3. Database query execution
4. Password verification (both encrypted and plain text)
5. Session variable setting
6. OTP decision logic
7. All Response.Redirect calls with try/catch blocks

### p0_PasswordChangeForced.aspx.vb Debug Points:
1. Session validation on page load
2. Password change operation
3. Database update verification
4. Flag clearing confirmation

### OtpVerification.aspx.vb Debug Points:
1. Session validation for TEMP_USER_ID
2. OTP generation and sending
3. Login completion process

## Testing Steps

### 1. Test Login with Temporary Password
1. Login with a user that has `is_temporary = 1` or `force_change = 1`
2. Check debug output for:
   - Session variables being set correctly
   - Redirect to `p0_PasswordChangeForced.aspx`
3. Change password successfully
4. Check if user is redirected to main application or login page

### 2. Test Login with Encrypted Password (No OTP)
1. Login with user who has `password_migrated = 1` and no email
2. Check debug output for:
   - Password verification using hash
   - OTP decision (should be false due to no email)
   - Redirect to `blank.aspx`

### 3. Test Login with Encrypted Password (With OTP)
1. Login with user who has `password_migrated = 1` and valid email
2. Check debug output for:
   - Password verification using hash
   - OTP decision (should be true)
   - Session variables for OTP (TEMP_USER_ID, USER_EMAIL)
   - Redirect to `OtpVerification.aspx`

### 4. Test OTP Verification Flow
1. After successful OTP entry
2. Check debug output for:
   - Session transfer from TEMP_* to permanent variables
   - Redirect to `blank.aspx`

## Common Issues to Check

### Session Issues
- Are session variables persisting between page redirects?
- Is `Session.Abandon()` being called inappropriately?
- Are there any session timeouts?

### Database Issues
- Are the `is_temporary` and `force_change` flags being cleared properly after password change?
- Is the password hash being stored correctly?

### Redirect Issues
- Are there any exceptions during `Response.Redirect` calls?
- Is the redirect happening with the correct parameters?

### OTP Issues
- Is the email service available?
- Are the TEMP_* session variables being set correctly?

## Debug Output Analysis

Look for these patterns in the debug output:

### Successful Login Flow (No OTP):
```
=== LOGIN ATTEMPT STARTED ===
LOGIN DEBUG: User found in database
LOGIN DEBUG: Password validation successful
LOGIN DEBUG: No OTP required
LOGIN DEBUG: Redirecting to blank.aspx for normal login
LOGIN DEBUG: Redirect to blank.aspx completed
=== LOGIN ATTEMPT COMPLETED ===
```

### Successful Login Flow (With OTP):
```
=== LOGIN ATTEMPT STARTED ===
LOGIN DEBUG: User found in database
LOGIN DEBUG: Password validation successful
LOGIN DEBUG: OTP required: True
LOGIN DEBUG: Setting OTP session variables
LOGIN DEBUG: About to redirect to OtpVerification.aspx
LOGIN DEBUG: Redirect to OTP completed
=== LOGIN ATTEMPT COMPLETED ===
```

### Password Change Flow:
```
=== p0_PasswordChangeForced Session Debug ===
✅ Session validation passed - loading password change form
=== Password Change Debug ===
Password Change Verification:
is_temporary: False
force_change: False
password_migrated: True
```

## Troubleshooting Steps

1. **If users are redirected back to login after password change:**
   - Check if `blank.aspx` exists and is accessible
   - Verify session variables are maintained
   - Check for any errors in the redirect process

2. **If OTP verification fails:**
   - Check if `OtpVerification.aspx` is accessible
   - Verify TEMP_* session variables are set
   - Check email service connectivity

3. **If password verification fails:**
   - Check password hash generation
   - Verify salt storage and retrieval
   - Test fallback password verification

## Configuration Checks

1. **Connection String**: Verify `ServerId` is correctly configured
2. **Email Service**: Check `EmailServiceUrl` in app.config
3. **OTP Setting**: Check `EnableOTP` configuration flag
4. **Session Configuration**: Verify session state is enabled

## Next Steps

If users are still experiencing redirect issues:
1. Enable IIS logging to see actual HTTP requests/responses
2. Check browser developer tools for any JavaScript errors
3. Verify web.config session settings
4. Test with different browsers to rule out client-side issues
5. Check for any global error handlers that might be interfering
