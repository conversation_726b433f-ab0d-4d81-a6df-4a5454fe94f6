﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class P3_RET_Batal
    Inherits System.Web.UI.Page

    Public Sub Cari()
        Dim SQL As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "SELECT DISTINCT  R.NAMA AS NAMA, R.<PERSON>, CASE R.J_DAFTAR WHEN 1 THEN 'JB-' + CAST(NOPD AS VARCHAR(6)) WHEN 2 THEN 'JM-' + CAST(NOPD AS VARCHAR(6)) WHEN 3 THEN 'PJ-' + CAST(NOPD AS VARCHAR(6)) WHEN 4 THEN 'B-' + CAST(NOPD AS VARCHAR(6)) END AS 'NO. PENDAFTARAN', A.<PERSON>_<PERSON>TAL, P.NAMA AS 'PEMOHON',  CASE C.STATUS WHEN 'P' THEN 'BAHARU' END AS 'STATUS', C.PEMPROSES_TKH AS 'TARIKH PEMOHONAN' FROM JT_PENUH R INNER JOIN JT_PENUH_APC A ON (R.J_DAFTAR = A.J_DAFTAR) AND (R.NOKP = A.NOKP) INNER JOIN PN_RON_BATAL C ON (A.ID_BATAL = C.ID_BATAL)INNER JOIN PN_PENGGUNA P ON (C.PEMPROSES_ID = P.ID_PG) WHERE C.STATUS = 'P' ORDER BY 'TARIKH PEMOHONAN' DESC "
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, "ron_batal")
        Gd.DataSource = List_Data.Tables("ron_batal")
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'Add ACL 01102020 - OSH
        If Not Akses_Pg("P4", "Batal", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENGEKALAN NAMA"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Cari()

        'Check Grid View Is Empty Throw Message 01102020 - OSH 
        If Gd.Rows.Count = 0 Then
            Session("Msg_Tajuk") = "PENGEKALAN NAMA"
            Session("Msg_Isi") = "TIADA PEMOHONAN PEMBATALAN"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        'If Gd.Rows.Count > 0 Then cmd_Pilih.Visible = True Else cmd_Pilih.Visible = False
    End Sub


    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        
        e.Row.Cells(0).Width = Unit.Pixel(50)
        e.Row.Cells(1).Width = Unit.Pixel(50)
        e.Row.Cells(5).Visible = False 'RET CANCEL ID
        'e.Row.Cells(7).Visible = False
        'e.Row.Cells(6).Width = Unit.Pixel(150)
        e.Row.Cells(0).HorizontalAlign = HorizontalAlign.Center
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub


    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        If e.Row.Cells(8).Text = "LULUS" Or e.Row.Cells(8).Text = "TOLAK" Then e.Row.Cells(0).Text = ""
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("NAMA") = Gd.SelectedRow.Cells(2).Text
        Session("NOKP") = Gd.SelectedRow.Cells(3).Text
        Session("NOPD") = Gd.SelectedRow.Cells(4).Text
        Session("RET_ID") = Gd.SelectedRow.Cells(5).Text
        Response.Redirect("P3_RET_Batal_Proses.aspx")
    End Sub

    'Protected Sub cmd_Pilih_Click(ByVal sender As Object, ByVal e As EventArgs)
    '    Session("NAMA") = Gd.SelectedRow.Cells(2).Text
    '    Session("NOKP") = Gd.SelectedRow.Cells(3).Text
    '    Session("NOPD") = Gd.SelectedRow.Cells(4).Text
    '    Session("RET_TAHUN") = Gd.SelectedRow.Cells(5).Text
    '    Response.Redirect("P3_RET_Batal_Proses.aspx")
    'End Sub
End Class
