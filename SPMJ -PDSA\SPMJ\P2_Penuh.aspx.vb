﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Net
Imports System.IO
Imports RestSharp
Imports Newtonsoft.Json

Partial Public Class WebForm17
    Inherits System.Web.UI.Page

    Private J_Daftar As Int64 = 0
    Private Umur_Check As Boolean

    Public Sub Isi_APC(ByVal x As String)
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', stuff('0000',6-len(apc_no),len(apc_no),apc_no) as 'NO. APC', CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC', dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR', pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax  from pn_tpt_amalan pta inner join jt_penuh_apc jpa on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc", Cn)
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case ret when 1 then 'RET' else stuff('00000',6-len(apc_no),len(apc_no),apc_no) end as 'NO. APC', CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC', dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR', pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax  from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' and j_daftar=" & J_Daftar & " order by apc_tahun desc", Cn)
        'Comment origanal query 18032013
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case ret when 1 then 'RET' else stuff('00000',6-len(apc_no),len(apc_no),apc_no) end as 'NO. APC', CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC', dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR', pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax  from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc", Cn)
        ' improvement add registration type 18032013
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case ret when 1 then 'RET' else stuff('00000',6-len(apc_no),len(apc_no),apc_no) end as 'NO. APC',CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC',  dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR',  pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax,  case jpa.j_daftar  when 1 then 'JURURAWAT BERDAFTAR' when 2 then 'JURURAWAT MASYARAKAT' when 3 then 'PENOLONG JURURAWAT' end as 'PENDAFTARAN' from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc", Cn) ' COMMENT ORIGNAL 29122016 - OSH
        'Comment Original 1302017 - OSH 
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case when ret= '1' then 'RET'  when non_ap= '1' then 'TP' else stuff('00000',6-len(apc_no),len(apc_no),apc_no) end as 'NO. APC',CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC',  dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR',  pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax,  case jpa.j_daftar  when 1 then 'JURURAWAT BERDAFTAR' when 2 then 'JURURAWAT MASYARAKAT' when 3 then 'PENOLONG JURURAWAT' end as 'PENDAFTARAN' from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc", Cn)
        'Adjust sort by tahun apc and tarikh apc 13022017 - OSH  
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case when ret= '1' then 'RET'  when non_ap= '1' then 'TP' else stuff('00000',6-len(apc_no),len(apc_no),apc_no) end as 'NO. APC',CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC',  dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR',  pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax,  case jpa.j_daftar  when 1 then 'JURURAWAT BERDAFTAR' when 2 then 'JURURAWAT MASYARAKAT' when 3 then 'PENOLONG JURURAWAT' end as 'PENDAFTARAN' from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc, apc_tkh desc", Cn)
        'Adsust apc counter 7 decimal format 12062018 - OSH 
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case when ret= '1' then 'RET'  when non_ap= '1' then 'TP' else stuff('00000',7-len(apc_no),len(apc_no),apc_no) end as 'NO. APC',CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC',  dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR',  pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax,  case jpa.j_daftar  when 1 then 'JURURAWAT BERDAFTAR' when 2 then 'JURURAWAT MASYARAKAT' when 3 then 'PENOLONG JURURAWAT' end as 'PENDAFTARAN' from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc, apc_tkh desc", Cn)
        'Add 7 zero digit for display format 06122018 - OSH   
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case when ret= '1' then 'RET'  when non_ap= '1' then 'TP' else stuff('000000',7-len(apc_no),len(apc_no),apc_no) end as 'NO. APC',CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC',  dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR',  pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax,  case jpa.j_daftar  when 1 then 'JURURAWAT BERDAFTAR' when 2 then 'JURURAWAT MASYARAKAT' when 3 then 'PENOLONG JURURAWAT' end as 'PENDAFTARAN' from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc, apc_tkh desc", Cn)

        'Add APC status 20012019 - OSH 
        'Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case when ret= '1' then 'RET'  when non_ap= '1' then 'TP' else stuff('000000',7-len(apc_no),len(apc_no),apc_no) end as 'NO. APC',CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC',  dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR',  pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax,  case jpa.j_daftar  when 1 then 'JURURAWAT BERDAFTAR' when 2 then 'JURURAWAT MASYARAKAT' when 3 then 'PENOLONG JURURAWAT' end as 'PENDAFTARAN', case APC_BATAL  when  '1' then 'BATAL' when '0' then 'AKTIF'end as 'STATUS' from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc, apc_tkh desc", Cn)

        'Add CPD detail 24072024
        Dim List_Adp As New SqlDataAdapter("select apc_tahun as 'TAHUN', case when ret= '1' then 'RET'  when non_ap= '1' then 'TP' else stuff('000000',7-len(apc_no),len(apc_no),apc_no) end as 'NO. APC',CONVERT(varchar(12), apc_tkh, 103) as 'TARIKH APC',  dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR',  pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax,  case jpa.j_daftar  when 1 then 'JURURAWAT BERDAFTAR' when 2 then 'JURURAWAT MASYARAKAT' when 3 then 'PENOLONG JURURAWAT' end as 'PENDAFTARAN', cpd as 'CPD POINT' ,case APC_BATAL  when  '1' then 'BATAL' when '0' then 'AKTIF'end as 'STATUS' from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & x & "' order by apc_tahun desc, apc_tkh desc", Cn)

        List_Adp.Fill(List_Data, "jt_penuh_apc")
        Gd_APC.DataSource = List_Data.Tables("jt_penuh_apc")
        Gd_APC.DataBind()
    End Sub

    Public Sub Isi_Pinda()

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Declare variable MYCPD2 web service 07022017 -OSH
        Dim myCPD As New MyCPD2_SVC.Service1Client
        Dim user As New MyCPD2_SVC.UserDetails
        Dim person As New UserCPD

        'Dim s_date, e_date As Date
        Dim s_date, e_date As DateTime
        Dim hcp As Integer = 0
        Dim tx_s_date, tx_e_date As String

        'Comment Original 12112019 - OSH 
        's_date = DateTime.ParseExact("01/10/" & (Now.Year - 2) & "", "dd/MM/yyyy", CultureInfo.InvariantCulture)
        'e_date = DateTime.ParseExact("30/09/" & (Now.Year - 1) & "", "dd/MM/yyyy", CultureInfo.InvariantCulture)

        'Fix Year 
        s_date = DateTime.ParseExact("01/10/" & (Now.Year - 1) & "", "dd/MM/yyyy", CultureInfo.InvariantCulture)
        e_date = DateTime.ParseExact("30/09/" & (Now.Year) & "", "dd/MM/yyyy", CultureInfo.InvariantCulture)


        tx_s_date = s_date.ToString("yyyy-MM-dd")
        tx_e_date = e_date.ToString("yyyy-MM-dd")
        's_date = "01/10/" & (Now.Year - 2) & ""
        'e_date = "30/09/" & (Now.Year - 1) & ""

        'tx_s_date = Format(s_date, "yyyy-MM-dd")
        'tx_e_date = Format(e_date, "yyyy-MM-dd")

        Dim watch As Stopwatch = Stopwatch.StartNew()
        watch.Start()

        Dim dt1, dt2, dt3, dt4, dt5, dt6, dt7, dt8, dt9, dt10, dt11, dt12, dt13 As DateTime

        Cmd.CommandText = "select * from jt_penuh where nokp = '" & Session("NOKP") & "'"
        'Cmd.CommandText = "select * from jt_penuh_old where nokp = '" & Session("NOKP") & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr("j_daftar")) Then
                If Rdr("j_daftar") = 1 Then Tx_NoPd.Text = "jb-"
                If Rdr("j_daftar") = 2 Then Tx_NoPd.Text = "jm-"
                If Rdr("j_daftar") = 3 Then Tx_NoPd.Text = "pj-"
                If Rdr("j_daftar") = 4 Then Tx_NoPd.Text = "b-"
                J_Daftar = Rdr("j_daftar")
            End If
            'Comment original 20062018 -OSH
            'If Not IsDBNull(Rdr("tkh_daftar")) Then Tx_Tkh_Daftar.Text = Rdr("tkh_daftar") Else Tx_Tkh_Daftar.Text = ""
            If Not IsDBNull(Rdr("tkh_daftar")) Then dt1 = Rdr("tkh_daftar") : Tx_Tkh_Daftar.Text = dt1.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama")
            If Not IsDBNull(Rdr("nokp")) Then Tx_NoKP.Text = Rdr("nokp")
            'Comment original 19082024 - OSH 
            'If Not IsDBNull(Rdr("nopd")) Then Tx_NoPd.Text += Rdr("nopd").ToString : Tx_NoPd.Visible = True
            'Passing Registration Number for seaching 19082024 - OSH
            If Not IsDBNull(Rdr("nopd")) Then Session("nopd_only") = Rdr("nopd") : Tx_NoPd.Text += Rdr("nopd").ToString : Tx_NoPd.Visible = True
            If Not IsDBNull(Rdr("tpt_lahir")) Then Tx_Tpt_Lahir.Text = Rdr("tpt_lahir")
            If Not IsDBNull(Rdr("tkh_lahir")) Then dt2 = Rdr("tkh_lahir") : Tx_Tkh_Lahir.Text = dt2.ToString("dd'/'MM'/'yyyy")
            'Tx_Tkh_Lahir.Text = String.Format("{0:d/M/yyyy }", Rdr("tkh_lahir").ToString)
            If Not IsDBNull(Rdr("warganegara")) Then Cb_Warga.Items.FindByValue(Rdr("warganegara")).Selected = True
            If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") = "" Then Cb_Jantina.SelectedIndex = 0 Else If Rdr("jantina") > 0 Then Cb_Jantina.Items.FindByValue(Rdr("jantina")).Selected = True
            If Not IsDBNull(Rdr("bangsa")) Then If Rdr("bangsa") > 0 Then Cb_Bangsa.Items.FindByValue(Rdr("bangsa")).Selected = True
            If Not IsDBNull(Rdr("etnik")) Then If Rdr("etnik") > 0 Then Cb_Etnik.Enabled = True : Cb_Etnik.Items.FindByValue(Rdr("etnik")).Selected = True : Cb_Etnik.Enabled = True
            If Not IsDBNull(Rdr("umur")) Then Tx_Umur.Text = Rdr("umur") Else Tx_Umur.Text = 0
            If Not IsDBNull(Rdr("agama")) Then If Rdr("agama") > 0 Then Cb_Agama.Items.FindByValue(Rdr("agama")).Selected = True
            If Not IsDBNull(Rdr("t_kahwin")) Then If Rdr("t_kahwin") > 0 Then Cb_Kahwin.Items.FindByValue(Rdr("t_kahwin")).Selected = True
            If Not IsDBNull(Rdr("tp_alamat")) Then Tx_TP_Alamat.Text = Rdr("tp_alamat")
            If Not IsDBNull(Rdr("tp_poskod")) Then Tx_TP_Poskod.Text = Rdr("tp_poskod")
            If Not IsDBNull(Rdr("tp_bandar")) Then Tx_TP_Bandar.Text = Rdr("tp_bandar")
            'Comment Original 03082022 - OSH 
            'If Len(Tx_NoKP.Text) = 12 Or Left(Tx_NoKP.Text, 1) = "T" Then Fn_Negara(1) Else Fn_Negara(0)
            'Fix Old IC Number identification issue avoid profile load - Ticket #441738 03082022 - OSH 
            If Len(Tx_NoKP.Text) = 12 Or Left(Tx_NoKP.Text, 1) = "T" Or Rdr("Jenis_KP") = "0" Then Fn_Negara(1) Else Fn_Negara(0)
            If Not IsDBNull(Rdr("tp_negeri")) Then If Rdr("tp_negeri") > 0 Then Cb_TP_Negeri.Items.FindByValue(Rdr("tp_negeri")).Selected = True
            If Not IsDBNull(Rdr("sm_alamat")) Then Tx_SM_Alamat.Text = Rdr("sm_alamat")
            If Not IsDBNull(Rdr("sm_poskod")) Then Tx_SM_Poskod.Text = Rdr("sm_poskod")
            If Not IsDBNull(Rdr("sm_bandar")) Then Tx_SM_Bandar.Text = Rdr("sm_bandar")
            'Comment Original 03082022 - OSH 
            'If Len(Tx_NoKP.Text) = 12 Or Left(Tx_NoKP.Text, 1) = "T" Then Fn_Negara_SM(1) Else Fn_Negara_SM(0) ' Add Local states and Countries list 26052016 - OSH
            'Fix Old IC Number identification issue avoid profile load - Ticket #441738 03082022 - OSH 
            If Len(Tx_NoKP.Text) = 12 Or Left(Tx_NoKP.Text, 1) = "T" Or Rdr("Jenis_KP") = "0" Then Fn_Negara_SM(1) Else Fn_Negara_SM(0)
            If Not IsDBNull(Rdr("sm_negeri")) Then If Rdr("sm_negeri") > 0 Then Cb_SM_Negeri.Items.FindByValue(Rdr("sm_negeri")).Selected = True
            If Not IsDBNull(Rdr("tel_r")) Then Tx_Tel_R.Text = Rdr("tel_r")
            If Not IsDBNull(Rdr("tel_hp")) Then Tx_Tel_HP.Text = Rdr("tel_hp")
            If Not IsDBNull(Rdr("emel")) Then Tx_Emel.Text = Rdr("emel")
            If Not IsDBNull(Rdr("id_kolej")) Then If Rdr("id_kolej") > 0 Then Cb_Kolej.Items.FindByValue(Rdr("id_kolej")).Selected = True
            If Not IsDBNull(Rdr("tajaan")) Then If Rdr("tajaan") > 0 Then Cb_Tajaan.Items.FindByValue(Rdr("tajaan")).Selected = True
            If Not IsDBNull(Rdr("sesi_bulan")) Then If Rdr("sesi_bulan") > 0 Then Cb_Sesi_Bulan.Items.FindByValue(Rdr("sesi_bulan")).Selected = True
            If Not IsDBNull(Rdr("sesi_tahun")) Then If Rdr("sesi_tahun") > 0 Then Cb_Sesi_Tahun.Items.FindByValue(Rdr("sesi_tahun")).Selected = True
            If Not IsDBNull(Rdr("tkh_latihan_mula")) Then dt3 = Rdr("tkh_latihan_mula") : Tx_M_Latihan.Text = dt3.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_latihan_tamat")) Then dt4 = Rdr("tkh_latihan_tamat") : Tx_T_Latihan.Text = dt4.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_periksa_akhir")) Then dt5 = Rdr("tkh_periksa_akhir") : Tx_Periksa.Text = dt5.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_mula_khidmat")) Then dt6 = Rdr("tkh_mula_khidmat") : Tx_Mula_Khidmat.Text = dt6.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_sah_jwtn")) Then dt7 = Rdr("tkh_sah_jwtn") : Tx_Sah_Jawatan.Text = dt7.ToString("dd'/'MM'/'yyyy")
            'If Not IsDBNull(Rdr("cpd")) Then Tx_CPD.Text = Rdr("cpd") Else Tx_CPD.Text = 0
            If Not IsDBNull(Rdr("gred")) Then If Rdr("gred") > 0 Then Cb_Gred.Items.FindByValue(Rdr("gred")).Selected = True
            'If Not IsDBNull(Rdr("gelaran_jwtn")) Then If Rdr("gelaran_jwtn") > 0 Then Cb_Gelaran.Items.FindByValue(Rdr("gelaran_jwtn")).Selected = True : If Rdr("gelaran_jwtn") = 5 Then Tx_Nama.BackColor = Drawing.Color.Red : Tx_NoPd.Text = Replace(Tx_NoPd.Text, "jb-", "jkm-") : Tx_NoPd.Text += " (mental)"
            If Not IsDBNull(Rdr("gelaran_jwtn")) Then If Rdr("gelaran_jwtn") > 0 Then Cb_Gelaran.Items.FindByValue(Rdr("gelaran_jwtn")).Selected = True : If Rdr("gelaran_jwtn") = 7 Then Tx_Nama.BackColor = Drawing.Color.Red : Tx_NoPd.Text = Replace(Tx_NoPd.Text, "jb-", "jkm-") : Tx_NoPd.Text += " (mental)"

            If Not IsDBNull(Rdr("nopd_ku")) Then Tx_NoPd_KU.Text = Rdr("nopd_ku")
            If Not IsDBNull(Rdr("nopd_kj")) Then Tx_NoPd_KJ.Text = Rdr("nopd_kj")
            If Not IsDBNull(Rdr("nopd_jm")) Then tx_noJM.Text = Rdr("nopd_jm")
            If Not IsDBNull(Rdr("nopd_pj")) Then tx_noPJ.Text = Rdr("nopd_pj")

            If Not IsDBNull(Rdr("nopd_b1")) Then Tx_NoPd_KBI.Text = Rdr("nopd_b1")
            If Not IsDBNull(Rdr("id_kolej_b1")) Then If Rdr("id_kolej_b1") > 0 Then Cb_Kolej_B1.Items.FindByValue(Rdr("id_kolej_b1")).Selected = True
            If Not IsDBNull(Rdr("tkh_daftar_b1")) Then dt8 = Rdr("tkh_daftar_b1") : Tx_Daftar_B1.Text = dt8.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_latihan_mula_b1")) Then dt9 = Rdr("tkh_latihan_mula_b1") : Tx_M_Latihan_B1.Text = dt9.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_latihan_tamat_b1")) Then dt10 = Rdr("tkh_latihan_tamat_b1") : Tx_T_Latihan_B1.Text = dt10.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_periksa_akhir_b1")) Then dt11 = Rdr("tkh_periksa_akhir_b1") : Tx_Periksa_B1.Text = dt11.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("log_status")) Then If Rdr("log_status") > 0 Then Cb_Status.Items.FindByValue(Rdr("log_status")).Selected = True
            If Not IsDBNull(Rdr("log_catatan")) Then Tx_Catatan.Text = Rdr("log_catatan")


            ''Check and select hcp types 07022017- OSH 
            'If Not IsDBNull(Rdr("j_daftar")) Then
            '    If Rdr("j_daftar") = 1 Then hcp = "8" 'JB
            '    If Rdr("j_daftar") = 2 Then hcp = "70" 'JM
            '    If Rdr("j_daftar") = 3 Then hcp = "69" 'PJ
            'End If


            ''Call Web API via restsharp 23042019 - OSH
            'Dim baseURL As String
            'Dim client As New RestClient()
            'baseURL = "https://www.spmj.moh.gov.my"
            'client.BaseUrl = New Uri(baseURL)

            ''Dim request As New RestRequest("/cpdapi/api/cpd/GetActualPoint?{id}", Method.GET)
            ''request.AddParameter("ic", "value")
            ''request.AddUrlSegment("ic", "600622125158")

            ''request.AddParameter("hcpId", "value")
            ''request.AddUrlSegment("hcpId", "8")

            ''request.AddParameter("start_Date", "value")
            ''request.AddUrlSegment("start_Date", "2015-01-01")

            ''request.AddParameter("end_Date", "value")
            ''request.AddUrlSegment("end_Date", "2017-12-31")

            ''Dim request As New RestRequest("https://www.spmj.moh.gov.my/cpdapi/api/cpd/GetActualPoint?ic=600622125158&hcpId=8&start_Date=2015-01-01&end_Date=2017-12-31", Method.GET)
            ''Dim request As New RestRequest("/cpdapi/api/cpd/GetHcpList", Method.GET)
            'Dim request As New RestRequest("/cpdapi/api/cpd/GetActualPoint", Method.GET)

            'request.AddQueryParameter("ic", Session("NOKP"))
            'request.AddQueryParameter("hcpId", hcp)
            'request.AddQueryParameter("start_Date", tx_s_date)
            'request.AddQueryParameter("end_Date", tx_e_date)

            ''request.AddQueryParameter("ic", "600622125158")
            ''request.AddQueryParameter("hcpId", "8")
            ''request.AddQueryParameter("start_Date", "2015-01-01")
            ''request.AddQueryParameter("end_Date", "2017-12-31")

            ''request.AddParameter("ic", "600622125158", ParameterType.QueryString)
            ''request.AddParameter("hcpId", "8", ParameterType.QueryString)
            ''request.AddParameter("start_Date", "2015-01-01", ParameterType.QueryString)
            ''request.AddParameter("end_Date", "2017-12-31", ParameterType.QueryString)

            'request.RequestFormat = DataFormat.Json


            'Dim reponse As RestResponse = client.Execute(request)
            'Dim content As String = reponse.Content
            ''Dim dserial As New JsonDeserializer
            ''Dim jsonO As Object = dserial.Deserialize(Of String)(reponse)

            'If content = "[]" Then
            '    Tx_CPD.Text = "0"
            '    Tx_CPD.ForeColor = Drawing.Color.OrangeRed
            '    Tx_CPD.Font.Bold = True
            '    lbMyCPD_Status.Font.Bold = True
            '    lbMyCPD_Status.Text = "MAKLUMAT MYCPD DARI " & tx_s_date & " SEHINGGA " & tx_e_date & ""
            'Else
            '    Dim oResult = JsonConvert.DeserializeObject(Of List(Of Object))(content)


            '    'Check web API service connection 
            '    If Response.StatusCode = HttpStatusCode.OK Then

            '        'assign populate deserial object of "content" to string 
            '        For Each dResult As Object In oResult

            '            Tx_CPD.Text = dResult("actual_points").ToString

            '            Tx_CPD.ForeColor = Drawing.Color.OrangeRed
            '            Tx_CPD.Font.Bold = True
            '            lbMyCPD_Status.Font.Bold = True
            '            lbMyCPD_Status.Text = "MAKLUMAT MYCPD DARI " & tx_s_date & " SEHINGGA " & tx_e_date & ""

            '        Next
            '    Else
            'lbMyCPD_Status.Font.Bold = True
            'lbMyCPD_Status.Text = "GANGUAN PERKHIDMATAN MYCPD TIADA DATA DALAM TALIAN"
            'End If
            '    End If

            'Check and select hcp types 07022017- OSH 
            'If Not IsDBNull(Rdr("j_daftar")) Then
            '    If Rdr("j_daftar") = 1 Then hcp = "8" 'JB
            '    If Rdr("j_daftar") = 2 Then hcp = "70" 'JM
            '    If Rdr("j_daftar") = 3 Then hcp = "69" 'PJ
            'End If

            'user.IdentityNo = Tx_NoKP.Text.Trim
            'user.Hcp = Convert.ToString(hcp)
            'user.DateStart = tx_s_date
            'user.DateEnd = tx_e_date

            'Tx_CPD.ForeColor = Drawing.Color.OrangeRed
            'Tx_CPD.Font.Bold = True
            'Tx_CPD.Text = myCPD.ActualPoint(user) ' Down 22052017 - OSH

            'lbMyCPD_Status.Font.Bold = True
            'lbMyCPD_Status.Text = "MAKLUMAT MYCPD DARI " & s_date & " SEHINGGA " & e_date & "" ' Down 22052017 - OSH
            'myCPD.Close()


        End If
        Rdr.Close()

        watch.Stop()
        ''Comment Original 02062017 - OSH
        ''Cmd.CommandText = "INSERT INTO MYCPD2_LOG ( NOKP,TKH_TEMPOH, CPD, M_PROSES, LOG_ID, TKH_LOG) VALUES ('" & Session("NOKP") & "','MAKLUMAT MYCPD DARI " & s_date & " SEHINGGA " & e_date & "','" & Tx_CPD.Text & "','" & watch.Elapsed.TotalSeconds & "','" & Session("ID_PG") & "',getdate())"
        ''Add Fixing Statement 02062017 -OSH
        'Cmd.CommandText = "INSERT INTO MYCPD2_LOG ( NOKP,TKH_TEMPOH, CPD, M_PROSES, LOG_ID, TKH_LOG) VALUES ('" & Session("NOKP") & "','" & lbMyCPD_Status.Text & "','" & Tx_CPD.Text & "','" & watch.Elapsed.TotalSeconds & "','" & Session("ID_PG") & "',getdate())"
        'Cmd.ExecuteNonQuery()




        'Close Web Service Poor Performance 16062016 -OSH
        lbMyCPD_Status.Font.Bold = True
        lbMyCPD_Status.Text = "GANGUAN PERKHIDMATAN MYCPD TIADA DATA DALAM TALIAN"

        'Comment Original 16062016 - OSH 
        'Dim myCPD As New MyCPD_SVC.Service
        'Dim Point As New Integer

        ''Reopen Test 28092015 - OSH 
        'If Not IsDBNull(myCPD.Actual_APC(Tx_NoKP.Text.Trim, "01/10/" & (Now.Year - 1), "30/09/" & (Now.Year))) Then
        '    Point = myCPD.Actual_APC(Tx_NoKP.Text.Trim, "01/10/" & (Now.Year - 1), "30/09/" & (Now.Year))
        '    lbMyCPD_Status.Font.Bold = True
        '    lbMyCPD_Status.Text = " MAKLUMAT MYCPD 01/10" & (Now.Year - 1) & " - 30/09/" & (Now.Year) & ""
        'Else
        '    Point = 0
        '    lbMyCPD_Status.Font.Bold = True
        '    lbMyCPD_Status.Text = " TIADA MAKLUMAT MYCPD 01/10" & (Now.Year - 1) & " - 30/09/" & (Now.Year) & ""
        'End If

        'If Point = 0 Then 'skip klu swasta / cpd point = 0
        'ElseIf CInt(Tx_CPD.Text) = Point Then ' skip klu cpd point = database
        'ElseIf CInt(Tx_CPD.Text) <> Point Then 'update cpd point yg latest
        '    Tx_CPD.Text = Point
        'End If


        'Tx_Umur.Text = 0
        'Kira_Umur(Tx_NoKP.Text, Tx_Umur)

        Dim x, y As TextBox, i As Int16
        Cmd.CommandText = "select * from jt_penuh_kelayakan where nokp = '" & Session("NOKP") & "' and jenis = 1"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            x = Gd.Rows.Item(i).FindControl("Tx_Layak")
            y = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")
            x.Text = Rdr("kelayakan")
            'Comment original 19062018 -OSH
            'If Not IsDBNull(Rdr("tkh_kelayakan")) Then y.Text = Rdr("tkh_kelayakan")
            'Fixing date calender issues 119062018 - OSH
            If Not IsDBNull(Rdr("tkh_kelayakan")) Then dt12 = Rdr("tkh_kelayakan") : y.Text = dt12.ToString("dd'/'MM'/'yyyy")
            Gd.Rows.Item(i).Visible = True
            i = i + 1
        End While
        Rdr.Close()

        i = 0
        Cmd.CommandText = "select * from jt_penuh_kelayakan where nokp = '" & Session("NOKP") & "' and jenis = 2"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            x = GdA.Rows.Item(i).FindControl("Tx_Layak")
            y = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")
            x.Text = Rdr("kelayakan")
            'Comment original 19062018 -OSH
            'If Not IsDBNull(Rdr("tkh_kelayakan")) Then y.Text = Rdr("tkh_kelayakan")
            'Fixing date calender issues 119062018 - OSH
            If Not IsDBNull(Rdr("tkh_kelayakan")) Then dt13 = Rdr("tkh_kelayakan") : y.Text = dt13.ToString("dd'/'MM'/'yyyy")
            GdA.Rows.Item(i).Visible = True
            i = i + 1
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Public Sub Simpan_Pinda()        
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String

        If Tx_NoPd_KBI.Text = "" Then Tx_NoPd_KBI.Text = "0"
        If Tx_NoPd_KU.Text = "" Then Tx_NoPd_KU.Text = "0"
        If Tx_NoPd_KJ.Text = "" Then Tx_NoPd_KJ.Text = "0"

        'Tarikh mula latihan
        Dim y As DateTime
        Dim z As String
        If Tx_M_Latihan.Text.Trim <> String.Empty Then
            z = Tx_M_Latihan.Text.Trim
            y = DateTime.ParseExact(z, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z = y.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z = "'" & z & "'"
        Else
            z = "NULL"
        End If

        'Tarikh tamat latihan
        Dim y1 As DateTime
        Dim z1 As String
        z1 = Tx_T_Latihan.Text.Trim
        If Tx_T_Latihan.Text.Trim <> String.Empty Then
            y1 = DateTime.ParseExact(z1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z1 = y1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z1 = "'" & z1 & "'"
        Else
            z1 = "NULL"
        End If

        'Tarikh peperiksaan
        Dim y2 As DateTime
        Dim z2 As String
        z2 = Tx_Periksa.Text.Trim
        If Tx_Periksa.Text.Trim <> String.Empty Then
            y2 = DateTime.ParseExact(z2, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z2 = y2.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z2 = "'" & z2 & "'"
        Else
            z2 = "NULL"
        End If

        'Tarikh Mula Perkhidmatan
        Dim y3 As DateTime
        Dim z3 As String
        z3 = Tx_Mula_Khidmat.Text.Trim
        If Tx_Mula_Khidmat.Text.Trim <> String.Empty Then
            y3 = DateTime.ParseExact(z3, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z3 = y3.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z3 = "'" & z3 & "'"
        Else
            z3 = "NULL"
        End If

        ' Tarikh Sah Jawatan 
        Dim y4 As DateTime
        Dim z4 As String
        z4 = Tx_Sah_Jawatan.Text.Trim
        If Tx_Sah_Jawatan.Text.Trim <> String.Empty Then
            y4 = DateTime.ParseExact(z4, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z4 = y4.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z4 = "'" & z4 & "'"
        Else
            z4 = "NULL"
        End If

        'Tarikh Daftar Bidan 1
        Dim y5 As DateTime
        Dim z5 As String
        z5 = Tx_Daftar_B1.Text.Trim
        If Tx_Daftar_B1.Text.Trim <> String.Empty Then
            y5 = DateTime.ParseExact(z5, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z5 = y5.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z5 = "'" & z5 & "'"
        Else
            z5 = "NULL"
        End If

        'Tarikh Mula Latihan Bidan 1
        Dim y6 As DateTime
        Dim z6 As String
        z6 = Tx_M_Latihan_B1.Text.Trim
        If Tx_M_Latihan_B1.Text.Trim <> String.Empty Then
            y6 = DateTime.ParseExact(z6, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z6 = y6.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z6 = "'" & z6 & "'"
        Else
            z6 = "NULL"
        End If

        'Tarikh Tamat Latihan Bidan 1
        Dim y7 As DateTime
        Dim z7 As String
        z7 = Tx_T_Latihan_B1.Text.Trim
        If Tx_T_Latihan_B1.Text.Trim <> String.Empty Then
            y7 = DateTime.ParseExact(z7, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z7 = y7.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z7 = "'" & z7 & "'"
        Else
            z7 = "NULL"
        End If

        'Tarikh Peperiksaan Bidan 1
        Dim y8 As DateTime
        Dim z8 As String
        z8 = Tx_Periksa_B1.Text.Trim
        If Tx_Periksa_B1.Text.Trim <> String.Empty Then
            y8 = DateTime.ParseExact(z8, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z8 = y8.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z8 = "'" & z8 & "'"
        Else
            z8 = "NULL"
        End If

        ''Tarikh Lahir
        Dim y11 As DateTime
        Dim z11 As String = ""
        z11 = Tx_Tkh_Lahir.Text.Trim
        If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
            y11 = DateTime.ParseExact(z11, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z11 = y11.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z11 = "'" & z11 & "'"
        Else
            z11 = "NULL"
        End If

        'tarikh daftar
        Dim y12 As DateTime
        Dim z12 As String = ""
        z12 = Tx_Tkh_Daftar.Text.Trim
        If Tx_Tkh_Daftar.Text.Trim <> String.Empty Then
            y12 = DateTime.ParseExact(z12, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z12 = y12.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z12 = "'" & z12 & "'"
        Else
            z12 = "NULL"
        End If
        'Comment Original - 24012018
        'SQL = "update jt_penuh set " & _
        '        "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "'," & _
        '        "tpt_lahir = '" & Tx_Tpt_Lahir.Text.Trim.ToUpper & "'," & _
        '        "tkh_lahir = " & Kira_Umur2(Tx_Tkh_Lahir.Text) & "," & _
        '        "warganegara = " & Cb_Warga.SelectedValue & "," & _
        '        "jantina = '" & Cb_Jantina.SelectedValue & "'," & _
        '        "bangsa = " & Cb_Bangsa.SelectedIndex & "," & _
        '        "etnik = " & Cb_Etnik.SelectedValue & "," & _
        '        "umur = " & Tx_Umur.Text & "," & _
        '        "agama = " & Cb_Agama.SelectedIndex & "," & _
        '        "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," & _
        '        "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "'," & _
        '        "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," & _
        '        "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "'," & _
        '        "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
        '        "tel_r = '" & Tx_Tel_R.Text.Trim & "'," & _
        '        "tel_hp = '" & Tx_Tel_HP.Text.Trim & "'," & _
        '        "emel = '" & Tx_Emel.Text.Trim & "'," & _
        '        "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim.ToUpper) & "'," & _
        '        "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," & _
        '        "sm_bandar = '" & Tx_SM_Bandar.Text.Trim.ToUpper & "'," & _
        '        "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
        '        "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," & _
        '        "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," & _
        '        "sesi_bulan = " & Cb_Sesi_Bulan.SelectedIndex & "," & _
        '        "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedValue & "'," & _
        '        "tkh_latihan_mula = " & Chk_Tkh(Tx_M_Latihan.Text.Trim) & "," & _
        '        "tkh_latihan_tamat = " & Chk_Tkh(Tx_T_Latihan.Text.Trim) & "," & _
        '        "tkh_periksa_akhir = " & Chk_Tkh(Tx_Periksa.Text.Trim) & "," & _
        '        "tkh_mula_khidmat = " & Chk_Tkh(Tx_Mula_Khidmat.Text.Trim) & "," & _
        '        "tkh_sah_jwtn = " & Chk_Tkh(Tx_Sah_Jawatan.Text) & "," & _
        '        "cpd = " & CInt(Tx_CPD.Text) & "," & _
        '        "gred = " & Cb_Gred.SelectedValue & "," & _
        '        "gelaran_jwtn = " & Cb_Gelaran.SelectedValue & "," & _
        '        "nopd_b1 = " & CInt(Tx_NoPd_KBI.Text) & "," & _
        '        "id_kolej_b1 = '" & Cb_Kolej_B1.SelectedItem.Value & "'," & _
        '        "tkh_daftar_b1 = " & Chk_Tkh(Tx_Daftar_B1.Text) & "," & _
        '        "tkh_latihan_mula_b1 = " & Chk_Tkh(Tx_M_Latihan_B1.Text) & "," & _
        '        "tkh_latihan_tamat_b1 = " & Chk_Tkh(Tx_T_Latihan_B1.Text) & "," & _
        '        "tkh_periksa_akhir_b1 = " & Chk_Tkh(Tx_Periksa_B1.Text) & "," & _
        '        "nopd_ku = " & CInt(Tx_NoPd_KU.Text) & "," & _
        '        "nopd_kj = " & CInt(Tx_NoPd_KJ.Text) & "," & _
        '        "log_status = '" & Cb_Status.SelectedItem.Value & "'," & _
        '        "log_catatan = '" & Apo(Tx_Catatan.Text.Trim.ToUpper) & "'," & _
        '        "mod_id = '" & Session("Id_PG") & "'," & _
        '        "mod_tkh = getdate() " & _
        '        "where nokp = '" & Session("NOKP") & "'"

        'Disable check date 24012018
        'SQL = "update jt_penuh set " & _
        '       "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "'," & _
        '       "tpt_lahir = '" & Tx_Tpt_Lahir.Text.Trim.ToUpper & "'," & _
        '       "tkh_lahir = " & z11 & "," & _
        '       "warganegara = " & Cb_Warga.SelectedValue & "," & _
        '       "jantina = '" & Cb_Jantina.SelectedValue & "'," & _
        '       "bangsa = " & Cb_Bangsa.SelectedValue & "," & _
        '       "etnik = " & Cb_Etnik.SelectedValue & "," & _
        '       "umur = " & Tx_Umur.Text & "," & _
        '       "agama = " & Cb_Agama.SelectedValue & "," & _
        '       "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," & _
        '       "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "'," & _
        '       "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," & _
        '       "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "'," & _
        '       "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
        '       "tel_r = '" & Tx_Tel_R.Text.Trim & "'," & _
        '       "tel_hp = '" & Tx_Tel_HP.Text.Trim & "'," & _
        '       "emel = '" & Tx_Emel.Text.Trim & "'," & _
        '       "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim.ToUpper) & "'," & _
        '       "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," & _
        '       "sm_bandar = '" & Tx_SM_Bandar.Text.Trim.ToUpper & "'," & _
        '       "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
        '       "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," & _
        '       "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," & _
        '       "sesi_bulan = " & Cb_Sesi_Bulan.SelectedValue & "," & _
        '       "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedValue & "'," & _
        '       "tkh_latihan_mula = " & z & "," & _
        '       "tkh_latihan_tamat = " & z1 & "," & _
        '       "tkh_periksa_akhir = " & z2 & "," & _
        '       "tkh_mula_khidmat = " & z3 & "," & _
        '       "tkh_sah_jwtn = " & z4 & "," & _
        '       "cpd = " & CInt(Tx_CPD.Text) & "," & _
        '       "gred = " & Cb_Gred.SelectedValue & "," & _
        '       "gelaran_jwtn = " & Cb_Gelaran.SelectedValue & "," & _
        '       "nopd_b1 = " & CInt(Tx_NoPd_KBI.Text) & "," & _
        '       "id_kolej_b1 = '" & Cb_Kolej_B1.SelectedItem.Value & "'," & _
        '       "tkh_daftar_b1 = " & z5 & "," & _
        '       "tkh_latihan_mula_b1 = " & z6 & "," & _
        '       "tkh_latihan_tamat_b1 = " & z7 & "," & _
        '       "tkh_periksa_akhir_b1 = " & z8 & "," & _
        '       "nopd_ku = " & CInt(Tx_NoPd_KU.Text) & "," & _
        '       "nopd_kj = " & CInt(Tx_NoPd_KJ.Text) & "," & _
        '       "log_status = '" & Cb_Status.SelectedItem.Value & "'," & _
        '       "log_catatan = '" & Apo(Tx_Catatan.Text.Trim.ToUpper) & "'," & _
        '       "mod_id = '" & Session("id_pg") & "'," & _
        '       "mod_tkh = getdate() " & _
        '       "where nokp = '" & Session("nokp") & "'"

        'Comment Original 19082024 - OSH
        'Remove Name update 10082018 - OSH
        'SQL = "update jt_penuh set " &
        '       "tpt_lahir = '" & Tx_Tpt_Lahir.Text.Trim.ToUpper & "'," &
        '       "tkh_lahir = " & z11 & "," &
        '       "warganegara = " & Cb_Warga.SelectedValue & "," &
        '       "jantina = '" & Cb_Jantina.SelectedValue & "'," &
        '       "bangsa = " & Cb_Bangsa.SelectedValue & "," &
        '       "etnik = " & Cb_Etnik.SelectedValue & "," &
        '       "umur = " & Tx_Umur.Text & "," &
        '       "agama = " & Cb_Agama.SelectedValue & "," &
        '       "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," &
        '       "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "'," &
        '       "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," &
        '       "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "'," &
        '       "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," &
        '       "tel_r = '" & Tx_Tel_R.Text.Trim & "'," &
        '       "tel_hp = '" & Tx_Tel_HP.Text.Trim & "'," &
        '       "emel = '" & Tx_Emel.Text.Trim & "'," &
        '       "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim.ToUpper) & "'," &
        '       "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," &
        '       "sm_bandar = '" & Tx_SM_Bandar.Text.Trim.ToUpper & "'," &
        '       "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," &
        '       "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," &
        '       "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," &
        '       "sesi_bulan = " & Cb_Sesi_Bulan.SelectedValue & "," &
        '       "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedValue & "'," &
        '       "tkh_latihan_mula = " & z & "," &
        '       "tkh_latihan_tamat = " & z1 & "," &
        '       "tkh_periksa_akhir = " & z2 & "," &
        '       "tkh_mula_khidmat = " & z3 & "," &
        '       "tkh_sah_jwtn = " & z4 & "," &
        '       "cpd = " & CInt(Tx_CPD.Text) & "," &
        '       "gred = " & Cb_Gred.SelectedValue & "," &
        '       "gelaran_jwtn = " & Cb_Gelaran.SelectedValue & "," &
        '       "nopd_b1 = " & CInt(Tx_NoPd_KBI.Text) & "," &
        '       "id_kolej_b1 = '" & Cb_Kolej_B1.SelectedItem.Value & "'," &
        '       "tkh_daftar_b1 = " & z5 & "," &
        '       "tkh_latihan_mula_b1 = " & z6 & "," &
        '       "tkh_latihan_tamat_b1 = " & z7 & "," &
        '       "tkh_periksa_akhir_b1 = " & z8 & "," &
        '       "nopd_ku = " & CInt(Tx_NoPd_KU.Text) & "," &
        '       "nopd_kj = " & CInt(Tx_NoPd_KJ.Text) & "," &
        '       "log_status = '" & Cb_Status.SelectedItem.Value & "'," &
        '       "log_catatan = '" & Apo(Tx_Catatan.Text.Trim.ToUpper) & "'," &
        '       "mod_id = '" & Session("id_pg") & "'," &
        '       "mod_tkh = getdate() " &
        '       "where nokp = '" & Session("nokp") & "'"

        'Remove CPD update 19082024 - OSH 
        SQL = "update jt_penuh set " &
               "tpt_lahir = '" & Tx_Tpt_Lahir.Text.Trim.ToUpper & "'," &
               "tkh_lahir = " & z11 & "," &
               "warganegara = " & Cb_Warga.SelectedValue & "," &
               "jantina = '" & Cb_Jantina.SelectedValue & "'," &
               "bangsa = " & Cb_Bangsa.SelectedValue & "," &
               "etnik = " & Cb_Etnik.SelectedValue & "," &
               "umur = " & Tx_Umur.Text & "," &
               "agama = " & Cb_Agama.SelectedValue & "," &
               "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," &
               "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "'," &
               "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," &
               "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "'," &
               "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," &
               "tel_r = '" & Tx_Tel_R.Text.Trim & "'," &
               "tel_hp = '" & Tx_Tel_HP.Text.Trim & "'," &
               "emel = '" & Tx_Emel.Text.Trim & "'," &
               "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim.ToUpper) & "'," &
               "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," &
               "sm_bandar = '" & Tx_SM_Bandar.Text.Trim.ToUpper & "'," &
               "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," &
               "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," &
               "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," &
               "sesi_bulan = " & Cb_Sesi_Bulan.SelectedValue & "," &
               "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedValue & "'," &
               "tkh_latihan_mula = " & z & "," &
               "tkh_latihan_tamat = " & z1 & "," &
               "tkh_periksa_akhir = " & z2 & "," &
               "tkh_mula_khidmat = " & z3 & "," &
               "tkh_sah_jwtn = " & z4 & "," &
               "gred = " & Cb_Gred.SelectedValue & "," &
               "gelaran_jwtn = " & Cb_Gelaran.SelectedValue & "," &
               "nopd_b1 = " & CInt(Tx_NoPd_KBI.Text) & "," &
               "id_kolej_b1 = '" & Cb_Kolej_B1.SelectedItem.Value & "'," &
               "tkh_daftar_b1 = " & z5 & "," &
               "tkh_latihan_mula_b1 = " & z6 & "," &
               "tkh_latihan_tamat_b1 = " & z7 & "," &
               "tkh_periksa_akhir_b1 = " & z8 & "," &
               "nopd_ku = " & CInt(Tx_NoPd_KU.Text) & "," &
               "nopd_kj = " & CInt(Tx_NoPd_KJ.Text) & "," &
               "log_status = '" & Cb_Status.SelectedItem.Value & "'," &
               "log_catatan = '" & Apo(Tx_Catatan.Text.Trim.ToUpper) & "'," &
               "mod_id = '" & Session("id_pg") & "'," &
               "mod_tkh = getdate() " &
               "where nokp = '" & Session("nokp") & "'"



        SQL += "; "
        SQL += "delete from jt_penuh_kelayakan where nokp = '" & Tx_NoKP.Text & "'; "

        'Comment Original 25012018
        'Dim i As Int16, tk, tt As TextBox
        'For i = 0 To Gd.Rows.Count - 1
        '    If Not Gd.Rows.Item(i).Visible Then Exit For
        '    tk = Gd.Rows.Item(i).FindControl("Tx_Layak")
        '    tt = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")

        '    SQL += "insert jt_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
        '    "'" & Tx_NoKP.Text.Trim & "'," & _
        '    "1," & _
        '    "'" & tk.Text.Trim & "'," & _
        '    "" & Chk_Tkh(tt.Text) & "" & _
        '    ")"
        '    SQL += ";"
        'Next

        'For i = 0 To GdA.Rows.Count - 1
        '    If Not GdA.Rows.Item(i).Visible Then Exit For
        '    tk = GdA.Rows.Item(i).FindControl("Tx_Layak")
        '    tt = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")

        '    SQL += "insert jt_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
        '    "'" & Tx_NoKP.Text.Trim & "'," & _
        '    "2," & _
        '    "'" & tk.Text.Trim & "'," & _
        '    "" & Chk_Tkh(tt.Text) & "" & _
        '    ")"
        '    SQL += ";"
        'Next

        'fIX DATES
        Dim i As Int16, tk, tt As TextBox
        For i = 0 To Gd.Rows.Count - 1
            If Not Gd.Rows.Item(i).Visible Then Exit For
            tk = Gd.Rows.Item(i).FindControl("Tx_Layak")
            tt = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")

            'Tarikh(Kelayakan)
            Dim y9 As DateTime
            Dim z9 As String
            z9 = tt.Text
            If tt.Text <> String.Empty Then
                y9 = DateTime.ParseExact(z9, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z9 = y9.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z9 = "'" & z9 & "'"
            Else
                z9 = "NULL"
            End If

            SQL += "insert jt_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
            "'" & Tx_NoKP.Text.Trim & "'," & _
            "1," & _
            "'" & tk.Text.Trim & "'," & _
            "" & z9 & "" & _
            ")"
            SQL += ";"
        Next

        For i = 0 To GdA.Rows.Count - 1
            If Not GdA.Rows.Item(i).Visible Then Exit For
            tk = GdA.Rows.Item(i).FindControl("Tx_Layak")
            tt = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")

            'Tarikh(Kelayakan)
            Dim y10 As DateTime
            Dim z10 As String
            z10 = tt.Text
            If tt.Text <> String.Empty Then
                y10 = DateTime.ParseExact(z10, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z10 = y10.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z10 = "'" & z10 & "'"
            Else
                z10 = "NULL"
            End If

            SQL += "insert jt_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
            "'" & Tx_NoKP.Text.Trim & "'," & _
            "2," & _
            "'" & tk.Text.Trim & "'," & _
            "" & z10 & "" & _
            ")"
            SQL += ";"
        Next
        'SQL += "update pelatih set " & _
        '       "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', " & _
        '       "warganegara = '" & Cb_Warga.SelectedItem.Value & "', " & _
        '       "jantina = '" & Cb_Jantina.SelectedIndex & "', " & _
        '       "bangsa = '" & Cb_Bangsa.SelectedIndex & "', " & _
        '       "agama = '" & Cb_Agama.SelectedIndex & "', " & _
        '       "t_kahwin = '" & Cb_Kahwin.SelectedIndex & "', " & _
        '       "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "', " & _
        '       "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "', " & _
        '       "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "', " & _
        '       "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "', " & _
        '       "tel = '" & Tx_Tel_R.Text.Trim & "', " & _
        '       "emel = '" & Tx_Emel.Text.Trim & "', " & _
        '       "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "', " & _
        '       "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "', " & _
        '       "sesi_bulan = '" & Cb_Sesi_Bulan.SelectedIndex & "', " & _
        '       "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedItem.Text & "', " & _
        '       "tkh_latihan_mula = " & Chk_Tkh(Tx_M_Latihan.Text) & ", " & _
        '       "tkh_latihan_tamat = " & Chk_Tkh(Tx_T_Latihan.Text) & " " & _
        '       "where nokp ='" & Session("NOKP") & "'"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Telah Dikemaskini...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try

    End Sub

    Public Sub Grid_Row(ByVal X As Int16, ByVal Y As Int16)
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter("select top " & X & " * from tmp_grid", Cn)

        List_Adp.Fill(List_Data, "tmp_grid")
        If Y = 1 Then Gd.DataSource = List_Data.Tables("tmp_grid") : Gd.DataBind()
        If Y = 2 Then GdA.DataSource = List_Data.Tables("tmp_grid") : GdA.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub
        Grid_Row(10, 1)
        Grid_Row(10, 2)

        'UICulture = "en-GB"
        'Culture = "en-GB"


        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        ''Cb_Warga.Items.FindByText("MALAYSIA").Selected = True

        'ETNIK
        Cmd.CommandText = "SELECT Dc_ETNIK, Id_ETNIK FROM PN_ETNIK ORDER BY DC_ETNIK"
        Rdr = Cmd.ExecuteReader()
        Cb_Etnik.Items.Clear()
        Cb_Etnik.Items.Add("")
        Cb_Etnik.Items.Item(0).Value = 0
        While Rdr.Read
            Cb_Etnik.Items.Add(Rdr(0))
            Cb_Etnik.Items.Item(Cb_Etnik.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'NEGERI - ALAMAT 
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        'Cb_SM_Negeri.Items.Clear()
        'Cb_SM_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
            'Cb_SM_Negeri.Items.Add(Rdr(0))
            'Cb_SM_Negeri.Items.Item(Cb_SM_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'NEGERI - ALAMAT 
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        'Cb_TP_Negeri.Items.Clear()
        'Cb_TP_Negeri.Items.Add("")
        Cb_SM_Negeri.Items.Clear()
        Cb_SM_Negeri.Items.Add("")
        While Rdr.Read
            'Cb_TP_Negeri.Items.Add(Rdr(0))
            'Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
            Cb_SM_Negeri.Items.Add(Rdr(0))
            Cb_SM_Negeri.Items.Item(Cb_SM_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        ' ADD BANGSA - SPMJ_RACE 26052014  - OSH 22012016
        Cmd.CommandText = "SELECT Dc_RACE, Id_RACE FROM SPMJ_REF_RACE ORDER BY DC_RACE"
        Rdr = Cmd.ExecuteReader()
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("")
        Cb_Bangsa.Items.Item(0).Value = 0
        While Rdr.Read
            Cb_Bangsa.Items.Add(Rdr(0))
            Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        ' ADD AGAMA - SPMJ_REF_AGAMA 26052014 - OSH 22012016
        Cmd.CommandText = "SELECT Dc_AGAMA, Id_AGAMA FROM SPMJ_REF_AGAMA ORDER BY DC_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        Cb_Agama.Items.Item(0).Value = 0
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cb_Sesi_Bulan.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 1
        Cb_Sesi_Bulan.Items.Add("FEBRUARI") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 2
        Cb_Sesi_Bulan.Items.Add("MAC") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 3
        Cb_Sesi_Bulan.Items.Add("APRIL") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 4
        Cb_Sesi_Bulan.Items.Add("MEI") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 5
        Cb_Sesi_Bulan.Items.Add("JUN") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 6
        Cb_Sesi_Bulan.Items.Add("JULAI") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 7
        Cb_Sesi_Bulan.Items.Add("OGOS") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 8
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 9
        Cb_Sesi_Bulan.Items.Add("OKTOBER") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 10
        Cb_Sesi_Bulan.Items.Add("NOVEMBER") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 11
        Cb_Sesi_Bulan.Items.Add("DISEMBER") : Cb_Sesi_Bulan.Items.Item(Cb_Sesi_Bulan.Items.Count - 1).Value = 12

        Cb_Sesi_Tahun.Items.Add("(TAHUN)") : Cb_Sesi_Tahun.Items.Item(Cb_Sesi_Tahun.Items.Count - 1).Value = 0
        'Comment Original 22052017-OSH
        'For i = 0 To 60
        For i = 0 To 100
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i) : Cb_Sesi_Tahun.Items.Item(Cb_Sesi_Tahun.Items.Count - 1).Value = Year(Now) - i
        Next

        'KOLEJ
        If Session("PINDA") = True Then
            Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ ORDER BY DC_KOLEJ"
            Rdr = Cmd.ExecuteReader()
            Cb_Kolej.Items.Clear()
            Cb_Kolej.Items.Add("")
            While Rdr.Read
                Cb_Kolej.Items.Add(Rdr(0))
                Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
        Else
            Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS = " & RadioButtonList1.SelectedValue & "ORDER BY DC_KOLEJ"
            Rdr = Cmd.ExecuteReader()
            Cb_Kolej.Items.Clear()
            Cb_Kolej.Items.Add("")
            While Rdr.Read
                Cb_Kolej.Items.Add(Rdr(0))
                Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
        End If

        'KOLEJ B1
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS <> 3 ORDER BY DC_KOLEJ "
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej_B1.Items.Clear()
        Cb_Kolej_B1.Items.Add("")
        While Rdr.Read
            Cb_Kolej_B1.Items.Add(Rdr(0))
            Cb_Kolej_B1.Items.Item(Cb_Kolej_B1.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            'Cb_Tajaan.Items.Add(New ListItem(Rdr(0), Rdr(1)))
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'GRED
        Cmd.CommandText = "select dc_gred, id_gred from pn_gred order by dc_gred"
        Rdr = Cmd.ExecuteReader()
        Cb_Gred.Items.Clear()
        Cb_Gred.Items.Add("")
        Cb_Gred.Items.Item(0).Value = 0
        While Rdr.Read
            Cb_Gred.Items.Add(Rdr(0))
            Cb_Gred.Items.Item(Cb_Gred.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        ''GELARAN
        'Cmd.CommandText = "select dc_gelaran, id_gelaran from pn_gelaran_jwtn order by dc_gelaran"
        'Rdr = Cmd.ExecuteReader()
        'Cb_Gelaran.Items.Clear()
        'Cb_Gelaran.Items.Add("")
        'Cb_Gelaran.Items.Item(0).Value = 0
        'While Rdr.Read
        '    Cb_Gelaran.Items.Add(Rdr(0))
        '    Cb_Gelaran.Items.Item(Cb_Gelaran.Items.Count - 1).Value = Rdr(1)
        'End While        
        'Rdr.Close()

        Cn.Close()
        If Session("PINDA") = True Then Isi_Pinda() : Isi_APC(Session("NOKP"))

        'Colour text on textbox catatan 27122016 -OSH
        Tx_Catatan.ForeColor = Drawing.Color.Red
        Tx_Catatan.Font.Bold = True

        'Add calculate actual age 15092020 - OSH 
        Dim X As Integer
        If Tx_Tkh_Lahir.Text <> "" Then
            X = Kira_Umur_Tkh_Server(Tx_Tkh_Lahir.Text.ToString)
            If X > CInt(Tx_Umur.Text) Then
                Tx_Umur.Text = X ' Populate Umur 
            End If
            Umur_Check = False
        Else
            Umur_Check = True
        End If
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer, G As String ', x As Button
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$" & sender.id) - InStr(sender.uniqueid, "$ctl") - 4))
        G = Mid(sender.uniqueid, InStr(sender.uniqueid, "$Gd") + 1, InStr(sender.uniqueid, "$ctl") - InStr(sender.uniqueid, "$Gd") - 1)
        Try
            sender.visible = False
            If G = "Gd" Then Gd.Rows.Item(i - 1).Visible = True
            If G = "GdA" Then GdA.Rows.Item(i - 1).Visible = True
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        Gd.Height = Unit.Pixel(1)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(3).Width = Unit.Pixel(22)
        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Private Sub GdA_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdA.RowCreated
        GdA.Height = Unit.Pixel(1)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(3).Width = Unit.Pixel(22)
        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Protected Sub Cb_Bangsa_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Bangsa.SelectedIndexChanged
        If Cb_Bangsa.SelectedIndex > 4 Then Cb_Etnik.Enabled = True : Cb_Etnik.SelectedIndex = -1 Else Cb_Etnik.Enabled = False : Cb_Etnik.SelectedIndex = -1
    End Sub

    Private Sub Gd_APC_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_APC.RowCreated
        e.Row.Height = Unit.Pixel(1)
        e.Row.Cells(0).Width = Unit.Pixel(30)
        e.Row.Cells(1).Width = Unit.Pixel(50)
        e.Row.Cells(2).Width = Unit.Pixel(50)
        e.Row.Cells(7).Visible = False
        e.Row.Cells(8).Visible = False
        e.Row.Cells(9).Visible = False
        e.Row.Cells(10).Visible = False
        e.Row.Cells(11).Visible = False
        e.Row.Cells(12).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub Gd_APC_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_APC.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Try
            If CInt(e.Row.Cells(2).Text) = Year(Now) Then
                e.Row.RowState = DataControlRowState.Selected
                Tx_APC.Text = e.Row.Cells(3).Text & "/" & e.Row.Cells(2).Text
                Tx_Tpt_Amalan.Text = Fx(e.Row.Cells(5).Text)
                Tx_Amalan_Sektor.Text = Fx(e.Row.Cells(6).Text)
                Tx_Amalan_Alamat.Text = Fx(e.Row.Cells(7).Text)
                Tx_Amalan_Poskod.Text = Fx(e.Row.Cells(8).Text)
                Tx_Amalan_Bandar.Text = Fx(e.Row.Cells(9).Text)
                Tx_Amalan_Negeri.Text = Fx(e.Row.Cells(10).Text)
                Tx_Amalan_Tel.Text = Fx(e.Row.Cells(11).Text)
                Tx_Amalan_Fax.Text = Fx(e.Row.Cells(12).Text)
            End If
            If e.Row.Cells(3).Text = "&nbsp;" Then e.Row.Cells(3).Text = "RET"
        Catch ex As Exception
        End Try

        'Add colour code on retation and non-application 10022017 - OSH
        Try
            If e.Row.RowType = DataControlRowType.DataRow Then
                If (e.Row.Cells(3).Text = "TP") Then
                    e.Row.BackColor = Drawing.Color.AliceBlue
                ElseIf (e.Row.Cells(3).Text = "RET") Then
                    e.Row.BackColor = Drawing.Color.BlanchedAlmond
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub


    Protected Sub Gd_APC_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd_APC.SelectedIndexChanged
        Tx_APC.Text = Gd_APC.SelectedRow.Cells(3).Text & "/" & Gd_APC.SelectedRow.Cells(2).Text
        Gd_APC.SelectedRow.Cells(3).Text = Replace(Gd_APC.SelectedRow.Cells(3).Text, "&nbsp;/", "")
        Tx_Tpt_Amalan.Text = Fx(Gd_APC.SelectedRow.Cells(5).Text)
        Tx_Amalan_Sektor.Text = Fx(Gd_APC.SelectedRow.Cells(6).Text)
        Tx_Amalan_Alamat.Text = Fx(Gd_APC.SelectedRow.Cells(7).Text)
        Tx_Amalan_Poskod.Text = Fx(Gd_APC.SelectedRow.Cells(8).Text)
        Tx_Amalan_Bandar.Text = Fx(Gd_APC.SelectedRow.Cells(9).Text)
        Tx_Amalan_Negeri.Text = Fx(Gd_APC.SelectedRow.Cells(10).Text)
        Tx_Amalan_Tel.Text = Fx(Gd_APC.SelectedRow.Cells(11).Text)
        Tx_Amalan_Fax.Text = Fx(Gd_APC.SelectedRow.Cells(12).Text)
    End Sub

    Public Sub Mandatori()
        Dim X As String = ""

        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
        If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
        If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.SelectedItem.Text = "MALAYSIA" Then If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
        If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.SelectedItem.Text = "MALAYSIA" Then
            If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
        Else
            If Cb_TP_Negeri.Text.Trim = "" Then X += "Negara, "
        End If
        'Comment Original 17092020 - OSH 
        'If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi, "
        'Improvement check space-blank input 17092020 - OSH
        'If Cb_Kolej.Text.Trim = "" Or Cb_Kolej.SelectedValue < 1 Then X += "Kolej/Institusi, "
        'Fix bugs -#904023 29032021 - OSH 
        If Cb_Kolej.Text.Trim = "" Or Cb_Kolej.SelectedValue < "1" Then X += "Kolej/Institusi, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        'If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        'If Tx_T_Latihan.Text.Trim = "" Then X += "Tarikh Tamat Latihan, "
        If Tx_Periksa.Text.Trim = "" Then X += "Tarikh Peperiksaan Akhir, "
        If Tx_CPD.Text.Trim = "" Then Tx_CPD.Text = "0"

        If Cb_Status.SelectedIndex = 0 Then X += "Status, "
        If Umur_Check = True Or Tx_Umur.Text = "0" Then X += "Umur, " 'Add check age and brithdate 15092020 - OSH 
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:" + X) : Exit Sub
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        Mandatori()
        If Session("PINDA") = True Then Simpan_Pinda() : Exit Sub
    End Sub

    Protected Sub cmdHantar0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar0.Click
        Mandatori()

        If Session("PINDA") = True Then
            Simpan_Pilih()

            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

            'Channel KPSL Nurses Custom Process 27062024 - OSH 
            'Cmd.CommandText = "select a.nokp, r.xm_jenis, w.Sektor from jt_penuh r left outer join pn_kpsl_lantikan_jb a on (r.nokp = a.nokp) inner join jt_penuh_apc l on (r.nokp = l.nokp) left outer join pn_tpt_amalan w on l.Id_Amalan = w.Id_Amalan where w.Sektor = '1'and l.ret = '0' and l.Apc_Batal = '0' and l.Apc_Tahun = YEAR(getdate())-1 and r.nama like '" & Tx_Nama.Text.Trim & "%' and r.nokp like '" & Tx_NoKP.Text & "%' and r.nopd like '" & Tx_NoPd.Text.Trim & "%'"

            'retrive record via sesion nopd_only value 19082024 - OSH 
            'Cmd.CommandText = "select a.nokp, r.xm_jenis, w.Sektor from jt_penuh r left outer join pn_kpsl_lantikan_jb a on (r.nokp = a.nokp) inner join jt_penuh_apc l on (r.nokp = l.nokp) left outer join pn_tpt_amalan w on l.Id_Amalan = w.Id_Amalan where w.Sektor = '1'and l.ret = '0' and l.Apc_Batal = '0' and l.Apc_Tahun = YEAR(getdate())-1 and r.nama like '" & Apo(Tx_Nama.Text.Trim) & "%' and r.nokp like '" & Tx_NoKP.Text & "%' and r.nopd like '" & Session("nopd_only").ToString & "%'"

            'Fix missing previous year bug - Ticket #294287 -30082024 - OSH 
            'Cmd.CommandText = "select top 1 a.nokp, r.xm_jenis, w.Sektor from jt_penuh r left outer join pn_kpsl_lantikan_jb a on (r.nokp = a.nokp) inner join jt_penuh_apc l on (r.nokp = l.nokp) left outer join pn_tpt_amalan w on l.Id_Amalan = w.Id_Amalan where w.Sektor = '1'and l.ret = '0' and l.Apc_Batal = '0' and l. non_ap is null and r.nama like '" & Apo(Tx_Nama.Text.Trim) & "%' and r.nokp like '" & Tx_NoKP.Text & "%' and r.nopd like '" & Session("nopd_only").ToString & "%' order by cast(l.apc_tahun as int) desc"
            'Fix mistake not full apc bugs 29102024 - OSH 
            Cmd.CommandText = "select top 1 a.nokp, r.xm_jenis, w.Sektor from jt_penuh r left outer join pn_kpsl_lantikan_jb a on (r.nokp = a.nokp) inner join jt_penuh_apc l on (r.nokp = l.nokp) left outer join pn_tpt_amalan w on l.Id_Amalan = w.Id_Amalan where l.ret = '0' and l.Apc_Batal = '0' and l. non_ap is null and r.nama like '" & Apo(Tx_Nama.Text.Trim) & "%' and r.nokp like '" & Tx_NoKP.Text & "%' and r.nopd like '" & Session("nopd_only").ToString & "%' order by cast(l.apc_tahun as int) desc"

            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                'Comment Original 13122024 -OSH
                'If Not IsDBNull(Rdr(1)) Then
                If Not IsDBNull(Rdr(1)) And Not IsDBNull(Rdr(2)) Then
                    If IsDBNull(Rdr(0)) And Rdr(1) = "KPSL" And Rdr(2) = "1" Then
                        Session("KPSL_IC") = Tx_NoKP.Text.Trim 'IC
                        If Tx_CPD.Text <> "" Then Session("KPSL_CPD") = Tx_CPD.Text.Trim 'CPD
                        Response.Redirect("P3_APC_Lantik_KPSL.aspx")
                    End If
                End If
            End While
            Rdr.Close()

            'Check current or future APC exist before add tray 22052020 - OSH 
            Try
                Cmd.CommandText = "Select count (nokp) from jt_penuh_apc where ret = 0 and apc_batal = 0  and apc_tahun between year(getdate()) and Year(getdate()) + 1 And NoKP = '" & Tx_NoKP.Text & "'"
                Rdr = Cmd.ExecuteReader
                If Rdr.Read And Rdr(0) = 2 Then
                    Rdr.Close()
                    Cn.Close()
                    Msg(Me, "APC Tahun Semasa and Hadapan Telah Wujud !") : Exit Sub
                Else
                    Rdr.Close() : Cn.Close()
                    If Cn.State <> ConnectionState.Open Then Cn.Open()
                    Cmd.CommandText = "select * from tmp_adv_apc where nokp = '" & Tx_NoKP.Text & "'"
                    Rdr = Cmd.ExecuteReader
                    If Rdr.Read Then
                        Rdr.Close()
                        Cn.Close()
                        Msg(Me, "Rekod telah wujud dalam bakul senarai") : Exit Sub
                    Else
                        Rdr.Close()
                        Cmd.CommandText = "insert tmp_adv_apc (id_pg, j_daftar, nokp, cpd) select '" & Session("Id_PG") & "', j_daftar, nokp,'" & Tx_CPD.Text.Trim & "' from jt_penuh where nokp =  '" & Tx_NoKP.Text & "'"
                        Cmd.ExecuteNonQuery()
                        Cn.Close()
                        Response.Redirect("p3_apc_cari2.aspx")
                    End If
                End If
            Catch ex As Exception
                Cn.Close()
                Msg(Me, ex.Message)
            End Try
        End If
    End Sub

    Protected Sub RadioButtonList1_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles RadioButtonList1.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS=" & RadioButtonList1.SelectedValue & " ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    'Protected Sub Tx_Tkh_Lahir_TextChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Tx_Tkh_Lahir.TextChanged
    '    'If Tx_Tkh_Lahir.Text <> "" Then Kira_Umur3(CDate(Tx_Tkh_Lahir.Text), Tx_Umur)
    '    'Dim y As DateTime
    '    'Dim z As String
    '    'z = Tx_Tkh_Lahir.Text
    '    'y = DateTime.ParseExact(z, "dd/MM/yyyy", CultureInfo.InvariantCulture)
    '    'z = y.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
    '    'Tx_Umur.Text = DateDiff(DateInterval.Year, y, Now)
    'End Sub

    Public Sub Fn_Negara(ByVal x As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If x = 0 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Else
            Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        End If

        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

    End Sub

    Public Sub Fn_Negara_SM(ByVal x As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If x = 0 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Else
            Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        End If

        Rdr = Cmd.ExecuteReader()
        Cb_SM_Negeri.Items.Clear()
        Cb_SM_Negeri.Items.Add("")
        While Rdr.Read
            Cb_SM_Negeri.Items.Add(Rdr(0))
            Cb_SM_Negeri.Items.Item(Cb_SM_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

    End Sub

    Protected Sub bt_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles bt_Cari.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE DC_KOLEJ LIKE '%" & tx_Cari.Text & "%' ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Cb_Kolej_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kolej.SelectedIndexChanged
        RadioButtonList1.Items(0).Selected = False : RadioButtonList1.Items(1).Selected = False : RadioButtonList1.Items(2).Selected = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Comment Original 29032021 - OSH 
        'Cmd.CommandText = "SELECT JENIS FROM PN_KOLEJ WHERE ID_KOLEJ =" & Cb_Kolej.Text
        Cmd.CommandText = "SELECT JENIS FROM PN_KOLEJ WHERE ID_KOLEJ = '" & Cb_Kolej.Text & "' "
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Select Case Rdr(0)
                Case 1 : RadioButtonList1.Items(0).Selected = True
                Case 2 : RadioButtonList1.Items(1).Selected = True
                Case 3 : RadioButtonList1.Items(2).Selected = True
                Case Else
            End Select
        End While
        Rdr.Close()
        Cn.Close()

        tx_Cari.Text = ""
    End Sub

    Public Sub Fn_Day(ByVal X As DropDownList)
        X.Items.Add("(HARI)")
        For i = 1 To 31
            X.Items.Add(i)
        Next
    End Sub

    Public Sub Fn_Month(ByVal X As DropDownList)
        X.Items.Add("(BULAN)")
        X.Items.Add("JANUARI") : X.Items.Item(X.Items.Count - 1).Value = 1
        X.Items.Add("FEBRUARI") : X.Items.Item(X.Items.Count - 1).Value = 2
        X.Items.Add("MAC") : X.Items.Item(X.Items.Count - 1).Value = 3
        X.Items.Add("APRIL") : X.Items.Item(X.Items.Count - 1).Value = 4
        X.Items.Add("MEI") : X.Items.Item(X.Items.Count - 1).Value = 5
        X.Items.Add("JUN") : X.Items.Item(X.Items.Count - 1).Value = 6
        X.Items.Add("JULAI") : X.Items.Item(X.Items.Count - 1).Value = 7
        X.Items.Add("OGOS") : X.Items.Item(X.Items.Count - 1).Value = 8
        X.Items.Add("SEPTEMBER") : X.Items.Item(X.Items.Count - 1).Value = 9
        X.Items.Add("OKTOBER") : X.Items.Item(X.Items.Count - 1).Value = 10
        X.Items.Add("NOVEMBER") : X.Items.Item(X.Items.Count - 1).Value = 11
        X.Items.Add("DISEMBER") : X.Items.Item(X.Items.Count - 1).Value = 12
    End Sub

    Public Sub Fn_Year(ByVal X As DropDownList)
        X.Items.Add("(TAHUN)")
        'Comment Original 22052017 -OSH
        'For i = 0 To 60
        For i = 0 To 100
            X.Items.Add(Year(Now) - i)
        Next
    End Sub

    Protected Sub Tx_Tkh_Lahir_TextChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Tx_Tkh_Lahir.TextChanged
        'Add calculate actual age 15092020 - OSH 
        Dim X As Integer
        If Tx_Tkh_Lahir.Text <> "" Then
            X = Kira_Umur_Tkh_Server(Tx_Tkh_Lahir.Text.ToString)
            If X > CInt(Tx_Umur.Text) Then
                Tx_Umur.Text = X
            End If
            Umur_Check = False
        Else
            Umur_Check = True
        End If
    End Sub

    Public Sub Simpan_Pilih()

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String

            If Tx_NoPd_KBI.Text = "" Then Tx_NoPd_KBI.Text = "0"
            If Tx_NoPd_KU.Text = "" Then Tx_NoPd_KU.Text = "0"
            If Tx_NoPd_KJ.Text = "" Then Tx_NoPd_KJ.Text = "0"

            'Tarikh mula latihan
            Dim y As DateTime
            Dim z As String
            If Tx_M_Latihan.Text.Trim <> String.Empty Then
                z = Tx_M_Latihan.Text.Trim
                y = DateTime.ParseExact(z, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z = y.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z = "'" & z & "'"
            Else
                z = "NULL"
            End If

            'Tarikh tamat latihan
            Dim y1 As DateTime
            Dim z1 As String
            z1 = Tx_T_Latihan.Text.Trim
            If Tx_T_Latihan.Text.Trim <> String.Empty Then
                y1 = DateTime.ParseExact(z1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z1 = y1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z1 = "'" & z1 & "'"
            Else
                z1 = "NULL"
            End If

            'Tarikh peperiksaan
            Dim y2 As DateTime
            Dim z2 As String
            z2 = Tx_Periksa.Text.Trim
            If Tx_Periksa.Text.Trim <> String.Empty Then
                y2 = DateTime.ParseExact(z2, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z2 = y2.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z2 = "'" & z2 & "'"
            Else
                z2 = "NULL"
            End If

            'Tarikh Mula Perkhidmatan
            Dim y3 As DateTime
            Dim z3 As String
            z3 = Tx_Mula_Khidmat.Text.Trim
            If Tx_Mula_Khidmat.Text.Trim <> String.Empty Then
                y3 = DateTime.ParseExact(z3, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z3 = y3.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z3 = "'" & z3 & "'"
            Else
                z3 = "NULL"
            End If

            ' Tarikh Sah Jawatan 
            Dim y4 As DateTime
            Dim z4 As String
            z4 = Tx_Sah_Jawatan.Text.Trim
            If Tx_Sah_Jawatan.Text.Trim <> String.Empty Then
                y4 = DateTime.ParseExact(z4, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z4 = y4.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z4 = "'" & z4 & "'"
            Else
                z4 = "NULL"
            End If

            'Tarikh Daftar Bidan 1
            Dim y5 As DateTime
            Dim z5 As String
            z5 = Tx_Daftar_B1.Text.Trim
            If Tx_Daftar_B1.Text.Trim <> String.Empty Then
                y5 = DateTime.ParseExact(z5, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z5 = y5.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z5 = "'" & z5 & "'"
            Else
                z5 = "NULL"
            End If

            'Tarikh Mula Latihan Bidan 1
            Dim y6 As DateTime
            Dim z6 As String
            z6 = Tx_M_Latihan_B1.Text.Trim
            If Tx_M_Latihan_B1.Text.Trim <> String.Empty Then
                y6 = DateTime.ParseExact(z6, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z6 = y6.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z6 = "'" & z6 & "'"
            Else
                z6 = "NULL"
            End If

            'Tarikh Tamat Latihan Bidan 1
            Dim y7 As DateTime
            Dim z7 As String
            z7 = Tx_T_Latihan_B1.Text.Trim
            If Tx_T_Latihan_B1.Text.Trim <> String.Empty Then
                y7 = DateTime.ParseExact(z7, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z7 = y7.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z7 = "'" & z7 & "'"
            Else
                z7 = "NULL"
            End If

            'Tarikh Peperiksaan Bidan 1
            Dim y8 As DateTime
            Dim z8 As String
            z8 = Tx_Periksa_B1.Text.Trim
            If Tx_Periksa_B1.Text.Trim <> String.Empty Then
                y8 = DateTime.ParseExact(z8, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z8 = y8.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z8 = "'" & z8 & "'"
            Else
                z8 = "NULL"
            End If

            ''Tarikh Lahir
            Dim y11 As DateTime
            Dim z11 As String = ""
            z11 = Tx_Tkh_Lahir.Text.Trim
            If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
                y11 = DateTime.ParseExact(z11, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z11 = y11.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z11 = "'" & z11 & "'"
            Else
                z11 = "NULL"
            End If

            'tarikh daftar
            Dim y12 As DateTime
            Dim z12 As String = ""
            z12 = Tx_Tkh_Daftar.Text.Trim
            If Tx_Tkh_Daftar.Text.Trim <> String.Empty Then
                y12 = DateTime.ParseExact(z12, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                z12 = y12.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                z12 = "'" & z12 & "'"
            Else
                z12 = "NULL"
            End If

        'Remove CPD update 19082024 - OSH 
        SQL = "update jt_penuh set " &
                   "tpt_lahir = '" & Tx_Tpt_Lahir.Text.Trim.ToUpper & "'," &
                   "tkh_lahir = " & z11 & "," &
                   "warganegara = " & Cb_Warga.SelectedValue & "," &
                   "jantina = '" & Cb_Jantina.SelectedValue & "'," &
                   "bangsa = " & Cb_Bangsa.SelectedValue & "," &
                   "etnik = " & Cb_Etnik.SelectedValue & "," &
                   "umur = " & Tx_Umur.Text & "," &
                   "agama = " & Cb_Agama.SelectedValue & "," &
                   "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," &
                   "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "'," &
                   "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," &
                   "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "'," &
                   "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," &
                   "tel_r = '" & Tx_Tel_R.Text.Trim & "'," &
                   "tel_hp = '" & Tx_Tel_HP.Text.Trim & "'," &
                   "emel = '" & Tx_Emel.Text.Trim & "'," &
                   "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim.ToUpper) & "'," &
                   "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," &
                   "sm_bandar = '" & Tx_SM_Bandar.Text.Trim.ToUpper & "'," &
                   "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," &
                   "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," &
                   "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," &
                   "sesi_bulan = " & Cb_Sesi_Bulan.SelectedValue & "," &
                   "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedValue & "'," &
                   "tkh_latihan_mula = " & z & "," &
                   "tkh_latihan_tamat = " & z1 & "," &
                   "tkh_periksa_akhir = " & z2 & "," &
                   "tkh_mula_khidmat = " & z3 & "," &
                   "tkh_sah_jwtn = " & z4 & "," &
                   "gred = " & Cb_Gred.SelectedValue & "," &
                   "gelaran_jwtn = " & Cb_Gelaran.SelectedValue & "," &
                   "nopd_b1 = " & CInt(Tx_NoPd_KBI.Text) & "," &
                   "id_kolej_b1 = '" & Cb_Kolej_B1.SelectedItem.Value & "'," &
                   "tkh_daftar_b1 = " & z5 & "," &
                   "tkh_latihan_mula_b1 = " & z6 & "," &
                   "tkh_latihan_tamat_b1 = " & z7 & "," &
                   "tkh_periksa_akhir_b1 = " & z8 & "," &
                   "nopd_ku = " & CInt(Tx_NoPd_KU.Text) & "," &
                   "nopd_kj = " & CInt(Tx_NoPd_KJ.Text) & "," &
                   "log_status = '" & Cb_Status.SelectedItem.Value & "'," &
                   "log_catatan = '" & Apo(Tx_Catatan.Text.Trim.ToUpper) & "'," &
                   "mod_id = '" & Session("id_pg") & "'," &
                   "mod_tkh = getdate() " &
                   "where nokp = '" & Session("nokp") & "'"

            SQL += "; "
            SQL += "delete from jt_penuh_kelayakan where nokp = '" & Tx_NoKP.Text & "'; "

            'fIX DATES
            Dim i As Int16, tk, tt As TextBox
            For i = 0 To Gd.Rows.Count - 1
                If Not Gd.Rows.Item(i).Visible Then Exit For
                tk = Gd.Rows.Item(i).FindControl("Tx_Layak")
                tt = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")

                'Tarikh(Kelayakan)
                Dim y9 As DateTime
                Dim z9 As String
                z9 = tt.Text
                If tt.Text <> String.Empty Then
                    y9 = DateTime.ParseExact(z9, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                    z9 = y9.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                    z9 = "'" & z9 & "'"
                Else
                    z9 = "NULL"
                End If

                SQL += "insert jt_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" &
                "'" & Tx_NoKP.Text.Trim & "'," &
                "1," &
                "'" & tk.Text.Trim & "'," &
                "" & z9 & "" &
                ")"
                SQL += ";"
            Next

            For i = 0 To GdA.Rows.Count - 1
                If Not GdA.Rows.Item(i).Visible Then Exit For
                tk = GdA.Rows.Item(i).FindControl("Tx_Layak")
                tt = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")

                'Tarikh(Kelayakan)
                Dim y10 As DateTime
                Dim z10 As String
                z10 = tt.Text
                If tt.Text <> String.Empty Then
                    y10 = DateTime.ParseExact(z10, "dd/MM/yyyy", CultureInfo.InvariantCulture)
                    z10 = y10.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
                    z10 = "'" & z10 & "'"
                Else
                    z10 = "NULL"
                End If

                SQL += "insert jt_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" &
                "'" & Tx_NoKP.Text.Trim & "'," &
                "2," &
                "'" & tk.Text.Trim & "'," &
                "" & z10 & "" &
                ")"
                SQL += ";"
            Next

        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try

    End Sub
End Class