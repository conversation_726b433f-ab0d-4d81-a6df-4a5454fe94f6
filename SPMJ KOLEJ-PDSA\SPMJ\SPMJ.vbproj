﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8F6F8AC3-C8BE-4285-92D3-AA4BDCDC6AEF}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{F184B08F-C81C-45F6-A57F-5ABD9991F28F}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <RootNamespace>SPMJ</RootNamespace>
    <AssemblyName>SPMJ</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UseIISExpress>false</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>SPMJ.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <WarningsAsErrors>
    </WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>SPMJ.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <WarningsAsErrors>
    </WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AjaxControlToolkit, Version=3.0.20820.16598, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\Local Settings\Application Data\Microsoft\VisualStudio\9.0\ProjectAssemblies\6jdcyrxx01\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.10.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\SPMJ -PDSA - VS 2008\SPMJ\bin\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.xmlworker, Version=5.5.10.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\SPMJ -PDSA - VS 2008\SPMJ\bin\itextsharp.xmlworker.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\..\WINDOWS\assembly\GAC_MSIL\Microsoft.ReportViewer.WebForms\9.0.0.0__b03f5f7f11d50a3a\Microsoft.ReportViewer.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Web.Extensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.Mobile" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Collections.Specialized" />
    <Import Include="System.Configuration" />
    <Import Include="System.Text" />
    <Import Include="System.Text.RegularExpressions" />
    <Import Include="System.Web" />
    <Import Include="System.Web.Caching" />
    <Import Include="System.Web.SessionState" />
    <Import Include="System.Web.Security" />
    <Import Include="System.Web.Profile" />
    <Import Include="System.Web.UI" />
    <Import Include="System.Web.UI.WebControls" />
    <Import Include="System.Web.UI.WebControls.WebParts" />
    <Import Include="System.Web.UI.HtmlControls" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ForcePasswordChange.aspx" />
    <Content Include="Login_J.aspx" />
    <Content Include="OtpVerification.aspx" />
    <Content Include="Pelatih_Daftar1a.aspx" />
    <Content Include="Pelatih_Daftar1b.aspx" />
    <Content Include="Pelatih_Daftar1c.aspx" />
    <Content Include="Pelatih_Daftar2.aspx" />
    <Content Include="Pelatih_Daftar2a.aspx" />
    <Content Include="Pelatih_Daftar3bi.aspx" />
    <Content Include="Pelatih_Pinda2a.aspx" />
    <Content Include="Pelatih_Pinda2b.aspx" />
    <Content Include="Pelatih_Pinda3b.aspx" />
    <Content Include="Saring2.aspx" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Blank.aspx.designer.vb">
      <DependentUpon>Blank.aspx</DependentUpon>
    </Compile>
    <Compile Include="Blank.aspx.vb">
      <DependentUpon>Blank.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EmailServiceClient.vb" />
    <Compile Include="ForcePasswordChange.aspx.designer.vb">
      <DependentUpon>ForcePasswordChange.aspx</DependentUpon>
    </Compile>
    <Compile Include="ForcePasswordChange.aspx.vb">
      <DependentUpon>ForcePasswordChange.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Kolej.Master.designer.vb">
      <DependentUpon>Kolej.Master</DependentUpon>
    </Compile>
    <Compile Include="Kolej.Master.vb">
      <DependentUpon>Kolej.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login_J.aspx.designer.vb">
      <DependentUpon>Login_J.aspx</DependentUpon>
    </Compile>
    <Compile Include="Login_J.aspx.vb">
      <DependentUpon>Login_J.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Mesej.aspx.designer.vb">
      <DependentUpon>Mesej.aspx</DependentUpon>
    </Compile>
    <Compile Include="Mesej.aspx.vb">
      <DependentUpon>Mesej.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="OtpVerification.aspx.designer.vb">
      <DependentUpon>OtpVerification.aspx</DependentUpon>
    </Compile>
    <Compile Include="OtpVerification.aspx.vb">
      <DependentUpon>OtpVerification.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PasswordHelper.vb" />
    <Compile Include="Pelatih_Cr.aspx.designer.vb">
      <DependentUpon>Pelatih_Cr.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Cr.aspx.vb">
      <DependentUpon>Pelatih_Cr.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Daftar1a.aspx.designer.vb">
      <DependentUpon>Pelatih_Daftar1a.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Daftar1a.aspx.vb">
      <DependentUpon>Pelatih_Daftar1a.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Daftar1b.aspx.designer.vb">
      <DependentUpon>Pelatih_Daftar1b.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Daftar1b.aspx.vb">
      <DependentUpon>Pelatih_Daftar1b.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Daftar1c.aspx.designer.vb">
      <DependentUpon>Pelatih_Daftar1c.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Daftar1c.aspx.vb">
      <DependentUpon>Pelatih_Daftar1c.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Daftar2.aspx.designer.vb">
      <DependentUpon>Pelatih_Daftar2.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Daftar2.aspx.vb">
      <DependentUpon>Pelatih_Daftar2.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Daftar2a.aspx.designer.vb">
      <DependentUpon>Pelatih_Daftar2a.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Daftar2a.aspx.vb">
      <DependentUpon>Pelatih_Daftar2a.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Daftar3bi.aspx.designer.vb">
      <DependentUpon>Pelatih_Daftar3bi.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Daftar3bi.aspx.vb">
      <DependentUpon>Pelatih_Daftar3bi.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Pinda1.aspx.designer.vb">
      <DependentUpon>Pelatih_Pinda1.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Pinda1.aspx.vb">
      <DependentUpon>Pelatih_Pinda1.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Pinda2a.aspx.designer.vb">
      <DependentUpon>Pelatih_Pinda2a.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Pinda2a.aspx.vb">
      <DependentUpon>Pelatih_Pinda2a.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Pinda2b.aspx.designer.vb">
      <DependentUpon>Pelatih_Pinda2b.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Pinda2b.aspx.vb">
      <DependentUpon>Pelatih_Pinda2b.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pelatih_Pinda3b.aspx.designer.vb">
      <DependentUpon>Pelatih_Pinda3b.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pelatih_Pinda3b.aspx.vb">
      <DependentUpon>Pelatih_Pinda3b.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Pwd.aspx.designer.vb">
      <DependentUpon>Pwd.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pwd.aspx.vb">
      <DependentUpon>Pwd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Saring.aspx.designer.vb">
      <DependentUpon>Saring.aspx</DependentUpon>
    </Compile>
    <Compile Include="Saring.aspx.vb">
      <DependentUpon>Saring.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Saring2.aspx.designer.vb">
      <DependentUpon>Saring2.aspx</DependentUpon>
    </Compile>
    <Compile Include="Saring2.aspx.vb">
      <DependentUpon>Saring2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SN_Calon_B.aspx.designer.vb">
      <DependentUpon>SN_Calon_B.aspx</DependentUpon>
    </Compile>
    <Compile Include="SN_Calon_B.aspx.vb">
      <DependentUpon>SN_Calon_B.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="SPMJ_Mod.vb" />
    <Compile Include="Ralat.aspx.designer.vb">
      <DependentUpon>Ralat.aspx</DependentUpon>
    </Compile>
    <Compile Include="Ralat.aspx.vb">
      <DependentUpon>Ralat.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Blank.aspx" />
    <Content Include="Image\Border2.gif" />
    <Content Include="Image\Border200.gif" />
    <Content Include="Image\Border230.gif" />
    <Content Include="Image\Border250.gif" />
    <Content Include="Image\Border280.gif" />
    <Content Include="Image\Border300.gif" />
    <Content Include="Image\Border400.gif" />
    <Content Include="Image\Border550.gif" />
    <Content Include="Image\Border600.gif" />
    <Content Include="Image\Border650.gif" />
    <Content Include="Image\Bg_Arrow.gif" />
    <Content Include="Image\Bg_Atas.gif" />
    <Content Include="Image\Bg_Dot.gif" />
    <Content Include="Image\Bg_Dot2.gif" />
    <Content Include="Image\Bg_No.gif" />
    <Content Include="Image\Bg_No2.gif" />
    <Content Include="Image\Bg_Sgt.gif" />
    <Content Include="Image\Bg_Trans.gif" />
    <Content Include="Image\Border.gif" />
    <Content Include="Image\Hd_Top.gif" />
    <Content Include="Image\LJM-Bg.gif" />
    <Content Include="Image\Load.gif" />
    <Content Include="Kolej.css" />
    <Content Include="Kolej.Master" />
    <Content Include="Mesej.aspx" />
    <Content Include="Pelatih_Cr.aspx" />
    <Content Include="Pelatih_Pinda1.aspx" />
    <Content Include="Pwd.aspx" />
    <Content Include="Saring.aspx" />
    <Content Include="Ralat.aspx" />
    <Content Include="SN_Calon_B.aspx" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\PublishProfiles\FolderProfile.pubxml" />
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{967B4E0D-AD0C-4609-AB67-0FA40C0206D8}" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>55054</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>
          </IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>