﻿Imports System.IO
Imports System.Data
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Text
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports iTextSharp.text.html
Imports iTextSharp.text.html.simpleparser

Partial Public Class SN_Calon_B
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        If Cb_Kursus.SelectedIndex < 1 Then Cb_Kursus.Focus() : Exit Sub

        Dim ResultDate As Boolean
        Dim TotalCanidate As Integer
        Dim TotalEnroll As Integer
        Dim ID_EX As Integer

        'Resullt Date 
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select top 1 id_xm,tkh_papar from pn_xm where j_xm=" & Cb_Kursus.SelectedIndex & " order by tahun desc, siri desc, j_xm desc"
        Rdr = Cmd.ExecuteReader()

        If Rdr.Read Then
            '    'Exam Result
            '    If Not IsDBNull(Rdr("tkh_papar")) Then
            '        Fn_Result()
            '        'Comment Original 14092018 - OSH
            '        'Else
            If Not IsDBNull(Rdr("tkh_papar")) Then
                ResultDate = True
            Else
                ResultDate = False
            End If
            ID_EX = Rdr("id_xm") ' id peperiksaan 

            '        '    If DateDiff(DateInterval.Day, DateTime.Now, ExamDate) <= 14 And CInt(Rdr("ag")) > 0 And CInt(Rdr("status")) = 1 Then Fn_Candidate()
            '    End If

            '    If DateDiff(DateInterval.Day, DateTime.Now, ExamDate) > 14 And CInt(Rdr("ag")) > 0 And CInt(Rdr("status")) = 1 Then Fn_Enlist()

        End If
            Rdr.Close()
            Cn.Close()

            'Total Student
            Dim Cn2 As New OleDbConnection : Dim Cmd2 As New OleDbCommand : Dim Rdr2 As OleDbDataReader
            Cn2.ConnectionString = ServerId : Cn2.Open() : Cmd2.Connection = Cn2
            Cmd2.CommandText = "select count(nokp) from pelatih where  saring = 1 and j_kursus=" & Cb_Kursus.SelectedIndex & "and id_kolej = " & Session("id_kolej") & " "
            Rdr2 = Cmd2.ExecuteReader()

            If Rdr2.Read Then
                TotalEnroll = Rdr2(0)
            End If
            Rdr2.Close()
            Cn2.Close()

            'Total Canidate
            Dim Cn3 As New OleDbConnection : Dim Cmd3 As New OleDbCommand : Dim Rdr3 As OleDbDataReader
            Cn3.ConnectionString = ServerId : Cn3.Open() : Cmd3.Connection = Cn3
            Cmd3.CommandText = "select count(nokp) from xm_calon where  ag is not null and id_xm = " & ID_EX & "and  nokp in (select nokp from pelatih where  saring = 1 and j_kursus=" & Cb_Kursus.SelectedIndex & "and id_kolej = " & Session("id_kolej") & ")"
            Rdr3 = Cmd3.ExecuteReader()

            If Rdr3.Read Then
                TotalCanidate = Rdr3(0)
            End If
            Rdr3.Close()
            Cn3.Close()

            'Improve check exam stage progress 14092018 - OSH 
        If ResultDate = True Then
            'If String.IsNullOrEmpty(ResultDate.ToString) Then
            Fn_Result()
        ElseIf (TotalEnroll - TotalCanidate = 0) And ResultDate = Nothing Then
            Fn_Candidate()
        Else
            Fn_Enlist()
        End If
    End Sub

    Private Sub Fn_Enlist()
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim Tajuk As String = ""
        Dim K1, K2, K3 As Int16


        ' Declare Paper Size
        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
        'Declare table and column size
        Dim table As New PdfPTable(4)

        'Declare Font Type and SIze
        Dim font As New Font(font.FontFamily.TIMES_ROMAN, 10, font.BOLD)

        'Declare Font Type and SIze - DATA 
        Dim font2 As New Font(font.FontFamily.TIMES_ROMAN, 8, font.NORMAL)

        'Check select
        If Cb_Kursus.Text = "" Then
            MsgBox("Sila Pilih Jenis Kursus")
        End If

        If Cb_Kursus.SelectedValue = "1" Then
            K1 = "1"
            K2 = "5"
            K3 = "8"
            Tajuk = "SENARAI SARING BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT - " & Session("Dc_KOLEJ") & ""
        ElseIf Cb_Kursus.SelectedValue = "2" Then
            K1 = "2"
            K2 = "2"
            K3 = "2"
            Tajuk = "SENARAI SARING BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT - " & Session("Dc_KOLEJ") & ""
        ElseIf Cb_Kursus.SelectedValue = "3" Then
            K1 = "3"
            K2 = "3"
            K3 = "3"
            Tajuk = "SENARAI SARING BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT - " & Session("Dc_KOLEJ") & ""
        ElseIf Cb_Kursus.SelectedValue = "4" Then
            K1 = "4"
            K2 = "4"
            K3 = "4"
            Tajuk = "SENARAI SARING BAGI PEPERIKSAAN KEBIDANAN BAHAGIAN I - " & Session("Dc_KOLEJ") & ""
        End If

        'Set Cells Sizes - Width
        Dim TWidth() As Single = {40.0F, 100.0F, 110.0F, 120.0F}

        'repeat header
        table.HeaderRows = 2
        table.HorizontalAlignment = 1 '1: center

        'leave a gap before and after the table
        table.SpacingBefore = 20.0F
        table.SpacingAfter = 30.0F
        table.SetTotalWidth(TWidth) 'assign length sizes to table 


        Dim cell As New PdfPCell(New Phrase(Tajuk))
        cell.Colspan = 4
        cell.Border = 0
        cell.HorizontalAlignment = 1
        table.AddCell(cell)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Kolej_Exam_Enlist"
        cmd.Parameters.Add("@id_kolej", SqlDbType.Int, 4).Value = Session("ID_KOLEJ")
        cmd.Parameters.Add("@j_kursus", SqlDbType.Int, 4).Value = K1
        cmd.Parameters.Add("@j_kursus2", SqlDbType.Int, 4).Value = K2
        cmd.Parameters.Add("@j_kursus3", SqlDbType.Int, 4).Value = K3
        cmd.Connection = con

        Try
            con.Open()
            Dim rdr As SqlDataReader = cmd.ExecuteReader()
            Dim i As Integer = 1

            'Comment Original 15082018 - OSH 
            'table.AddCell(New PdfPCell(New Phrase("NO.", font)))
            'table.AddCell(New PdfPCell(New Phrase("NAMA", font)))
            'table.AddCell(New PdfPCell(New Phrase("NO. KAD PENGENALAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("KURSUS", font)))

            'Fixing Text Alighment 15082018 - OSH 
            Dim CH1 As New PdfPCell(New Phrase("NO", font))
            Dim CH2 As New PdfPCell(New Phrase("NAMA", font))
            Dim CH3 As New PdfPCell(New Phrase("NO. KAD PENGENALAN", font))
            Dim CH4 As New PdfPCell(New Phrase("KURSUS", font))

            CH1.HorizontalAlignment = Element.ALIGN_CENTER
            CH2.HorizontalAlignment = Element.ALIGN_CENTER
            CH3.HorizontalAlignment = Element.ALIGN_CENTER
            CH4.HorizontalAlignment = Element.ALIGN_CENTER


            CH1.VerticalAlignment = Element.ALIGN_MIDDLE
            CH2.VerticalAlignment = Element.ALIGN_MIDDLE
            CH3.VerticalAlignment = Element.ALIGN_MIDDLE
            CH4.VerticalAlignment = Element.ALIGN_MIDDLE


            table.AddCell(CH1)
            table.AddCell(CH2)
            table.AddCell(CH3)
            table.AddCell(CH4)

            If rdr.HasRows Then
                While rdr.Read()
                    'Comment Original 15082018 - OSH 
                    'table.AddCell(New PdfPCell(New Phrase(i, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(0).ToString, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(1).ToString, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(2).ToString, font)))


                    'Fixing Text Alighment 15082018 - OSH 
                    Dim CD1 As New PdfPCell(New Phrase(i, font2)) 'NO
                    Dim CD2 As New PdfPCell(New Phrase(rdr(0).ToString, font2)) 'NAMA
                    Dim CD3 As New PdfPCell(New Phrase(rdr(1).ToString, font2)) 'NO. KAD PENGENALAN
                    Dim CD4 As New PdfPCell(New Phrase(rdr(2).ToString, font2)) 'PUSAT PEPERIKSAAN

                    CD1.HorizontalAlignment = Element.ALIGN_CENTER
                    CD2.HorizontalAlignment = Element.ALIGN_LEFT
                    CD3.HorizontalAlignment = Element.ALIGN_CENTER
                    CD4.HorizontalAlignment = Element.ALIGN_CENTER

                    CD1.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD2.VerticalAlignment = Element.ALIGN_LEFT
                    CD3.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD4.VerticalAlignment = Element.ALIGN_MIDDLE

                    table.AddCell(CD1)
                    table.AddCell(CD2)
                    table.AddCell(CD3)
                    table.AddCell(CD4)

                    i = i + 1
                End While
            Else
                Msg(Me, "TIADA MAKLUMAT")
            End If
            rdr.Close()

            'Dump to pdf
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            pdfDoc.Add(table)
            pdfDoc.Close()
            HttpContext.Current.Response.ContentType = "application/pdf"
            HttpContext.Current.Response.AddHeader("content-disposition", _
           "attachment;filename=saring_list.pdf")
            HttpContext.Current.Response.Cache.SetCacheability(HttpCacheability.NoCache)
            HttpContext.Current.Response.Write(pdfDoc)
            HttpContext.Current.ApplicationInstance.CompleteRequest()

        Catch ex As Exception
            'Response.Write(ex.Message)
            Msg(Me, ex.Message)
        End Try
        con.Close()
        con.Dispose()
    End Sub

    Private Sub Fn_Candidate()
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim Tajuk As String = ""
        Dim K1, K2, K3 As Int16

        'Connection for exam details 13092018 - OSH 
        Dim con2 As New SqlConnection(ServerId_SQL)
        Dim cmd2 As New SqlCommand()
        cmd2.Connection = con2
        con2.Open()
        Dim rdr2 As SqlDataReader

        ' Declare Paper Size
        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)

        'Declare table and column size
        Dim table As New PdfPTable(6)

        'Declare Font Type and SIze
        Dim font As New Font(font.FontFamily.TIMES_ROMAN, 10, font.BOLD)

        'Declare Font Type and SIze - DATA 
        Dim font2 As New Font(font.FontFamily.TIMES_ROMAN, 8, font.NORMAL)

        If Cb_Kursus.SelectedValue = "1" Then
            K1 = "1"
            K2 = "5"
            K3 = "8"
            'Add query exam details 13092018 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 1 and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI CALON BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT - " & Session("Dc_KOLEJ") & " - SIRI " & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()
        ElseIf Cb_Kursus.SelectedValue = "2" Then
            K1 = "2"
            K2 = "2"
            K3 = "2"
            'Add query exam details 13092018 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 1 and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI CALON BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT - " & Session("Dc_KOLEJ") & "- SIRI " & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()
        ElseIf Cb_Kursus.SelectedValue = "3" Then
            K1 = "3"
            K2 = "3"
            K3 = "3"
            'Add query exam details 13092018 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 1 and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI CALON BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT - " & Session("Dc_KOLEJ") & "- SIRI " & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()
        ElseIf Cb_Kursus.SelectedValue = "4" Then
            K1 = "4"
            K2 = "4"
            K3 = "4"
            'Add query exam details 13092018 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 1 and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI CALON BAGI PEPERIKSAAN KEBIDANAN BAHAGIAN I - " & Session("Dc_KOLEJ") & " - SIRI " & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()
        End If

        ' Set Cells Sizes - Width
        'Dim TWidth() As Integer = {12, 10, 26, 10}
        Dim TWidth() As Single = {40.0F, 100.0F, 110.0F, 120.0F, 125.0F, 100.0F}

        'repeat header
        table.HeaderRows = 2
        table.HorizontalAlignment = 1 '1: center

        'leave a gap before and after the table
        table.SpacingBefore = 20.0F
        table.SpacingAfter = 30.0F
        table.SetTotalWidth(TWidth) 'assign length sizes to table 

        Dim cell As New PdfPCell(New Phrase(Tajuk))
        cell.Colspan = 6
        cell.Border = 0
        'cell.HorizontalAlignment = 1 
        cell.HorizontalAlignment = Element.ALIGN_CENTER
        table.AddCell(cell)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Kolej_Exam_Candidate"
        cmd.Parameters.Add("@id_kolej", SqlDbType.Int, 4).Value = Session("ID_KOLEJ")
        cmd.Parameters.Add("@j_kursus", SqlDbType.Int, 4).Value = K1
        cmd.Parameters.Add("@j_kursus2", SqlDbType.Int, 4).Value = K2
        cmd.Parameters.Add("@j_kursus3", SqlDbType.Int, 4).Value = K3
        cmd.Connection = con

        Try
            con.Open()
            Dim rdr As SqlDataReader = cmd.ExecuteReader()
            Dim i As Integer = 1

            'Comment Original 15082018 -OSH
            'Table Header Labeling 
            'table.AddCell(New PdfPCell(New Phrase("NO", font)))
            'table.AddCell(New PdfPCell(New Phrase("ANGKA GILIRAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("NAMA", font)))
            'table.AddCell(New PdfPCell(New Phrase("NO. KAD PENGENALAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("PUSAT PEPERIKSAAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("KURSUS", font)))


            'Fixing Text Alighment 15082018 - OSH 
            Dim CH1 As New PdfPCell(New Phrase("NO", font))
            Dim CH2 As New PdfPCell(New Phrase("ANGKA GILIRAN", font))
            Dim CH3 As New PdfPCell(New Phrase("NAMA", font))
            Dim CH4 As New PdfPCell(New Phrase("NO. KAD PENGENALAN", font))
            Dim CH5 As New PdfPCell(New Phrase("PUSAT PEPERIKSAAN", font))
            Dim CH6 As New PdfPCell(New Phrase("KURSUS", font))

            CH1.HorizontalAlignment = Element.ALIGN_CENTER
            CH2.HorizontalAlignment = Element.ALIGN_CENTER
            CH3.HorizontalAlignment = Element.ALIGN_CENTER
            CH4.HorizontalAlignment = Element.ALIGN_CENTER
            CH5.HorizontalAlignment = Element.ALIGN_CENTER
            CH6.HorizontalAlignment = Element.ALIGN_CENTER

            CH1.VerticalAlignment = Element.ALIGN_MIDDLE
            CH2.VerticalAlignment = Element.ALIGN_MIDDLE
            CH3.VerticalAlignment = Element.ALIGN_MIDDLE
            CH4.VerticalAlignment = Element.ALIGN_MIDDLE
            CH5.VerticalAlignment = Element.ALIGN_MIDDLE
            CH6.VerticalAlignment = Element.ALIGN_MIDDLE

            table.AddCell(CH1)
            table.AddCell(CH2)
            table.AddCell(CH3)
            table.AddCell(CH4)
            table.AddCell(CH5)
            table.AddCell(CH6)



            If rdr.HasRows Then
                'Populate rows table 
                While rdr.Read()

                    'Comment Original 15082018 -OSH
                    'table.AddCell(New PdfPCell(New Phrase(i, font))) 'NO
                    'table.AddCell(New PdfPCell(New Phrase(rdr(3).ToString, font))) 'ANGKA GILIRAN
                    'table.AddCell(New PdfPCell(New Phrase(rdr(0).ToString, font))) 'NAMA
                    'table.AddCell(New PdfPCell(New Phrase(rdr(1).ToString, font))) 'NO. KAD PENGENALAN
                    'table.AddCell(New PdfPCell(New Phrase(rdr(2).ToString, font))) 'PUSAT PEPERIKSAAN
                    'table.AddCell(New PdfPCell(New Phrase(rdr(4).ToString, font))) 'KURSUS


                    'Fixing Text Alighment 15082018 - OSH 
                    Dim CD1 As New PdfPCell(New Phrase(i, font2)) 'NO
                    Dim CD2 As New PdfPCell(New Phrase(rdr(3).ToString, font2)) 'ANGKA GILIRAN
                    Dim CD3 As New PdfPCell(New Phrase(rdr(0).ToString, font2)) 'NAMA
                    Dim CD4 As New PdfPCell(New Phrase(rdr(1).ToString, font2)) 'NO. KAD PENGENALAN
                    Dim CD5 As New PdfPCell(New Phrase(rdr(2).ToString, font2)) 'PUSAT PEPERIKSAAN
                    Dim CD6 As New PdfPCell(New Phrase(rdr(4).ToString, font2)) 'KURSUS

                    CD1.HorizontalAlignment = Element.ALIGN_CENTER
                    CD2.HorizontalAlignment = Element.ALIGN_CENTER
                    CD3.HorizontalAlignment = Element.ALIGN_LEFT
                    CD4.HorizontalAlignment = Element.ALIGN_CENTER
                    CD5.HorizontalAlignment = Element.ALIGN_LEFT
                    CD6.HorizontalAlignment = Element.ALIGN_CENTER

                    CD1.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD2.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD3.VerticalAlignment = Element.ALIGN_LEFT
                    CD4.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD5.VerticalAlignment = Element.ALIGN_LEFT
                    CD6.VerticalAlignment = Element.ALIGN_MIDDLE

                    table.AddCell(CD1)
                    table.AddCell(CD2)
                    table.AddCell(CD3)
                    table.AddCell(CD4)
                    table.AddCell(CD5)
                    table.AddCell(CD6)
                    i = i + 1

                End While
            Else
                Msg(Me, "TIADA MAKLUMAT")
            End If
            rdr.Close()
            con.Close()
            con.Dispose()

            'Dump to pdf
            ' PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            ' pdfDoc.Open()
            ' pdfDoc.Add(table)
            ' pdfDoc.Close()
            ' Response.ContentType = "application/pdf"
            ' Response.AddHeader("content-disposition", _
            '"attachment;filename=candidate_list.pdf")
            ' Response.Cache.SetCacheability(HttpCacheability.NoCache)
            ' Response.Write(pdfDoc)
            ' Response.End()

            'Dump to pdf
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            pdfDoc.Add(table)
            pdfDoc.Close()
            HttpContext.Current.Response.ContentType = "application/pdf"
            HttpContext.Current.Response.AddHeader("content-disposition", _
           "attachment;filename=candidate_list.pdf")
            HttpContext.Current.Response.Cache.SetCacheability(HttpCacheability.NoCache)
            HttpContext.Current.Response.Write(pdfDoc)
            HttpContext.Current.ApplicationInstance.CompleteRequest()

        Catch ex As Exception
            'Response.Write(ex.Message)
            Msg(Me, ex.Message)
        End Try

    End Sub

    Private Sub Fn_Result()
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim Tajuk As String = ""
        Dim K1, K2, K3 As Int16

        ' Declare Paper Size
        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
        'Declare table and column size
        Dim table As New PdfPTable(7)
        'Declare Font Type and SIze
        Dim font As New Font(font.FontFamily.TIMES_ROMAN, 10, font.BOLD)

        'Declare Font Type and SIze - DATA 
        Dim font2 As New Font(font.FontFamily.TIMES_ROMAN, 8, font.NORMAL)

        'Connection for exam details 13092018 - OSH 
        Dim con2 As New SqlConnection(ServerId_SQL)
        Dim cmd2 As New SqlCommand()
        cmd2.Connection = con2
        con2.Open()
        Dim rdr2 As SqlDataReader

      
        If Cb_Kursus.SelectedValue = "1" Then
            K1 = "1"
            K2 = "5"
            K3 = "8"

            'Comment Original 30062021 - OSH 
            'Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT - " & Session("Dc_KOLEJ") & ""
            'Add query exam details 30062021 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 0 and tkh_papar is not null and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT - " & Session("Dc_KOLEJ") & "- SIRI" & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()
            
        ElseIf Cb_Kursus.SelectedValue = "2" Then
            K1 = "2"
            K2 = "2"
            K3 = "2"
            'Comment Original 30062021 - OSH 
            'Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT - " & Session("Dc_KOLEJ") & ""
            'Add query exam details 30062021 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 0 and tkh_papar is not null and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT - " & Session("Dc_KOLEJ") & "- SIRI" & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()

        ElseIf Cb_Kursus.SelectedValue = "3" Then
            K1 = "3"
            K2 = "3"
            K3 = "3"
            'Comment Original 30062021 - OSH 
            'Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT - " & Session("Dc_KOLEJ") & ""
            'Add query exam details 30062021 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 0 and tkh_papar is not null and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT - " & Session("Dc_KOLEJ") & "- SIRI" & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()

        ElseIf Cb_Kursus.SelectedValue = "4" Then
            K1 = "4"
            K2 = "4"
            K3 = "4"
            'Comment Original 30062021 - OSH 
            'Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN KEBIDANAN BAHAGIAN I - " & Session("Dc_KOLEJ") & ""
            'Add query exam details 30062021 - OSH  
            cmd2.CommandText = "select top 1 siri,tahun,t1_tkh from pn_xm where status = 0 and tkh_papar is not null and j_xm = " & K1 & " order by id_xm desc"
            rdr2 = cmd2.ExecuteReader()
            rdr2.Read()
            Tajuk = "SENARAI KEPUTUSAN BAGI PEPERIKSAAN KEBIDANAN BAHAGIAN I - " & Session("Dc_KOLEJ") & "- SIRI" & rdr2("siri") & "/" & rdr2("tahun") & " (" & CDate(rdr2("t1_tkh")).ToString("dd-MM-yyyy") & ")"
            rdr2.Close()
            con2.Close()

        End If

        'Set Cells Sizes - Width
        Dim TWidth() As Single = {40.0F, 65.0F, 120.0F, 100.0F, 130.0F, 60.0F, 90.0F}

        'repeat header
        table.HeaderRows = 2
        table.HorizontalAlignment = 1 '1: center

        'leave a gap before and after the table
        table.SpacingBefore = 20.0F
        table.SpacingAfter = 30.0F
        table.SetTotalWidth(TWidth) 'assign length sizes to table 


        Dim cell As New PdfPCell(New Phrase(Tajuk))
        cell.Colspan = 7
        cell.Border = 0
        'Comment Original 30062021  - OSH
        'cell.HorizontalAlignment = 1
        'Adjust Title Alignment To Left 30062021 - OSH
        cell.HorizontalAlignment = Element.ALIGN_LEFT
        table.AddCell(cell)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Kolej_Exam_Result"
        cmd.Parameters.Add("@id_kolej", SqlDbType.Int, 4).Value = Session("ID_KOLEJ")
        cmd.Parameters.Add("@j_kursus", SqlDbType.Int, 4).Value = K1
        cmd.Parameters.Add("@j_kursus2", SqlDbType.Int, 4).Value = K2
        cmd.Parameters.Add("@j_kursus3", SqlDbType.Int, 4).Value = K3
        cmd.Parameters.Add("@j_peperiksaan", SqlDbType.Int, 4).Value = Cb_Kursus.SelectedValue
        cmd.Connection = con

        Try
            con.Open()
            Dim rdr As SqlDataReader = cmd.ExecuteReader()
            Dim i As Integer = 1

            'Comment Original 15082018 -OSH
            'table.AddCell(New PdfPCell(New Phrase("NO", font)))
            'table.AddCell(New PdfPCell(New Phrase("ANGKA GILIRAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("NAMA", font)))
            'table.AddCell(New PdfPCell(New Phrase("NO. KAD PENGENALAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("PUSAT PEPERIKSAAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("KURSUS", font)))
            'table.AddCell(New PdfPCell(New Phrase("KEPUTUSAN", font)))


            'Fixing Text Alighment 15082018 - OSH 
            Dim CH1 As New PdfPCell(New Phrase("NO", font))
            Dim CH2 As New PdfPCell(New Phrase("ANGKA GILIRAN", font))
            Dim CH3 As New PdfPCell(New Phrase("NAMA", font))
            Dim CH4 As New PdfPCell(New Phrase("NO. KAD PENGENALAN", font))
            Dim CH5 As New PdfPCell(New Phrase("PUSAT PEPERIKSAAN", font))
            Dim CH6 As New PdfPCell(New Phrase("KURSUS", font))
            Dim CH7 As New PdfPCell(New Phrase("KEPUTUSAN", font))

            CH1.HorizontalAlignment = Element.ALIGN_CENTER
            CH2.HorizontalAlignment = Element.ALIGN_CENTER
            CH3.HorizontalAlignment = Element.ALIGN_CENTER
            CH4.HorizontalAlignment = Element.ALIGN_CENTER
            CH5.HorizontalAlignment = Element.ALIGN_CENTER
            CH6.HorizontalAlignment = Element.ALIGN_CENTER
            CH7.HorizontalAlignment = Element.ALIGN_CENTER

            CH1.VerticalAlignment = Element.ALIGN_MIDDLE
            CH2.VerticalAlignment = Element.ALIGN_MIDDLE
            CH3.VerticalAlignment = Element.ALIGN_MIDDLE
            CH4.VerticalAlignment = Element.ALIGN_MIDDLE
            CH5.VerticalAlignment = Element.ALIGN_MIDDLE
            CH6.VerticalAlignment = Element.ALIGN_MIDDLE
            CH7.VerticalAlignment = Element.ALIGN_MIDDLE

            table.AddCell(CH1)
            table.AddCell(CH2)
            table.AddCell(CH3)
            table.AddCell(CH4)
            table.AddCell(CH5)
            table.AddCell(CH6)
            table.AddCell(CH7)

            If rdr.HasRows Then
                While rdr.Read()
                    'Comment Original 15082018 -OSH
                    'table.AddCell(New PdfPCell(New Phrase(i, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(3).ToString, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(0).ToString, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(1).ToString, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(2).ToString, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(4).ToString, font)))
                    'table.AddCell(New PdfPCell(New Phrase(rdr(5).ToString, font)))


                    'Fixing Text Alighment 15082018 - OSH 
                    Dim CD1 As New PdfPCell(New Phrase(i, font2)) 'NO
                    Dim CD2 As New PdfPCell(New Phrase(rdr(3).ToString, font2)) 'ANGKA GILIRAN
                    Dim CD3 As New PdfPCell(New Phrase(rdr(0).ToString, font2)) 'NAMA
                    Dim CD4 As New PdfPCell(New Phrase(rdr(1).ToString, font2)) 'NO. KAD PENGENALAN
                    Dim CD5 As New PdfPCell(New Phrase(rdr(2).ToString, font2)) 'PUSAT PEPERIKSAAN
                    Dim CD6 As New PdfPCell(New Phrase(rdr(4).ToString, font2)) 'KURSUS
                    Dim CD7 As New PdfPCell(New Phrase(rdr(5).ToString, font2)) 'kEPURUSAN 

                    CD1.HorizontalAlignment = Element.ALIGN_CENTER
                    CD2.HorizontalAlignment = Element.ALIGN_CENTER
                    CD3.HorizontalAlignment = Element.ALIGN_LEFT
                    CD4.HorizontalAlignment = Element.ALIGN_CENTER
                    CD5.HorizontalAlignment = Element.ALIGN_LEFT
                    CD6.HorizontalAlignment = Element.ALIGN_CENTER
                    CD7.HorizontalAlignment = Element.ALIGN_CENTER

                    CD1.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD2.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD3.VerticalAlignment = Element.ALIGN_LEFT
                    CD4.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD5.VerticalAlignment = Element.ALIGN_LEFT
                    CD6.VerticalAlignment = Element.ALIGN_MIDDLE
                    'Comment Original 30062021 - OSH 
                    'CD7.HorizontalAlignment = Element.ALIGN_CENTER
                    'Fix Vertical Alignment Issues 30062021 - OSH
                    CD7.VerticalAlignment = Element.ALIGN_MIDDLE

                    table.AddCell(CD1)
                    table.AddCell(CD2)
                    table.AddCell(CD3)
                    table.AddCell(CD4)
                    table.AddCell(CD5)
                    table.AddCell(CD6)
                    table.AddCell(CD7)

                    i = i + 1
                End While
            Else
                Msg(Me, "TIADA MAKLUMAT")
            End If
            rdr.Close()

            'Dump to pdf
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            pdfDoc.Add(table)
            pdfDoc.Close()
            HttpContext.Current.Response.ContentType = "application/pdf"
            HttpContext.Current.Response.AddHeader("content-disposition", _
           "attachment;filename=result_list.pdf")
            HttpContext.Current.Response.Cache.SetCacheability(HttpCacheability.NoCache)
            HttpContext.Current.Response.Write(pdfDoc)
            HttpContext.Current.ApplicationInstance.CompleteRequest()

        Catch ex As Exception
            'Response.Write(ex.Message)
            Msg(Me, ex.Message)
        End Try
        con.Close()
        con.Dispose()
    End Sub
End Class