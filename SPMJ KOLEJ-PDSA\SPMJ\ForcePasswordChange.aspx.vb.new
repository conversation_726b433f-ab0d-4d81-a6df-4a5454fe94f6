Imports System.Data.OleDb
Imports System.Text.RegularExpressions

Partial Public Class ForcePasswordChange
    Inherits System.Web.UI.Page
    
    Private ReadOnly emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
    Private emailClient As EmailServiceClient
    
    ''' <summary>
    ''' Get database connection string - compatible with existing KOLEJ system
    ''' </summary>
    Private Function GetConnectionString() As String
        Try
            ' Try to use inherited ServerId property if available (from master page or inherited class)
            Dim inheritedConnStr As String = ""
            Try
                ' This might work if the page inherits a connection string property
                Dim propInfo = Me.GetType().GetProperty("ServerId")
                If propInfo IsNot Nothing Then
                    Dim propValue = propInfo.GetValue(Me, Nothing)
                    If propValue IsNot Nothing Then
                        inheritedConnStr = propValue.ToString()
                    End If
                End If
                If Not String.IsNullOrEmpty(inheritedConnStr) Then
                    Return inheritedConnStr
                End If
            Catch
                ' Ignore if property doesn't exist
            End Try
        Catch
            ' Ignore inheritance errors
        End Try
        
        ' Fallback to connection string from web.config
        Dim connStr As String = ""
        
        If ConfigurationManager.ConnectionStrings("DefaultConnection") IsNot Nothing Then
            connStr = ConfigurationManager.ConnectionStrings("DefaultConnection").ConnectionString
        End If
        
        If String.IsNullOrEmpty(connStr) Then
            ' Try alternate connection string names
            If ConfigurationManager.ConnectionStrings("KOLEJConnection") IsNot Nothing Then
                connStr = ConfigurationManager.ConnectionStrings("KOLEJConnection").ConnectionString
            End If
        End If
        
        If String.IsNullOrEmpty(connStr) Then
            If ConfigurationManager.ConnectionStrings("SPMJConnection") IsNot Nothing Then
                connStr = ConfigurationManager.ConnectionStrings("SPMJConnection").ConnectionString
            End If
        End If
        
        If String.IsNullOrEmpty(connStr) Then
            Throw New Exception("Connection string not found in web.config")
        End If
        
        Return connStr
    End Function

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Check if user is in force password change mode
        If Session("FORCE_PASSWORD_CHANGE") Is Nothing OrElse Session("TEMP_USER_ID") Is Nothing Then
            ' Invalid access - redirect to login
            If Session("TEMP_SYSTEM_TYPE") = "KOLEJ-PDSA" Then
                Response.Redirect("Login_J.aspx")
            Else
                Response.Redirect("Login.aspx")
            End If
            Return
        End If

        If Not IsPostBack Then
            ' Initialize email service client
            Try
                If Not String.IsNullOrEmpty(emailServiceUrl) Then
                    emailClient = New EmailServiceClient(emailServiceUrl)
                End If
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine("Email service not available: " & ex.Message)
            End Try
        End If
    End Sub

    Protected Sub btnChangePassword_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnChangePassword.Click
        Dim newPassword As String = txtNewPassword.Text.Trim()
        Dim confirmPassword As String = txtConfirmPassword.Text.Trim()
        Dim userId As String = ""
        Dim oldPassword As String = ""

        ' Get session data safely (.NET 3.5 compatible)
        If Session("TEMP_USER_ID") IsNot Nothing Then
            userId = Session("TEMP_USER_ID").ToString()
        End If

        If Session("TEMP_OLD_PASSWORD") IsNot Nothing Then
            oldPassword = Session("TEMP_OLD_PASSWORD").ToString()
        End If
        
        ' Debug session data
        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Force Change Debug:")
        System.Diagnostics.Debug.WriteLine("  UserId: " & userId)
        System.Diagnostics.Debug.WriteLine("  NewPassword Length: " & newPassword.Length.ToString())
        System.Diagnostics.Debug.WriteLine("  ConfirmPassword Length: " & confirmPassword.Length.ToString())
        System.Diagnostics.Debug.WriteLine("  OldPassword Length: " & oldPassword.Length.ToString())

        ' Test database connection and user existence
        System.Diagnostics.Debug.WriteLine("Database Diagnostics:")
        System.Diagnostics.Debug.WriteLine(TestDatabaseConnection())

        If Not String.IsNullOrEmpty(userId) Then
            System.Diagnostics.Debug.WriteLine("User Verification:")
            VerifyUserExists(userId)
        End If

        ' Validate session data
        If String.IsNullOrEmpty(userId) Then
            ShowMessage("Sesi tamat tempoh. Sila log masuk semula.", "error")
            Response.Redirect("Login_J.aspx")
            Return
        End If

        ' Validate inputs
        If String.IsNullOrEmpty(newPassword) Then
            ShowMessage("Sila masukkan kata laluan baharu.", "error")
            txtNewPassword.Focus()
            Return
        End If

        If String.IsNullOrEmpty(confirmPassword) Then
            ShowMessage("Sila sahkan kata laluan baharu.", "error")
            txtConfirmPassword.Focus()
            Return
        End If

        ' Validate password strength
        If Not ValidatePasswordStrength(newPassword, oldPassword) Then
            Return
        End If

        ' Check password confirmation
        If newPassword <> confirmPassword Then
            ShowMessage("Kata laluan baharu dan pengesahan tidak sama.", "error")
            txtConfirmPassword.Focus()
            Return
        End If
        
        Try
            ' Test PasswordHelper functionality
            System.Diagnostics.Debug.WriteLine("Testing PasswordHelper...")
            Dim testPasswordEntry() As String = PasswordHelper.CreatePasswordEntry("test123")
            System.Diagnostics.Debug.WriteLine("✅ PasswordHelper working - Hash length: " & testPasswordEntry(0).Length.ToString() & ", Salt length: " & testPasswordEntry(1).Length.ToString())
            
            ' Additional validation before attempting password update
            System.Diagnostics.Debug.WriteLine("Pre-update validation:")
            System.Diagnostics.Debug.WriteLine("  Connection string test...")
            
            Try
                Dim connTest As String = GetConnectionString()
                System.Diagnostics.Debug.WriteLine("  ✅ Connection string obtained: " & connTest.Substring(0, Math.Min(50, connTest.Length)) & "...")
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine("  ❌ Connection string error: " & ex.Message)
                ShowMessage("Ralat sambungan pangkalan data: " & ex.Message, "error")
                Return
            End Try
            
            System.Diagnostics.Debug.WriteLine("  User verification...")
            If Not VerifyUserExists(userId) Then
                System.Diagnostics.Debug.WriteLine("  ❌ User does not exist in database")
                ShowMessage("Ralat: Pengguna tidak dijumpai dalam pangkalan data.", "error")
                Return
            End If
            System.Diagnostics.Debug.WriteLine("  ✅ User exists in database")
            
            ' Update password with encryption - try multiple approaches for varchar(15) PWD column
            System.Diagnostics.Debug.WriteLine("Attempting password update...")
            
            ' CRITICAL FIX: Try workaround first for varchar(15) PWD column limitation
            Dim updateSuccess As Boolean = UpdatePasswordWorkaround(userId, newPassword)

            ' If workaround failed, try enhanced update
            If Not updateSuccess Then
                System.Diagnostics.Debug.WriteLine("Workaround failed, trying enhanced update...")
                updateSuccess = UpdateUserPasswordForce(userId, newPassword)
            End If
            
            ' If enhanced update failed, try basic update
            If Not updateSuccess Then
                System.Diagnostics.Debug.WriteLine("Enhanced update failed, trying basic update...")
                updateSuccess = UpdateUserPasswordBasic(userId, newPassword)
            End If

            If updateSuccess Then
                ' Send notification email if available
                SendPasswordChangeNotification(userId)

                ' Clear force password change session
                ClearForcePasswordChangeSession()

                ' Set regular session variables and redirect to main system
                CompleteLoginAfterPasswordChange()

            Else
                ShowMessage("Ralat menukar kata laluan. Sila cuba lagi atau hubungi pentadbir sistem.", "error")
            End If
        Catch ex As Exception
            ShowMessage("Ralat sistem berlaku: " & ex.Message, "error")
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Force Change Exception: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Validate password strength requirements
    ''' </summary>
    Private Function ValidatePasswordStrength(password As String, oldPassword As String) As Boolean
        Dim errors As New List(Of String)
        
        ' Check minimum length
        If password.Length < 8 Then
            errors.Add("Kata laluan mestilah sekurang-kurangnya 8 aksara")
        End If
        
        ' Check for uppercase letter
        If Not Regex.IsMatch(password, "[A-Z]") Then
            errors.Add("Kata laluan mesti mengandungi huruf besar (A-Z)")
        End If
        
        ' Check for lowercase letter
        If Not Regex.IsMatch(password, "[a-z]") Then
            errors.Add("Kata laluan mesti mengandungi huruf kecil (a-z)")
        End If
        
        ' Check for number
        If Not Regex.IsMatch(password, "[0-9]") Then
            errors.Add("Kata laluan mesti mengandungi nombor (0-9)")
        End If
        
        ' Check for special character
        If Not Regex.IsMatch(password, "[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]") Then
            errors.Add("Kata laluan mesti mengandungi aksara khas (!@#$%^&*)")
        End If
        
        ' Check if different from old password
        If password = oldPassword Then
            errors.Add("Kata laluan baharu mestilah berbeza daripada kata laluan lama")
        End If
        
        If errors.Count > 0 Then
            ShowMessage("Kata laluan tidak memenuhi keperluan:<br/>• " & String.Join("<br/>• ", errors.ToArray()), "error")
            txtNewPassword.Focus()
            Return False
        End If
        
        Return True
    End Function
    
    ''' <summary>
    ''' Update user password with encryption
    ''' </summary>
    Private Function UpdateUserPasswordForce(userId As String, newPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing

        Try
            Dim connectionString As String = GetConnectionString()
            connection = New OleDbConnection(connectionString)
            connection.Open()

            ' Create encrypted password
            Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(newPassword)
            Dim hashedPassword As String = passwordEntry(0)
            Dim salt As String = passwordEntry(1)

            ' First, try to update with enhanced security columns
            Using command As New OleDbCommand()
                command.Connection = connection
                
                ' Try enhanced update first
                Try
                    System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Attempting enhanced password update...")
                    System.Diagnostics.Debug.WriteLine("  User ID: " & userId)
                    System.Diagnostics.Debug.WriteLine("  Hash length: " & hashedPassword.Length)
                    System.Diagnostics.Debug.WriteLine("  Salt length: " & salt.Length)
                    command.CommandText = "UPDATE kj_pengguna SET PWD = ?, salt = ?, pwd_encrypted = 1, pwd_migrated = 1, migration_date = ?, force_change_date = ?, failed_login_attempts = 0 WHERE Id_PG = ?"
                    command.Parameters.Clear()
                    command.Parameters.AddWithValue("@PWD", hashedPassword)
                    command.Parameters.AddWithValue("@salt", salt)
                    command.Parameters.AddWithValue("@migration_date", DateTime.Now)
                    command.Parameters.AddWithValue("@force_change_date", DateTime.Now)
                    command.Parameters.AddWithValue("@Id_PG", userId)

                    System.Diagnostics.Debug.WriteLine("  Executing SQL: " & command.CommandText)
                    Dim rowsAffected As Integer = command.ExecuteNonQuery()
                    System.Diagnostics.Debug.WriteLine("  Rows affected: " & rowsAffected)

                    If rowsAffected > 0 Then
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Enhanced password update successful for user: " & userId)
                        Return True
                    Else
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: No rows affected - user may not exist or conditions not met")
                        ' Check if user actually exists
                        command.Parameters.Clear()
                        command.CommandText = "SELECT COUNT(*) FROM kj_pengguna WHERE Id_PG = ?"
                        command.Parameters.AddWithValue("@Id_PG", userId)
                        Dim userExists As Integer = Convert.ToInt32(command.ExecuteScalar())
                        System.Diagnostics.Debug.WriteLine("  User exists check: " & userExists & " records found")

                        Return False
                    End If
                Catch ex As Exception
                    ' Enhanced columns may not exist, try basic update
                    System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Enhanced update failed, trying basic update: " & ex.Message)
                    System.Diagnostics.Debug.WriteLine("  Exception type: " & ex.GetType().Name)
                    
                    Try
                        command.Parameters.Clear()
                        command.CommandText = "UPDATE kj_pengguna SET PWD = ? WHERE Id_PG = ?"
                        command.Parameters.AddWithValue("@PWD", hashedPassword)
                        command.Parameters.AddWithValue("@Id_PG", userId)

                        System.Diagnostics.Debug.WriteLine("  Executing basic SQL: " & command.CommandText)
                        System.Diagnostics.Debug.WriteLine("  User ID parameter: " & userId)
                        System.Diagnostics.Debug.WriteLine("  WARNING: PWD column is varchar(15) but hash length is: " & hashedPassword.Length)
                        
                        Dim rowsAffected As Integer = command.ExecuteNonQuery()
                        System.Diagnostics.Debug.WriteLine("  Basic update rows affected: " & rowsAffected)
                        
                        If rowsAffected > 0 Then
                            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Basic password update successful for user: " & userId)
                            Return True
                        Else
                            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Basic update - No rows affected for user: " & userId)
                            
                            ' Double-check user existence for basic update
                            command.Parameters.Clear()
                            command.CommandText = "SELECT Id_PG, PWD FROM kj_pengguna WHERE Id_PG = ?"
                            command.Parameters.AddWithValue("@Id_PG", userId)
                            
                            Using reader As OleDbDataReader = command.ExecuteReader()
                                If reader.Read() Then
                                    System.Diagnostics.Debug.WriteLine("  User found - ID: " & reader("Id_PG").ToString())
                                    System.Diagnostics.Debug.WriteLine("  Current PWD length: " & reader("PWD").ToString().Length)
                                Else
                                    System.Diagnostics.Debug.WriteLine("  User NOT found in database!")
                                End If
                            End Using
                            
                            Return False
                        End If
                    Catch basicEx As Exception
                        System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA: Basic update also failed: " & basicEx.Message)
                        Return False
                    End Try
                End Try
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Force password change error: " & ex.Message)
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Stack trace: " & ex.StackTrace)

            ' Show more detailed error for debugging
            ShowMessage("Ralat sistem: " & ex.Message, "error")
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Fallback method for basic password update without enhanced columns
    ''' </summary>
    Private Function UpdateUserPasswordBasic(userId As String, newPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        
        Try
            Dim connectionString As String = GetConnectionString()
            connection = New OleDbConnection(connectionString)
            connection.Open()
            
            ' Create encrypted password
            Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(newPassword)
            Dim hashedPassword As String = passwordEntry(0)
            
            ' Basic password update (just the pwd field)
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET pwd = ? WHERE id_pg = ?"
                command.Parameters.AddWithValue("@pwd", hashedPassword)
                command.Parameters.AddWithValue("@id_pg", userId)
                
                Dim rowsAffected As Integer = command.ExecuteNonQuery()
                
                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Basic password update:")
                System.Diagnostics.Debug.WriteLine("  Rows affected: " & rowsAffected.ToString())
                System.Diagnostics.Debug.WriteLine("  Hash stored: " & hashedPassword.Substring(0, 10) & "...")
                
                Return rowsAffected > 0
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Basic password update error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Send password change notification email
    ''' </summary>
    Private Sub SendPasswordChangeNotification(userId As String)
        Try
            If emailClient IsNot Nothing Then
                Dim userEmail As String = GetUserEmail(userId)
                If Not String.IsNullOrEmpty(userEmail) Then
                    ' You can implement email notification here
                    System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA Password changed for user: " & userId)
                End If
            End If
        Catch ex As Exception
            ' Don't fail password change if email fails
            System.Diagnostics.Debug.WriteLine("Email notification failed: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Get user email from database
    ''' </summary>
    Private Function GetUserEmail(userId As String) As String
        Dim connection As OleDbConnection = Nothing
        
        Try
            Dim connectionString As String = GetConnectionString()
            connection = New OleDbConnection(connectionString)
            connection.Open()
            
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT email FROM kj_pengguna WHERE id_pg = ?"
                command.Parameters.AddWithValue("@id_pg", userId)
                
                Dim result = command.ExecuteScalar()
                If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                    Return result.ToString()
                End If
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Get user email error: " & ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
        
        Return ""
    End Function

    ''' <summary>
    ''' Complete login process after successful password change
    ''' </summary>
    Private Sub CompleteLoginAfterPasswordChange()
        Try
            ' Set permanent session variables
            Session("Id_PG") = Session("TEMP_USER_ID")
            Session("PWD") = "" ' Don't store password
            Session("Id_KOLEJ") = Session("TEMP_ID_KOLEJ")
            Session("Dc_KOLEJ") = Session("TEMP_DC_KOLEJ")
            Session("ORIGIN") = Session("TEMP_ORIGIN")
            Session("SYSTEM_TYPE") = Session("TEMP_SYSTEM_TYPE")
            Session("PASSWORD_CHANGED") = True
            
            ' Clear temporary session data
            ClearForcePasswordChangeSession()
            
            ' Show success message
            ShowMessage("Kata laluan berjaya dikemaskini! Anda akan dibawa ke sistem utama dalam 3 saat.", "success")
            
            ' Redirect to main system after delay
            ClientScript.RegisterStartupScript(Me.GetType(), "redirect", "setTimeout(function() { window.location.href = 'blank.aspx'; }, 3000);", True)
            
        Catch ex As Exception
            ShowMessage("Ralat melengkapkan log masuk: " & ex.Message, "error")
        End Try
    End Sub

    ''' <summary>
    ''' Clear force password change session variables
    ''' </summary>
    Private Sub ClearForcePasswordChangeSession()
        Session.Remove("FORCE_PASSWORD_CHANGE")
        Session.Remove("TEMP_USER_ID")
        Session.Remove("TEMP_ID_KOLEJ")
        Session.Remove("TEMP_DC_KOLEJ")
        Session.Remove("TEMP_ORIGIN")
        Session.Remove("TEMP_SYSTEM_TYPE")
        Session.Remove("TEMP_OLD_PASSWORD")
    End Sub

    ''' <summary>
    ''' Show message to user with appropriate styling
    ''' </summary>
    Private Sub ShowMessage(message As String, messageType As String)
        lblMessage.Text = message
        pnlMessage.Visible = True
        
        Select Case messageType.ToLower()
            Case "success"
                divMessage.Attributes("class") = "message-panel message-success"
            Case "error"
                divMessage.Attributes("class") = "message-panel message-error"
            Case "warning"
                divMessage.Attributes("class") = "message-panel message-warning"
            Case Else
                divMessage.Attributes("class") = "message-panel message-warning"
        End Select
    End Sub

    ''' <summary>
    ''' Verify if user exists in database and get basic info for debugging
    ''' </summary>
    Private Function VerifyUserExists(userId As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        
        Try
            Dim connectionString As String = GetConnectionString()
            connection = New OleDbConnection(connectionString)
            connection.Open()
            
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT COUNT(*) FROM kj_pengguna WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@Id_PG", userId)
                
                Dim count As Integer = Convert.ToInt32(command.ExecuteScalar())
                
                System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA User verification:")
                System.Diagnostics.Debug.WriteLine("  UserId: " & userId)
                System.Diagnostics.Debug.WriteLine("  User exists: " & (count > 0).ToString())
                
                Return count > 0
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA User verification error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    ''' <summary>
    ''' Test database connection and table structure
    ''' </summary>
    Private Function TestDatabaseConnection() As String
        Dim connection As OleDbConnection = Nothing
        Dim results As String = ""
        
        Try
            Dim connectionString As String = GetConnectionString()
            connection = New OleDbConnection(connectionString)
            connection.Open()
            
            results &= "✅ Database connection successful" & vbCrLf
            
            ' Test basic table access
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT COUNT(*) FROM kj_pengguna"
                
                Dim userCount As Integer = Convert.ToInt32(command.ExecuteScalar())
                results &= "✅ kj_pengguna table accessible, " & userCount.ToString() & " users found" & vbCrLf
            End Using
            
            ' Test column existence
            Try
                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "SELECT salt, pwd_encrypted FROM kj_pengguna WHERE 1=0"
                    command.ExecuteNonQuery()
                    results &= "✅ Enhanced security columns (salt, pwd_encrypted) exist" & vbCrLf
                End Using
            Catch ex As Exception
                results &= "⚠️ Enhanced security columns missing: " & ex.Message & vbCrLf
            End Try
            
        Catch ex As Exception
            results &= "❌ Database connection failed: " & ex.Message & vbCrLf
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
        
        Return results
    End Function

    ''' <summary>
    ''' CRITICAL FIX: PWD column workaround for varchar(15) limitation
    ''' This addresses the root cause - PWD column is too small for SHA256 hash
    ''' </summary>
    Private Function UpdatePasswordWorkaround(userId As String, newPassword As String) As Boolean
        Dim connection As OleDbConnection = Nothing
        
        Try
            Dim connectionString As String = GetConnectionString()
            connection = New OleDbConnection(connectionString)
            connection.Open()
            
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA WORKAROUND: Handling varchar(15) PWD column limitation")
            
            ' Since PWD column is only 15 chars, we'll use a different approach:
            ' 1. Store a shorter hash in PWD column  
            ' 2. Store the full hash and salt in the salt column (nvarchar 255)
            ' 3. Use pwd_encrypted flag to indicate new encryption method
            
            Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(newPassword)
            Dim fullHash As String = passwordEntry(0)
            Dim salt As String = passwordEntry(1)
            
            ' Create a shortened version for PWD column (first 15 chars of hash)
            Dim shortHash As String = fullHash.Substring(0, Math.Min(15, fullHash.Length))
            
            ' Store full hash+salt combination in the salt field as JSON-like string
            Dim fullSecurityData As String = fullHash & "|" & salt
            
            System.Diagnostics.Debug.WriteLine("  Original hash length: " & fullHash.Length)
            System.Diagnostics.Debug.WriteLine("  Truncated hash for PWD: " & shortHash.Length & " chars")
            System.Diagnostics.Debug.WriteLine("  Full security data length: " & fullSecurityData.Length)
            
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "UPDATE kj_pengguna SET PWD = ?, salt = ?, pwd_encrypted = 1, pwd_migrated = 1, migration_date = ?, force_change_date = ? WHERE Id_PG = ?"
                command.Parameters.AddWithValue("@PWD", shortHash)
                command.Parameters.AddWithValue("@salt", fullSecurityData)
                command.Parameters.AddWithValue("@migration_date", DateTime.Now)
                command.Parameters.AddWithValue("@force_change_date", DateTime.Now)
                command.Parameters.AddWithValue("@Id_PG", userId)
                
                Dim rowsAffected As Integer = command.ExecuteNonQuery()
                
                If rowsAffected > 0 Then
                    System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA WORKAROUND: Password update successful")
                    System.Diagnostics.Debug.WriteLine("  PWD field: " & shortHash)
                    System.Diagnostics.Debug.WriteLine("  Salt field contains: full_hash|salt")
                    System.Diagnostics.Debug.WriteLine("  pwd_encrypted: 1 (indicates new method)")
                    Return True
                Else
                    System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA WORKAROUND: No rows affected")
                    Return False
                End If
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("KOLEJ-PDSA WORKAROUND ERROR: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

End Class
