<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="Pwd.aspx.vb" Inherits="SPMJ.PasswordManagement" 
    title="SPMJ KOLEJ - Pengurusan <PERSON>" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        /* SPMJ KOLEJ Clean Password Management Styles */
        .pwd-container {
            max-width: 600px;
            margin: 20px auto;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .pwd-header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .pwd-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .pwd-content {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .message-panel {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message-success {
            background: #dcfce7;
            border: 1px solid #bbf7d0;
            color: #166534;
        }
        
        .message-error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        
        .message-warning {
            background: #fef3c7;
            border: 1px solid #fde68a;
            color: #d97706;
        }
        
        .service-status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 12px;
            text-align: center;
        }
        
        .service-online {
            background: #dcfce7;
            border: 1px solid #bbf7d0;
            color: #166534;
        }
        
        .service-offline {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        
        .password-requirements {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 12px;
        }
        
        .requirement-list {
            margin: 10px 0 0 0;
            padding-left: 20px;
        }
        
        .requirement-list li {
            margin-bottom: 5px;
            color: #64748b;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="pwd-container">
        <!-- Header -->
        <div class="pwd-header">
            <h2>🔐 Pengurusan Kata Laluan</h2>
            <div>Sistem Pengurusan Maklumat Jururawat - KOLEJ</div>
        </div>
        
        <!-- Content -->
        <div class="pwd-content">
            <!-- Service Status -->
            <div id="serviceStatus" class="service-status service-offline">
                ⚠️ Memeriksa status perkhidmatan e-mel...
            </div>
            
            <!-- Message Panel -->
            <asp:Panel ID="pnlMessage" runat="server" Visible="false">
                <div id="divMessage" class="message-panel">
                    <asp:Label ID="lblMessage" runat="server"></asp:Label>
                </div>
            </asp:Panel>
            
            <!-- Password Requirements -->
            <div class="password-requirements">
                <strong>Keperluan Kata Laluan Baharu:</strong>
                <ul class="requirement-list">
                    <li>Sekurang-kurangnya 8 aksara</li>
                    <li>Mengandungi huruf besar (A-Z)</li>
                    <li>Mengandungi huruf kecil (a-z)</li>
                    <li>Mengandungi nombor (0-9)</li>
                    <li>Mengandungi aksara khas (!@#$%^&*)</li>
                    <li>Berbeza daripada kata laluan lama</li>
                </ul>
            </div>
            
            <!-- Password Form -->
            <div class="form-group">
                <label class="form-label" for="txtCurrentPassword">Kata Laluan Semasa:</label>
                <asp:TextBox ID="txtCurrentPassword" runat="server" TextMode="Password" CssClass="form-input" placeholder="Masukkan kata laluan semasa"></asp:TextBox>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="txtNewPassword">Kata Laluan Baharu:</label>
                <asp:TextBox ID="txtNewPassword" runat="server" TextMode="Password" CssClass="form-input" placeholder="Masukkan kata laluan baharu"></asp:TextBox>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="txtConfirmPassword">Sahkan Kata Laluan Baharu:</label>
                <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" CssClass="form-input" placeholder="Sahkan kata laluan baharu"></asp:TextBox>
            </div>
            
            <div class="form-group">
                <asp:Button ID="btnChangePassword" runat="server" Text="Tukar Kata Laluan" CssClass="btn-primary" OnClick="btnChangePassword_Click" />
            </div>
        </div>
    </div>
    
    <script type="text/javascript">
        // Simple service status check
        function checkEmailServiceStatus() {
            var statusDiv = document.getElementById('serviceStatus');
            
            try {
                // Call the web method to check service status
                var xhr = new XMLHttpRequest();
                xhr.open('POST', 'Pwd.aspx/CheckEmailServiceHealth', true);
                xhr.setRequestHeader('Content-Type', 'application/json; charset=utf-8');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                var response = JSON.parse(xhr.responseText);
                                if (response.d) {
                                    var serviceData = JSON.parse(response.d);
                                    updateServiceStatus(serviceData.status === 'online', serviceData.message);
                                } else {
                                    updateServiceStatus(false, 'Tiada respons dari perkhidmatan');
                                }
                            } catch (e) {
                                updateServiceStatus(false, 'Ralat memproses respons');
                            }
                        } else {
                            updateServiceStatus(false, 'Ralat sambungan perkhidmatan');
                        }
                    }
                };
                xhr.send('{}');
            } catch (e) {
                updateServiceStatus(false, 'Ralat memeriksa status perkhidmatan');
            }
        }
        
        function updateServiceStatus(isOnline, message) {
            var statusDiv = document.getElementById('serviceStatus');
            if (isOnline) {
                statusDiv.className = 'service-status service-online';
                statusDiv.innerHTML = '✅ Perkhidmatan E-mel: Dalam Talian (' + (message || 'Operasi normal') + ')';
            } else {
                statusDiv.className = 'service-status service-offline';
                statusDiv.innerHTML = '⚠️ Perkhidmatan E-mel: Luar Talian (' + (message || 'Pemberitahuan dinyahaktifkan') + ')';
            }
        }
        
        // Check status when page loads
        window.onload = function() {
            checkEmailServiceStatus();
        };
        
        // Form validation
        function validatePasswordForm() {
            var currentPwd = document.getElementById('<%= txtCurrentPassword.ClientID %>').value;
            var newPwd = document.getElementById('<%= txtNewPassword.ClientID %>').value;
            var confirmPwd = document.getElementById('<%= txtConfirmPassword.ClientID %>').value;
            
            if (!currentPwd) {
                alert('Sila masukkan kata laluan semasa.');
                return false;
            }
            
            if (!newPwd) {
                alert('Sila masukkan kata laluan baharu.');
                return false;
            }
            
            if (newPwd !== confirmPwd) {
                alert('Kata laluan baharu dan pengesahan tidak sama.');
                return false;
            }
            
            if (newPwd.length < 8) {
                alert('Kata laluan mesti sekurang-kurangnya 8 aksara.');
                return false;
            }
            
            return true;
        }
        
        // Attach validation to form submit
        document.addEventListener('DOMContentLoaded', function() {
            var form = document.forms[0];
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validatePasswordForm()) {
                        e.preventDefault();
                        return false;
                    }
                });
            }
        });
    </script>
</asp:Content>
