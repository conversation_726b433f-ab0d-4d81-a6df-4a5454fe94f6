﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_P2_Sah_Nama
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'Fix security access 23072018 - OSH 
        If Session("ORIGIN") = "yes" Then
            If IsPostBack Then Exit Sub
            'TAHUN APC
            For i = Now.Year To Now.Year - 5 Step -1
                Cb_Tahun.Items.Add(i)
            Next

            'JAWATAN
            With Cb_Jawatan
                .Items.Clear()
                .Items.Add("JURURAWAT BERDAFTAR")
                .Items.Item(.Items.Count - 1).Value = "1"
                .Items.Add("JURURAWAT MASYARAKAT")
                .Items.Item(.Items.Count - 1).Value = "2"
                .Items.Add("PENOLONG JURURAWAT")
                .Items.Item(.Items.Count - 1).Value = "3"
            End With
        Else
            Response.Redirect("p0_Login.aspx")
        End If

        'Comment Ori 23072018 -OSH 
        'If IsPostBack Then Exit Sub
        ''TAHUN APC
        'For i = Now.Year To Now.Year - 5 Step -1
        '    Cb_Tahun.Items.Add(i)
        'Next

        ''JAWATAN
        'With Cb_Jawatan
        '    .Items.Clear()
        '    .Items.Add("JURURAWAT BERDAFTAR")
        '    .Items.Item(.Items.Count - 1).Value = "1"
        '    .Items.Add("JURURAWAT MASYARAKAT")
        '    .Items.Item(.Items.Count - 1).Value = "2"
        '    .Items.Add("PENOLONG JURURAWAT")
        '    .Items.Item(.Items.Count - 1).Value = "3"
        'End With
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim j_daftar As Integer = Cb_Jawatan.SelectedValue
        Dim L, P, X, T, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13 As Long
        Dim L1, P1, X1, L2, P2, X2, L3, P3, X3, L4, P4, X4, L5, P5, X5, L6, P6, X6 As Long
        Dim L7, P7, X7, L8, P8, X8, L9, P9, X9, L10, P10, X10, L11, P11, X11, L12, P12, X12, L13, P13, X13 As Long
        Dim Tajuk, Tajuk2 As String
        Tajuk = "LAPORAN STATISTIK PENGESAHAN PENDAFTARAN BAGI " & Cb_Jawatan.SelectedItem.Text & ", " & Cb_Tahun.Text
        Tajuk2 = "MENGIKUT NEGERI, BULAN DAN JANTINA"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        'Response.Write("<table  border=""1"" >")

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='41' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='41' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: left;'>NEGERI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JANUARI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>FEBRUARI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>MAC</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>APRIL</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>MEI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JUN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JULAI</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>OGOS</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SEPTEMBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>OKTOBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>NOVEMBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>DISEMBER</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: center;'>JUMLAH KESELURUHAN</td>"
        Header += "</tr>"
        Header += "<tr style='vertical-align: middle; text-align: center;'>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "    <td>L</td><td>P</td><td>X</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'NEGERI
        Cmd.CommandText = "SELECT Id_NEGERI, Dc_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "Negeri")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td>" + dr.Item(1) + "</td> "

            'JAN
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=1 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=1 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=1 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L1 += L : P1 += P : X1 += X : T1 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'FEB
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=2 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=2 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=2 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L2 += L : P2 += P : X2 += X : T2 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'MAC
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=3 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=3 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=3 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L3 += L : P3 += P : X4 += X : T3 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'APR
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=4 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=4 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=4 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L4 += L : P4 += P : X4 += X : T4 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'MEI
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=5 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=5 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=5 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L5 += L : P5 += P : X5 += X : T5 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'JUN
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=6 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=6 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=6 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L6 += L : P6 += P : X6 += X : T6 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'JUL
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=7 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=7 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=7 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L7 += L : P7 += P : X7 += X : T7 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'OGOS
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=8 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=8 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=8 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L8 += L : P8 += P : X8 += X : T8 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'SEP
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=9 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=9 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=9 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L9 += L : P9 += P : X9 += X : T9 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'OKT
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=10 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=10 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=10 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L10 += L : P10 += P : X10 += X : T10 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'NOV
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=11 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=11 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=11 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L11 += L : P11 += P : X11 += X : T11 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'DIS
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=12 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=12 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where month(tkh_daftar)=12 and year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L12 += L : P12 += P : X12 += X : T12 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

            'jumlah
            L = 0 : P = 0 : X = 0 : T = 0
            Cmd.CommandText = "select count(nokp) from jt_penuh where year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=1 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and jantina=2 and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Cmd.CommandText = "select count(nokp) from jt_penuh where year(tkh_daftar)=" & tahun & " and j_daftar=" & j_daftar & " and tp_negeri=" & dr.Item(0) & ""
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            L13 += L : P13 += P : X13 += X : T13 += L + P + X
            Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"
            Header += "    <td>" & L + P + X & "</td>"
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH</td>"
        Header += "    <td>" & L1 & "</td><td>" & P1 & "</td><td>" & X1 & "</td>"
        Header += "    <td>" & L2 & "</td><td>" & P2 & "</td><td>" & X2 & "</td>"
        Header += "    <td>" & L3 & "</td><td>" & P3 & "</td><td>" & X3 & "</td>"
        Header += "    <td>" & L4 & "</td><td>" & P4 & "</td><td>" & X4 & "</td>"
        Header += "    <td>" & L5 & "</td><td>" & P5 & "</td><td>" & X5 & "</td>"
        Header += "    <td>" & L6 & "</td><td>" & P6 & "</td><td>" & X6 & "</td>"
        Header += "    <td>" & L7 & "</td><td>" & P7 & "</td><td>" & X7 & "</td>"
        Header += "    <td>" & L8 & "</td><td>" & P8 & "</td><td>" & X8 & "</td>"
        Header += "    <td>" & L9 & "</td><td>" & P9 & "</td><td>" & X9 & "</td>"
        Header += "    <td>" & L10 & "</td><td>" & P10 & "</td><td>" & X10 & "</td>"
        Header += "    <td>" & L11 & "</td><td>" & P11 & "</td><td>" & X11 & "</td>"
        Header += "    <td>" & L12 & "</td><td>" & P12 & "</td><td>" & X12 & "</td>"
        Header += "    <td>" & L13 & "</td><td>" & P13 & "</td><td>" & X13 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T1 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T2 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T3 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T4 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T5 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T6 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T7 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T8 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T9 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T10 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T11 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T12 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'></td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>" & T13 & "</td>"
        Header += "</tr>"

        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub

    Protected Sub Cb_Jawatan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Jawatan.SelectedIndexChanged

    End Sub
End Class