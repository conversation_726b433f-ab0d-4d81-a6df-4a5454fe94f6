Imports System.Security.Cryptography
Imports System.Text

Module TestHashLength
    Sub Main()
        ' Test SHA256 Base64 hash length
        Dim testPassword As String = "testpassword123"
        Dim saltBytes(31) As Byte
        Dim rng As New RNGCryptoServiceProvider()
        rng.GetBytes(saltBytes)
        
        Dim salt As String = Convert.ToBase64String(saltBytes)
        Dim passwordBytes As Byte() = Encoding.UTF8.GetBytes(testPassword)
        Dim saltBytesFromString As Byte() = Convert.FromBase64String(salt)
        
        Dim combinedBytes(passwordBytes.Length + saltBytesFromString.Length - 1) As Byte
        Array.Copy(passwordBytes, 0, combinedBytes, 0, passwordBytes.Length)
        Array.Copy(saltBytesFromString, 0, combinedBytes, passwordBytes.Length, saltBytesFromString.Length)
        
        Using sha256 As New SHA256CryptoServiceProvider()
            Dim hashBytes As Byte() = sha256.ComputeHash(combinedBytes)
            Dim hash As String = Convert.ToBase64String(hashBytes)
            
            Console.WriteLine("Salt length: " & salt.Length)
            Console.WriteLine("Hash length: " & hash.Length)
            Console.WriteLine("Sample salt: " & salt)
            Console.WriteLine("Sample hash: " & hash)
        End Using
        
        Console.ReadLine()
    End Sub
End Module
