﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_TPC_Lulus_Warga
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 2 Step -1
            Cb_Tahun.Items.Add(i)
        Next

    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim K, S, X, T As Long
        Dim Tajuk, Tajuk2 As String

        Tajuk = "Laporan Statistik Kelulusan Pendaftaran TPC pada Tahun " + Cb_Tahun.Text
        Tajuk2 = "mengikut <PERSON>araan dan Sektor(Kerajaan/Swasta)"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "laporan" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>TARAF KEWARGANEGARAAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>KERAJAAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>SWASTA</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        
        Header = ""

        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>BUKAN PENDUDUK</td>"

        K = 0 : S = 0 : X = 0 : T = 0
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 1 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN WHERE Sektor = 1))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then K = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 1 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN WHERE Sektor = 2))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then S = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 1 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then T = Rdr(0) : X = Rdr(0) - K - S
        Rdr.Close()
        T = K + S + X
        Header += "    <td>" & K & "</td><td>" & S & "</td><td>" & X & "</td><td>" & T & "</td>"

        Header += "</tr>"

        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>PENDUDUK TETAP</td>"
        K = 0 : S = 0 : X = 0 : T = 0
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 2 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN WHERE Sektor = 1))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then K = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 2 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN WHERE Sektor = 2))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then S = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 2 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then T = Rdr(0) : X = Rdr(0) - K - S
        Rdr.Close()
        T = K + S + X
        Header += "    <td>" & K & "</td><td>" & S & "</td><td>" & X & "</td><td>" & T & "</td>"
        Header += "</tr>"

        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>BERKAHWIN (MALAYSIA)</td>"

        K = 0 : S = 0 : X = 0 : T = 0
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 3 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN WHERE Sektor = 1))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then K = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 3 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN WHERE Sektor = 2))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then S = Rdr(0)
        Rdr.Close()
        Cmd.CommandText = "SELECT COUNT(NoKP) FROM JT_TPC WHERE Taraf_Warganegara = 3 AND NOKP IN (SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & ") AND NoKP IN (SELECT NoKP FROM JT_TPC_MAJIKAN WHERE Tpt_Amalan IN (SELECT Id_Amalan FROM PN_TPT_AMALAN))"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then T = Rdr(0) : X = Rdr(0) - K - S
        Rdr.Close()
        T = K + S + X
        Header += "    <td>" & K & "</td><td>" & S & "</td><td>" & X & "</td><td>" & T & "</td>"
        Header += "</tr>"

        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub

    
End Class