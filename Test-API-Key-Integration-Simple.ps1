# Test SPMJ KOLEJ Microservice Integration - Simple Version

Write-Host "=== SPMJ KOLEJ MICROSERVICE INTEGRATION TEST ===" -ForegroundColor Green

$baseUrl = "http://localhost:5000"
$apiKey = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"

# Test 1: Check configuration files
Write-Host "`n1. Checking configuration..." -ForegroundColor Cyan
$webConfigPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\Web.config"

if (Test-Path $webConfigPath) {
    $webConfig = Get-Content $webConfigPath -Raw
    
    if ($webConfig -like "*EmailServiceApiKey*") {
        Write-Host "✅ API Key configured in Web.config" -ForegroundColor Green
    } else {
        Write-Host "❌ API Key not found in Web.config" -ForegroundColor Red
    }
    
    if ($webConfig -like "*localhost:5000*") {
        Write-Host "✅ Microservice URL configured correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ Microservice URL not configured" -ForegroundColor Red
    }
}

# Test 2: Check application files
Write-Host "`n2. Checking application files..." -ForegroundColor Cyan
$forcePasswordPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx.vb"
$emailClientPath = "d:\source_code\.NET 3.5. - Q\SPMJ KOLEJ-PDSA\SPMJ\EmailServiceClient.vb"

if (Test-Path $forcePasswordPath) {
    $content = Get-Content $forcePasswordPath -Raw
    if ($content -like "*SendPasswordChangeNotification*") {
        Write-Host "✅ ForcePasswordChange has microservice integration" -ForegroundColor Green
    } else {
        Write-Host "❌ ForcePasswordChange missing microservice integration" -ForegroundColor Red
    }
}

if (Test-Path $emailClientPath) {
    $content = Get-Content $emailClientPath -Raw
    if ($content -like "*X-API-Key*") {
        Write-Host "✅ EmailServiceClient has API Key support" -ForegroundColor Green
    } else {
        Write-Host "❌ EmailServiceClient missing API Key support" -ForegroundColor Red
    }
}

# Test 3: Try to connect to microservice
Write-Host "`n3. Testing microservice connectivity..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/health" -Method GET -TimeoutSec 5
    Write-Host "✅ Microservice is running and accessible" -ForegroundColor Green
    Write-Host "   Response: $response" -ForegroundColor White
} catch {
    Write-Host "⚠️ Microservice not accessible: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "   (This is normal if microservice is not running yet)" -ForegroundColor Gray
}

Write-Host "`n=== SUMMARY ===" -ForegroundColor Green
Write-Host "✅ Microservice integration configured for:" -ForegroundColor Green
Write-Host "   - URL: http://localhost:5000/" -ForegroundColor White
Write-Host "   - API Key authentication: Required" -ForegroundColor White
Write-Host "   - Password change notifications: Enabled" -ForegroundColor White

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start microservice on http://localhost:5000" -ForegroundColor White
Write-Host "2. Test password change in SPMJ KOLEJ application" -ForegroundColor White
Write-Host "3. Verify email notifications are sent" -ForegroundColor White
