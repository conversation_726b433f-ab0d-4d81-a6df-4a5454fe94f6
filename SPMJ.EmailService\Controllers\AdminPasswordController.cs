using Microsoft.AspNetCore.Mvc;
using SPMJ.EmailService.Models;
using SPMJ.EmailService.Services;

namespace SPMJ.EmailService.Controllers;

[ApiController]
[Route("api/admin/password")]
public class AdminPasswordController : ControllerBase
{
    private readonly IAdminPasswordEmailService _adminPasswordEmailService;
    private readonly ILogger<AdminPasswordController> _logger;

    public AdminPasswordController(
        IAdminPasswordEmailService adminPasswordEmailService, 
        ILogger<AdminPasswordController> logger)
    {
        _adminPasswordEmailService = adminPasswordEmailService;
        _logger = logger;
    }

    /// <summary>
    /// Send password notification email (for set/reset operations by admin)
    /// </summary>
    [HttpPost("send-notification")]
    public async Task<ActionResult<EmailResponse>> SendPasswordNotification([FromBody] AdminPasswordNotificationRequest request)
    {
        try
        {
            _logger.LogInformation("Sending password notification email to {Email} for user {UserId}", 
                request.To, request.Data.UserId);

            if (string.IsNullOrEmpty(request.To) || string.IsNullOrEmpty(request.Data.UserId) || 
                string.IsNullOrEmpty(request.Data.Password))
            {
                return BadRequest(new EmailResponse
                {
                    Success = false,
                    Message = "Required fields missing: recipient email, user ID, and password are required"
                });
            }

            var result = await _adminPasswordEmailService.SendPasswordNotificationAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return StatusCode(500, result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password notification email to {Email}", request.To);
            return StatusCode(500, new EmailResponse
            {
                Success = false,
                Message = "Internal server error while sending email"
            });
        }
    }

    /// <summary>
    /// Send welcome email for new users
    /// </summary>
    [HttpPost("send-welcome")]
    public async Task<ActionResult<EmailResponse>> SendWelcomeEmail([FromBody] WelcomeEmailRequest request)
    {
        try
        {
            _logger.LogInformation("Sending welcome email to {Email} for user {UserId}", 
                request.To, request.Data.UserId);

            if (string.IsNullOrEmpty(request.To) || string.IsNullOrEmpty(request.Data.UserId) || 
                string.IsNullOrEmpty(request.Data.TempPassword))
            {
                return BadRequest(new EmailResponse
                {
                    Success = false,
                    Message = "Required fields missing: recipient email, user ID, and temporary password are required"
                });
            }

            var result = await _adminPasswordEmailService.SendWelcomeEmailAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return StatusCode(500, result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending welcome email to {Email}", request.To);
            return StatusCode(500, new EmailResponse
            {
                Success = false,
                Message = "Internal server error while sending welcome email"
            });
        }
    }    /// <summary>
    /// Send password change notification email (when user changes their own password)
    /// </summary>
    [HttpPost("send-change-notification")]
    public async Task<ActionResult<EmailResponse>> SendPasswordChangeNotification([FromBody] PasswordChangeNotificationRequest request)
    {
        try
        {
            _logger.LogInformation("Sending password change notification to {Email} for user {UserId}", 
                request.To, request.Data.UserId);

            if (string.IsNullOrEmpty(request.To) || string.IsNullOrEmpty(request.Data.UserId))
            {
                return BadRequest(new EmailResponse
                {
                    Success = false,
                    Message = "Required fields missing: recipient email and user ID are required"
                });
            }

            // Create notification request
            var notificationRequest = new AdminPasswordNotificationRequest
            {
                To = request.To,
                Subject = request.Subject ?? "Password Changed - SPMJ System",
                TemplateType = "password_change_notification",
                Data = new AdminPasswordNotificationData
                {
                    UserId = request.Data.UserId,
                    UserName = request.Data.UserName ?? request.Data.UserId,
                    SystemName = request.Data.SystemName ?? "SPMJ",
                    Timestamp = request.Data.Timestamp ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    SupportEmail = request.Data.SupportEmail ?? "<EMAIL>",
                    Password = "***", // Not showing actual password in change notification
                    IsTemporary = false
                }
            };

            var result = await _adminPasswordEmailService.SendPasswordNotificationAsync(notificationRequest);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return StatusCode(500, result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password change notification to {Email}", request.To);
            return StatusCode(500, new EmailResponse
            {
                Success = false,
                Message = "Internal server error while sending password change notification"
            });
        }
    }

    /// <summary>
    /// Send force reset notification email
    /// </summary>
    [HttpPost("send-force-reset")]
    public async Task<ActionResult<EmailResponse>> SendForceResetEmail([FromBody] AdminPasswordNotificationRequest request)
    {
        try
        {
            _logger.LogInformation("Sending force reset email to {Email} for user {UserId}", 
                request.To, request.Data.UserId);

            if (string.IsNullOrEmpty(request.To) || string.IsNullOrEmpty(request.Data.UserId) || 
                string.IsNullOrEmpty(request.Data.Password))
            {
                return BadRequest(new EmailResponse
                {
                    Success = false,
                    Message = "Required fields missing: recipient email, user ID, and password are required"
                });
            }

            // Override template type for force reset
            request.TemplateType = "force_reset";
            request.Subject = "Password Reset - SPMJ System (Action Required)";

            var result = await _adminPasswordEmailService.SendPasswordNotificationAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return StatusCode(500, result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending force reset email to {Email}", request.To);
            return StatusCode(500, new EmailResponse
            {
                Success = false,
                Message = "Internal server error while sending force reset email"
            });
        }
    }

    /// <summary>
    /// Validate email format
    /// </summary>
    [HttpPost("validate-email")]
    public ActionResult<EmailResponse> ValidateEmail([FromBody] string email)
    {
        try
        {
            var isValid = _adminPasswordEmailService.IsValidEmailFormat(email);
            
            return Ok(new EmailResponse
            {
                Success = isValid,
                Message = isValid ? "Email format is valid" : "Invalid email format"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating email format for {Email}", email);
            return StatusCode(500, new EmailResponse
            {
                Success = false,
                Message = "Internal server error while validating email"
            });
        }
    }

    /// <summary>
    /// Health check endpoint for admin password email services
    /// </summary>
    [HttpGet("health")]
    public ActionResult<object> HealthCheck()
    {
        return Ok(new
        {
            Service = "AdminPasswordEmailService",
            Status = "Healthy",
            Timestamp = DateTime.Now,
            Version = "1.0.0",
            Endpoints = new[]
            {
                "/api/admin/password/send-notification",
                "/api/admin/password/send-welcome", 
                "/api/admin/password/send-force-reset",
                "/api/admin/password/validate-email"
            }
        });
    }

    /// <summary>
    /// Test SMTP connectivity (debugging endpoint)
    /// </summary>
    [HttpGet("test-smtp")]
    public async Task<ActionResult<EmailResponse>> TestSmtpConnection()
    {
        try
        {
            var result = await SmtpTestService.TestSmtpConnectionAsync(
                "smtp.gmail.com", 
                587, 
                "<EMAIL>", 
                "ejbe mhrr elwf ynwx"
            );
            
            return Ok(new EmailResponse
            {
                Success = result,
                Message = result ? "SMTP connection test successful" : "SMTP connection test failed"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SMTP connection test failed");
            return StatusCode(500, new EmailResponse
            {
                Success = false,
                Message = $"SMTP test error: {ex.Message}"
            });
        }
    }
}
