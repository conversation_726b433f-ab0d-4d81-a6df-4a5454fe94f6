@echo off
echo Fixing PN_AdminPasswordManager Type Load Error...
echo.

REM Backup original files
echo Creating backups...
copy /Y "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx" "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.bak"
copy /Y "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.vb" "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.vb.bak"
copy /Y "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.designer.vb" "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.designer.vb.bak"
echo.

REM Apply fixes
echo Applying fixes...
copy /Y "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.vb.fix" "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.vb"
copy /Y "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.designer.vb.fix" "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA\SPMJ\PN_AdminPasswordManager.aspx.designer.vb"
echo.

echo Requesting application restart...
echo You may need to restart your web server or application pool for changes to take effect.
echo.

echo Fix process completed!
pause
