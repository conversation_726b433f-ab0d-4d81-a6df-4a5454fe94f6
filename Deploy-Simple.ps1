# SPMJ KOLEJ Simple File Deployment
param(
    [string]$TargetPath = "C:\temp\SPMJ_Deployment",
    [string]$MicroserviceUrl = "http://localhost:5000"
)

Write-Host "=== SPMJ KOLEJ FILE DEPLOYMENT ===" -ForegroundColor Green

# Check email microservice
Write-Host "Checking email microservice..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$MicroserviceUrl/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "Email microservice is running and healthy" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Gray
} catch {
    Write-Host "Email microservice not accessible: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Continuing with deployment..." -ForegroundColor Gray
}

# Deploy files
Write-Host "Deploying files to: $TargetPath" -ForegroundColor Cyan

$sourceFiles = @{
    "SPMJ KOLEJ-PDSA\SPMJ\SPMJ_KOLEJ_PWD.dll" = "bin\SPMJ_KOLEJ_PWD.dll"
    "SPMJ KOLEJ-PDSA\SPMJ\Pwd.aspx" = "Pwd.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\ForcePasswordChange.aspx" = "ForcePasswordChange.aspx"
    "SPMJ KOLEJ-PDSA\SPMJ\Web.config" = "Web.config"
}

# Create target directories
if (-not (Test-Path $TargetPath)) {
    New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
    Write-Host "Created deployment directory: $TargetPath" -ForegroundColor Green
}

if (-not (Test-Path "$TargetPath\bin")) {
    New-Item -ItemType Directory -Path "$TargetPath\bin" -Force | Out-Null
    Write-Host "Created bin directory" -ForegroundColor Green
}

# Copy files
Write-Host "Copying files..." -ForegroundColor Cyan
$deployedCount = 0

foreach ($source in $sourceFiles.Keys) {
    $target = "$TargetPath\$($sourceFiles[$source])"
    if (Test-Path $source) {
        try {
            Copy-Item -Path $source -Destination $target -Force
            $fileInfo = Get-ChildItem $target
            Write-Host "SUCCESS: $($sourceFiles[$source]) - $($fileInfo.Length) bytes" -ForegroundColor Green
            $deployedCount++
        } catch {
            Write-Host "ERROR: Failed to deploy $source - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "ERROR: Source file not found - $source" -ForegroundColor Red
    }
}

Write-Host "Deployment Summary:" -ForegroundColor White
Write-Host "Files deployed: $deployedCount / $($sourceFiles.Count)" -ForegroundColor Green

# Verification
Write-Host "Verifying deployment..." -ForegroundColor Cyan
$verifyFiles = @(
    "$TargetPath\bin\SPMJ_KOLEJ_PWD.dll",
    "$TargetPath\Pwd.aspx",
    "$TargetPath\ForcePasswordChange.aspx",
    "$TargetPath\Web.config"
)

foreach ($file in $verifyFiles) {
    if (Test-Path $file) {
        $info = Get-ChildItem $file
        Write-Host "FOUND: $(Split-Path $file -Leaf) - $($info.Length) bytes" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $(Split-Path $file -Leaf)" -ForegroundColor Red
    }
}

Write-Host "=== DEPLOYMENT COMPLETE ===" -ForegroundColor Green
Write-Host "Files deployed to: $TargetPath" -ForegroundColor White
