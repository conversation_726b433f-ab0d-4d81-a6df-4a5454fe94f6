﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_XM_SemakCalon.aspx.vb" Inherits="SPMJ.WebForm7" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 28px;
        }
        .style2
        {
            width: 166px;
        }
        .style3
        {
            height: 28px;
            width: 166px;
        }
        .style4
        {
            width: 701px;
        }
        .style5
        {
            height: 28px;
            width: 701px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style2"></td>
            <td class="style4"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td class="style4">&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style3"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#666666" class="style5">Semak Calon Peperiksaan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style4">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style4">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">KOLEJ/INSTITUSI</asp:TextBox>
                <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="520px">
        </asp:DropDownList>
            </td>
            <td align="center"></td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style4">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">JENIS PEPERIKSAAN</asp:TextBox>
                <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="200px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT BERDAFTAR</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT MASYARAKAT</asp:ListItem>
                    <asp:ListItem Value="3">PENOLONG JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">KEBIDANAN I</asp:ListItem>
        </asp:DropDownList>
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style4">&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox 
                    ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">STATUS</asp:TextBox>
                <asp:DropDownList ID="Cb_Status" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="130px">
                    <asp:ListItem>DITERIMA</asp:ListItem>
                    <asp:ListItem>DITOLAK</asp:ListItem>
                    <asp:ListItem>BELUM DIPROSES</asp:ListItem>
        </asp:DropDownList>
                <asp:Button ID="cmd_Cari" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
            &nbsp;<asp:Button ID="cmd_Cari0" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="EXCEL" Width="60px" />
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White" class="style4">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td class="style4">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td class="style4">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" GridLines="Horizontal" BorderColor="#666666">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="#F4F4F4" Height="21px" />
                    <Columns>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="PILIH">
                            <ItemTemplate>
                                <asp:CheckBox ID="chk_Pilih" runat="server" />
                            </ItemTemplate>
                            <EditItemTemplate>
                                <asp:CheckBox ID="CheckBox1" runat="server" />
                            </EditItemTemplate>
                            <HeaderTemplate>
                                PILIH
                            </HeaderTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="True" />
                    <HeaderStyle BackColor="#666666" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td>
            <td></td>
        </tr>
    
    
        <tr>
            <td class="style2">&nbsp;</td>
            <td class="style4">
                <br />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
