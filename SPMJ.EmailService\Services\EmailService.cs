using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;
using SPMJ.EmailService.Models;

namespace SPMJ.EmailService.Services;

public class EmailService : IEmailService
{
    private readonly EmailSettings _emailSettings;
    private readonly ILogger<EmailService> _logger;

    public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger)
    {
        _emailSettings = emailSettings.Value;
        _logger = logger;
    }

    public async Task<bool> SendPasswordResetEmailAsync(string email, string userName, string resetToken, string? baseUrl = null)
    {
        try
        {
            var resetUrl = $"{baseUrl ?? "http://localhost:8080"}/PasswordResetModern.aspx?token={resetToken}";
            
            var subject = "SPMJ - Reset Kata Laluan";
            var htmlBody = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5282;'>Reset Kata Laluan SPMJ</h2>
                        <p>Assalamualaikum {userName},</p>
                        <p>Anda telah meminta untuk reset kata laluan bagi akaun SPMJ anda.</p>
                        <p>Sila klik pautan di bawah untuk reset kata laluan:</p>
                        <div style='margin: 20px 0;'>
                            <a href='{resetUrl}' 
                               style='background-color: #2c5282; color: white; padding: 12px 24px; 
                                      text-decoration: none; border-radius: 4px; display: inline-block;'>
                                Reset Kata Laluan
                            </a>
                        </div>
                        <p><strong>Pautan ini akan luput dalam masa 24 jam.</strong></p>
                        <p>Jika anda tidak meminta reset kata laluan, sila abaikan email ini.</p>
                        <hr style='margin: 30px 0; border: 1px solid #e2e8f0;'>
                        <p style='font-size: 12px; color: #666;'>
                            Email ini dijana secara automatik. Sila jangan balas email ini.
                        </p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, htmlBody);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send password reset email to {Email}", email);
            return false;
        }
    }

    public async Task<bool> SendPasswordResetEmailAsync(string email, string userName, string tempPassword)
    {
        try
        {
            var subject = "SPMJ - Kata Laluan Sementara (Pemulihan)";
            var htmlBody = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5282;'>Pemulihan Kata Laluan SPMJ</h2>
                        <p>Assalamualaikum {userName},</p>
                        <p>Anda telah meminta pemulihan kata laluan untuk akaun SPMJ anda.</p>
                        <div style='background-color: #f7fafc; padding: 15px; border-radius: 4px; margin: 20px 0;'>
                            <p><strong>Kata Laluan Sementara:</strong></p>
                            <p style='font-family: monospace; font-size: 18px; color: #e53e3e; font-weight: bold;'>
                                {tempPassword}
                            </p>
                        </div>
                        <p><strong>Penting:</strong></p>
                        <ul>
                            <li>Sila log masuk menggunakan kata laluan sementara ini</li>
                            <li>Anda akan diminta untuk menukar kata laluan selepas log masuk</li>
                            <li>Kata laluan sementara ini hanya sah untuk satu kali penggunaan</li>
                        </ul>
                        <p>Jika anda tidak meminta pemulihan kata laluan, sila hubungi pentadbir sistem segera.</p>
                        <hr style='margin: 30px 0; border: 1px solid #e2e8f0;'>
                        <p style='font-size: 12px; color: #666;'>
                            Email ini dijana secara automatik. Sila jangan balas email ini.<br>
                            Sistem Pengurusan Jururawat Malaysia (SPMJ)<br>
                            Kementerian Kesihatan Malaysia
                        </p>
                    </div>
                </body>
                </html>";

            await SendEmailAsync(email, subject, htmlBody);
            _logger.LogInformation("Password recovery email sent successfully to {Email}", email);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send password recovery email to {Email}", email);
            return false;
        }
    }

    public async Task<bool> SendAdminPasswordEmailAsync(string email, string userName, string tempPassword, string adminName)
    {
        try
        {
            var subject = "SPMJ - Kata Laluan Sementara";
            var htmlBody = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5282;'>Kata Laluan Sementara SPMJ</h2>
                        <p>Assalamualaikum {userName},</p>
                        <p>Admin <strong>{adminName}</strong> telah menetapkan kata laluan sementara untuk akaun SPMJ anda.</p>
                        <div style='background-color: #f7fafc; padding: 15px; border-radius: 4px; margin: 20px 0;'>
                            <p><strong>Kata Laluan Sementara:</strong></p>
                            <p style='font-family: monospace; font-size: 18px; color: #e53e3e; font-weight: bold;'>
                                {tempPassword}
                            </p>
                        </div>
                        <p><strong>Penting:</strong></p>
                        <ul>
                            <li>Sila log masuk menggunakan kata laluan sementara ini</li>
                            <li>Anda akan diminta untuk menukar kata laluan pada log masuk pertama</li>
                            <li>Kata laluan baru mestilah sekurang-kurangnya 6 aksara</li>
                        </ul>
                        <hr style='margin: 30px 0; border: 1px solid #e2e8f0;'>
                        <p style='font-size: 12px; color: #666;'>
                            Email ini dijana secara automatik. Sila jangan balas email ini.
                        </p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, htmlBody);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send admin password email to {Email}", email);
            return false;
        }
    }

    public async Task<bool> SendOtpEmailAsync(string email, string userName, string otpCode, string purpose)
    {
        try
        {
            var purposeText = purpose switch
            {
                "LOGIN" => "Log Masuk",
                "PASSWORD_RESET" => "Reset Kata Laluan",
                "ADMIN_ACTION" => "Tindakan Admin",
                _ => "Pengesahan"
            };

            var subject = $"SPMJ - Kod OTP untuk {purposeText}";
            var htmlBody = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5282;'>Kod Pengesahan SPMJ</h2>
                        <p>Assalamualaikum {userName},</p>
                        <p>Berikut adalah kod pengesahan untuk <strong>{purposeText}</strong>:</p>
                        <div style='background-color: #f7fafc; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>
                            <p style='font-family: monospace; font-size: 32px; color: #2c5282; font-weight: bold; margin: 0; letter-spacing: 5px;'>
                                {otpCode}
                            </p>
                        </div>
                        <p><strong>Kod ini akan luput dalam masa 5 minit.</strong></p>
                        <p>Jika anda tidak meminta kod ini, sila abaikan email ini.</p>
                        <hr style='margin: 30px 0; border: 1px solid #e2e8f0;'>
                        <p style='font-size: 12px; color: #666;'>
                            Email ini dijana secara automatik. Sila jangan balas email ini.
                        </p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, htmlBody);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send OTP email to {Email}", email);
            return false;
        }
    }

    public async Task<bool> SendWelcomeEmailAsync(string email, string userName, string tempPassword)
    {
        try
        {
            var subject = "Selamat Datang ke Sistem SPMJ";
            var htmlBody = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5282;'>Selamat Datang ke Sistem SPMJ</h2>
                        <p>Assalamualaikum {userName},</p>
                        <p>Akaun SPMJ anda telah berjaya diwujudkan. Berikut adalah maklumat log masuk anda:</p>
                        <div style='background-color: #f7fafc; padding: 15px; border-radius: 4px; margin: 20px 0;'>
                            <p><strong>ID Pengguna:</strong> {userName}</p>
                            <p><strong>Kata Laluan Sementara:</strong></p>
                            <p style='font-family: monospace; font-size: 18px; color: #e53e3e; font-weight: bold;'>
                                {tempPassword}
                            </p>
                        </div>
                        <p><strong>Langkah seterusnya:</strong></p>
                        <ol>
                            <li>Log masuk ke sistem menggunakan maklumat di atas</li>
                            <li>Anda akan diminta untuk menukar kata laluan</li>
                            <li>Sila pilih kata laluan yang kuat (sekurang-kurangnya 6 aksara)</li>
                        </ol>
                        <hr style='margin: 30px 0; border: 1px solid #e2e8f0;'>
                        <p style='font-size: 12px; color: #666;'>
                            Email ini dijana secara automatik. Sila jangan balas email ini.
                        </p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, htmlBody);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send welcome email to {Email}", email);
            return false;
        }
    }

    private async Task<bool> SendEmailAsync(string toEmail, string subject, string htmlBody)
    {
        try
        {
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress(_emailSettings.FromName, _emailSettings.FromEmail));
            message.To.Add(new MailboxAddress("", toEmail));
            message.Subject = subject;

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = htmlBody
            };
            message.Body = bodyBuilder.ToMessageBody();

            using var client = new SmtpClient();
            await client.ConnectAsync(_emailSettings.SmtpServer, _emailSettings.SmtpPort, 
                _emailSettings.UseSsl ? SecureSocketOptions.StartTls : SecureSocketOptions.None);
            
            await client.AuthenticateAsync(_emailSettings.Username, _emailSettings.Password);
            await client.SendAsync(message);
            await client.DisconnectAsync(true);

            _logger.LogInformation("Email sent successfully to {Email}", toEmail);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Email}", toEmail);
            return false;
        }
    }
}
