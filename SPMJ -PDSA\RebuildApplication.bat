@echo off
echo Rebuilding the SPMJ web application...

echo.
echo Stopping IIS service if running...
net stop w3svc

echo.
echo Clearing temporary ASP.NET files...
IF EXIST %windir%\Microsoft.NET\Framework\v2.0.50727\Temporary ASP.NET Files\spmj (
    del /q /s "%windir%\Microsoft.NET\Framework\v2.0.50727\Temporary ASP.NET Files\spmj\*.*"
)

echo.
echo Rebuilding the application...
cd "d:\source_code\.NET 3.5. - Q\SPMJ -PDSA"
"%windir%\Microsoft.NET\Framework\v2.0.50727\aspnet_compiler.exe" -v /SPMJ -p SPMJ -f -d Temp

echo.
echo Copying compiled files...
xcopy /y /s "Temp\*.*" "SPMJ\*.*"

echo.
echo Starting IIS service...
net start w3svc

echo.
echo The application has been recompiled.
echo Please try accessing the page now.
echo.

pause
