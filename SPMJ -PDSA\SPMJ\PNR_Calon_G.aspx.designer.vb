﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:2.0.50727.8784
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On



Partial Public Class PNR_Calon_G

    '''<summary>
    '''cmdPDF control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdPDF As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdReg control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdReg As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''Label1 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents Label1 As Global.System.Web.UI.WebControls.Label

    '''<summary>
    '''Tx_Tahun control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents Tx_Tahun As Global.System.Web.UI.WebControls.TextBox

    '''<summary>
    '''Tx_Tkh_Mula control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents Tx_Tkh_Mula As Global.System.Web.UI.WebControls.TextBox

    '''<summary>
    '''cmdWord control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdWord As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdFail control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdFail As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''Tx_Tkh_Tamat control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents Tx_Tkh_Tamat As Global.System.Web.UI.WebControls.TextBox

    '''<summary>
    '''cmdExcel control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdExcel As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdLow control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdLow As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdStorePDF control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdStorePDF As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdPDF_T control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdPDF_T As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdStorePDF_T control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdStorePDF_T As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdNestTable control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdNestTable As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdStoreNested control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdStoreNested As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdSizePDF control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdSizePDF As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdPopulateTable control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdPopulateTable As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdPDFheader control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdPDFheader As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdTable control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdTable As Global.System.Web.UI.WebControls.Button

    '''<summary>
    '''cmdRows control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cmdRows As Global.System.Web.UI.WebControls.Button
End Class
